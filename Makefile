# Supply Chain 开发环境 Makefile
# 提供简洁高效的开发环境启动命令

.PHONY: dev-backend dev-frontend dev-shop help

# 默认目标
help:
	@echo "Supply Chain 开发环境命令："
	@echo "  make dev-backend   - 启动supply-chain后端API服务"
	@echo "  make dev-frontend  - 启动supply-chain管理后台前端开发服务器"
	@echo "  make dev-shop      - 启动supply-chain商城前端开发服务器"
	@echo "  make help          - 显示此帮助信息"

# 启动后端API服务
dev-backend:
	@echo "启动supply-chain后端API服务..."
	cd supply-chain && go run main.go -c $(CURDIR)/supply-chain/

# 启动管理后台前端开发服务器
dev-frontend:
	@echo "启动supply-chain管理后台前端开发服务器..."
	cd gin-vue-admin/web && npm run serve

# 启动商城前端开发服务器
dev-shop:
	@echo "启动supply-chain商城前端开发服务器..."
	cd shop-side-shop && npm run serve
