package v1

import (
	"ad/model"
	"ad/request"
	"ad/service"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

// @Tags Ad
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ad/createAd [post]
func CreateAd(c *gin.Context) {
	var ad model.Ad
	err := c.ShouldBindJSON(&ad)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateAd(ad); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "新增广告'"+ad.Title+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags Ad
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /ad/deleteAd [post]
func DeleteAd(c *gin.Context) {
	var ad model.Ad
	err := c.ShouldBindJSON(&ad)
	err = source.DB().First(&ad, ad.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteAd(ad); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "删除广告'"+ad.Title+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags Ad
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /ad/deleteAdByIds [post]
func DeleteAdByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteAdByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "批量删除广告'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags Ad
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /ad/updateAd [post]
func UpdateAd(c *gin.Context) {
	var ad model.Ad
	err := c.ShouldBindJSON(&ad)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateAd(ad); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "修改广告'"+ad.Title+"'")
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func ChangeStatus(c *gin.Context) {
	var ad model.Ad
	err := c.ShouldBindJSON(&ad)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.ChangeStatus(ad); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Ad
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "用id查询"
// @Success 200 {object} model.Ad
// @Router /ad/findAd [post]
func FindAd(c *gin.Context) {
	var ad model.Ad
	err := c.ShouldBindQuery(&ad)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, read := service.GetAd(ad.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}

// @Tags Ad
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AdSearch true "分页获取列表"
// @Success 200 {string} string []model.Ad
// @Router /ad/getAdList [post]
func GetAdList(c *gin.Context) {
	var pageInfo request.AdSearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetAdInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 应用
// @Summary 获取ApplicationLevel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationSearch true "获取ApplicationLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ad/getAdCategoryOption [get]
func GetAdCategoryOption(c *gin.Context) {
	list := service.GetAdCategoryList()
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List: list,
	}, "获取成功", c)

}

// @Tags 应用
// @Summary 获取ApplicationLevel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationSearch true "获取ApplicationLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ad/getAdChannelOption [get]
func GetAdChannelOption(c *gin.Context) {
	err, list := service.GetAdChannelList(request.AdChannelSearch{})
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}

}
