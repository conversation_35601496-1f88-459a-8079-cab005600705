package v1

import (
	"ad/model"
	"ad/request"
	"ad/service"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

// @Tags adChannel表
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdChannel true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/adChannel/createAdChannel [post]
func CreateAdChannel(c *gin.Context) {
	var adChannel model.AdChannel
	var err error
	err = c.ShouldBindJSON(&adChannel)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateAdChannel(adChannel); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "新增频道'"+adChannel.Title+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags adChannel表
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdChannel true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /api/adChannel/deleteAdChannel [post]
func DeleteAdChannel(c *gin.Context) {
	var adChannel model.AdChannel
	var err error
	err = c.ShouldBindJSON(&adChannel)
	err = source.DB().First(&adChannel, adChannel.ID).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteAdChannel(adChannel); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "删除频道'"+adChannel.Title+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags adChannel表
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /api/adChannel/deleteAdChannelByIds [post]
func DeleteAdChannelByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	var err error
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteAdChannelByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "批量删除频道'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags adChannel表
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdChannel true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /api/adChannel/updateAdChannel [post]
func UpdateAdChannel(c *gin.Context) {
	var adChannel model.AdChannel
	var err error
	err = c.ShouldBindJSON(&adChannel)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateAdChannel(adChannel); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "修改频道'"+adChannel.Title+"'")
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags adChannel表
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdChannel true "用id查询"
// @Success 200 {object} model.AdChannel
// @Router /api/adChannel/findAdChannel [post]
func FindAdChannel(c *gin.Context) {
	var adChannel model.AdChannel
	var err error
	err = c.ShouldBindQuery(&adChannel)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, readChannel := service.GetAdChannel(adChannel.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"readChannel": readChannel}, c)
	}
}

// @Tags adChannel表
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AdChannelSearch true "分页获取列表"
// @Success 200 {string} string []model.AdChannel
// @Router /api/adChannel/getAdChannelList [post]
func GetAdChannelList(c *gin.Context) {
	var pageInfo request.AdChannelSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAdChannelInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
