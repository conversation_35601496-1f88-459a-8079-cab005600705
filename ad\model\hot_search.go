// 自动生成模板AdChannel
package model

import (
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type HotSearch  struct {
	source.GVA_MODEL
	Title string `json:"title" form:"title" gorm:"column:title;comment:;type:varchar(255);size:255;"`
	IsDisplay    *int      `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:tinyint;size:1;"` //是否显示
	Sort    int      `json:"sort" form:"sort" gorm:"column:sort;comment:排序;type:smallint;size:6;"` //是否显示
	}
