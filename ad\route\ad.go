package route

import (
	"ad/api/v1"
	"github.com/gin-gonic/gin"
)

// 后台私有
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	AdRouter := Router.Group("ad")
	{
		AdRouter.POST("createAd", v1.CreateAd)                      // 新建Ad
		AdRouter.DELETE("deleteAd", v1.DeleteAd)                    // 删除Ad
		AdRouter.DELETE("deleteAdByIds", v1.DeleteAdByIds)          // 批量删除Ad
		AdRouter.PUT("updateAd", v1.UpdateAd)                       // 更新Ad
		AdRouter.PUT("changeStatus", v1.ChangeStatus)                       // 修改状态
		AdRouter.GET("findAd", v1.FindAd)                           // 根据ID获取Ad
		AdRouter.GET("getAdList", v1.GetAdList)                     // 获取Ad列表
		AdRouter.GET("getAdCategoryOption", v1.GetAdCategoryOption) // 获取Ad列表
		AdRouter.GET("getAdChannelOption", v1.GetAdChannelOption) // 获取Ad列表
		AdRouter.POST("createAdChannel", v1.CreateAdChannel)   // 新建AdChannel
		AdRouter.DELETE("deleteAdChannel", v1.DeleteAdChannel) // 删除AdChannel
		AdRouter.DELETE("deleteAdChannelByIds", v1.DeleteAdChannelByIds) // 批量删除AdChannel
		AdRouter.PUT("updateAdChannel", v1.UpdateAdChannel)    // 更新AdChannel
		AdRouter.GET("findAdChannel", v1.FindAdChannel)        // 根据ID获取AdChannel
		AdRouter.GET("getAdChannelList", v1.GetAdChannelList)  // 获取AdChannel列表
	}
}

// 后台公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {

}

// 前端公共
func InitUserPublicRouter(Router *gin.RouterGroup) {

}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {

}
