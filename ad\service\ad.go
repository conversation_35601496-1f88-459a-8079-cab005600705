package service

import (
	"ad/model"
	"ad/request"
	yzRequest "yz-go/request"
	"yz-go/response"
	"yz-go/source"
)

//
//@function: CreateAd
//@description: 创建Ad记录
//@param: ad model.Ad
//@return: err error

func CreateAd(ad model.Ad) (err error) {
	err = source.DB().Create(&ad).Error
	return err
}

//
//@function: DeleteAd
//@description: 删除Ad记录
//@param: ad model.Ad
//@return: err error

func DeleteAd(ad model.Ad) (err error) {
	err = source.DB().Delete(&ad).Error
	return err
}

//
//@function: DeleteAdByIds
//@description: 批量删除Ad记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteAdByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.Ad{}, "id in ?", ids.Ids).Error
	return err
}

//
//@function: UpdateAd
//@description: 更新Ad记录
//@param: ad *model.Ad
//@return: err error

func UpdateAd(ad model.Ad) (err error) {
	err = source.DB().Save(&ad).Error
	return err
}

func ChangeStatus(ad model.Ad) (err error) {
	err = source.DB().Updates(&ad).Error
	return err
}

//
//@function: GetAd
//@description: 根据id获取Ad记录
//@param: id uint
//@return: err error, ad model.Ad

func GetAd(id uint) (err error, ad model.Ad) {
	err = source.DB().Where("id = ?", id).First(&ad).Error
	return
}

//
//@function: GetAdInfoList
//@description: 分页获取Ad记录
//@param: info request.AdSearch
//@return: err error, list interface{}, total int64

func GetAdInfoList(info request.AdSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.Ad{})
	var ads []model.Ad
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&ads).Error
	return err, ads, total
}

//
//@function: GetAdInfoList
//@param: info request.AdSearch
//@return: err error, list interface{}, total int64

func GetAdCategoryList() (list interface{}) {

	var options = []model.AdCategory{
		{ID: 1, Title: "首页-banner（N）"},
		{ID: 2, Title: "首页-频道广场-左侧大图（2）"},
		{ID: 3, Title: "首页-频道广场-右侧小图（24）"},
		{ID: 4, Title: "首页-产品盒子-标题图（1）"},
		{ID: 5, Title: "首页-频道广场-标题图（1）"},
		{ID: 6, Title: "首页-多个产品盒子-标题图（N）"},
		{ID: 7, Title: "首页-为你推荐-标题图（1）"},
	}

	return options
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetProduct
//@description: 根据id获取广告
//@param: id uint
//@return: err error, product model.SalesProduct

func GetAdList(info model.Ad) (err error, list []response.Banner) {
	var banner []response.Banner
	db := source.DB().Model(&model.Ad{})
	if info.Cid != 0 {
		db.Where("cid = ?", info.Cid)
	}

	db.Where("status = 1")
	err = db.Order("sort desc").Find(&banner).Error
	return err, banner
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetProduct
//@description: 根据id获取广告
//@param: id uint
//@return: err error, product model.SalesProduct

func GetAdFirst(info model.Ad) (err error, list response.Banner) {
	var banner response.Banner
	db := source.DB().Model(&model.Ad{})
	if info.Cid != 0 {
		db.Where("cid = ?", info.Cid)
	}

	db.Where("status = 1")
	err = db.First(&banner).Error
	return err, banner
}

func GetAdFirstWithCustom(info model.Ad) (err error, list response.Banner) {
	var banner response.Banner
	db := source.DB().Model(&model.Ad{})
	if info.Cid != 0 {
		db.Where("cid = ?", info.Cid)
	}

	db.Where("status = 1")
	err = db.First(&banner).Error
	return err, banner
}
