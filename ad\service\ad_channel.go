package service

import (
	"ad/model"
	yzRequest "yz-go/request"
	"ad/request"
	"yz-go/source"
	"yz-go/response"
)

//
//@function: CreateAdChannel
//@description: 创建AdChannel记录
//@param: adChannel model.AdChannel
//@return: err error

func CreateAdChannel(adChannel model.AdChannel) (err error) {
	err = source.DB().Create(&adChannel).Error
	return err
}

//
//@function: DeleteAdChannel
//@description: 删除AdChannel记录
//@param: adChannel model.AdChannel
//@return: err error

func DeleteAdChannel(adChannel model.AdChannel) (err error) {
	err = source.DB().Delete(&adChannel).Error
	return err
}

//
//@function: DeleteAdChannelByIds
//@description: 批量删除AdChannel记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteAdChannelByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.AdChannel{},"id in ?",ids.Ids).Error
	return err
}

//
//@function: UpdateAdChannel
//@description: 更新AdChannel记录
//@param: adChannel *model.AdChannel
//@return: err error

func UpdateAdChannel(adChannel model.AdChannel) (err error) {
	err = source.DB().Save(&adChannel).Error
	return err
}

//
//@function: GetAdChannel
//@description: 根据id获取AdChannel记录
//@param: id uint
//@return: err error, adChannel model.AdChannel

func GetAdChannel(id uint) (err error, adChannel model.AdChannel) {
	err = source.DB().Where("id = ?", id).First(&adChannel).Error
	return
}

//
//@function: GetAdChannelInfoList
//@description: 分页获取AdChannel记录
//@param: info request.AdChannelSearch
//@return: err error, list interface{}, total int64

func GetAdChannelInfoList(info request.AdChannelSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := source.DB().Model(&model.AdChannel{})
    var adChannels []model.AdChannel
    // 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&adChannels).Error
	return err, adChannels, total
}

//
//@function: GetAdChannelInfoList
//@description: 分页获取AdChannel记录
//@param: info request.AdChannelSearch
//@return: err error, list interface{}, total int64

func GetAdChannelList(info request.AdChannelSearch) (err error, list []response.Channel) {

    // 创建db
	db := source.DB().Model(&model.AdChannel{})
    var adChannels []response.Channel
    // 如果有条件搜索 下方会自动创建搜索语句
	err = db.Limit(12).Find(&adChannels).Error
	return err, adChannels
}