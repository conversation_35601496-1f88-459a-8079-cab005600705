package app

import (
	"after-sales/model"
	"after-sales/request"
	"after-sales/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// @Tags 应用
// @Summary 创建AfterSalesAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAddress true "创建AfterSalesAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /application/createAfterSalesAddress [post]
func CreateAfterSalesAddress(c *gin.Context) {
	var application model.AfterSalesAddress
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateAfterSalesAddress(application); err != nil {
		log.Log().Error("售后,创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("售后,创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 应用
// @Summary 删除AfterSalesAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAddress true "删除AfterSalesAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /application/deleteAfterSalesAddress [delete]
func DeleteAfterSalesAddress(c *gin.Context) {
	var application model.AfterSalesAddress
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteAfterSalesAddress(application); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 应用
// @Summary 批量删除AfterSalesAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除AfterSalesAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /application/deleteAfterSalesAddressByIds [delete]
func DeleteAfterSalesAddressByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteAfterSalesAddressByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags 应用
// @Summary 更新AfterSalesAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAddress true "更新AfterSalesAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /application/updateAfterSalesAddress [put]
func UpdateAfterSalesAddress(c *gin.Context) {
	var application model.AfterSalesAddress
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateAfterSalesAddress(application); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags 应用
// @Summary 用id查询AfterSalesAddress
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSalesAddress true "用id查询AfterSalesAddress"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /application/findAfterSalesAddress [get]
func FindAfterSalesAddress(c *gin.Context) {
	var application model.AfterSalesAddress
	_ = c.ShouldBindQuery(&application)
	if err, reapplication := service.GetAfterSalesAddress(application.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reapplication": reapplication}, c)
	}
}

// @Tags 应用
// @Summary 分页获取AfterSalesAddress列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AfterSalesAddressSearch true "分页获取AfterSalesAddress列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /application/getAfterSalesAddressList [get]
func GetAfterSalesAddressList(c *gin.Context) {
	var pageInfo request.AfterSalesAddressSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAfterSalesAddressInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
