package v1

import (
	"after-sales/model"
	orderModel "order/model"

	"after-sales/request"
	"after-sales/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

// Send
// @Tags 售后
// @Summary 售后发货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "发货信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/send [post]
func Send(c *gin.Context) {
	var sendRequest request.SendRequest
	err := c.ShouldBindJSON(&sendRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)

	err = service.SendAfterSales(sendRequest, 0, appID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type GetReasonsRequest struct {
	AfterSaleType model.AfterSalesType `json:"after_sale_type" form:"after_sale_type"`
	IsReceived    int                  `json:"is_received" form:"is_received"` //0未收到货 1已
}

// FindAfterSales
// @Tags 售后
// @Summary 用id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} service.AfterSalesForDetail
// @Router /api/afterSales/get [get]
func FindAfterSales(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)
	appID := utils.GetAppID(c)
	err, info := service.FindAfterSales(reqId.Id)
	if err != nil || info.Order.ApplicationID != appID {
		log.Log().Error("获取失败,不存在这个售后或者不属于这个采购端!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败,不存在这个售后或者不属于这个采购端", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

// FindAfterSales
// @Tags 售后
// @Summary 用id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用OrderItemId查询售后信息"
// @Success 200 {object} service.AfterSalesForDetail
// @Router /api/afterSales/getAfterSalesByOrderItemId [get]
func FindAfterSalesByOrderItemId(c *gin.Context) {
	var reqId yzRequest.GetByOrderItemId
	_ = c.ShouldBindQuery(&reqId)
	appID := utils.GetAppID(c)
	err, info := service.GetAfterSalesByOrderItemId(reqId.OrderItemId)
	if err != nil || info.Order.ApplicationID != appID {
		log.Log().Error("获取失败,不存在这个售后或者不属于这个采购端", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败,不存在这个售后或者不属于这个采购端", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

// GetAfterSalesList
// @Tags 售后
// @Summary 获取售后列表
// @accept application/json
// @Produce application/json
// @Param data body request.AfterSalesSearch true "获取售后列表"
// @Success 200 {object} []model.Region{}
// @Router /api/afterSales/list [get]
func GetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)
	pageInfo.ApplicationId = appID

	if err, list, total, afterSalesStatusCounts := service.GetAfterSalesList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"list": list,
			"total":                  total,
			"page":                   pageInfo.Page,
			"pageSize":               pageInfo.PageSize,
			"afterSalesStatusCounts": afterSalesStatusCounts,
		}, "获取成功", c)
	}
}

type OrderItem struct {
	ID        uint   `json:"id"`
	SkuID     uint   `json:"sku_id"`
	Title     string `json:"title"`
	ProductID uint   `json:"product_id"`
	OrderID   uint   `json:"order_id"`
}

// CreateAfterSales
// @Tags 售后
// @Summary 创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/create [post]
func CreateAfterSales(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if requestCreateAfterSales.OrderID == 0 && requestCreateAfterSales.ThirdOrderSN == "" {
		yzResponse.FailWithMessage("请提交售后订单id或者采购端订单号", c)
		return
	}
	if requestCreateAfterSales.OrderItemID == 0 {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交售后子订单id", c)
		return
	}
	var orderItemModel orderModel.OrderItemModel
	err = source.DB().Where("id = ?", requestCreateAfterSales.OrderItemID).First(&orderItemModel).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("子订单不存在"+err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	var order model.Order
	if requestCreateAfterSales.OrderID != 0 {
		if orderItemModel.OrderID != requestCreateAfterSales.OrderID {
			yzResponse.FailWithMessage("子订单不属于这个订单", c)
			return
		}
		err = source.DB().Where("application_id = ?", appID).Where("id = ?", requestCreateAfterSales.OrderID).First(&order).Error
	} else {
		err = source.DB().Where("id = ?", orderItemModel.OrderID).Where("application_id = ?", appID).Where("third_order_sn = ?", requestCreateAfterSales.ThirdOrderSN).First(&order).Error
	}
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("订单记录不存在或者不属于这个采购端", c)
		return
	}

	//var orderItemModel orderModel.OrderItemModel
	//err = source.DB().Where("order_id = ?", order.ID).Where("id = ?", requestCreateAfterSales.OrderItemID).First(&orderItemModel).Error
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage("子订单不属于这个订单"+err.Error(), c)
	//	return
	//}
	requestCreateAfterSales.AfterSaleType = 2
	requestCreateAfterSales.Ip = c.ClientIP()
	err, afterSalesId := service.ApplyAfterSales(requestCreateAfterSales, order.UserID)
	if err != nil {
		log.Log().Error("申请售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(afterSalesId, c)
}

// Close
// @Tags 售后
// @Summary 关闭售后
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/close [post]
func Close(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)

	err = service.CloseAfterSales(as, 0, appID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// CreateAfterSales
// @Tags 售后
// @Summary 修改售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "修改售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/save [post]
func SaveAfterSales(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)
	var order model.Order
	err = source.DB().Where("application_id = ?", appID).Where("id = ?", requestCreateAfterSales.OrderID).First(&order).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("订单记录不存在或者不属于这个采购端", c)
		return
	}
	requestCreateAfterSales.AfterSaleType = 2
	requestCreateAfterSales.Ip = c.ClientIP()
	err = service.SaveAfterSales(requestCreateAfterSales)

	if err != nil {
		log.Log().Error("修改售后申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("修改成功，请等待审核", c)
}

// OrderAfterSalesCreate
// @Tags 售后
// @Summary 整个订单申请售后
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "整个订单申请售后"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/orderAfterSalesCreate [post]
func OrderAfterSalesCreate(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if requestCreateAfterSales.OrderID == 0 && requestCreateAfterSales.ThirdOrderSN == "" {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交中台订单id或者第三方订单号", c)
		return
	}
	appID := utils.GetAppID(c)
	var order model.Order
	if requestCreateAfterSales.OrderID == 0 {
		err = source.DB().Preload("OrderItems").Where("application_id = ?", appID).Where("third_order_sn = ?", requestCreateAfterSales.ThirdOrderSN).First(&order).Error
	} else {
		err = source.DB().Preload("OrderItems").Where("application_id = ?", appID).Where("id = ?", requestCreateAfterSales.OrderID).First(&order).Error
	}
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("订单记录不存在或者不属于这个采购端", c)
		return
	}
	var afterSalesIds []uint
	var errs string
	//循环申请
	for _, item := range order.OrderItems {
		var createAfterSales request.AfterSales
		createAfterSales.OrderID = item.OrderID
		createAfterSales.Amount = item.Amount
		createAfterSales.OrderItemID = item.ID
		createAfterSales.IsReceived = requestCreateAfterSales.IsReceived
		createAfterSales.RefundType = requestCreateAfterSales.RefundType
		createAfterSales.Reason = requestCreateAfterSales.Reason
		createAfterSales.Description = requestCreateAfterSales.Description
		createAfterSales.IsReceived = requestCreateAfterSales.IsReceived
		createAfterSales.DetailImages = requestCreateAfterSales.DetailImages
		createAfterSales.ReasonType = requestCreateAfterSales.ReasonType
		createAfterSales.AfterSaleType = 2
		createAfterSales.Ip = c.ClientIP()

		err, afterSalesId := service.ApplyAfterSales(createAfterSales, order.UserID)
		if err != nil {
			errs += "子订单" + strconv.Itoa(int(item.ID)) + ":" + err.Error() + ","
			log.Log().Error("申请售后失败", zap.Any("err", err))
			continue
		}
		afterSalesIds = append(afterSalesIds, afterSalesId)
		fmt.Println(strconv.Itoa(int(item.ID)))
	}
	if errs != "" {
		yzResponse.FailWithMessage(errs, c)
		return
	}

	yzResponse.OkWithData(afterSalesIds, c)
}

// Send
// @Tags 售后
// @Summary 消息成功之后商城通知中台 中台进行改变状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "发货信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/send [post]
func MessageSuccess(c *gin.Context) {
	var messageSuccess request.MessageSuccess
	err := c.ShouldBindJSON(&messageSuccess)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)

	err = service.MessageSuccess(appID, messageSuccess)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Send
// @Tags 售后
// @Summary 获取售后所有待商城确认或者错误的消息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "发货信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/send [post]
func GetMessageError(c *gin.Context) {

	appID := utils.GetAppID(c)
	appShopID := utils.GetAppShopID(c)

	err, data := service.GetMessageError(appID, appShopID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
	return
}

// Close
// @Tags 售后
// @Summary 获取订单支持的售后方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/getAfterSalesTypeNameMap [get]
func GetAfterSalesTypeNameMap(c *gin.Context) {
	var as request.AfterSalesTypeSearch
	err := c.ShouldBindQuery(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if as.OrderID == 0 && as.OrderItemID == 0 {
		yzResponse.FailWithMessage("请提交订单id", c)
		return
	}
	if as.OrderID == 0 && as.OrderItemID != 0 {
		var orderItem OrderItem
		err = source.DB().Where("id = ?", as.OrderItemID).First(&orderItem).Error
		if err != nil {
			log.Log().Error("获取订单失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("不存在订单", c)
			return
		}
		as.OrderID = orderItem.OrderID
	}
	appID := utils.GetAppID(c)
	var order model.Order
	err = source.DB().Where("id = ?", as.OrderID).First(&order).Error
	if err != nil {
		log.Log().Error("获取订单失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("不存在订单", c)
		return
	}
	if appID != order.ApplicationID {
		log.Log().Error("订单不属于这个采购端!", zap.Any("OrderID", as.OrderID))
		yzResponse.FailWithMessage("订单不属于这个采购端", c)
		return
	}

	err, data := service.GetAfterSalesType(as.OrderID, as.OrderItemID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
	return
}

// Receive
// @Tags 售后
// @Summary 售后用户收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/userReceive [post]
func UserReceive(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	appID := utils.GetAppID(c)
	err = service.UserReceive(as, 0, 0, 0, appID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}
