package v1

import (
	"after-sales/model"
	"after-sales/request"
	"after-sales/service"
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	orderModel "order/model"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

// Send
// @Tags 售后
// @Summary 售后发货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "发货信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/send [post]
func Send(c *gin.Context) {
	var sendRequest request.SendRequest
	err := c.ShouldBindJSON(&sendRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)
	err = service.SendAfterSales(sendRequest, userID, 0)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type GetReasonsRequest struct {
	AfterSaleType model.AfterSalesType `json:"after_sale_type" form:"after_sale_type"`
	IsReceived    int                  `json:"is_received" form:"is_received"` //0未收到货 1已
}

// Receive
// @Tags 售后
// @Summary 售后用户收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "售后信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/userReceive [post]
func UserReceive(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)
	err = service.UserReceive(as, userID, 0, 0, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// GetReasonList
// @Tags 售后
// @Summary 获取退款理由
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} refund.AfterSales
// @Router /api/afterSales/reason/list [get]
func GetReasonList(c *gin.Context) {
	var getReasonsRequest GetReasonsRequest
	err := c.ShouldBindQuery(&getReasonsRequest)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}

	info := service.GetReasons(getReasonsRequest.AfterSaleType, getReasonsRequest.IsReceived)
	RefundReason := map[model.RefundReason]string{}

	for _, value := range info {
		_, name := model.GetRefundReasonName(value)
		RefundReason[value] = name
	}

	yzResponse.OkWithData(gin.H{"reasons": RefundReason}, c)
}

// FindAfterSales
// @Tags 售后
// @Summary 用id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} service.AfterSalesForDetail
// @Router /api/afterSales/get [get]
func FindAfterSales(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)
	userID := ufv1.GetUserID(c)
	err, info := service.FindAfterSales(reqId.Id)
	if err != nil || info.UserID != userID {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

// FindAfterSalesByOrderItemId
// @Tags 售后
// @Summary 用id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用OrderItemId查询售后信息"
// @Success 200 {object} service.AfterSalesForDetail
// @Router /api/afterSales/getAfterSalesByOrderItemId [get]
func FindAfterSalesByOrderItemId(c *gin.Context) {
	var reqId yzRequest.GetByOrderItemId
	_ = c.ShouldBindQuery(&reqId)
	userID := ufv1.GetUserID(c)
	err, info := service.GetAfterSalesByOrderItemId(reqId.OrderItemId)
	if err != nil || info.UserID != userID {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

// GetAfterSalesList
// @Tags 售后
// @Summary 获取售后列表
// @accept application/json
// @Produce application/json
// @Param data body request.AfterSalesSearch true "获取Area列表"
// @Success 200 {object} []model.Region{}
// @Router /api/afterSales/list [get]
func GetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	pageInfo.UserID = userID
	if err, list, total, afterSalesStatusCounts := service.GetAfterSalesList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"list": list,
			"total":                  total,
			"page":                   pageInfo.Page,
			"pageSize":               pageInfo.PageSize,
			"afterSalesStatusCounts": afterSalesStatusCounts,
		}, "获取成功", c)
	}
}

type OrderItem struct {
	ID        uint   `json:"id"`
	SkuID     uint   `json:"sku_id"`
	Title     string `json:"title"`
	ProductID uint   `json:"product_id"`
	OrderID   uint   `json:"order_id"`
}

// CreateAfterSales
// @Tags 售后
// @Summary 创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/create [post]
func CreateAfterSales(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	uid := ufv1.GetUserID(c)
	requestCreateAfterSales.AfterSaleType = 1
	requestCreateAfterSales.Ip = c.ClientIP()

	err, afterSalesId := service.ApplyAfterSales(requestCreateAfterSales, uid)

	if err != nil {
		log.Log().Error("申请售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithData(afterSalesId, c)
}

// CreateAfterSales
// @Tags 售后
// @Summary 创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/create [post]
func AfterSalesBeforeCheck(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if requestCreateAfterSales.OrderID == 0 && requestCreateAfterSales.ThirdOrderSN == "" {
		yzResponse.FailWithMessage("请提交售后订单id或者采购端订单号", c)
		return
	}
	if requestCreateAfterSales.OrderItemID == 0 {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("请提交售后子订单id", c)
		return
	}
	var orderItemModel orderModel.OrderItemModel
	err = source.DB().Where("id = ?", requestCreateAfterSales.OrderItemID).First(&orderItemModel).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("子订单不存在"+err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	var order model.Order
	if requestCreateAfterSales.OrderID != 0 {
		if orderItemModel.OrderID != requestCreateAfterSales.OrderID {
			yzResponse.FailWithMessage("子订单不属于这个订单", c)
			return
		}
		err = source.DB().Where("application_id = ?", appID).Where("id = ?", requestCreateAfterSales.OrderID).First(&order).Error
	} else {
		err = source.DB().Where("id = ?", orderItemModel.OrderID).Where("application_id = ?", appID).Where("third_order_sn = ?", requestCreateAfterSales.ThirdOrderSN).First(&order).Error
	}
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("订单记录不存在或者不属于这个采购端", c)
		return
	}

	//var orderItemModel orderModel.OrderItemModel
	//err = source.DB().Where("order_id = ?", order.ID).Where("id = ?", requestCreateAfterSales.OrderItemID).First(&orderItemModel).Error
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage("子订单不属于这个订单"+err.Error(), c)
	//	return
	//}
	requestCreateAfterSales.AfterSaleType = 2
	requestCreateAfterSales.Ip = c.ClientIP()

	err, _, _, _, _, _ = service.AfterSalesBeforeCheck(requestCreateAfterSales, order.UserID)

	if err != nil {
		log.Log().Error("申请售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("可以申请", c)
}

// Close
// @Tags 售后
// @Summary 关闭售后
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/close [post]
func Close(c *gin.Context) {
	var as model.AfterSales
	err := c.ShouldBindJSON(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	uid := ufv1.GetUserID(c)

	err = service.CloseAfterSales(as, uid, 0)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// Close
// @Tags 售后
// @Summary 获取订单支持的售后方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "审核信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/afterSales/getAfterSalesTypeNameMap [get]
func GetAfterSalesTypeNameMap(c *gin.Context) {
	var as request.AfterSalesTypeSearch
	err := c.ShouldBindQuery(&as)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if as.OrderID == 0 {
		yzResponse.FailWithMessage("请提交订单id", c)
		return
	}

	err, data := service.GetAfterSalesType(as.OrderID, as.OrderItemID)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
	return
}

// CreateAfterSalesAll CreateAfterSales
// @Tags 售后
// @Summary 创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/create [post]
func CreateAfterSalesAll(c *gin.Context) {
	var requestCreateAfterSales request.AfterSales
	err := c.ShouldBindJSON(&requestCreateAfterSales)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if requestCreateAfterSales.OrderID == 0 {
		yzResponse.FailWithMessage("请提交订单id", c)
		return
	}
	//查询这个订单是否已有售后
	var afterSales []model.AfterSales
	var notIn = []int{0, -1}
	err = source.DB().Model(&model.AfterSales{}).Where("status not in ?", notIn).Where("order_id = ?", requestCreateAfterSales.OrderID).Find(&afterSales).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("查询售后记录错误"+err.Error(), c)
		return
	}
	if len(afterSales) > 0 {
		yzResponse.FailWithMessage("订单存在正在进行中的售后", c)
		return
	}
	var order model.Order
	err = source.DB().Model(&model.Order{}).Preload("OrderItems").Where("id = ?", requestCreateAfterSales.OrderID).First(&order).Error
	if err != nil {
		yzResponse.FailWithMessage("订单不存在"+err.Error(), c)
		return
	}
	uid := ufv1.GetUserID(c)
	var afterSalesIds []uint
	for key, item := range order.OrderItems {
		//等于0代表已经售后完成了
		if item.Amount == 0 {
			continue
		}
		var createAfterSales request.AfterSales
		createAfterSales.OrderItemID = item.ID
		createAfterSales.OrderID = item.OrderID
		createAfterSales.Amount = item.Amount
		createAfterSales.ReasonType = model.RefundReason(5)
		createAfterSales.Reason = "整单退款"
		createAfterSales.Description = "整单退款"
		createAfterSales.IsReceived = 1
		createAfterSales.RefundType = model.AfterSalesType(0)
		createAfterSales.TechnicalServicesFee = item.TechnicalServicesFee
		createAfterSales.RefundWay = model.RefundWay(1)
		createAfterSales.Num = item.Qty
		createAfterSales.AfterSaleType = 1
		//第一个申请的加上运费
		if key == 0 {
			createAfterSales.Freight = order.Freight
		}
		var afterSalesId uint
		createAfterSales.Ip = c.ClientIP()
		err, afterSalesId = service.ApplyAfterSales(createAfterSales, uid)
		if err != nil {
			log.Log().Error("申请售后失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		afterSalesIds = append(afterSalesIds, afterSalesId)

	}

	yzResponse.OkWithData(afterSalesIds, c)
}
