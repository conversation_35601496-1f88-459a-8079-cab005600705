module after-sales

go 1.21

require (
	gin-vue-admin v1.0.0
	github.com/gin-gonic/gin v1.6.3
	github.com/go-playground/validator/v10 v10.4.1
	github.com/jinzhu/copier v0.3.2
	github.com/streadway/amqp v1.1.0
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	notification v1.0.0
	order v1.0.0
	product v1.0.0
	region v1.0.0
	user v1.0.0
	yz-go v1.0.0
)

replace (
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product => ../product
	region => ../region
	sales => ../sales
	shipping => ../shipping
	shop => ../shop
	supplier => ../supplier
	user => ../user
	wechatpay-go-main => ../wechatpay-go-main
	yz-go => ../yz-go

)
