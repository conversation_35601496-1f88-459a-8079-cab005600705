package route

import (
	fav1 "after-sales/api/app/v1"
	fv1 "after-sales/api/f/v1"
	v1 "after-sales/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	AfterSalesRouter := Router.Group("afterSales")
	{
		//AfterSalesRouter.POST("create", v1.CreateAfterSales) // 申请售后

		AfterSalesRouter.GET("list", v1.GetAfterSalesList)                       // 售后列表
		AfterSalesRouter.GET("exportAfterSalesList", v1.ExportAfterSalesList)    // 售后列表导出
		AfterSalesRouter.GET("get", v1.FindAfterSales)                           // 根据id获取售后详情
		AfterSalesRouter.GET("getByOrderItemId", v1.FindAfterSalesByOrderItemId) // 根据orderItemId获取售后详情（获取最近的一个）
		AfterSalesRouter.POST("close", v1.Close)                                 // 售后关闭
		AfterSalesRouter.POST("send", v1.Send)                                   // 售后发货
		AfterSalesRouter.POST("receive", v1.Receive)                             // 售后收货
		AfterSalesRouter.POST("afterSalesShopSend", v1.AfterSalesShopSend)       //售后换货时商家发货(产品说暂时不做部分发货-此API暂时没用)
		AfterSalesRouter.POST("userReceive", v1.UserReceive)                     // 售后用户收货
		AfterSalesRouter.POST("retryMessage", v1.RetryMessage)                   //重试消息，目前只有创建消息

		AfterSalesRouter.POST("refund", v1.Refund) // 售后退款

		AfterSalesRouter.POST("cronBarterSuccess", v1.CronBarterSuccess) // 测试定时完成换货操作

		AfterSalesRouter.GET("getAfterSalesCount", v1.GetAfterSalesCount) // 获取正在进行的售后数量
		AfterSalesRouter.GET("anewSynAfterSale", v1.AnewSynAfterSale)     // 获取正在进行的售后数量

		AfterSalesRouter.POST("ceshiAfterSales", v1.CeshiAfterSales) //手动推送售后消息

	}
	AuditRouter := Router.Group("afterSalesAudit")
	{
		AuditRouter.POST("pass", v1.PassAudit)            // 审核通过
		AuditRouter.POST("reject", v1.RejectAudit)        // 驳回请求
		AuditRouter.GET("list", v1.GetAfterSaleAuditList) // 审核列表
		AuditRouter.GET("get", v1.FindAfterSaleAudit)     // 审核详情
	}
	SupplierRouter := Router.Group("supplierRouterAfterSales")
	{
		SupplierRouter.POST("supplier/pass", v1.SupplierPassAudit)                           // 审核通过
		SupplierRouter.POST("supplier/reject", v1.SupplierRejectAudit)                       // 驳回请求
		SupplierRouter.POST("supplier/receive", v1.SupplierReceive)                          // 售后收货
		SupplierRouter.POST("supplier/refund", v1.SupplierRefund)                            // 售后退款
		SupplierRouter.POST("supplier/close", v1.SupplierClose)                              // 售后关闭
		SupplierRouter.GET("supplier/list", v1.SupplierGetAfterSalesList)                    // 售后列表
		SupplierRouter.GET("supplier/exportAfterSalesList", v1.SupplierExportAfterSalesList) // 售后列表导出
		SupplierRouter.POST("supplier/userReceive", v1.SupplierUserReceive)                  // 售后用户收货
		SupplierRouter.GET("supplier/getAfterSalesCount", v1.GetSupplierAfterSalesCount)     // 获取正在进行的售后数量

	}
}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {
	AfterSalesRouter := Router.Group("afterSales")
	{
		AfterSalesRouter.GET("list", fv1.GetAfterSalesList)   // 获取售后列表
		AfterSalesRouter.POST("create", fv1.CreateAfterSales) // 申请售后

		AfterSalesRouter.POST("createAfterSalesAll", fv1.CreateAfterSalesAll) // 整单退款-- 仅需要传订单id 会把所有子订单都申请售后

		AfterSalesRouter.POST("save", v1.SaveAfterSales)                                    // 修改售后
		AfterSalesRouter.GET("get", fv1.FindAfterSales)                                     // 获取通过售后id售后详情
		AfterSalesRouter.GET("getAfterSalesByOrderItemId", fv1.FindAfterSalesByOrderItemId) // 获取通过order_item_id获取售后详情

		AfterSalesRouter.GET("reason/list", fv1.GetReasonList)                         // 获取退款/退货理由
		AfterSalesRouter.POST("send", fv1.Send)                                        // 发货
		AfterSalesRouter.POST("close", fv1.Close)                                      // 取消订单
		AfterSalesRouter.GET("getAfterSalesTypeNameMap", fv1.GetAfterSalesTypeNameMap) // 获取订单支持的售后方式
		AfterSalesRouter.POST("userReceive", fv1.UserReceive)                          // 售后用户收货

	}

}

// 采购端API
func InitAfterSalesAppPrivateRouter(Router *gin.RouterGroup) {
	AfterSalesRouter := Router.Group("afterSales")
	{
		AfterSalesRouter.GET("list", fav1.GetAfterSalesList)                                 // 获取售后列表
		AfterSalesRouter.POST("create", fav1.CreateAfterSales)                               // 申请售后
		AfterSalesRouter.POST("save", fav1.SaveAfterSales)                                   // 修改售后
		AfterSalesRouter.GET("get", fav1.FindAfterSales)                                     // 获取通过售后id售后详情
		AfterSalesRouter.GET("getAfterSalesByOrderItemId", fav1.FindAfterSalesByOrderItemId) // 获取通过order_item_id获取售后详情
		AfterSalesRouter.POST("afterSalesBeforeCheck", fv1.AfterSalesBeforeCheck)            // 售后校验

		AfterSalesRouter.GET("reason/list", fv1.GetReasonList) // 获取退款/退货理由
		AfterSalesRouter.POST("send", fav1.Send)               // 发货
		AfterSalesRouter.POST("close", fav1.Close)             // 取消订单

		AfterSalesRouter.POST("orderAfterSalesCreate", fav1.OrderAfterSalesCreate) //订单申请售后
		AfterSalesRouter.POST("messageSuccess", fav1.MessageSuccess)               // 消息成功之后商城通知中台 中台进行改变状态
		AfterSalesRouter.POST("getMessageError", fav1.GetMessageError)             // 获取所有状态不是成功的消息

		AfterSalesRouter.GET("getAfterSalesTypeNameMap", fav1.GetAfterSalesTypeNameMap) // 获取订单支持的售后方式
		AfterSalesRouter.POST("userReceive", fav1.UserReceive)                          // 售后用户收货

	}

}
