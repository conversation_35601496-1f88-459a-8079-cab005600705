package router

import (
	"after-sales/api/app"
	"github.com/gin-gonic/gin"
)


func InitAppPrivateRouter(Router *gin.RouterGroup) {
	AfterSalesAddressRouter := Router.Group("afterSales")
	{
		AfterSalesAddressRouter.POST("createAfterSalesAddress", app.CreateAfterSalesAddress)                // 新建AfterSalesAddress
		AfterSalesAddressRouter.DELETE("deleteAfterSalesAddress", app.DeleteAfterSalesAddress)              // 删除AfterSalesAddress
		AfterSalesAddressRouter.DELETE("deleteAfterSalesAddressByIds", app.DeleteAfterSalesAddressByIds)    // 批量删除AfterSalesAddress
		AfterSalesAddressRouter.PUT("updateAfterSalesAddress", app.UpdateAfterSalesAddress)                 // 更新AfterSalesAddress
		AfterSalesAddressRouter.GET("findAfterSalesAddress", app.FindAfterSalesAddress)                     // 根据ID获取AfterSalesAddress
		AfterSalesAddressRouter.GET("getAfterSalesAddressList", app.GetAfterSalesAddressList)               // 获取AfterSalesAddress列表

	}
}

