package service

import (
	"after-sales/model"
	"after-sales/mq"
	"after-sales/request"
	"errors"
	"fmt"
	nmq "notification/mq"
	model3 "order/model"
	"order/refund"
	"os"
	model2 "product/model"
	"public-supply/common"
	regionModel "region/model"
	"strconv"
	"time"
	tradeModel "trade/model"
	"yz-go/component/log"
	"yz-go/config"
	yzModel "yz-go/model"
	yzRequest "yz-go/request"
	service2 "yz-go/service"
	"yz-go/source"
	"yz-go/utils"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type AfterSalesForDetail struct {
	model.AfterSales
	Order              Order                    `json:"order"`
	OrderItem          OrderItem                `json:"order_item"`
	AfterSalesAudit    model.AfterSalesAudit    `json:"after_sales_audit" gorm:"foreignkey:after_sales_id"`
	ReturnOrderExpress model.ReturnOrderExpress `json:"return_order_express"`
}

func (AfterSalesForDetail) TableName() string {
	return "after_sales"
}

// FindAfterSales
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetRefundApply
// @description: 根据id获取退款申请
// @param: id uint
// @return: err error, refundApply model.AfterSales
func FindAfterSales(id uint) (err error, afterSales AfterSalesForDetail) {
	//err = source.DB().Preload("AfterSalesSend").Preload("ShopAddress").Preload("ShopAddress.ProvinceName").Preload("ShopAddress.CityName").Preload("ShopAddress.DistrictName").Preload("User").Where("id = ?", id).Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("OrderItem").Preload("Order.Application").First(&afterSales).Error
	err = source.DB().Preload("ShopAddress").Preload("ShopAddress.ProvinceName").Preload("ShopAddress.CityName").Preload("ShopAddress.DistrictName").Preload("User").Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("OrderItem").Preload("AfterSalesSend").Where("id = ?", id).First(&afterSales).Error
	if err != nil {
		return
	}
	if afterSales.ShopAddress.ID == 0 {

		source.DB().Model(&model.ShopAddress{}).Preload("ProvinceName").Preload("CityName").Preload("DistrictName").Where("id = ?", afterSales.ShippingAddressID).First(&afterSales.ShopAddress)
	}
	return
}

// GetAfterSalesByOrderItemId
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetAfterSalesByOrderItemId
// @description: 根据orderItemId获取未关闭的退款申请
// @param: orderItemId uint
// @return: err error, refundApply model.AfterSales
func GetAfterSalesById(Id uint) (err error, afterSales model.AfterSales) {
	err = source.DB().Preload("User").Preload("User").Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("Order.Supplier").Preload("OrderItem").Where("id = ?", Id).Last(&afterSales).Error
	if err != nil {
		return
	}
	return
}

// GetAfterSalesByOrderItemId
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetAfterSalesByOrderItemId
// @description: 根据orderItemId获取未关闭的退款申请
// @param: orderItemId uint
// @return: err error, refundApply model.AfterSales
func GetAfterSalesByOrderItemId(orderItemId uint) (err error, afterSales model.AfterSales) {
	//err = source.DB().Preload("User").Preload("User").Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("Order.Supplier").Preload("OrderItem").Where("order_item_id = ?", orderItemId).Last(&afterSales).Error
	err = source.DB().Preload("Order.Supplier").Preload("AfterSalesSend").Preload("ShopAddress").Preload("ShopAddress.ProvinceName").Preload("ShopAddress.CityName").Preload("ShopAddress.DistrictName").Preload("User").Where("order_item_id = ?", orderItemId).Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("Order.ShippingAddress").Preload("OrderItem").Preload("OrderItem.ItemExpress").Preload("OrderItem.ItemExpress.OrderExpress").Preload("Order.Application").First(&afterSales).Error

	if err != nil {
		return
	}
	if afterSales.ShopAddress.ID == 0 && afterSales.ShippingAddressID != 0 {
		source.DB().Model(&model.ShopAddress{}).Preload("ProvinceName").Preload("CityName").Preload("DistrictName").Where("id = ?", afterSales.ShippingAddressID).First(&afterSales.ShopAddress)
	}
	if afterSales.ShippingAddressID == 0 && afterSales.NewShippingAddressID == 0 {
		var shopAddress ShopAddress
		err, shopAddress = GetShopAddressBySupplierId(afterSales.Order.SupplierID, 0, "0")
		if err != nil {
			err = nil
		}
		_ = copier.Copy(&afterSales.ShopAddress, shopAddress)
		afterSales.ShippingAddressID = shopAddress.ID

	}
	return
}

// GetAfterSalesAuditByAfterSalesId
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetAfterSalesAuditByAfterSalesId
// @description: 根据afterSalesId获取最后一条审核记录
// @param: orderItemId uint
// @return: err error, refundApply model.AfterSalesAudit
func GetAfterSalesAuditByAfterSalesId(afterSalesId uint) (err error, afterSalesAudit model.AfterSalesAudit) {
	err = source.DB().Where("after_sales_id = ?", afterSalesId).Last(&afterSalesAudit).Error
	return
}

// 收货地址
type ShopAddress struct {
	ID           uint                   `json:"id"`
	Contacts     string                 `json:"contacts" form:"contacts" gorm:"column:contacts;comment:联系人;type:varchar(255);size:255;"`
	Tel          string                 `json:"tel" form:"tel" gorm:"column:tel;comment:手机号;varchar(20);size:20;"`
	Plane        string                 `json:"plane" form:"plane" gorm:"column:plane;comment:座机号;type:varchar(20);size:20;"`
	Address      string                 `json:"address" form:"address" gorm:"column:address;comment:详细地址;varchar(255);size:255;"`
	AddressType  yzModel.JsonStringList `json:"address_type" gorm:"column:address_type;comment:地址类型;type:json;"` //地址类型(0,1,2 退货地址，收票地址，发货地址)
	IsDefault    int                    `json:"is_default" form:"is_default" gorm:"column:is_default;comment:-1都不是默认0是退货地址1是收票地址2是发货地址;type:tinyint;size:1;"`
	Province     int                    `json:"province" form:"province" gorm:"column:province;comment:省;type:int(11);size:11;"`
	City         int                    `json:"city" form:"city" gorm:"column:city;comment:市;type:int(11);size:11;"`
	District     int                    `json:"district" form:"district" gorm:"column:district;comment:区;type:int(11);size:11;"`
	ProvinceName regionModel.Region     `json:"province_name" gorm:"foreignKey:Province"`
	CityName     regionModel.Region     `json:"city_name" gorm:"foreignKey:City"`
	DistrictName regionModel.Region     `json:"district_name" gorm:"foreignKey:District"`
}

type AdminAfterSalesForDetail struct {
	model.AfterSales
}

func (AdminAfterSalesForDetail) TableName() string {
	return "after_sales"
}

// GetAfterSales
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetRefundApply
// @description: 根据id获取退款申请
// @param: id uint
// @return: err error, refundApply model.AfterSales
func GetAfterSalesAdmin(id uint) (err error, afterSales AdminAfterSalesForDetail) {
	err = source.DB().Preload("AfterSalesSend").Preload("ShopAddress").Preload("ShopAddress.ProvinceName").Preload("ShopAddress.CityName").Preload("ShopAddress.DistrictName").Preload("User").Where("id = ?", id).Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("Order.ShippingAddress").Preload("OrderItem").Preload("OrderItem.ItemExpress").Preload("OrderItem.ItemExpress.OrderExpress").Preload("Order.Application").First(&afterSales).Error
	if err != nil {
		return
	}
	if afterSales.ShopAddress.ID == 0 && afterSales.ShippingAddressID != 0 {
		source.DB().Model(&model.ShopAddress{}).Preload("ProvinceName").Preload("CityName").Preload("DistrictName").Where("id = ?", afterSales.ShippingAddressID).First(&afterSales.ShopAddress)
	}
	if afterSales.ShippingAddressID == 0 && afterSales.NewShippingAddressID == 0 {
		var shopAddress ShopAddress
		err, shopAddress = GetShopAddressBySupplierId(afterSales.Order.SupplierID, 0, "0")
		if err != nil {
			err = nil
		}
		_ = copier.Copy(&afterSales.ShopAddress, shopAddress)
		afterSales.ShippingAddressID = shopAddress.ID

	}

	return
}

// @author: [yunzhong](https://www.yunzmall.com)
// @function: GetShopAddressBySupplierId
// @description: 根据SupplierId获取ShopAddress记录
// @param: id uint
// @return: err error, supplierQualification model.ShopAddress
func GetShopAddressBySupplierId(supplierId uint, isDefault int, addressType string) (err error, shopAddress ShopAddress) {
	err = source.DB().Preload("ProvinceName").Preload("DistrictName").Preload("CityName").Where("supplier_id = ?", supplierId).Where("is_show = ?", 0).Where("deleted_at is null and is_default = ?", isDefault).Where("address_type LIKE ?", "%"+addressType+"%").First(&shopAddress).Error
	return
}

// GetAfterSales
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetRefundApply
// @description: 根据id获取退款申请
// @param: id uint
// @return: err error, refundApply model.AfterSales
func GetAfterSales(id uint) (err error, afterSales model.AfterSales) {
	err = source.DB().Preload("User").Where("id = ?", id).Preload("AfterSalesAudit").Preload("ReturnOrderExpress").Preload("Order").Preload("OrderItem").First(&afterSales).Error
	if err != nil {
		return
	}
	return
}

// GetAfterSaleAudit
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetAfterSaleAudit
// @description: 根据id获取退款审核信息
// @param: id uint
// @return: err error, afterSaleAudit model.AfterSalesAudit
func GetAfterSaleAudit(id uint) (err error, afterSaleAudit model.AfterSalesAudit) {
	err = source.DB().Where("id = ?", id).Find(&afterSaleAudit).Error
	if err != nil {
		return
	}
	return
}

// UpdateAfterSales
// @author: [piexlmax](https://github.com/piexlmax)
// @function: UpdateAfterSales
// @description: 编辑售后信息
// @param: id uint
// @return: err error, AfterSales model.AfterSales
func UpdateAfterSales(afterSales model.AfterSales) (err error) {
	err = source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales).Error
	if err != nil {
		return
	}
	return
}

// UpdateAfterSalesAudit
// @author: [piexlmax](https://github.com/piexlmax)
// @function: UpdateAfterSalesAudit
// @description: 编辑审核信息
// @param: id uint
// @return: err error, afterSaleAudit model.AfterSalesAudit
func UpdateAfterSalesAudit(afterSaleAudit model.AfterSalesAudit) (err error) {
	err = source.DB().Where("id = ?", afterSaleAudit.ID).Updates(&afterSaleAudit).Error
	if err != nil {
		return
	}
	return
}

type Order struct {
	ID                   uint               `json:"id"`
	OrderSN              uint               `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`
	ApplicationID        uint               `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;index;"`                           // 应用id
	ThirdOrderSN         string             `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`                                   // 采购端单号
	SupplierID           uint               `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"`                                        // 供应商id
	TechnicalServicesFee uint               `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"` // 技术服务费
	Freight              uint               `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                    // 运费(单位:分)
	Application          Application        `json:"application" gorm:"foreignKey:ApplicationID"`
	GatherSupplySN       string             `json:"gather_supply_sn" form:"gather_supply_sn" gorm:"column:gather_supply_sn;comment:供应链单号"`    // 供应链单号
	GatherSupplyMsg      string             `json:"gather_supply_msg" form:"gather_supply_msg" gorm:"column:gather_supply_msg;comment:供应链消息"` // 供应链单号
	GatherSupplyType     uint               `json:"gather_supply_type" form:"gather_supply_type" gorm:"column:gather_supply_type;comment:供应链类型id"`
	GatherSupplyID       uint               `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"`    // 供应链id
	StatusName           string             `json:"status_name"`                                                                                        // 状态名
	Status               model3.OrderStatus `json:"status"`                                                                                             // 状态
	OrderType            int                `json:"order_type" form:"order_type" gorm:"column:order_type;comment:订单类型 1 套餐订单;default:0;index;"` //订单类型 1 套餐订单
	OrderTypeNote        string             `json:"order_type_note" form:"order_type_note" gorm:"column:order_type_note;"`
	GatherSupply         GatherSupply       `json:"gather_supply" gorm:"foreignKey:GatherSupplyID"`
	Supplier             Supplier           `json:"supplier" gorm:"foreignKey:SupplierID"`
}

func (o *Order) AfterFind(tx *gorm.DB) (err error) {
	o.StatusName = model3.GetStatusName(o.Status)

	return
}

type GatherSupply struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Logo       string `json:"logo"` //logo
	CategoryID uint   `json:"category_id"`
}
type Application struct {
	ID      uint   `json:"id"`
	AppName string `json:"appName"`
}

func (Application) TableName() string {
	return "application"
}

type OrderItem struct {
	ID           uint   `json:"id"`
	SkuTitle     string `json:"sku_title"`
	Title        string `json:"title"`
	ProductID    uint   `json:"product_id"`
	OrderID      uint   `json:"order_id"`
	ImageUrl     string `json:"image_url"`
	SendStatus   int    `json:"send_status"`
	Amount       int    `json:"amount"`
	RefundAmount uint   `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额;"` // 退款金额 (分)                           // 总价(元)

}
type AfterSalesForList struct {
	model.AfterSales
	StatusName      string                `json:"status_name"`
	RefundTypeName  string                `json:"refund_type_name"`
	Order           Order                 `json:"order"`
	OrderItem       OrderItem             `json:"order_item"`
	AfterSalesAudit model.AfterSalesAudit `json:"after_sales_audit" gorm:"foreignkey:after_sales_id"`
}

//func (receiver AfterSalesAudit) TableName() string {
//	return "after_sales_audits"
//}

func (receiver *AfterSalesForList) AfterFind(tx *gorm.DB) (err error) {
	err, receiver.StatusName = model.GetStatusName(receiver.Status)
	if err != nil {
		return
	}
	err, receiver.RefundTypeName = model.GetAfterSalesTypeName(receiver.RefundType)
	if err != nil {
		return
	}
	return
}
func (receiver AfterSalesForList) TableName() string {
	return "after_sales"
}

type AfterSalesStatusCount struct {
	Status int  `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"` // 状态
	Count  uint `json:"count" form:"count" gorm:"column:count;comment:累计数量;type:uint;size:11;"`   // 状态数量
}
type AfterSalesStatus struct {
	WaitingReview        uint `json:"waiting_review"`         //待审核数量
	WaitingUserSend      uint `json:"waiting_user_send"`      //待买家发货数量
	WaitingShopReceiving uint `json:"waiting_shop_receiving"` //待商家收货数量
	WaitingShopSend      uint `json:"waiting_shop_send"`      //待商家发货数量
	WaitingUserReceiving uint `json:"waiting_user_receiving"` //待买家收货数量
	WaitingRefund        uint `json:"waiting_refund"`         //待退款
	Completed            uint `json:"completed"`              //已完成数量
	Close                uint `json:"close"`                  //已关闭数量
	Reject               uint `json:"reject"`                 //已驳回数量
}

// GetAfterSalesList
//
// @function: GetAfterSalesList
// @description: 分页获取Ad记录
// @param: info model.AfterSales
// @return: err error, list interface{}, total int64
func GetAfterSalesList(info request.AfterSalesSearch) (err error, list []AfterSalesForList, total int64, afterSalesStatus AfterSalesStatus) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&AfterSalesForList{})
	afterSalesStatusDb := source.DB().Model(&AfterSalesForList{})

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.UserID != 0 {
		db.Where("after_sales.user_id", info.UserID)
		afterSalesStatusDb.Where("after_sales.user_id", info.UserID)
	}

	var isJoinOrder = 2     //是否已经关联了 order表
	var isJoinOrderItem = 2 //是否已经关联了 order_item表
	if info.AfterSaleSN != "" {
		db.Where("after_sales.after_sale_sn", info.AfterSaleSN)
		afterSalesStatusDb.Where("after_sales.after_sale_sn", info.AfterSaleSN)

	}
	if info.RefundType != nil {
		db.Where("after_sales.type = ?", info.RefundType)
		afterSalesStatusDb.Where("after_sales.type = ?", info.RefundType)

	}
	if info.StartAt != "" {
		db.Where("after_sales.created_at >= ?", info.StartAt)
		afterSalesStatusDb.Where("after_sales.created_at >= ?", info.StartAt)

	}
	if info.EndAt != "" {
		db.Where("after_sales.created_at <= ?", info.EndAt)
		afterSalesStatusDb.Where("after_sales.created_at <= ?", info.EndAt)

	}
	if info.OrderID != 0 {
		db.Where("after_sales.order_id <= ?", info.OrderID)
		afterSalesStatusDb.Where("after_sales.order_id <= ?", info.OrderID)

	}
	if info.OrderSN != 0 {
		isJoinOrder = 1
		db.Joins("INNER join orders on orders.id = after_sales.order_id and orders.order_sn like ?", "%"+strconv.Itoa(int(info.OrderSN))+"%")
		afterSalesStatusDb.Joins("INNER join orders on orders.id = after_sales.order_id and orders.order_sn like ?", "%"+strconv.Itoa(int(info.OrderSN))+"%")

	}
	if info.ThirdOrderSN != "" {
		if isJoinOrder == 1 {
			db.Where("orders.third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
			afterSalesStatusDb.Where("orders.third_order_sn like ?", "%"+info.ThirdOrderSN+"%")

		} else {
			isJoinOrder = 1
			db.Joins("INNER join orders on orders.id = after_sales.order_id and orders.third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
			afterSalesStatusDb.Joins("INNER join orders on orders.id = after_sales.order_id and orders.third_order_sn like ?", "%"+info.ThirdOrderSN+"%")

		}
	}

	if info.OrderType != 0 {
		if isJoinOrder == 1 {
			db.Where("orders.order_type = ?", info.OrderType)
			afterSalesStatusDb.Where("orders.order_type = ?", info.OrderType)

		} else {
			isJoinOrder = 1
			db.Joins("INNER join orders on orders.id = after_sales.order_id and orders.order_type = ?", info.OrderType)
			afterSalesStatusDb.Joins("INNER join orders on orders.id = after_sales.order_id and orders.order_type = ?", info.OrderType)
		}
	}

	if info.OrderTypeNote != "" {
		if isJoinOrder == 1 {
			db.Where("orders.order_type_note like ?", "%"+info.OrderTypeNote+"%")
			afterSalesStatusDb.Where("orders.order_type_note like ?", "%"+info.OrderTypeNote+"%")

		} else {
			isJoinOrder = 1
			db.Joins("INNER join orders on orders.id = after_sales.order_id and orders.third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
			afterSalesStatusDb.Joins("INNER join orders on orders.id = after_sales.order_id and orders.third_order_sn like ?", "%"+info.ThirdOrderSN+"%")

		}
	}

	if info.ProductTitle != "" {
		isJoinOrderItem = 1
		db.Joins("INNER join order_items on order_items.id = after_sales.order_item_id and order_items.title like ?", "%"+info.ProductTitle+"%")
		afterSalesStatusDb.Joins("INNER join order_items on order_items.id = after_sales.order_item_id and order_items.title like ?", "%"+info.ProductTitle+"%")

	}
	if info.SupplierId != nil {
		if isJoinOrderItem == 1 {
			db.Where("order_items.supplier_id = ?", info.SupplierId)
			afterSalesStatusDb.Where("order_items.supplier_id = ?", info.SupplierId)

		} else {
			isJoinOrderItem = 1
			db.Joins("INNER join order_items on order_items.id = after_sales.order_item_id and order_items.supplier_id = ?", info.SupplierId)
			afterSalesStatusDb.Joins("INNER join order_items on order_items.id = after_sales.order_item_id and order_items.supplier_id = ?", info.SupplierId)

		}
	}
	if info.SendStatus != nil {
		if isJoinOrderItem == 1 {
			db.Where("order_items.send_status = ?", info.SendStatus)
			afterSalesStatusDb.Where("order_items.send_status = ?", info.SendStatus)

		} else {
			isJoinOrderItem = 1
			db.Joins("INNER join order_items on order_items.id = after_sales.order_item_id and order_items.send_status = ?", info.SendStatus)
			afterSalesStatusDb.Joins("INNER join order_items on order_items.id = after_sales.order_item_id and order_items.send_status = ?", info.SendStatus)

		}
	}
	if info.CompanyCode != "" {
		db.Joins("INNER join return_order_expresses on return_order_expresses.id = after_sales.return_order_express_id and return_order_expresses.company_code = ?", info.CompanyCode)
		afterSalesStatusDb.Joins("INNER join return_order_expresses on return_order_expresses.id = after_sales.return_order_express_id and return_order_expresses.company_code = ?", info.CompanyCode)

	}
	if info.GatherSupplyId != nil {
		if isJoinOrder == 1 {
			db.Where("orders.gather_supply_id = ?", info.GatherSupplyId)
			afterSalesStatusDb.Where("orders.gather_supply_id = ?", info.GatherSupplyId)
		} else {
			isJoinOrder = 1
			db.Joins("INNER join orders on orders.id = after_sales.order_id and orders.gather_supply_id = ?", info.GatherSupplyId)
			afterSalesStatusDb.Joins("INNER join orders on orders.id = after_sales.order_id and orders.gather_supply_id = ?", info.GatherSupplyId)

		}
	}

	if info.ApplicationId != 0 {
		if isJoinOrder == 1 {
			db.Where("orders.application_id = ?", info.ApplicationId)
			afterSalesStatusDb.Where("orders.application_id = ?", info.ApplicationId)

		} else {
			isJoinOrder = 1
			db.Joins("INNER join orders on orders.id = after_sales.order_id and orders.application_id = ?", info.ApplicationId)
			afterSalesStatusDb.Joins("INNER join orders on orders.id = after_sales.order_id and orders.application_id = ?", info.ApplicationId)

		}
	}
	db.Joins("INNER join after_sales_audits on after_sales.id = after_sales_audits.after_sales_id")
	afterSalesStatusDb.Joins("INNER join after_sales_audits on after_sales.id = after_sales_audits.after_sales_id")
	var afterSalesStatusCounts []AfterSalesStatusCount

	err = afterSalesStatusDb.Select("COALESCE(count(after_sales.id), 0) as count,after_sales.status").Group("after_sales.status").Find(&afterSalesStatusCounts).Error

	if err != nil {
		return
	}
	for _, item := range afterSalesStatusCounts {
		switch item.Status {
		case 1:
			afterSalesStatus.WaitingUserSend = item.Count
			break
		case 2:
			afterSalesStatus.WaitingShopReceiving = item.Count
			break
		case 3:
			afterSalesStatus.WaitingRefund = item.Count
			break
		case 4:
			afterSalesStatus.Completed = item.Count
			break
		case 5:
			afterSalesStatus.WaitingShopSend = item.Count
			break
		case 6:
			afterSalesStatus.WaitingUserReceiving = item.Count
			break
		case -1:
			afterSalesStatus.Close = item.Count
			break
		}
	}
	err = afterSalesStatusDb.Select("COALESCE(count(after_sales.id), 0) as count,after_sales_audits.status").Group("after_sales_audits.status").Where("after_sales.status = 0").Find(&afterSalesStatusCounts).Error

	if err != nil {
		return
	}
	//待审核  已驳回
	for _, item := range afterSalesStatusCounts {
		switch item.Status {
		case 0:
			afterSalesStatus.WaitingReview = item.Count
			break
		case -1:
			afterSalesStatus.Reject = item.Count
			break
		}
	}
	if info.Status != nil {
		switch *info.Status {
		case 0:
			db.Where("after_sales.status", info.Status).Where("after_sales_audits.status = ?", model.PendingStatus)
			break
		case -10:
			db.Where("after_sales.status = 0").Where("after_sales_audits.status = ?", model.ClosedRefundStatus)
			break
		default:
			db.Where("after_sales.status", info.Status)
			break
		}
	}

	err = db.Select("after_sales.id").Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Group("after_sales.id").Preload("User").Preload("Order").Preload("OrderItem").Preload("AfterSalesAudit").Preload("Order.Application").Preload("Order.GatherSupply").Preload("Order.Supplier").Select("after_sales.*").Order("after_sales.created_at desc").Offset(offset).Find(&list).Error
	return
}

type AfterSalesAuditSearch struct {
	model.AfterSalesAudit
	yzRequest.PageInfo
}

// GetAfterSalesAuditList
//
// @function: GetAfterSalesAuditList
// @description: 分页获取Ad记录
// @param: info model.AfterSalesAudit
// @return: err error, list interface{}, total int64
func GetAfterSalesAuditList(info AfterSalesAuditSearch) (err error, list []model.AfterSalesAudit, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.AfterSalesAudit{})
	//var ads []AfterSalesAudit
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.AfterSalesID != 0 {
		db.Where("after_sales_id", info.AfterSalesID)
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&list).Error
	return err, list, total
}

// ExportAfterSalesList 导出售后列表
func ExportAfterSalesList(info request.AfterSalesSearch) (err error, filePath string) {
	// 获取售后列表数据
	info.PageSize = 5000
	err, list, total, _ := GetAfterSalesList(info)
	if err != nil {
		return err, ""
	}
	if total == 0 {
		return errors.New("没有数据可导出"), ""
	}

	// 创建Excel文件
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")

	// 设置表头
	headers := []string{"售后单号", "订单编号", "商品名称", "商品规格", "退款金额(元)", "退款数量", "售后类型", "售后状态", "申请时间", "供应商", "供应链", "采购端名称", "订单状态", "运费(元)", "技术服务费(元)"}
	for i, header := range headers {
		cell := string(rune('A'+i)) + "1"
		f.SetCellValue("Sheet1", cell, header)
	}

	// 写入数据
	for i, item := range list {
		row := i + 2
		// 格式化退款金额为元
		refundAmount := float64(item.Amount) / 100.0

		// 获取售后类型名称
		_, refundTypeName := model.GetAfterSalesTypeName(item.RefundType)

		var statusName string
		// 获取状态名称
		if item.Status == 0 {
			statusName = item.AfterSalesAudit.StatusName
		} else {
			_, statusName = model.GetStatusName(item.Status)
		}

		// 写入每一行数据
		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", row), item.AfterSaleSN)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", row), strconv.FormatUint(uint64(item.Order.OrderSN), 10))
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", row), item.OrderItem.Title)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", row), item.OrderItem.SkuTitle)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", row), refundAmount)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", row), item.Num)
		f.SetCellValue("Sheet1", fmt.Sprintf("G%d", row), refundTypeName)
		f.SetCellValue("Sheet1", fmt.Sprintf("H%d", row), statusName)
		f.SetCellValue("Sheet1", fmt.Sprintf("I%d", row), item.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", fmt.Sprintf("J%d", row), item.Order.Supplier.Name)
		f.SetCellValue("Sheet1", fmt.Sprintf("K%d", row), item.Order.GatherSupply.Name)
		f.SetCellValue("Sheet1", fmt.Sprintf("L%d", row), item.Order.Application.AppName)
		f.SetCellValue("Sheet1", fmt.Sprintf("M%d", row), item.Order.StatusName)
		f.SetCellValue("Sheet1", fmt.Sprintf("N%d", row), float64(item.Order.Freight)/100.0)
		f.SetCellValue("Sheet1", fmt.Sprintf("O%d", row), float64(item.Order.TechnicalServicesFee)/100.0)
	}

	// 设置为活动工作表
	f.SetActiveSheet(index)

	// 生成文件名和保存路径
	fileName := fmt.Sprintf("售后列表_%s.xlsx", time.Now().Format("20060102150405"))
	//filePath = fmt.Sprintf("./public/export/%s", fileName)
	filePath = config.Config().Local.Path + "/export_order/"

	fileLink := config.Config().Local.Path + "/export_order/" + fileName
	// 确保导出目录存在
	if err = os.MkdirAll(filePath, os.ModePerm); err != nil {
		return err, ""
	}

	// 保存文件
	if err = f.SaveAs(fileLink); err != nil {
		return err, ""
	}

	// 生成下载链接
	link := fileLink

	// 发送导出完成通知
	err = service2.PublishNotify("售后列表导出完成", fmt.Sprintf("共导出了%d条数据", total))
	if err != nil {
		log.Log().Error("售后列表导出通知发送失败：", zap.Error(err))
	}

	return nil, link

}

// CreateAfterSales
//
// @function: CreateAfterSales
// @description: 创建售后记录
// @param: ad model.AfterSales
// @return: err error
func CreateAfterSales(as model.AfterSales) (err error, res model.AfterSales) {
	err = source.DB().Create(&as).Error
	return err, as
}

// CreateAfterSalesAudit
//
// @function: CreateAfterSalesAudit
// @description: 创建售后审核记录
// @param: ad model.AfterSalesAudit
// @return: err error
func CreateAfterSalesAudit(afterSalesAudit model.AfterSalesAudit) (err error) {
	err = source.DB().Create(&afterSalesAudit).Error
	return err
}

// CreateReturnOrderExpress
//
// @function: CreateReturnOrderExpress
// @description: 创建售后退货记录
// @param: ad model.ReturnOrderExpress
// @return: err error
func CreateReturnOrderExpress(roe model.ReturnOrderExpress) (err error, res model.ReturnOrderExpress) {
	err = source.DB().Create(&roe).Error
	return err, roe
}

// GetReasons
//
// @function: GetReasons
// @description: 创建售后退货记录
// @param: afterSaleType model.AfterSaleType
// @param: isReceived bool
// @return: model.ReturnOrderExpress
func GetReasons(afterSaleType model.AfterSalesType, isReceived int) (reasons []model.RefundReason) {
	if afterSaleType == model.Refund && isReceived == 0 {
		reasons = []model.RefundReason{
			model.DontWant,
			model.NotDelivery,
			model.NotSend,
			model.NoExpressInfo,
			model.EmptyExpressPackage,
			model.Others,
		}
	} else {
		reasons = []model.RefundReason{
			model.NotLike,
			model.WrongMaterial,
			model.WrongSize,
			model.WrongFashion,
			model.RefundFreight,
			model.WrongGoods,
			model.FakeProducts,
			model.BrokenProducts,
			model.QualityProblems,
		}
	}
	return
}

// GetLog
//
// @function: GetLog
// @description: 添加AfterSales.log日志内容
// @param: ad model.AfterSales
// @return: err error
func GetLog(logAfterSales request.LogAfterSales, afterSales model.AfterSales) (err error, res model.AfterSales) {
	var afterSalesLog model.Log

	afterSalesLog.CreatedAt = time.Now().Format("2006-01-02 15:04:05")
	afterSalesLog.Price = afterSales.Amount
	afterSalesLog.AdminID = logAfterSales.AdminId
	err, statusName := model.GetStatusName(afterSales.Status)

	err, refundTypeName := model.GetAfterSalesTypeName(afterSales.RefundType)
	if err != nil {
		return err, res
	}

	err, reasonTypeName := model.GetRefundReasonName(afterSales.ReasonType)

	if err != nil {
		return err, res
	}
	afterSalesLog.RefundTypeName = refundTypeName
	afterSalesLog.ReasonTypeName = reasonTypeName
	afterSalesLog.Description = afterSales.Description
	afterSalesLog.Price = afterSales.Amount
	afterSalesLog.Freight = afterSales.Freight
	afterSalesLog.TechnicalServicesFee = afterSales.TechnicalServicesFee
	afterSalesLog.BarterSkuTitle = afterSales.BarterSkuTitle
	afterSalesLog.BarterNum = afterSales.BarterNum
	afterSalesLog.BarterSkuID = afterSales.BarterSkuID
	afterSalesLog.ImageUrl = afterSales.ImageUrl
	afterSalesLog.RefundWay = afterSales.RefundWay
	afterSalesLog.RefundNum = afterSales.Num
	afterSalesLog.AfterSaleType = afterSales.AfterSaleType
	afterSalesLog.Ip = afterSales.Ip
	//如果存在adminId 就是商家操作的，如果不存在则是用户操作的
	switch afterSales.Status {
	case model.WaitAuditStatus:
		var caseString = "" //原因
		//换货时没有原因
		if afterSales.RefundType != model.Barter {
			caseString = ",原因" + reasonTypeName
		}
		if afterSales.ID != 0 && afterSales.AfterSalesAudit.Status == model.PendingStatus {
			afterSalesLog.Content = "修改了" + refundTypeName + "申请," + caseString
		} else {
			afterSalesLog.Content = "发起了" + refundTypeName + "申请" + caseString
		}

		if afterSales.ReasonType == model.Others {
			afterSalesLog.Content += ":" + afterSales.Reason
		}

		break
	case model.WaitSendStatus:
		afterSalesLog.Content = "商家同意了本次售后申请,当前状态:" + statusName
		break
	case model.WaitReceiveStatus:
		afterSalesLog.Content = "已发货,当前状态:" + statusName
		break
	case model.WaitRefundStatus:
		switch afterSales.RefundType {
		case model.Return:
			afterSalesLog.Content = "商家已确认收货,当前状态:" + statusName
			break
		default:
			afterSalesLog.Content = "商家已审核通过,当前状态" + statusName
		}
		break
	case model.WaitshopSendStatus:
		var AfterSalesSend model.AfterSalesSend
		source.DB().Where("after_sales_id = ?", afterSales.ID).Order("id desc").First(&AfterSalesSend)
		var count = 0
		source.DB().Model(&model.AfterSalesSend{}).Where("after_sales_id = ?", afterSales.ID).Select("count(*) as count").Pluck("count", &count)

		afterSalesLog.Content = "商家第" + strconv.Itoa(count) + "次发货,当前状态:" + statusName
		afterSalesLog.CompanyCode = AfterSalesSend.CompanyCode
		afterSalesLog.CompanyName = AfterSalesSend.CompanyName
		afterSalesLog.ExpressNo = AfterSalesSend.ExpressNo
		afterSalesLog.Num = AfterSalesSend.Num
		break
	case model.WaitUserReceiveStatus:
		var AfterSalesSend model.AfterSalesSend
		source.DB().Where("after_sales_id = ?", afterSales.ID).Order("id desc").First(&AfterSalesSend)
		var count = 0
		source.DB().Model(&model.AfterSalesSend{}).Where("after_sales_id = ?", afterSales.ID).Select("count(*) as count").Pluck("count", &count)
		if count == 1 {
			afterSalesLog.Content = "商家已确认收货,并且发货,当前状态:" + statusName
		} else {
			afterSalesLog.Content = "商家第" + strconv.Itoa(count) + "次发货,当前状态:" + statusName
		}
		afterSalesLog.CompanyCode = AfterSalesSend.CompanyCode
		afterSalesLog.CompanyName = AfterSalesSend.CompanyName
		afterSalesLog.ExpressNo = AfterSalesSend.ExpressNo
		afterSalesLog.Num = AfterSalesSend.Num
		break
	case model.CompletedStatus:
		afterSalesLog.Price = afterSales.PracticalAmount
		err, refundTypeName = model.GetAfterSalesTypeName(afterSales.RefundType)
		afterSalesLog.Content = "售后:" + refundTypeName + "完成"
		if logAfterSales.ReturnMsg != "" {
			afterSalesLog.Content += "," + logAfterSales.ReturnMsg
		}
		break
	case model.ClosedStatus:
		err, refundTypeName = model.GetAfterSalesTypeName(afterSales.RefundType)
		if err != nil {
			return err, res
		}

		if logAfterSales.AdminId != 0 {
			afterSalesLog.Content = "商家:" + statusName
		} else if logAfterSales.Appid != 0 {
			afterSalesLog.Content = "采购端:" + statusName
		} else {
			afterSalesLog.Content = "用户" + statusName + "" + refundTypeName
		}

		break
	}

	afterSales.Logs = append(afterSales.Logs, afterSalesLog)

	return err, afterSales
}

// GetAfterSalesAuditCountByAfterSalesId
//
// @function: GetAfterSalesAuditCountByAfterSalesId
// @description: 通过AfterSalesId 获取用户申请审核的次数
// @param: ad model.AfterSalesAudit
// @return: err error
func GetAfterSalesAuditCountByAfterSalesId(afterSalesId uint) (err error, count int64) {
	err = source.DB().Model(&model.AfterSalesAudit{}).Where("after_sales_id", afterSalesId).Count(&count).Error
	return err, count
}

type OperationAfterSales struct {
	model3.Order
	request.AfterSales
	model3.OrderItem
	GatherSupply
}
type Handle func(OperationAfterSales) error

type AfterSalesOperation struct {
	OperationAfterSales
}

var HandleFunc []Handle

func (o AfterSalesOperation) GetBeforeHandle() []Handle {
	return HandleFunc
}
func SetBeforeHandle(handleFunc func(OperationAfterSales) error) {
	HandleFunc = append(HandleFunc, handleFunc)
	return
}

// 获取退款方式
type AfterSalesTypeHandler func(OperationAfterSalesType) (error, interface{})

type OperationAfterSalesType struct {
	model3.Order
	OrderItemID uint
	GatherSupply
}

var afterSalesTypeHandlers []AfterSalesTypeHandler

// 注册处理函数
func RegisterAfterSalesTypeHandler(handler AfterSalesTypeHandler) {
	afterSalesTypeHandlers = append(afterSalesTypeHandlers, handler)
}

// 获取处理函数
func GetAfterSalesTypeHandlers() []AfterSalesTypeHandler {
	return afterSalesTypeHandlers
}

// ApplyAfterSales
//
// @function: ApplyAfterSales
// @description: 售后申请
// @param: ad model.AfterSales
// @return: err error
func ApplyAfterSales(requestCreateAfterSales request.AfterSales, uid uint) (err error, afterSalesId uint) {
	//if requestCreateAfterSales.Amount == 0 && requestCreateAfterSales.RefundType != model.Barter && requestCreateAfterSales.RefundWay == model.RefundWayPrice {
	//	err = errors.New("请输入退款金额")
	//	return
	//}
	// 操作结构
	var orderItem model3.OrderItem
	//var order model3.Order
	var afterSalesData model.AfterSales
	//var skus model2.Sku
	err, orderItem, _, afterSalesData, requestCreateAfterSales, _ = AfterSalesBeforeCheck(requestCreateAfterSales, uid)
	if err != nil {
		return
	}
	//如果查询到记录并且记录未被关闭 则新建待审核的审核记录，如果已关闭则全部新建（旧）
	//如果查询到记录 原纪录关闭就变为待审核继续使用（新）
	if afterSalesData.ID != 0 {
		//关闭的重新开启
		if afterSalesData.Status == model.ClosedStatus {
			afterSalesData.Status = model.WaitAuditStatus
			err = source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSalesData.ID).Save(&afterSalesData).Error
			if err != nil {
				err = errors.New("售后修改失败" + err.Error())
				log.Log().Error("售后修改失败", zap.Any("err", err))
				return
			}
		}
		//如果不是待审核不让改
		if afterSalesData.Status != model.WaitAuditStatus {
			_, statusName := model.GetStatusName(afterSalesData.Status)
			err = errors.New(fmt.Sprintf("售后不可修改,售后状态:%s", statusName))
			log.Log().Error("售后申请", zap.Any("err", err))
			return
		}
		var AfterSalesAuditData model.AfterSalesAudit
		err, AfterSalesAuditData = GetAfterSalesAuditByAfterSalesId(afterSalesData.ID)
		if err != nil {
			log.Log().Error("获取审核记录失败", zap.Any("err", err))
			err = errors.New("获取审核记录失败" + err.Error())
			return
		}
		//if AfterSalesAuditData.Status == model.PendingStatus {
		//	log.Log().Error("已存在申请", zap.Any("err", AfterSalesAuditData))
		//	_, statusName := model.GetAuditStatusName(AfterSalesAuditData.Status)
		//	err = errors.New(fmt.Sprintf("%s", statusName))
		//	return
		//}
		afterSalesData.OrderItemID = orderItem.ID
		afterSalesData.Amount = requestCreateAfterSales.Amount
		afterSalesData.Freight = requestCreateAfterSales.Freight
		afterSalesData.TechnicalServicesFee = requestCreateAfterSales.TechnicalServicesFee
		afterSalesData.ReasonType = requestCreateAfterSales.ReasonType
		afterSalesData.Description = requestCreateAfterSales.Description
		afterSalesData.IsReceived = requestCreateAfterSales.IsReceived
		afterSalesData.RefundType = requestCreateAfterSales.RefundType
		afterSalesData.Reason = requestCreateAfterSales.Reason
		afterSalesData.BarterSkuID = requestCreateAfterSales.BarterSkuID
		afterSalesData.BarterNum = requestCreateAfterSales.BarterNum
		afterSalesData.BarterSkuTitle = requestCreateAfterSales.BarterSkuTitle
		afterSalesData.ImageUrl = requestCreateAfterSales.ImageUrl
		afterSalesData.RefundWay = requestCreateAfterSales.RefundWay
		afterSalesData.Num = requestCreateAfterSales.Num
		afterSalesData.AfterSaleType = requestCreateAfterSales.AfterSaleType
		afterSalesData.Ip = requestCreateAfterSales.Ip

		var logAfterSales request.LogAfterSales
		err, afterSalesData = GetLog(logAfterSales, afterSalesData)

		if err = UpdateAfterSales(afterSalesData); err != nil {
			log.Log().Error("修改申请记录失败!", zap.Any("err", err))
			err = errors.New("修改申请记录失败")
			return
		}

		//再次创建申请记录
		//afterSalesAudit := model.AfterSalesAudit{
		//	AfterSalesID: afterSalesData.ID,
		//	AdminID:      0,
		//	ReasonType:       afterSalesData.ReasonType,
		//	Status:       model.PendingStatus,
		//	Reason:requestCreateAfterSales.Reason,
		//}
		//if err = CreateAfterSalesAudit(afterSalesAudit); err != nil {
		//	err = errors.New("创建审核记录失败")
		//	return
		//}
		//创建变为修改状态
		AfterSalesAuditData.Status = model.PendingStatus
		AfterSalesAuditData.ReasonType = afterSalesData.ReasonType
		AfterSalesAuditData.Reason = requestCreateAfterSales.Reason
		source.DB().Save(&AfterSalesAuditData)

		afterSalesId = afterSalesData.ID
	} else {
		as := model.AfterSales{
			UserID:               uid,
			OrderItemID:          requestCreateAfterSales.OrderItemID,
			Amount:               requestCreateAfterSales.Amount,
			ReasonType:           requestCreateAfterSales.ReasonType,
			Description:          requestCreateAfterSales.Description,
			IsReceived:           requestCreateAfterSales.IsReceived,
			DetailImages:         requestCreateAfterSales.DetailImages,
			RefundType:           requestCreateAfterSales.RefundType,
			OrderID:              orderItem.OrderID,
			ProductID:            orderItem.ProductID,
			SkuID:                orderItem.SkuID,
			Freight:              requestCreateAfterSales.Freight,
			TechnicalServicesFee: requestCreateAfterSales.TechnicalServicesFee,
			Status:               model.WaitAuditStatus,
			Reason:               requestCreateAfterSales.Reason,
			BarterSkuID:          requestCreateAfterSales.BarterSkuID,
			BarterNum:            requestCreateAfterSales.BarterNum,
			BarterSkuTitle:       requestCreateAfterSales.BarterSkuTitle,
			ImageUrl:             requestCreateAfterSales.ImageUrl,
			RefundWay:            requestCreateAfterSales.RefundWay,
			Num:                  requestCreateAfterSales.Num,
			AfterSaleType:        requestCreateAfterSales.AfterSaleType,
			Ip:                   requestCreateAfterSales.Ip,
		}
		var logAfterSales request.LogAfterSales
		err, as = GetLog(logAfterSales, as)
		var afterSales model.AfterSales
		err, afterSales = CreateAfterSales(as)
		if err != nil {
			log.Log().Error("售后申请,创建失败!", zap.Any("err", err))
			err = errors.New("售后申请,创建失败")
			return
		}

		afterSalesAudit := model.AfterSalesAudit{
			AfterSalesID: afterSales.ID,
			AdminID:      0,
			ReasonType:   afterSales.ReasonType,
			Status:       model.PendingStatus,
			Reason:       requestCreateAfterSales.Reason,
		}

		if err = CreateAfterSalesAudit(afterSalesAudit); err != nil {
			log.Log().Error("创建审核记录失败!", zap.Any("err", err))
			err = errors.New("创建审核记录失败")
			return
		}

		afterSalesId = afterSales.ID

	}
	//修改售后状态
	_ = SaveOrderAndOrderItemRefundStatus(orderItem.ID, orderItem.OrderID, model3.Refunding)
	//if err != nil {
	//	return
	//}

	err = nmq.PublishMessage(uid, "afterSaleOrder", orderItem.SupplierID)
	//消息调整为更新就是更新 创建就是创建
	if afterSalesData.ID != 0 {
		err = mq.PublishMessage(afterSalesData.ID, mq.AfterSalesUpdate, 0)
		if err != nil {
			log.Log().Error("售后修改推送消息失败!", zap.Any("err", err))
			err = errors.New("售后修改成功,推送消息失败" + err.Error())
			return
		}
	} else {
		err = mq.PublishMessage(afterSalesId, mq.AfterSalesCreate, 0)
		if err != nil {
			log.Log().Error("售后申请推送消息失败!", zap.Any("err", err))
			err = errors.New("售后申请成功,推送消息失败" + err.Error())
			return
		}
	}
	return
}

// 售后前置校验
func AfterSalesBeforeCheck(requestCreateAfterSales request.AfterSales, uid uint) (err error, orderItem model3.OrderItem, order model3.Order, afterSalesData model.AfterSales, resRequestCreateAfterSales request.AfterSales, skus model2.Sku) {

	err = source.DB().Where("id = ?", requestCreateAfterSales.OrderItemID).First(&orderItem).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("请选择要售后的子订单")
		return
	}
	if orderItem.CanRefund == 0 {
		log.Log().Error("已退款无法进行售后", zap.Any("orderItem", orderItem))
		err = errors.New("已退款,无需售后")
		return
	}

	err = source.DB().Where("id = ?", orderItem.OrderID).First(&order).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("获取订单失败")
		return
	}
	order.OrderItems = append(order.OrderItems, orderItem)
	var gatherSupply GatherSupply
	//如果时供应链订单则验证
	if order.GatherSupplyID != 0 {
		err = source.DB().First(&gatherSupply, order.GatherSupplyID).Error
		if err != nil {
			err = errors.New("供应链信息错误" + err.Error())
			return
		}
	}

	//判断是否支持该售后方式
	var result interface{}
	err, result = GetAfterSalesTypeNameMap(order, gatherSupply, requestCreateAfterSales.OrderItemID, 0)
	if err != nil {
		return
	}
	var ok bool
	var afterSalesTypeNameMap map[string]interface{}
	afterSalesTypeNameMap, ok = result.(map[string]interface{})
	if !ok {
		err = errors.New("售后类型映射格式错误")
		return
	}

	_, ok = afterSalesTypeNameMap[strconv.Itoa(int(requestCreateAfterSales.RefundType))]
	if !ok {
		_, statusName := model.GetAfterSalesTypeName(requestCreateAfterSales.RefundType)
		errMsg := fmt.Sprintf("该订单不支持%s", statusName)
		err = errors.New(errMsg)
		return
	}

	//如果时供应链订单则验证
	if gatherSupply.ID != 0 {
		afterSalesOperation := AfterSalesOperation{OperationAfterSales: OperationAfterSales{order, requestCreateAfterSales, orderItem, gatherSupply}}
		// 申请前钩子,多用于供应链前置验证
		for _, handle := range afterSalesOperation.GetBeforeHandle() {
			err = handle(OperationAfterSales{order, requestCreateAfterSales, orderItem, gatherSupply})
			if err != nil {
				//换货时既然判断售后方式已经验证通过了 代表开启了换货上游不支持也申请到本地 这里直接也判断为通过，到时候监听时不往上游同步
				_, setting := tradeModel.GetTradeSetting()
				if requestCreateAfterSales.RefundType == model.Barter && isBarter(order, gatherSupply, setting) == true {
					err = nil
				} else {
					return
				}
			}
		}
	}

	//换货
	//换货校验
	if requestCreateAfterSales.RefundType == model.Barter {
		if requestCreateAfterSales.BarterSkuID == 0 {
			err = errors.New("请选择换货的商品规格")
			return
		}
		err = source.DB().Where("id = ?", requestCreateAfterSales.BarterSkuID).First(&skus).Error
		if err != nil {
			err = errors.New("规格不存在")
			return
		}
		if skus.ProductID != orderItem.ProductID {
			err = errors.New("选择的商品不是当前订单的下单商品")
			return
		}
		if requestCreateAfterSales.BarterNum == 0 {
			err = errors.New("请填写换货数量")
			return
		}
		if requestCreateAfterSales.BarterNum > orderItem.Qty {
			err = errors.New("换货数量不可大于原本购买数量")
			return
		}
		//如果请求未传规格名称就使用这个规格的名称
		if requestCreateAfterSales.BarterSkuTitle == "" {
			requestCreateAfterSales.BarterSkuTitle = skus.Title
		}
		requestCreateAfterSales.ImageUrl = skus.ImageUrl

	} else {
		//如果是按照数量退款
		if requestCreateAfterSales.RefundWay == model.RefundWayNum {
			if requestCreateAfterSales.Num == 0 {
				err = errors.New("请填写退款商品数量")
				return
			}
			if requestCreateAfterSales.Num > orderItem.Qty {
				err = errors.New("退款商品数量大于下单数量")
				return
			}
			//因为没有单独的单价 所以需要 子订单支付金额/子订单商品数量
			requestCreateAfterSales.Amount = orderItem.Amount / orderItem.Qty * requestCreateAfterSales.Num //退款金额
		}
	}

	//如果没传金额则使用子订单金额  (不是换货的情况下)
	if requestCreateAfterSales.Amount == 0 && requestCreateAfterSales.RefundType != model.Barter {
		requestCreateAfterSales.Amount = orderItem.Amount
	}

	if orderItem.Amount < requestCreateAfterSales.Amount {
		log.Log().Error("退款金额大于商品金额")
		err = errors.New("退款金额大于商品金额")
		return
	}

	//如果技术服务费不等于0
	if requestCreateAfterSales.TechnicalServicesFee > 0 {
		//退款技术服务器不可大于订单的技术服务费
		if requestCreateAfterSales.TechnicalServicesFee > order.TechnicalServicesFee {
			log.Log().Error("退款的技术服务费金额大于订单技术服务费")
			err = errors.New("退款的技术服务费金额大于订单技术服务费")
			return
		}
		//新的订单存在子订单技术服务费，如果有退款的技术服务费,并且大于子订单技术服务费 就等于子订单的技术服务费金额
		if requestCreateAfterSales.TechnicalServicesFee > orderItem.TechnicalServicesFee {
			requestCreateAfterSales.TechnicalServicesFee = orderItem.TechnicalServicesFee
		}
		//技术服务费验证
		err, _ = TechnicalServicesFeeValidation(requestCreateAfterSales, orderItem)
		if err != nil {
			return
		}

	}

	err, afterSalesData = GetAfterSalesByOrderItemId(requestCreateAfterSales.OrderItemID)
	//如果不是查询不到的错误直接返回
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("售后查询失败" + err.Error())
		log.Log().Error("售后查询失败", zap.Any("err", err))
		return
	}
	//售后运费总计
	err, requestCreateAfterSales.Freight = AfterSalesFreight(order, afterSalesData, requestCreateAfterSales)
	resRequestCreateAfterSales = requestCreateAfterSales
	return
}

/*
*

	验证技术服务费
*/
func TechnicalServicesFeeValidation(requestCreateAfterSales request.AfterSales, orderItem model3.OrderItem) (err error, TechnicalServicesFee uint) {

	// 根据退款方式计算技术服务费
	if requestCreateAfterSales.TechnicalServicesFee > 0 {
		if requestCreateAfterSales.RefundWay == model.RefundWayPrice && requestCreateAfterSales.Amount > 0 {
			// 按退款金额比例计算技术服务费
			refundRatio := float64(requestCreateAfterSales.Amount) / float64(orderItem.Amount)
			TechnicalServicesFee = uint(float64(orderItem.TechnicalServicesFee) * refundRatio)
		} else if requestCreateAfterSales.RefundWay == model.RefundWayNum && requestCreateAfterSales.Num > 0 {
			// 按退款个数比例计算技术服务费
			refundRatio := float64(orderItem.TechnicalServicesFee) / float64(orderItem.Qty)
			TechnicalServicesFee = uint(float64(requestCreateAfterSales.Num) * refundRatio)
		}
		if requestCreateAfterSales.TechnicalServicesFee > TechnicalServicesFee {
			err = errors.New("最多可退技术服务费:" + utils.Fen2Yuan(TechnicalServicesFee) + "元")
			return
		}
	}
	return
}
func SaveOrderAndOrderItemRefundStatus(orderItemId, orderId uint, refundStatus model3.RefundStatus) (err error) {
	var isOrderRefundStatus = 1
	if refundStatus == model3.NotRefund {
		var afterSales []model.AfterSales
		source.DB().Model(&model.AfterSales{}).Where("order_item_id != ?", orderItemId).Where("order_id = ?", orderId).Where("status != ?", model.ClosedStatus).Find(&afterSales)
		if len(afterSales) != 0 {
			//如果有其他的状态不是已关闭，或者待审核状态则这里不改变订单的售后状态依旧是进行中
			for _, item := range afterSales {
				if isOrderRefundStatus == 0 {
					break
				}
				if item.Status == model.WaitAuditStatus {
					_, afterSalesAudit := GetAfterSalesAuditByAfterSalesId(item.ID)
					if afterSalesAudit.Status == model.PendingStatus {
						isOrderRefundStatus = 0
					}
				} else {
					isOrderRefundStatus = 0
				}
			}
		}
	}
	if isOrderRefundStatus == 1 {
		err = source.DB().Where("id = ?", orderId).Updates(&model3.OrderModel{
			RefundStatus: refundStatus,
		}).Error
		if err != nil {
			log.Log().Error("修改订单售后状态失败!", zap.Any("err", err))
			err = errors.New("修改订单售后失败")
			return
		}
	}

	err = source.DB().Where("id = ?", orderItemId).Updates(&model3.OrderItemModel{
		RefundStatus: refundStatus,
	}).Error

	if err != nil {
		log.Log().Error("修改OrderItem状态失败!", zap.Any("err", err))
		err = errors.New("修改子订单状态状态失败")
		return
	}
	return
}

// SaveAfterSales
//
// @function: SaveAfterSales
// @description: 修改售后
// @param: ad model.AfterSales
// @return: err error
func SaveAfterSales(requestCreateAfterSales request.AfterSales) (err error) {
	// 操作结构
	//var orderItem model3.OrderItem
	//var order model3.Order
	var afterSalesData model.AfterSales
	//var skus model2.Sku
	err, _, _, afterSalesData, requestCreateAfterSales, _ = AfterSalesBeforeCheck(requestCreateAfterSales, 0)
	if err != nil {
		return
	}

	if afterSalesData.Status != model.WaitAuditStatus {
		log.Log().Error("申请状态不正确无法修改", zap.Any("err", afterSalesData))
		_, statusName := model.GetStatusName(afterSalesData.Status)
		err = errors.New(fmt.Sprintf("售后无法修改,售后状态%s", statusName))
		return
	}
	if afterSalesData.AfterSalesAudit.Status != model.PendingStatus {
		log.Log().Error("审核状态不正确无法修改", zap.Any("err", afterSalesData))
		_, statusName := model.GetAuditStatusName(afterSalesData.AfterSalesAudit.Status)
		err = errors.New(fmt.Sprintf("售后无法修改,售后状态：%s", statusName))
		return
	}

	afterSalesData.Amount = requestCreateAfterSales.Amount
	afterSalesData.ReasonType = requestCreateAfterSales.ReasonType
	afterSalesData.Description = requestCreateAfterSales.Description
	afterSalesData.IsReceived = requestCreateAfterSales.IsReceived
	afterSalesData.RefundType = requestCreateAfterSales.RefundType
	afterSalesData.Reason = requestCreateAfterSales.Reason
	afterSalesData.DetailImages = requestCreateAfterSales.DetailImages
	afterSalesData.Freight = requestCreateAfterSales.Freight
	afterSalesData.TechnicalServicesFee = requestCreateAfterSales.TechnicalServicesFee
	afterSalesData.BarterSkuID = requestCreateAfterSales.BarterSkuID
	afterSalesData.BarterNum = requestCreateAfterSales.BarterNum
	afterSalesData.BarterSkuTitle = requestCreateAfterSales.BarterSkuTitle
	afterSalesData.ImageUrl = requestCreateAfterSales.ImageUrl
	afterSalesData.RefundWay = requestCreateAfterSales.RefundWay
	afterSalesData.Num = requestCreateAfterSales.Num
	afterSalesData.AfterSaleType = requestCreateAfterSales.AfterSaleType
	afterSalesData.Ip = requestCreateAfterSales.Ip
	var logAfterSales request.LogAfterSales

	err, afterSalesData = GetLog(logAfterSales, afterSalesData)

	if err = UpdateAfterSales(afterSalesData); err != nil {
		log.Log().Error("修改申请记录失败!", zap.Any("err", err))
		err = errors.New("修改申请记录失败")
		return
	}

	afterSalesAudit := model.AfterSalesAudit{
		ReasonType: afterSalesData.ReasonType,
	}

	if requestCreateAfterSales.ReasonType == model.Others {
		afterSalesAudit.Reason = requestCreateAfterSales.Reason
	}
	afterSalesAudit.ID = afterSalesData.AfterSalesAudit.ID

	if err = UpdateAfterSalesAudit(afterSalesAudit); err != nil {
		log.Log().Error("修改审核记录失败!", zap.Any("err", err))
		err = errors.New("修改审核记录失败")
		return
	}
	err = mq.PublishMessage(afterSalesData.ID, mq.AfterSalesUpdate, 0)
	return
}

// 售后运费计算
func AfterSalesFreight(order model3.Order, afterSalesData model.AfterSales, requestCreateAfterSales request.AfterSales) (err error, freight uint) {
	//售后运费总计
	var freightTotal uint = 0
	freightTotalDb := source.DB().Model(&model.AfterSales{}).Joins("left join after_sales_audits as asa on asa.after_sales_id = after_sales.id").Where("after_sales.order_id = ?", order.ID).Where("after_sales.status != -1 && (asa.status = 1 or asa.status = 0)").Group("after_sales.order_id").Select("sum(freight) as freight_total")
	if afterSalesData.ID != 0 {
		freightTotalDb.Where("after_sales.id != ?", afterSalesData.ID)
	}
	err = freightTotalDb.Pluck("freight_total", &freightTotal).Error
	if err != nil {
		log.Log().Error("获取失败运费失败", zap.Any("err", err))
		err = errors.New("获取售后总运费失败" + err.Error())
		return
	}
	freight = order.Freight - freightTotal //获取还可以售后的运费
	//如果申请的运费大于剩下的运费则直接等于剩下的运费,不然就是申请运费
	if requestCreateAfterSales.Freight > freight {
		log.Log().Error("退款的运费金额大于订单运费")
		requestCreateAfterSales.Freight = freight
	} else {
		freight = requestCreateAfterSales.Freight
	}
	return
}

// SendAfterSales
//
// @function: SendAfterSales
// @description: 售后发货
// @param: ad model.AfterSales
// @return: err error
// 采购端id 和用户id 哪个传了检验哪个
func SendAfterSales(sendRequest request.SendRequest, uid uint, appId uint) (err error) {

	// 查询并校验售后申请
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(sendRequest.AfterSalesID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取售后信息失败")
		return
	}
	if afterSales.NewShippingAddressID == 0 && sendRequest.ShippingAddressID == 0 {
		log.Log().Error("请设置默认退货地址(供应商商品由供应商设置默认退货地址)")
		err = errors.New("请设置默认退货地址(供应商商品由供应商设置默认退货地址)")
		return
	}
	if uid != 0 {
		if afterSales.UserID != uid {
			err = errors.New(fmt.Sprintf("该售后申请（%d）不属于当前用户", afterSales.ID))
			log.Log().Error("操作失败!", zap.Any("err", err))
			return
		}
	}
	if appId != 0 {
		if afterSales.Order.ApplicationID != appId {
			log.Log().Error("获取失败,不存在这个售后或者不属于这个采购端", zap.Any("err", err))
			err = errors.New("获取失败,不存在这个售后或者不属于这个采购端")
			return
		}
	}
	//if sendRequest.ShippingAddressID == 0 {
	//	log.Log().Error("发货失败，平台/供应商没有默认售后收货地址", zap.Any("err", err))
	//	err = errors.New("发货失败，平台/供应商没有默认售后收货地址")
	//	return
	//}
	// 售后申请改为待收货状态 //原model.WaitRefundStatus是待退款才能发货 调整为待发货的时候才可以发货
	if afterSales.Status != model.WaitSendStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行发货操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}
	var isCreateafterSalesShopAddress = 1 // 1不创建 2创建
	if afterSales.NewShippingAddressID == 0 {
		isCreateafterSalesShopAddress = 2
	}
	//因为商城是获取默认地址 可能出现以记录的地址与提交的发货地址不符合的情况这里判断一下是否需要创建 -- 如果商城调整为售后与商品一对一与中台能对应上 这里可以去掉
	//if afterSales.NewShippingAddressID != 0 {
	//	var afterSalesShopAddress model.AfterSalesShopAddress
	//	err = source.DB().Model(&model.AfterSalesShopAddress{}).Where("id = ?",afterSales.NewShippingAddressID).First(&afterSalesShopAddress).Error
	//	if err != nil {
	//		err = errors.New("记录的退货地址不存在")
	//		return
	//	}
	//
	//	if sendRequest.ShippingAddressID != 0 && afterSales.NewShippingAddressID != sendRequest.ShippingAddressID{
	//		var shopAddress model.ShopAddress
	//		err = source.DB().Model(&model.ShopAddress{}).Where("id = ?",sendRequest.ShippingAddressID).First(&shopAddress).Error
	//		//如果为空代表传的就是新地址的id不做创建操作
	//		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
	//			err = errors.New("退货地址不存在"+err.Error())
	//			return
	//		}
	//		if afterSalesShopAddress.Contacts == shopAddress.Contacts && afterSalesShopAddress.Tel == shopAddress.Tel && afterSalesShopAddress.Province == shopAddress.Province && afterSalesShopAddress.City == shopAddress.City && afterSalesShopAddress.District == shopAddress.District && afterSalesShopAddress.Address == shopAddress.Address{
	//				isCreateafterSalesShopAddress = 1
	//		}
	//
	//	}
	//}

	if isCreateafterSalesShopAddress == 2 {
		var shopAddress model.ShopAddress
		err = source.DB().Model(&model.ShopAddress{}).Where("id = ?", sendRequest.ShippingAddressID).First(&shopAddress).Error
		if err != nil {
			err = errors.New("退货地址不存在")
			return
		}
		var afterSalesShopAddress model.AfterSalesShopAddress
		err = copier.Copy(&afterSalesShopAddress, shopAddress)
		if err != nil {
			err = errors.New("退货地址保存失败")
			return
		}
		afterSalesShopAddress.ID = 0
		afterSalesShopAddress.CreatedAt = &source.LocalTime{Time: time.Now()}
		afterSalesShopAddress.UpdatedAt = &source.LocalTime{Time: time.Now()}
		err = source.DB().Save(&afterSalesShopAddress).Error
		if err != nil {
			err = errors.New("退货地址保存失败" + err.Error())
			return
		}
		afterSales.NewShippingAddressID = afterSalesShopAddress.ID
	}

	// 创建退货配送信息
	err, returnOrderExpress := CreateReturnOrderExpress(model.ReturnOrderExpress{
		AfterSalesID: sendRequest.AfterSalesID,
		CompanyCode:  sendRequest.CompanyCode,
		ExpressNo:    sendRequest.ExpressNo,
		CompanyName:  sendRequest.CompanyName,
	})
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("创建退货配送信息失败")
		return
	}
	afterSales.Status = model.WaitReceiveStatus
	afterSales.ReturnOrderExpressID = returnOrderExpress.ID
	var logAfterSales request.LogAfterSales
	logAfterSales.Appid = appId
	err, afterSales = GetLog(logAfterSales, afterSales)

	err = UpdateAfterSales(afterSales)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改售后记录失败")
		return
	}
	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesUserSend, 0)

	return err
}

// SendAfterSales
//
// @function: CloseAfterSales
// @description: 售后关闭
// @param: ad model.AfterSales
// @return: err error
// 采购端id 和用户id 哪个传了检验哪个
func CloseAfterSales(as model.AfterSales, uid uint, appId uint) (err error) {
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(as.ID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))

		err = errors.New("获取售后记录失败" + err.Error())

		return
	}

	if uid != 0 {
		if afterSales.UserID != uid {
			err = errors.New(fmt.Sprintf("该售后申请（%d）不属于当前用户", afterSales.ID))
			log.Log().Error("操作失败!", zap.Any("err", err))
			return
		}
	}
	if appId != 0 {
		if afterSales.Order.ApplicationID != appId {
			log.Log().Error("获取失败,不存在这个售后或者不属于这个采购端", zap.Any("err", err))
			err = errors.New("获取失败,不存在这个售后或者不属于这个采购端")
			return
		}
	}

	//不是待审核状态无法关闭 -- 用户关闭售后必须是待审核的
	//if afterSales.Status != model.WaitAuditStatus && uid != 0{
	//	var statusName string
	//	_, statusName = model.GetStatusName(afterSales.Status)
	//	err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行关闭操作", statusName))
	//	log.Log().Error("操作失败!", zap.Any("err", err))
	//	return
	//}
	//售后已经是关闭的直接返回成功
	if afterSales.Status == model.ClosedStatus {
		return nil
	}
	if afterSales.Status == model.CompletedStatus {
		var statusName string
		_, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行关闭操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}
	//查询最近的一条记录之后关闭
	err, AfterSalesAuditData := GetAfterSalesAuditByAfterSalesId(afterSales.ID)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取售后审核记录失败")

		return
	}

	AfterSalesAuditData.Status = model.ClosedRefundStatus

	err = UpdateAfterSalesAudit(AfterSalesAuditData)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改审核记录失败" + err.Error())
		return
	}
	//关闭售后表记录
	afterSales.Status = model.ClosedStatus
	var logAfterSales request.LogAfterSales
	logAfterSales.Appid = appId
	err, afterSales = GetLog(logAfterSales, afterSales)

	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改售后记录失败" + err.Error())
		return
	}

	//err = source.DB().Model(&model3.OrderItem{}).Where("id = ?", afterSales.OrderItemID).Update("refund_status", model3.NotRefund).Error
	//
	//if err != nil {
	//	log.Log().Error("修改OrderItem状态失败!", zap.Any("err", err))
	//	err = errors.New("修改子订单记录失败")
	//	return
	//}
	//修改售后状态
	_ = SaveOrderAndOrderItemRefundStatus(afterSales.OrderItemID, afterSales.OrderID, model3.NotRefund)

	//采购端API请求的关闭 不通知
	if appId == 0 {
		err = mq.PublishMessage(afterSales.ID, mq.AfterSalesClose, 0)
	} else {
		err = mq.PublishMessage(afterSales.ID, mq.AfterSalesClose, 1)

	}
	if err != nil {
		log.Log().Error("推送消息失败!", zap.Any("err", err))
		err = errors.New("推送消息失败" + err.Error())
		return
	}
	return err
}

/**
消息成功之后商城通知中台 中台进行改变状态
*/

func MessageSuccess(appId uint, messageSuccess request.MessageSuccess) (err error) {
	err = source.DB().Where("application_id = ?", appId).Where("order_item_id = ?", messageSuccess.OrderItemId).Where("after_sales_message_type = ?", messageSuccess.MessageType).Updates(&model.AfterSalesPushMessage{
		Status: 3,
	}).Error
	if err != nil {
		return
	}
	return
}

/*
*
获取所有状态不是成功的消息
*/
func GetMessageError(appId, appShopId uint) (err error, message []model.AfterSalesPushMessage) {
	db := source.DB()
	if appShopId != 0 {
		db = db.Where("application_shop_id = ?", appShopId)
	} else {
		db = db.Where("application_id = ?", appId)
	}
	err = db.Limit(100).Where("status in (-1,1,2)").Order("created_at asc").Find(&message).Error
	if err != nil {
		return
	}
	return
}

// 审核通过同步时执行
func SynPassAudit(afterSales model.AfterSales, afterSalesAudit model.AfterSalesAudit, afterSalesForDetail AfterSalesForDetail) (err error) {
	//保存第三方地址本地记录
	var shopAddress model.ShopAddress
	if afterSalesForDetail.ShopAddress.ID != 0 {
		err = copier.Copy(&shopAddress, afterSalesForDetail.ShopAddress)
		if err != nil {
			afterSales.SynType = -2
			afterSales.SynMessage = "同步售后审核通过状态失败,地址复制失败"
			source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales)
			log.Log().Error("同步售后审核通过状态失败,地址复制失败", zap.Any("err", err))
			err = errors.New(afterSales.SynMessage)
			return
		}
		shopAddress.ID = 0
		shopAddress.CreatedAt = &source.LocalTime{Time: time.Now()}
		shopAddress.UpdatedAt = &source.LocalTime{Time: time.Now()}
		shopAddress.IsShow = 1
		shopAddress.ThirdShopAddressId = afterSalesForDetail.ShopAddress.ID
		err = source.DB().Model(&model.ShopAddress{}).Save(&shopAddress).Error
		if err != nil {
			afterSales.SynType = -2
			afterSales.SynMessage = "同步售后审核通过状态失败，地址保存失败"
			source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales)
			log.Log().Error("同步售后审核通过状态失败，地址保存失败", zap.Any("err", err))
			err = errors.New(afterSales.SynMessage)
			return
		}
		//保存第三方地址本地记录 end
		afterSalesAudit.ShippingAddressID = shopAddress.ID //指定当前售后固定为这个地址
		err = PassAudit(afterSalesAudit, 0, 0)
	} else {
		err = PassAuditOld(afterSalesAudit, 0, 0)
	}

	if err != nil {
		afterSales.SynType = -2
		afterSales.SynMessage = "同步售后审核通过状态失败" + err.Error()
		source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales)
		log.Log().Error("同步售后审核通过状态失败", zap.Any("err", err))
		err = errors.New(afterSales.SynMessage)
	}
	return
}

/*
*

	审核通过
*/
func PassAudit(resAfterSaleAudit model.AfterSalesAudit, adminId uint, supplierId uint) (err error) {
	err, afterSaleAudit := GetAfterSaleAudit(resAfterSaleAudit.ID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}

	//如果已经审核通过则直接返回成功
	if afterSaleAudit.Status == model.PassedStatus {
		return
	}

	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(afterSaleAudit.AfterSalesID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}
	if afterSales.RefundType != 0 && resAfterSaleAudit.ShippingAddressID == 0 {
		log.Log().Error("请配置退货地址", zap.Any("resAfterSaleAudit", resAfterSaleAudit))
		err = errors.New("请配置退货地址")
		return
	}

	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}

	if afterSales.Status != model.WaitAuditStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行通过操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}
	if afterSales.RefundType != 0 && resAfterSaleAudit.ShippingAddressID != 0 {
		var shopAddress model.ShopAddress
		err = source.DB().Model(&model.ShopAddress{}).Where("id = ?", resAfterSaleAudit.ShippingAddressID).First(&shopAddress).Error
		if err != nil {
			err = errors.New("退货地址不存在")
			return
		}
		var afterSalesShopAddress model.AfterSalesShopAddress
		err = copier.Copy(&afterSalesShopAddress, shopAddress)
		if err != nil {
			err = errors.New("退货地址保存失败")
			return
		}
		afterSalesShopAddress.ID = 0
		afterSalesShopAddress.CreatedAt = &source.LocalTime{Time: time.Now()}
		afterSalesShopAddress.UpdatedAt = &source.LocalTime{Time: time.Now()}
		err = source.DB().Save(&afterSalesShopAddress).Error
		if err != nil {
			err = errors.New("退货地址保存失败" + err.Error())
			return
		}
		afterSales.NewShippingAddressID = afterSalesShopAddress.ID
	}

	err = UpdateAfterSalesAudit(model.AfterSalesAudit{AdminID: adminId, Model: source.Model{ID: afterSaleAudit.ID}, Status: model.PassedStatus})
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}
	//默认待发货，如果退款方式是 退款就是待退款
	var afterSalesStatus = model.WaitSendStatus

	if afterSales.RefundType == model.Refund {
		afterSalesStatus = model.WaitRefundStatus
	}

	afterSales.Status = afterSalesStatus
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)
	//
	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}

	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesPassTheAudit, 0)
	return
}

/*
审核通过 -- 旧版
*/
func PassAuditOld(resAfterSaleAudit model.AfterSalesAudit, adminId uint, supplierId uint) (err error) {
	err, afterSaleAudit := GetAfterSaleAudit(resAfterSaleAudit.ID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}

	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(afterSaleAudit.AfterSalesID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}

	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	if afterSales.Status != model.WaitAuditStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行通过操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}

	err = UpdateAfterSalesAudit(model.AfterSalesAudit{AdminID: adminId, Model: source.Model{ID: afterSaleAudit.ID}, Status: model.PassedStatus})
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}
	//默认待发货，如果退款方式是 退款就是待退款
	var afterSalesStatus = model.WaitSendStatus

	if afterSales.RefundType == model.Refund {
		afterSalesStatus = model.WaitRefundStatus
	}

	afterSales.Status = afterSalesStatus
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)
	//
	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}

	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesPassTheAudit, 0)
	return
}

// 拒绝申请 --同步使用
func SynRejectAudit(afterSales model.AfterSales, afterSalesAudit model.AfterSalesAudit) (err error) {
	err = RejectAudit(afterSalesAudit, 0, 0)
	if err != nil {
		afterSales.SynType = -3
		afterSales.SynMessage = "同步售后审核驳回状态失败" + err.Error()
		source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales)
		log.Log().Error("同步售后审核驳回状态失败", zap.Any("err", err))
		err = errors.New(afterSales.SynMessage)
	}
	return
}

// 售后完成--同步使用
func SynComplete(afterSales model.AfterSales) (err error) {
	if afterSales.Status == model.CompletedStatus {
		log.Log().Error("售后已完成无需同步", zap.Any("after_sales", afterSales))
		return nil
	}
	if afterSales.RefundType == model.Barter { //换货完成代表收货
		err = UserReceive(afterSales, 0, 0, 0, 0)
	} else if afterSales.RefundType == model.Return { //退货退款时 完成代表 收货 退款
		err = Receive(request.AfterSalesSend{
			Id: afterSales.ID,
		}, 0, 0)
		if err == nil {
			err, _ = Refund(afterSales, 0, 0)
		}
	} else { //退款时完成代表仅退款
		err, _ = Refund(afterSales, 0, 0)
	}
	if err != nil {
		afterSales.SynType = -4
		afterSales.SynMessage = "同步售后完成状态失败" + err.Error()
		source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales)
		log.Log().Error("同步售后完成状态失败", zap.Any("err", err))
		err = errors.New(afterSales.SynMessage)
	}
	return
}

// 换货--商家确认收货并发货 -- 同步使用
func SynReceiving(afterSales model.AfterSales, afterSalesForDetail AfterSalesForDetail) (err error) {
	if afterSales.RefundType == model.Barter {
		err = Receive(request.AfterSalesSend{
			Id:          afterSales.ID,
			CompanyName: afterSalesForDetail.AfterSalesSend[0].CompanyName,
			CompanyCode: afterSalesForDetail.AfterSalesSend[0].CompanyCode,
			ExpressNo:   afterSalesForDetail.AfterSalesSend[0].ExpressNo,
			Num:         afterSalesForDetail.AfterSalesSend[0].Num,
		}, 0, 0)
		if err != nil {
			afterSales.SynType = -5
			afterSales.SynMessage = "同步售后商家确认收货并发货状态失败" + err.Error()
			source.DB().Model(&model.AfterSales{}).Where("id = ?", afterSales.ID).Save(&afterSales)
			log.Log().Error("同步售后商家确认收货并发货状态失败", zap.Any("err", err))
			err = errors.New(afterSales.SynMessage)
		}
	}
	return
}

/*
*
拒绝申请
*/
func RejectAudit(resAfterSaleAudit model.AfterSalesAudit, adminId uint, supplierId uint) (err error) {
	err, afterSaleAudit := GetAfterSaleAudit(resAfterSaleAudit.ID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("未查询到审核记录")
		return
	}
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(afterSaleAudit.AfterSalesID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New(err.Error())
		return
	}
	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	if afterSales.Status != model.WaitAuditStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行通过操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}

	err = UpdateAfterSalesAudit(model.AfterSalesAudit{Model: source.Model{ID: afterSaleAudit.ID}, Status: model.ClosedRefundStatus, Cause: resAfterSaleAudit.Cause})
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改失败" + err.Error())
		return
	}

	var cause string
	cause = "商家拒绝了申请"
	if resAfterSaleAudit.Cause != "" {
		cause += ",原因：" + resAfterSaleAudit.Cause
	}
	var afterSaleslog model.Log
	afterSaleslog.CreatedAt = time.Now().Format("2006-01-02 15:04:05")
	afterSaleslog.AdminID = adminId
	afterSaleslog.Content = cause
	afterSaleslog.Price = afterSales.Amount
	afterSaleslog.Freight = afterSales.Freight
	afterSaleslog.TechnicalServicesFee = afterSales.TechnicalServicesFee
	afterSaleslog.BarterSkuTitle = afterSales.BarterSkuTitle
	afterSaleslog.BarterNum = afterSales.BarterNum
	afterSaleslog.BarterSkuID = afterSales.BarterSkuID
	afterSaleslog.ImageUrl = afterSales.ImageUrl
	afterSales.Logs = append(afterSales.Logs, afterSaleslog)
	err = UpdateAfterSales(afterSales)
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("日志添加失败" + err.Error())
		return
	}

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改失败" + err.Error())
		return
	}
	//err = source.DB().Model(&model3.OrderItem{}).Where("id = ?", afterSales.OrderItemID).Update("refund_status", model3.NotRefund).Error
	//if err != nil {
	//	log.Log().Error("修改OrderItem状态失败!", zap.Any("err", err))
	//	err = errors.New("修改子订单记录失败")
	//	return
	//}
	//修改售后状态
	_ = SaveOrderAndOrderItemRefundStatus(afterSales.OrderItemID, afterSales.OrderID, model3.NotRefund)
	//if err != nil {
	//	return
	//}
	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesRejectAudit, 0)
	//关闭这个售后申请
	//err = service.UpdateAfterSales(model.AfterSales{Model:source.Model{ID: afterSales.ID},Status: model.ClosedStatus})
	//if err != nil {
	//	log.Log().Error("操作失败!", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}
	return
}

/*
*

	售后收货
*/
func Receive(as request.AfterSalesSend, adminId uint, supplierId uint) (err error) {
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(as.Id); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取记录失败" + err.Error())
		return
	}

	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	if afterSales.Status != model.WaitReceiveStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行收货操作", statusName))
		return
	}
	//换货时
	if afterSales.RefundType == model.Barter {
		if as.CompanyCode == "" {
			err = errors.New("请选择发货的快递公司")
			return
		}
		if as.CompanyName == "" {
			err = errors.New("请选择发货的快递公司")
			return
		}
		if as.ExpressNo == "" {
			err = errors.New("请输入快递单号")
			return
		}
		if as.Num == 0 {
			err = errors.New("请输入数量")
			return
		}
		var total = 0
		source.DB().Model(&model.AfterSalesSend{}).Where("after_sale_id = ?", afterSales.ID).Select("sum(num) as total").Pluck("total", &total)
		if uint(total)+as.Num > afterSales.OrderItem.Qty {
			err = errors.New("发货总数量超过子订单商品数量")
			return
		}
		if as.Num > afterSales.BarterNum {
			err = errors.New("发货总数量超过换货数量")
			return
		}
		var AfterSalesSend model.AfterSalesSend
		AfterSalesSend.AfterSalesID = afterSales.ID
		AfterSalesSend.Num = uint(as.Num)
		AfterSalesSend.ExpressNo = as.ExpressNo
		AfterSalesSend.CompanyName = as.CompanyName
		AfterSalesSend.CompanyCode = as.CompanyCode
		AfterSalesSend.ImageUrl = afterSales.ImageUrl
		AfterSalesSend.BarterSkuID = afterSales.BarterSkuID
		AfterSalesSend.BarterSkuTitle = afterSales.BarterSkuTitle
		AfterSalesSend.ProductID = afterSales.ProductID
		err = source.DB().Create(&AfterSalesSend).Error
		if err != nil {
			err = errors.New("发货失败" + err.Error())
			return
		}
		if uint(total)+as.Num == afterSales.BarterNum {
			afterSales.SendStatus = 1
			afterSales.Status = model.WaitUserReceiveStatus
		} else {
			afterSales.SendStatus = 2
			afterSales.Status = model.WaitshopSendStatus
		}
		afterSales.SendNum = afterSales.SendNum + as.Num
	} else {
		afterSales.Status = model.WaitRefundStatus
		afterSales.SendStatus = 1
	}
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)

	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改记录失败" + err.Error())
		return
	}

	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesReceiving, 0)
	return
}

/*
售后发货
*/
func AfterSalesShopSend(as request.AfterSalesSend, adminId uint, supplierId uint) (err error) {
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(as.Id); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取记录失败" + err.Error())
		return
	}

	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	if afterSales.Status != model.WaitshopSendStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行发货操作", statusName))
		return
	}
	//换货时
	if afterSales.RefundType != model.Barter {
		err = errors.New("并非是换货的售后，无需发货")
		return
	}
	if as.CompanyCode == "" {
		err = errors.New("请选择发货的快递公司")
		return
	}
	if as.CompanyName == "" {
		err = errors.New("请选择发货的快递公司")
		return
	}
	if as.ExpressNo == "" {
		err = errors.New("请输入快递单号")
		return
	}
	if as.Num == 0 {
		err = errors.New("请输入数量")
		return
	}
	var total = 0
	source.DB().Model(&model.AfterSalesSend{}).Where("after_sale_id = ?", afterSales.ID).Select("sum(num) as total").Pluck("total", &total)
	if afterSales.SendNum+as.Num > afterSales.OrderItem.Qty {
		err = errors.New("发货总数量超过子订单商品数量")
		return
	}
	if afterSales.SendNum+as.Num > afterSales.BarterNum {
		err = errors.New("发货总数量超过换货数量")
		return
	}
	var AfterSalesSend model.AfterSalesSend
	AfterSalesSend.AfterSalesID = afterSales.ID
	AfterSalesSend.Num = uint(as.Num)
	AfterSalesSend.ExpressNo = as.ExpressNo
	AfterSalesSend.CompanyName = as.CompanyName
	AfterSalesSend.CompanyCode = as.CompanyCode
	AfterSalesSend.ImageUrl = afterSales.ImageUrl
	AfterSalesSend.BarterSkuID = afterSales.BarterSkuID
	AfterSalesSend.BarterSkuTitle = afterSales.BarterSkuTitle
	AfterSalesSend.ProductID = afterSales.ProductID
	err = source.DB().Create(&AfterSalesSend).Error
	if err != nil {
		err = errors.New("发货失败" + err.Error())
		return
	}
	if afterSales.SendNum+as.Num == afterSales.OrderItem.Qty {
		afterSales.SendStatus = 1
		afterSales.Status = model.WaitUserReceiveStatus
	} else {
		afterSales.SendStatus = 2
		afterSales.Status = model.WaitshopSendStatus
	}
	afterSales.SendNum = afterSales.SendNum + as.Num
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)

	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改记录失败" + err.Error())
		return
	}

	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesReceiving, 0)
	return
}

/*
*

	退款
*/
func Refund(as model.AfterSales, adminId uint, supplierId uint) (err error, returnMsg string) {
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(as.ID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取记录失败" + err.Error())

		return
	}

	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	if afterSales.Status != model.WaitRefundStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行退款操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}

	var orderItem model3.OrderItem
	err = source.DB().Where("id = ?", afterSales.OrderItemID).First(&orderItem).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))

		return
	}

	returnMsg = "退款成功"
	if orderItem.CanRefund == 0 {
		returnMsg = "商品已主动退款,无需再次退款"
		log.Log().Error("子订单已主动退款，售后不在进行退款操作！", zap.Any("err", orderItem))
	}

	//此处缺一个调用退款方法 进行给用户打钱(当退款方式为原路返回时，线下支付不用任何操作)
	if afterSales.PayMethod == model.OriginPay && orderItem.CanRefund == 1 {
		//afterSales.Amount //分
		fmt.Println("此处为原路返回调用的方法")
		if afterSales.RefundWay == model.RefundWayPrice {
			err = refund.RefundOrderItem(afterSales.OrderItemID, afterSales.Amount, afterSales.Freight, afterSales.TechnicalServicesFee, 0)
		} else {
			err = refund.RefundOrderItem(afterSales.OrderItemID, afterSales.Amount, afterSales.Freight, afterSales.TechnicalServicesFee, afterSales.Num)
		}
		if err != nil {
			log.Log().Error("退款失败", zap.Any("err", err))
			err = errors.New("退款失败" + err.Error())
			return
		}
	}

	afterSales.Status = model.CompletedStatus
	afterSales.SuccessAt = &source.LocalTime{Time: time.Now()}
	if as.PracticalAmount == 0 {
		afterSales.PracticalAmount = afterSales.Amount
	} else {
		afterSales.PracticalAmount = as.PracticalAmount
	}
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	logAfterSales.ReturnMsg = returnMsg
	err, afterSales = GetLog(logAfterSales, afterSales)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("日志添加失败" + err.Error())
		return
	}
	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改失败" + err.Error())
		return
	}
	////修改订单子表状态
	//err = source.DB().Where("id = ?", afterSales.OrderItemID).Updates(&model3.OrderItemModel{
	//	RefundStatus: model3.RefundComplete,
	//}).Error
	//if err != nil {
	//	log.Log().Error("操作失败!", zap.Any("err", err))
	//
	//	err = errors.New("操作失败" + err.Error())
	//	return
	//}
	//修改售后状态
	_ = SaveOrderAndOrderItemRefundStatus(afterSales.OrderItemID, afterSales.OrderID, model3.RefundComplete)
	//if err != nil {
	//	return
	//}
	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesComplete, 0)
	return
}
func Close(as model.AfterSales, adminId uint, supplierId uint) (err error) {

	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(as.ID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取失败" + err.Error())
		return
	}
	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	//后台关闭售后无需待审核
	//if afterSales.Status != model.WaitAuditStatus {
	//	var statusName string
	//	err, statusName = model.GetStatusName(afterSales.Status)
	//	err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行关闭操作", statusName))
	//	log.Log().Error("操作失败!", zap.Any("err", err))
	//
	//	return
	//}

	if afterSales.Status == model.CompletedStatus || afterSales.Status == model.ClosedStatus {
		var statusName string
		_, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行关闭操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}

	//查询最近的一条记录之后关闭
	err, AfterSalesAuditData := GetAfterSalesAuditByAfterSalesId(afterSales.ID)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取失败" + err.Error())
		return
	}

	AfterSalesAuditData.Status = model.ClosedRefundStatus

	err = UpdateAfterSalesAudit(AfterSalesAuditData)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改失败" + err.Error())
		return
	}
	//关闭售后表记录
	afterSales.Status = model.ClosedStatus
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)

	err = UpdateAfterSales(afterSales)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改失败" + err.Error())
		return
	}

	//err = source.DB().Model(&model3.OrderItem{}).Where("id = ?", afterSales.OrderItemID).Update("refund_status", model3.NotRefund).Error

	//修改售后状态
	_ = SaveOrderAndOrderItemRefundStatus(afterSales.OrderItemID, afterSales.OrderID, model3.NotRefund)

	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesClose, 0)
	if err != nil {
		log.Log().Error("售后关闭消息推送失败", zap.Any("err", err))
		err = errors.New("售后关闭消息推送失败" + err.Error())
		return
	}
	return
}

/*
换货 用户收货
*/
func UserReceive(as model.AfterSales, userId uint, adminId uint, supplierId uint, appId uint) (err error) {
	var afterSales model.AfterSales
	if err, afterSales = GetAfterSales(as.ID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("获取记录失败" + err.Error())

		return
	}
	if userId != 0 {
		if afterSales.UserID != userId {
			err = errors.New(fmt.Sprintf("该售后申请（%d）不属于当前用户", afterSales.ID))
			log.Log().Error("操作失败!", zap.Any("err", err))
			return
		}
	}
	if supplierId != 0 {
		var orderData Order
		err = source.DB().Where("id = ?", afterSales.OrderID).First(&orderData).Error
		if err != nil {
			err = errors.New("订单不存在" + err.Error())
			return
		}
		if orderData.SupplierID != supplierId {
			err = errors.New("订单不属于这个供应商")
			return
		}
	}
	if appId != 0 {
		if afterSales.Order.ApplicationID != appId {
			log.Log().Error("获取失败,不存在这个售后或者不属于这个采购端", zap.Any("err", err))
			err = errors.New("获取失败,不存在这个售后或者不属于这个采购端")
			return
		}
	}
	if afterSales.Status != model.WaitUserReceiveStatus {
		var statusName string
		err, statusName = model.GetStatusName(afterSales.Status)
		err = errors.New(fmt.Sprintf("%s状态的售后申请，无法执行收货操作", statusName))
		log.Log().Error("操作失败!", zap.Any("err", err))
		return
	}

	afterSales.Status = model.CompletedStatus
	afterSales.SuccessAt = &source.LocalTime{Time: time.Now()}
	var logAfterSales request.LogAfterSales
	logAfterSales.AdminId = adminId
	err, afterSales = GetLog(logAfterSales, afterSales)
	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("日志添加失败" + err.Error())
		return
	}
	err = UpdateAfterSales(afterSales)

	if err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		err = errors.New("修改失败" + err.Error())
		return
	}
	//err = refund.RefundOrderItem(afterSales.OrderItemID, 0, 0, 0, afterSales.BarterNum)
	//if err != nil {
	//	log.Log().Error("换货更新订单信息失败", zap.Any("err", err))
	//	err = errors.New("换货更新订单信息失败" + err.Error())
	//	return
	//}
	//修改订单子表状态
	//err = source.DB().Where("id = ?", afterSales.OrderItemID).Updates(&model3.OrderItemModel{
	//	RefundStatus: model3.RefundComplete,
	//}).Error
	//if err != nil {
	//	log.Log().Error("操作失败!", zap.Any("err", err))
	//
	//	err = errors.New("操作失败" + err.Error())
	//	return
	//}
	//修改售后状态
	_ = SaveOrderAndOrderItemRefundStatus(afterSales.OrderItemID, afterSales.OrderID, model3.RefundComplete)
	//if err != nil {
	//	return
	//}
	err = mq.PublishMessage(afterSales.ID, mq.AfterSalesComplete, 0)
	return
}

type Supplier struct {
	source.Model
	Name              string     `json:"name" form:"name" gorm:"column:name;comment:供应商名称;type:varchar(255);size:255;"`
	Uid               uint       `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;type:int;size:11;"`
	Realname          string     `json:"realname" form:"realname" gorm:"column:realname;comment:姓名;type:varchar(255);size:255;"`
	Mobile            string     `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
	CategoryId        int        `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;type:int;size:11;"`
	GoodsCount        int        `json:"goods_count" form:"goods_count" gorm:"column:goods_count;comment:商品数量;type:int;size:11;"`
	OrderPriceTotal   uint       `json:"order_price_total" form:"order_price_total" gorm:"column:order_price_total;comment:订单总额;type:int(20);"`
	Status            int        `json:"status"`
	DeductionRatio    *int       `json:"deduction_ratio"`
	DeductionType     int        `json:"deduction_type"`
	SelltType         *int       `json:"sellt_type" gorm:"default:0"`
	NeedVerify        int        `json:"need_verify"`
	UserId            uint       `json:"user_id" gorm:"<-:create"` // 允许读和创建
	User              model.User `json:"user" gorm:"foreignKey:Uid"`
	SettlementBalance uint       `json:"settlement_balance" gorm:"<-:false"`
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetSupplierByUserId
//@description: 根据UserId获取Supplier记录
//@param: id uint
//@return: err error, supplier model.SaveSupplier

func GetSupplierByUserId(id uint) (err error, supplier Supplier) {
	err = source.DB().Where("user_id = ?", id).First(&supplier).Error

	//var supplyID int
	//tableName := "suppliers"
	//err = source.DB().Table(tableName).Where("user_id = ?", id).Pluck("id", &supplyID).Error
	var sid int
	if supplier.ID <= 0 {
		err = source.DB().Table("supplier_clerks").Where("user_id = ?", id).Pluck("sid", &sid).Error
		if sid <= 0 {
			return
		}
		err = source.DB().Where("id = ?", sid).First(&supplier).Error
	}
	return
}

/*
*

		获取当前订单可使用的售后方式
	    types 0 代表获取支持的售后方式包含上游不支持本地勾选了支持的 （设置在订单-交易设置-上游禁售后数据仍同步）
	    types 1 仅返回上游支持的
*/
func GetAfterSalesTypeNameMap(orderData model3.Order, gatherSupply GatherSupply, orderItemID uint, types int) (err error, data interface{}) {

	_, setting := tradeModel.GetTradeSetting()
	//获取第三方售后方式 目前只会获取中台供应链
	if gatherSupply.ID != 0 {
		op := OperationAfterSalesType{
			Order:        orderData,
			OrderItemID:  orderItemID,
			GatherSupply: gatherSupply,
		}
		//第三方获取到直接返回  以后如果有其他第三方需要需要返回中台需要的售后格式
		for _, handler := range GetAfterSalesTypeHandlers() {
			err, data = handler(op)
			if err != nil {
				return
			}
		}
		//如果data=nil 代表这个供应链没有单独的获取售后方式使用公共的
		//type = 1直接返回第三方返回的售后方式
		if types == 1 && data != nil {
			return
		}
		//types=0代表获取可以同步到本地的售后方式
		if types == 0 && data != nil {
			var isBarterBool bool
			//var isReturnBool bool
			//var isRefundBool bool

			var ok bool
			var afterSalesTypeNameMap map[string]interface{}
			afterSalesTypeNameMap, ok = data.(map[string]interface{})
			if !ok {
				err = errors.New("售后类型映射格式错误")
				return
			}
			//因为map[int]转换会报错所以只能用string 保障返回给前端的依旧是int 这里重组
			//如果没有换货这里判断本地是否开启如果开启则返回换货
			_, isBarterBool = afterSalesTypeNameMap[strconv.Itoa(int(model.Barter))]
			//如果支持换货直接返回  因为目前只可以设置换货本地支持
			if isBarterBool == true {
				return
			}
			//_, isReturnBool = afterSalesTypeNameMap[strconv.Itoa(int(model.Return))]
			//_, isRefundBool = afterSalesTypeNameMap[strconv.Itoa(int(model.Refund))]
			//if isRefundBool {
			//	afterSalesTypeString[model.Refund] = "退款"
			//}
			//if isReturnBool {
			//	afterSalesTypeString[model.Return] = "退款退货"
			//}
			if isBarter(orderData, gatherSupply, setting) == true {
				afterSalesTypeNameMap["2"] = "换货"
			}
			data = afterSalesTypeNameMap
			return
		}
	}

	//第三方没有API 可在这里进行单独处理
	var afterSalesType []model.AfterSalesType
	//是否可以退货退款
	if isReturn(orderData, gatherSupply) == true {
		afterSalesType = append(afterSalesType, model.Return)
	}
	//是否可以仅退款
	if isRefund(orderData, gatherSupply) == true {
		afterSalesType = append(afterSalesType, model.Refund)
	}
	//平台订单可换货
	if isBarter(orderData, gatherSupply, setting) == true {
		afterSalesType = append(afterSalesType, model.Barter)
	}
	var afterSalesTypeString = make(map[string]interface{})
	for _, value := range afterSalesType {
		_, name := model.GetAfterSalesTypeName(value)
		afterSalesTypeString[strconv.Itoa(int(value))] = name
	}
	data = afterSalesTypeString

	return
}

// orderItemID 不一定有
func GetAfterSalesType(orderId uint, orderItemID uint) (err error, data interface{}) {
	var orderData model3.Order
	err = source.DB().Where("id = ?", orderId).Preload("OrderItems").Find(&orderData).Error
	if err != nil {
		err = errors.New("订单不存在")
		return
	}

	var gatherSupply GatherSupply
	if orderData.GatherSupplyID != 0 {
		err = source.DB().First(&gatherSupply, orderData.GatherSupplyID).Error
		if err != nil {
			err = errors.New("供应链信息错误" + err.Error())
			return
		}
	}
	err, data = GetAfterSalesTypeNameMap(orderData, gatherSupply, orderItemID, 0)
	return
}

/*
*

	是否可以退货退款
*/
func isReturn(orderData model3.Order, gatherSupply GatherSupply) (isBarter bool) {
	if orderData.ID == 0 {
		return false
	}
	return true
}

/*
*

	是否可以仅退款
*/
func isRefund(orderData model3.Order, gatherSupply GatherSupply) (isBarter bool) {
	if orderData.ID == 0 {
		return false
	}
	if gatherSupply.CategoryID == common.SUPPLY_JD_VOP {
		return false
	}
	return true
}

/*
*

	是否可以换货
*/
func isBarter(orderData model3.Order, gatherSupply GatherSupply, setting tradeModel.TradeValue) (isBarter bool) {
	if orderData.ID == 0 {
		return false
	}
	if orderData.Status == model3.WaitSend {
		return false
	}

	for _, value := range setting.AfterSalesSyn {
		if value == 2 {
			return true
		}
	}
	if orderData.GatherSupplyID != 0 {
		if gatherSupply.CategoryID == common.SUPPLY_MAIGER {
			return true
		}
		if gatherSupply.CategoryID != common.SUPPLY_SHAMA && gatherSupply.CategoryID != common.SUPPLY_YIYATONG && gatherSupply.CategoryID != common.SUPPLY_YOUXUAN {
			return false
		}

	}
	return true
}

/*
*
换货自动完成（默认7天）
*/
func CronBarterSuccess() {
	var afterSales []model.AfterSales
	currentTime := time.Now().Unix() - (7 * 24 * 60 * 60)

	err := source.DB().Where("status = ?", model.WaitUserReceiveStatus).Where("updated_at < ?", time.Unix(currentTime, 0).Format("2006-01-02 15:04:05")).Find(&afterSales).Error
	//代表没有需要完成的换货
	if err != nil {
		return
	}
	for _, item := range afterSales {
		_ = UserReceive(item, 0, 1, 0, 0) //收货
	}
	//同时清除一个月之前的售后消息记录
	ClearAfterSalesMessages()
}

// 清除一个月之前的商品同步记录日志
func ClearAfterSalesMessages() {
	// 获取当前时间
	now := time.Now()
	var batchDeleteSize = 20000 //删除数量

	// 获取一个月之前的时间
	oneMonthAgo := now.AddDate(0, -1, 0) //删除一个月之前的数据

	DeleteAfterSalesMessages(batchDeleteSize, oneMonthAgo)
}
func DeleteAfterSalesMessages(batchDeleteSize int, oneMonthAgo time.Time) {
	res := source.DB().Where("created_at <= ?", oneMonthAgo).Limit(batchDeleteSize).Delete(&model.AfterSalesPushMessage{})
	if res.RowsAffected > 0 {
		DeleteAfterSalesMessages(batchDeleteSize, oneMonthAgo)
	}
	return
}

/*
获取正在售后的数量
*/
func GetAfterSalesCount(supplierId uint) (total int64) {
	var notStatus = "4,-1"
	db := source.DB().Model(&model.AfterSales{}).Joins("INNER join orders on after_sales.order_id = orders.id").Joins("INNER join after_sales_audits on after_sales.id = after_sales_audits.after_sales_id and after_sales_audits.status != -1")

	if supplierId != 0 {
		db.Where("orders.supplier_id = ?", supplierId)
	}
	err := db.Where("after_sales.status not in (" + notStatus + ")").Group("after_sales.id").Count(&total).Error
	if err != nil {
		total = 0
	}

	return
}
