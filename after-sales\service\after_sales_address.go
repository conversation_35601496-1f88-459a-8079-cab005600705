package service

import (
	"after-sales/model"
	"after-sales/request"
	yzRequest "yz-go/request"
	"yz-go/source"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateAfterSalesAddress
//@description: 创建AfterSalesAddress记录
//@param: afterSales model.AfterSalesAddress
//@return: err error

func CreateAfterSalesAddress(afterSales model.AfterSalesAddress) (err error) {
	err = source.DB().Create(&afterSales).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteAfterSalesAddress
//@description: 删除AfterSalesAddress记录
//@param: afterSales model.AfterSalesAddress
//@return: err error

func DeleteAfterSalesAddress(afterSales model.AfterSalesAddress) (err error) {
	err = source.DB().Delete(&afterSales).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteAfterSalesAddressByIds
//@description: 批量删除AfterSalesAddress记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteAfterSalesAddressByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.AfterSalesAddress{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateAfterSalesAddress
//@description: 更新AfterSalesAddress记录
//@param: afterSales *model.AfterSalesAddress
//@return: err error

func UpdateAfterSalesAddress(afterSales model.AfterSalesAddress) (err error) {
	err = source.DB().Save(&afterSales).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetAfterSalesAddress
//@description: 根据id获取AfterSalesAddress记录
//@param: id uint
//@return: err error, afterSales model.AfterSalesAddress

func GetAfterSalesAddress(id uint) (err error, afterSales model.AfterSalesAddress) {
	err = source.DB().Preload("AfterSalesAddressPaySort").Where("id = ?", id).First(&afterSales).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetAfterSalesAddressInfoList
//@description: 分页获取AfterSalesAddress记录
//@param: info request.AfterSalesAddressSearch
//@return: err error, list interface{}, total int64

func GetAfterSalesAddressInfoList(info request.AfterSalesAddressSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&model.AfterSalesAddress{})
	var afterSaless []model.AfterSalesAddress
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&afterSaless).Error
	return err, afterSaless, total
}

