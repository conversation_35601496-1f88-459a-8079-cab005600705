package cron

import (
	"aggregated-payment-split-settlement/service"
	"yz-go/cron"
)

// 同步绑定解绑审核状态
func SynSubMerBindSplitSettlementStatusCron() {
	task := cron.Task{
		Key:  "SynSubMerBindSplitSettlementStatusCron",
		Name: "同步绑定解绑审核状态",
		Spec: "0 0/10 * * * *",
		Handle: func(task cron.Task) {
			_ = service.SynSubMerBindSplitSettlementStatusCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}
