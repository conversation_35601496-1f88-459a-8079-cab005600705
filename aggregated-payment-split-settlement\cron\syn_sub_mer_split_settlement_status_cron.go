package cron

import (
	"aggregated-payment-split-settlement/service"
	"yz-go/cron"
)

// 同步分账申请，修改状态 审核成功同步子账户详情
func SynSubMerSplitSettlementStatusCron() {

	task := cron.Task{
		Key:  "synSubMerSplitSettlementStatusCron",
		Name: "同步分账申请，修改状态 审核成功同步子账户详情",
		Spec: "0 0/10 * * * *",
		Handle: func(task cron.Task) {
			_ = service.SynSubMerSplitSettlementStatusCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}
