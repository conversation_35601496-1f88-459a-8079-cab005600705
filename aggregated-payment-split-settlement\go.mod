module aggregated-payment-split-settlement

go 1.21

require (
	application v1.0.0
	gin-vue-admin v1.0.0
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/gin-gonic/gin v1.6.3
	github.com/shopspring/decimal v1.3.1
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	order v1.0.0
	user v1.0.0
	yz-go v1.0.0
)

replace (
	after-sales => ../after-sales
	application => ../application
	category v1.0.0 => ../category
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product v1.0.0 => ../product
	public-supply => ../public-supply
	purchase-account => ../purchase-account
	region v1.0.0 => ../region
	sales => ../sales
	shipping => ../shipping
	shop => ../shop
	shopping-cart => ../shopping-cart
	trade v1.0.0 => ../trade
	user v1.0.0 => ../user
	wechatpay-go-main => ../wechatpay-go-main
	yz-go v1.0.0 => ../yz-go

)
