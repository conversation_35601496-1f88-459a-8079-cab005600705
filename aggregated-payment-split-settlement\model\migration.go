package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate()

	// 菜单,权限
	menus := []model.SysMenu{}
	//sysBaseMenu := []model.SysBaseMenu{}
	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)

	if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(48) == true || utils.LocalEnv() != true {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}
	return
}
