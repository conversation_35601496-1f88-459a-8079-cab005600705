package v1

import (
	"ai-assistant/model"
	"ai-assistant/service"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	yzResponse "yz-go/response"
)

func UpdateAiSetting(c *gin.Context) {
	var sysSetting model.AiSetting
	err := c.Should<PERSON>ind<PERSON>(&sysSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	fmt.Println(sysSetting.Value)
	sysSetting.Key = "ai_setting"
	err = service.SaveAiSetting(sysSetting)

	if err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	model.ResetAi()
	yzResponse.OkWithMessage("修改成功", c)

}

func FindAiSetting(c *gin.Context) {
	err, tradeSetting := service.ShowAiSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": tradeSetting}, c)

}
