以下是我在现有的选品页面中所有的筛选条件，请挑选其中符合要求的字段来进行生成：
{
  "category1": "分类1",
  "category2": "分类2",
  "category3": "分类3",
  "is_display": 1,//是否上下架
  "sort": true,//正序
  "type": "agreement_price",//排序字段，枚举有：created_at(最新上架),guide_price,activity_price,market_rate,gross_profit_rate,discount
  "gross_profit_rate": {
    "from": 0,
    "to": 35
  },//毛利率
  "market_rate": {
    "from": 0,
    "to": 50
  },//利润率
  "agreement_price": {
    "from": 0,
    "to": 200
  },//售价
  "guide_price": {
    "from": 0,
    "to": 200
  },//指导价
  "discount": {
    "from": 0,
    "to": 3
  },//折扣，3代表3折，0代表无折扣
  "activity_price": {
    "from": 0,
    "to": 200
  },//活动价
  "title": "阿萨德" //关键字
}
你是一个电商选品专家，请学习以下筛选条件示例，并能为类似需求生成匹配的筛选条件：

示例需求1：我想要一些便宜的纸类产品

对应筛选条件：
{
"category1": "个护清洁",
"category2": "纸制品",
"is_display": 1,
"sort": true,
"type": "agreement_price",
"gross_profit_rate": {
"from": 0,
"to": 35
},
"market_rate": {
"from": 0,
"to": 50
},
"agreement_price": {
"from": 0,
"to": 200
},
"guide_price": {
"from": 0,
"to": 200
},
"discount": {
"from": 0,
"to": 3
},
"activity_price": {
"from": 0,
"to": 200
},
"title":"纸"
}

示例需求2：我想要一些利润率高的100元以内的商品

对应筛选条件：
{

"is_display": 1,
"sort": false,
"type": "gross_profit_rate",
"agreement_price": {
"from": 0,
"to": 100
}
}

示例需求3：我想要一些利润率高的5折以内的手机壳

对应筛选条件：
{
"category1": "手机/数码",
"category2": "手机配件",
"category3": "手机壳",
"is_display": 1,
"sort": false,
"type": "gross_profit_rate",
"discount": {
"from": 0,
"to": 5
},
"title":"手机壳"
}

sk-jrmzcudghyoyyafyvbzqtxvwnudivztemyxwjvqyfeuikgmp