package component

import (
	"ai-assistant/model"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"
	"yz-go/source"
)

type SiliconFlow struct{}

type SiliconFlowResponse struct {
	Id      string `json:"id"`
	Object  string `json:"object"`
	Created int    `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	SystemFingerprint string `json:"system_fingerprint"`
}

func (s *SiliconFlow) Chat(message string) (map[string]interface{}, error) {
	const (
		apiURL = "https://api.siliconflow.cn/v1/chat/completions"
	)
	err, apiKey := GetSiliconFlowApiKey()
	if err != nil {
		return nil, err
	}

	requestBody := map[string]interface{}{
		"model":             "Qwen/Qwen2.5-Coder-7B-Instruct",
		"stream":            false,
		"max_tokens":        512,
		"temperature":       0.7,
		"top_p":             0.7,
		"top_k":             50,
		"frequency_penalty": 0.5,
		"n":                 1,
		"stop":              []string{},
		"messages": []map[string]string{
			{
				"role":    "system",
				"content": getSystemPrompt(),
			},
			{
				"role":    "user",
				"content": message,
			},
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("marshal request body failed: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 20 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned non-200 status: %d", resp.StatusCode)
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed: %w", err)
	}

	var result SiliconFlowResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %w", err)
	}

	if len(result.Choices) == 0 {
		return nil, errors.New("no choices in response")
	}

	return ExtractJSONFromText(result.Choices[0].Message.Content)
}

// ExtractJSONFromText 从文本中提取JSON内容
func ExtractJSONFromText(text string) (map[string]interface{}, error) {
	// 使用正则表达式匹配```json格式
	re := regexp.MustCompile(`(?s)\x60{3}json\n(.*?)\n\x60{3}`)
	matches := re.FindStringSubmatch(text)

	var jsonStr string
	if len(matches) >= 2 {
		jsonStr = matches[1]
	} else {
		// 如果没有匹配到```json标记，尝试直接判断text本身是否为合法JSON
		textTrim := strings.TrimSpace(text)
		if strings.HasPrefix(textTrim, "{") && strings.HasSuffix(textTrim, "}") {
			jsonStr = textTrim
		} else {
			// 尝试匹配文本中的JSON对象
			re = regexp.MustCompile(`(?s)\{.*\}`)
			matches = re.FindStringSubmatch(text)
			if len(matches) > 0 {
				jsonStr = matches[0]
			} else {
				return nil, fmt.Errorf(text)
			}
		}
	}

	// 解析JSON到map
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &result); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	return result, nil
}

type Category struct {
	source.Model
	Name         string `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"` //分类名称
	Level        int    `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`         //等级
	HasChildrens bool   `json:"hasChildrens"`
	ParentID     uint   `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"` //父级id
}

// getSystemPrompt 返回系统提示信息
// 添加新的函数来获取分类数据
// 移除 CategoryTree 结构体，改用字符串数组存储完整路径

func getCategoryData() (string, error) {
	var categories []Category
	err := source.DB().Where("is_plugin = 0 and is_display = 1 and level = 1 ").Find(&categories).Error
	if err != nil {
		return "", fmt.Errorf("获取分类数据失败: %v", err)
	}

	// 构建分类路径
	categoryPaths := buildCategoryPaths(categories)

	// 转换为JSON
	categoriesJSON, err := json.Marshal(categoryPaths)
	if err != nil {
		return "", fmt.Errorf("转换分类数据失败: %v", err)
	}

	return string(categoriesJSON), nil
}

func buildCategoryPaths(categories []Category) []string {
	// 创建分类映射，方便查找父级
	categoryMap := make(map[uint]Category)
	for _, category := range categories {
		categoryMap[category.ID] = category
	}

	// 存储所有完整分类路径
	var paths []string

	// 遍历所有分类
	for _, category := range categories {
		if !category.HasChildrens { // 只处理叶子节点
			path := []string{category.Name}
			currentID := category.ParentID

			// 向上查找父级分类
			for currentID != 0 {
				if parent, exists := categoryMap[currentID]; exists {
					path = append([]string{parent.Name}, path...)
					currentID = parent.ParentID
				} else {
					break
				}
			}

			// 将路径数组转换为字符串
			fullPath := strings.Join(path, ",")
			paths = append(paths, fullPath)
		}
	}

	return paths
}

// 构建分类树的辅助结构
// 修改 CategoryTree 结构
type CategoryTree struct {
	N string         `json:"n"`           // Name 缩写为 n
	C []CategoryTree `json:"c,omitempty"` // Children 缩写为 c
}

// 修改 buildCategoryTree 和 getChildren 函数中的相应字段
func buildCategoryTree(categories []Category) []CategoryTree {
	// 创建根节点map
	rootNodes := make([]CategoryTree, 0)
	categoryMap := make(map[uint][]Category)

	// 按父ID分组
	for _, category := range categories {
		categoryMap[category.ParentID] = append(categoryMap[category.ParentID], category)
	}

	// 获取一级分类（ParentID = 0）
	if level1Categories, ok := categoryMap[0]; ok {
		for _, category := range level1Categories {
			node := CategoryTree{
				N: category.Name,
				C: getChildren(category.ID, categoryMap),
			}
			rootNodes = append(rootNodes, node)
		}
	}
	return rootNodes
}

func getChildren(parentID uint, categoryMap map[uint][]Category) []CategoryTree {
	children := make([]CategoryTree, 0)
	if subCategories, ok := categoryMap[parentID]; ok {
		for _, category := range subCategories {
			node := CategoryTree{
				N: category.Name,
				C: getChildren(category.ID, categoryMap),
			}
			children = append(children, node)
		}
	}
	return children
}

// 修改 getSystemPrompt 函数
func getSystemPrompt() string {
	categories, err := getCategoryData()
	if err != nil {
		// 如果获取分类失败，使用默认提示词
		categories = "[]"
	}

	return fmt.Sprintf(`以下是系统支持的所有商品分类：
%s

以下是选品页面中所有的筛选条件，请根据上述分类和以下条件进行选品推荐：
{
  "category1": "分类1",
  "category2": "分类2",
  "category3": "分类3",
  "is_display": 1,//是否上下架
  "sort": true,//正序
  "type": "agreement_price",//排序字段，枚举有：created_at(最新上架),guide_price,activity_price,market_rate,gross_profit_rate,discount
  "gross_profit_rate": {
    "from": 0,
    "to": 35
  },//毛利率
  "market_rate": {
    "from": 0,
    "to": 50
  },//利润率
  "agreement_price": {
    "from": 0,
    "to": 200
  },//售价
  "guide_price": {
    "from": 0,
    "to": 200
  },//指导价
  "discount": {
    "from": 0,
    "to": 3
  },//折扣，3代表3折，0代表无折扣
  "activity_price": {
    "from": 0,
    "to": 200
  },//活动价
  "title": "阿萨德" //关键字
}
你是一个电商选品专家，请学习以下筛选条件示例，并能为类似需求生成匹配的筛选条件：

示例需求1：我想要一些便宜的纸类产品

对应筛选条件：
{
"category1": "个护清洁",
"category2": "纸制品",
"is_display": 1,
"sort": true,
"type": "agreement_price",
"gross_profit_rate": {
"from": 0,
"to": 35
},
"market_rate": {
"from": 0,
"to": 50
},
"agreement_price": {
"from": 0,
"to": 200
},
"guide_price": {
"from": 0,
"to": 200
},
"discount": {
"from": 0,
"to": 3
},
"activity_price": {
"from": 0,
"to": 200
},
"title":"纸"
}

示例需求2：我想要一些利润率高的100元以内的商品

对应筛选条件：
{

"is_display": 1,
"sort": false,
"type": "gross_profit_rate",
"agreement_price": {
"from": 0,
"to": 100
}
}

示例需求3：我想要一些利润率高的5折以内的手机壳

对应筛选条件：
{
"category1": "手机/数码",
"category2": "手机配件",
"category3": "手机壳",
"is_display": 1,
"sort": false,
"type": "gross_profit_rate",
"discount": {
"from": 0,
"to": 5
},
"title":"手机壳"
}
`, categories)
}

func GetSiliconFlowApiKey() (error, string) {
	var aiSetting model.AiSetting
	err := source.DB().Where("`key` = ?", "ai_setting").First(&aiSetting).Error
	if err != nil {
		return err, ""
	}

	if aiSetting.Value.SiliconFlowAppKey == "" {
		return errors.New("silicon flow app key not configured"), ""
	}

	return nil, aiSetting.Value.SiliconFlowAppKey
}
