package service

import (
	"ai-assistant/model"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
	"yz-go/utils"
)

// 基础设置敏感信息掩码字段

func AiSettingMaskFields() []string {
	return []string{"silicon_flow_app_key"}
}

func ShowAiSetting() (err error, setting model.AiSetting) {
	if err, setting = GetAiSetting(); err != nil {
		return
	}
	return
}

type UpdateMask struct {
	Field string `json:"field"`
	Value string `json:"value"`
}

func UpdateAiSettingMask(p UpdateMask) (err error) {
	// 验证提交字段是否在掩码字段中
	if !utils.IsStringInSlice(p.Field, AiSettingMaskFields()) {
		err = errors.New("字段验证错误，请重试")
		return
	}

	// 查询设置
	var sysSetting model.AiSetting
	if err, sysSetting = GetAiSetting(); err != nil {
		return
	}

	var valueByte []byte
	if valueByte, err = json.Marshal(sysSetting.Value); err != nil {
		return
	}

	// 解析新 byteValue 为 map
	var valueMap map[string]interface{}
	if err = json.Unmarshal(valueByte, &valueMap); err != nil {
		return
	}

	// 根据字段名设置字段的值
	for k, _ := range valueMap {
		if k == p.Field {
			valueMap[k] = p.Value
		}
	}

	// 将处理后的 valueMap 重新序列化为 JSON
	if valueByte, err = json.Marshal(valueMap); err != nil {
		return
	}

	var settingValue model.AiValue
	if err = json.Unmarshal(valueByte, &settingValue); err != nil {
		return
	}

	sysSetting.Value = settingValue

	if err = source.DB().Omit("created_at").Save(sysSetting).Error; err != nil {
		return
	}

	return
}

func GetAiSetting() (err error, sysSetting model.AiSetting) {

	err = source.DB().Where("`key` = ?", "ai_setting").First(&sysSetting).Error

	return
}

func SaveAiSetting(data model.AiSetting) (err error) {
	var sysSetting model.AiSetting
	if err, sysSetting = GetAiSetting(); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	err = nil

	// 更新时，保留敏感字段原值
	if sysSetting.ID > 0 {
		sysSetting.Value = data.Value
		if err = source.DB().Omit("created_at").Save(&sysSetting).Error; err != nil {
			return
		}
	} else {
		if err = source.DB().Create(&data).Error; err != nil {
			return
		}
	}

	return
}
