package v1

import (
	"ali-open/listener"
	"ali-open/model"
	"ali-open/request"
	"ali-open/service"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/url"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func CallBack(c *gin.Context) {
	var reqData model.CallBackData
	bytes, _ := c.GetRawData()
	urlStr := "http://yx.cn/?" + string(bytes)
	log.Log().Info("阿里回调消息接受", zap.Any("info", urlStr))
	myUrl, _ := url.Parse(urlStr)
	params, _ := url.ParseQuery(myUrl.RawQuery)
	message := params["message"]
	if len(message) == 0 {
		log.Log().Info("阿里aliopen回调消息接受1")
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	err := json.Unmarshal([]byte(message[0]), &reqData)
	if err != nil {
		log.Log().Info("阿里aliopen回调消息接受2", zap.Any("info", err))
		yzResponse.FailWithMessage("参数解析错误", c)
		return
	}
	log.Log().Info("aliopen 消息", zap.Any("info", reqData))
	if reqData.Type == "ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS" {
		err = service.CallBackOrderService(reqData)
	} else if "PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE" == reqData.Type || "PRODUCT_RELATION_VIEW_PRODUCT_REPOST" == reqData.Type || "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY" == reqData.Type {
		err = service.CallBackService(reqData)
	}

	//err = mq.PublishMessage(1, reqData, 1)
	if err != nil {
		log.Log().Error("阿里aliopen回调消息mq err", zap.Any("info", err))
		yzResponse.FailWithMessage("处理错误", c)

		return
	}

	yzResponse.Ok(c)

}

//func CallBackOrder(c *gin.Context) {
//	var reqData model.CallBackOrderData
//	bytes, _ := c.GetRawData()
//	urlStr := "http://yx.cn/?" + string(bytes)
//	log.Log().Info("阿里订单回调发货", zap.Any("info", urlStr))
//	myUrl, _ := url.Parse(urlStr)
//	params, _ := url.ParseQuery(myUrl.RawQuery)
//	message := params["message"]
//	if len(message) == 0 {
//		yzResponse.FailWithMessage("参数错误", c)
//		return
//	}
//
//	err := json.Unmarshal([]byte(message[0]), &reqData)
//	if err != nil {
//		yzResponse.FailWithMessage("参数解析错误", c)
//		return
//	}
//	//if reqData.Data.OrderId == "" {
//	//	yzResponse.FailWithMessage("参数错误", c)
//	//	return
//	//}
//	if err != nil {
//		log.Log().Error("解析json失败", zap.Any("err", err))
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	if reqData.Type == "ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS" {
//
//	}
//
//	if err = service.CallBackOrderService(reqData); err != nil {
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	} else {
//		yzResponse.Ok(c)
//	}
//
//}

// @Tags GetSupplier
// @Summary 阿里巴巴获取供应商
// @Security
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /aliOpen/GetSupplier [put]
func GetSupplier(c *gin.Context) {

	var ali service.Alibb
	var data request.SupplierDomain
	err := c.ShouldBindJSON(&data)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	ali.ShopID = data.ShopID

	ali.Init()

	if err = ali.GetShop(data.Domain); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func SetSetting(c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SetSetting(setting); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("设置成功", c)
	}
}

func SetSupplierSetting(c *gin.Context) {
	var setting model.CompanyName
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SetSupplierSetting(setting); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("设置成功", c)
	}
}

type ReqData struct {
	ID uint `json:"id"`
}

func GetSetting(c *gin.Context) {

	var reqData request.ShopData

	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.GetSetting(reqData.ShopID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func GetSettingList(c *gin.Context) {

	if err, data := service.GetSettingList(); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

// @Tags GetProduct
// @Summary 用id查询alibb商品
// @Security GetProduct
// @accept application/json
// @Produce application/json
// @Param data body model.product true "用id查询alibb商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /aliOpen/GetProduct [get]
func GetProduct(c *gin.Context) {
	var id request.ID
	err := c.ShouldBindJSON(&id)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var alibb service.Alibb
	alibb.ShopID = id.ShopID
	alibb.Init()
	if err, product := alibb.GetProduct(id.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(product, c)
	}
}

func BindSupplier(c *gin.Context) {
	var bind request.BindSupplier
	err := c.ShouldBindJSON(&bind)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.BindSupplier(bind); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func CreateDomain(c *gin.Context) {
	var domain request.SupplierDomain
	err := c.ShouldBindJSON(&domain)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.CreateDomain(domain); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func DeleteDomain(c *gin.Context) {
	var id request.ID
	err := c.ShouldBindJSON(&id)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.DeleteDomain(id); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func BindProduct(c *gin.Context) {
	var param request.BindProduct
	err := c.ShouldBindJSON(&param)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.BindProduct(param); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}
func CancelBindProduct(c *gin.Context) {
	var param request.BindProduct
	err := c.ShouldBindJSON(&param)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.CancelBindProduct(param.ProductID, param.SkuID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func DeleteBindProduct(c *gin.Context) {
	var param request.BindProduct
	err := c.ShouldBindJSON(&param)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.DeleteBindProduct(param); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

type RequestKey struct {
	Key string `json:"key" form:"key" query:"key"`
}

func GetSupplierList(c *gin.Context) {

	var key RequestKey
	err := c.ShouldBindQuery(&key)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.GetSupplierList(key.Key); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func ManualAlibbOrder(c *gin.Context) {

	var param request.OrderID
	err := c.ShouldBindJSON(&param)
	if err != nil {
		log.Log().Error("解析参数失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = listener.ManualAlibbOrder(param.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}

}

func SetToken(c *gin.Context) {
	var code request.Code
	err := c.ShouldBindJSON(&code)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var ali service.Alibb
	ali.ShopID = "aliOpenSetting" + code.ShopID
	ali.Init()
	if err := ali.CodeGetToken(code.Code); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}
