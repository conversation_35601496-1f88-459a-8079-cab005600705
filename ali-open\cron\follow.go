package cron

import (
	"ali-open/model"
	request2 "ali-open/request"
	"ali-open/service"
	"encoding/json"
	"go.uber.org/zap"
	url2 "net/url"
	model3 "public-supply/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

var dataSetting model3.SupplySetting

func PushAlibbFollowHandle() {
	task := cron.Task{
		Key:  "alibbFollowcheck",
		Name: "alibb关注检测定时任务",
		Spec: "0 0 13 * * ?",
		Handle: func(task cron.Task) {
			Follow()
		},
		Status: cron.ENABLED,
	}

	cron.PushTask(task)

}

var setting model.Setting

func Follow() {
	var AliProduct []model.AliProduct
	err := source.DB().Where("follow!=?", 1).Find(&AliProduct).Error
	if err != nil {
		log.Log().Error("ali-oprn Follow err", zap.Any("err", err))
		return
	}

	if len(AliProduct) == 0 {
		return
	}

	for _, item := range AliProduct {
		err, sysSetting := service.GetSetting(item.ShopID)
		if err != nil {
			log.Log().Error("GetSetting err", zap.Any("err", err))
			return
		}
		err = json.Unmarshal([]byte(sysSetting.Value), &setting)
		if err != nil {
			log.Log().Error("Unmarshal err", zap.Any("err", err))

			return
		}

		id := strconv.Itoa(int(item.AliProductID))

		err = AliFollow(id)
		if err != nil {
			log.Log().Error("AliFollow err", zap.Any("err", err))
			continue
		}

	}

}

func AliFollow(productID string) (err error) {

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.follow.crossborder/" + setting.Key
	reqData := url2.Values{}
	reqData.Add("access_token", setting.Token)
	reqData.Add("productId", productID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", service.Sign(url, setting.Secret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	log.Log().Info("info", zap.Any("阿里关注商品", string(resData)), zap.Any("商品id", productID))

	var ResOrderData request2.Follow

	json.Unmarshal(resData, &ResOrderData)
	if ResOrderData.Message != "success" {
		log.Log().Error("err", zap.Any("阿里关注商品失败", ResOrderData), zap.Any("商品id", productID))
	} else {
		var aliproduct model.AliProduct

		aliproduct.Follow = 1
		source.DB().Where("ali_product_id=?", productID).Updates(&aliproduct)
	}

	return

}
