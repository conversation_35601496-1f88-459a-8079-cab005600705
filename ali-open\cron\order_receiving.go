package cron

import (
	"ali-open/model"
	"ali-open/service"
	"fmt"
	"go.uber.org/zap"
	v1 "order/api/v1"
	omodel "order/model"
	orderRequest2 "order/request"
	"product/mq"
	express2 "shipping/express"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushAlibbOrderPayHandle() {
	task := cron.Task{
		Key:  "alibborderPaycheck",
		Name: "alibb支付状态检测定时任务",
		Spec: "0 * */5 * * *",
		Handle: func(task cron.Task) {
			OrderPayCron()
		},
		Status: cron.ENABLED,
	}

	cron.PushTask(task)

}
func PushAlibbProductSaleHandle() {
	task := cron.Task{
		Key:  "alibbProductSalecheck1",
		Name: "alibb上下架状态状态检测定时任务1",
		Spec: "* * 11 * * *",
		Handle: func(task cron.Task) {
			//SyncSale()
		},
		Status: cron.ENABLED,
	}

	cron.PushTask(task)

}

func OrderPayCron() {

	var aliOrder []model.AliOrder
	err := source.DB().Where("pay_status=?", 1).Where("created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)").Find(&aliOrder).Error
	if err != nil {
		log.Log().Info("alibb支付状态检测定时任务", zap.Any("info", err))
		return
	}

	for _, item := range aliOrder {
		var alibb service.Alibb
		alibb.ShopID = item.ShopID
		alibb.Init()
		orderID, _ := strconv.Atoi(item.OrderID)

		var orders omodel.Order
		err = source.DB().Where("order_sn=?", item.OrderSN).Where("status=?", 1).First(&orders).Error
		if err != nil {
			log.Log().Info("alibb支付状态检测定时任务", zap.Any("info", err))

		}
		if orders.ID > 1 {
			alibb.Pay(int64(orderID))
		}

	}

}

func PushAlibbOrderDeliverHandle() {
	task := cron.Task{
		Key:  "alibborderdelivercheck",
		Name: "alibb供应链发货查询定时任务",
		Spec: "0 * */2 * * ?",
		Handle: func(task cron.Task) {
			//OrderDeliverCron()
		},
		Status: cron.ENABLED,
	}

	cron.PushTask(task)

}

func OrderDeliverCron() {
	log.Log().Info("alibb供应链代发货订单查询OrderDeliverCron")

	var deliverOrder []omodel.Order
	err := source.DB().Preload("OrderItems").Where("status=? and gather_supply_type=?  ", 1, 7).Find(&deliverOrder).Error
	if err != nil {
		log.Log().Info("alibb供应链代发货订单查询", zap.Any("info", err))
		return
	}

	if len(deliverOrder) == 0 {
		return
	}

	for _, od := range deliverOrder {
		for _, odItem := range od.OrderItems {
			var alibb service.Alibb

			if odItem.SendStatus == 1 {
				continue
			}
			log.Log().Info("alibb查询订单sku是否发货", zap.Any("sku", odItem), zap.Any("订单sn", od.OrderSN))

			var aliProduct model.AliProduct
			source.DB().Where("product_id=? and sku_id=?", odItem.ProductID, odItem.SkuID).First(&aliProduct)

			if aliProduct.ID <= 0 {
				//log.Log().Error("AlibbOrder product当前未查出绑定数据", zap.Any("info", odItem))
				continue
			}
			var aliOrder model.AliOrder
			source.DB().Where("product_id=? and sku_id=? and order_sn=?", aliProduct.AliProductID, aliProduct.AliSkuID, od.OrderSN).First(&aliOrder)
			if aliOrder.ID <= 0 {
				log.Log().Error("AlibbOrder  订单当前未查出数据", zap.Any("info", aliProduct))
				continue
			}

			alibb.ShopID = aliProduct.ShopID
			alibb.Init()
			var CompanyName, No string
			err, CompanyName, No = alibb.AlibbOrderSelect(aliOrder.OrderID, aliProduct.AliProductID)
			if err != nil {
				log.Log().Error("AlibbOrderSelecterr当前订单信息未查询到物流", zap.Any("info", err))
				continue
			}
			var orderRequest v1.HandleOrderRequest
			var code string

			if CompanyName == "" || No == "" {
				log.Log().Error("当前订单信息未查询到物流", zap.Any("info", aliProduct))
				continue
			}

			err, code = ExpressList(CompanyName)
			if err != nil {
				log.Log().Error("alibb查询物流信息错误3", zap.Any("info", err.Error()))
				continue
			}

			if code == "" {
				log.Log().Error("alibb当前订单信息未查询到物流code", zap.Any("info", code))
				log.Log().Error("alibb当前订单信息未查询到物流code", zap.Any("info", CompanyName))
				continue
			}
			var ids = []orderRequest2.OrderItemSendInfo{{ID: odItem.ID, Num: odItem.Qty}}
			orderRequest.OrderID = od.ID
			orderRequest.ExpressNo = No
			orderRequest.OrderItemIDs = ids
			orderRequest.CompanyCode = code
			log.Log().Info("alibb发货信息", zap.Any("info", orderRequest))
			err = ExpressSent(orderRequest)
			if err != nil {
				continue
			}

		}

	}

}

// 获取快递code
func ExpressList(name string) (err error, code string) {

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

func SyncSale() (err error) {

	var aliProduct []model.AliProduct
	err = source.DB().Find(&aliProduct).Error
	if err != nil {
		return
	}
	var alibb service.Alibb

	for _, item := range aliProduct {
		alibb.ShopID = item.ShopID
		alibb.Init()
		StrId := strconv.Itoa(int(item.AliProductID))
		_, product := alibb.GetProduct(StrId)
		productId := uint(item.ProductID)
		var colum = make(map[string]interface{})
		if product.ProductInfo.Status == "published" {
			var productMessageType mq.ProductMessageType //队列消息类型
			productMessageType = mq.OnSale
			colum["is_display"] = 1

			err = mq.PublishMessage(productId, productMessageType, 0)
		} else {
			var productMessageType mq.ProductMessageType //队列消息类型
			productMessageType = mq.Undercarriage
			colum["is_display"] = 0

			err = mq.PublishMessage(productId, productMessageType, 0)

		}

		err = source.DB().Table("products").Where("id=?", productId).UpdateColumns(&colum).Error

	}

	return

}
