package listener

import (
	"ali-open/model"
	"ali-open/mq"
	"ali-open/service"
	"go.uber.org/zap"
	"yz-go/component/log"
)

func PushAliOpenCallBackHandles() {
	log.Log().Info("注册阿里aliopen callback队列监听")
	mq.PushHandles("aliOpenCallBackListener", func(reqData model.CallBackData) (err error) {

		log.Log().Info("阿里aliopen回调消息mq 接受", zap.Any("info", reqData))

		if reqData.Type == "ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS" {
			err = service.CallBackOrderService(reqData)
		} else if "PRODUCT_RELATION_VIEW_PRODUCT_EXPIRE" == reqData.Type || "PRODUCT_RELATION_VIEW_PRODUCT_REPOST" == reqData.Type || "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY" == reqData.Type {
			err = service.CallBackService(reqData)
		}

		log.Log().Info("阿里aliOpenCallBack回调消息mq 完成", zap.Any("info", err))

		return nil
	})
}
