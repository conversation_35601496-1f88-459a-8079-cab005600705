package listener

import (
	alimodel "ali-open/model"
	"ali-open/service"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"order/model"
	"order/mq"
	"public-supply/request"
	prequest "public-supply/request"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

func PushAlibbOrderHandles() {
	log.Log().Info("注册阿里巴巴订单队列监听")
	mq.PushHandles("alibbOrderPaid", func(orderMsg mq.OrderMessage) (err error) {
		if orderMsg.MessageType != mq.Paid {
			log.Log().Info("阿里巴巴订单队列监听：不是订单支付消息，不处理")
			return
		}
		log.Log().Info("阿里巴巴监听订单：", zap.Any("orderMsg", orderMsg))
		AlibbOrder(int64(orderMsg.OrderID))
		return nil
	})
}

func IsEnable(key string) bool {
	err, sysSetting := service.GetSetting(key)
	if err != nil {
		log.Log().Error("阿里巴巴监听订单，获取配置错误", zap.Any("err", err))
		return false
	}
	var mySetting alimodel.Setting
	err = json.Unmarshal([]byte(sysSetting.Value), &mySetting)
	if err != nil {
		log.Log().Error("阿里巴巴监听订单，解析配置错误", zap.Any("err", err))
		return false
	}
	log.Log().Info("阿里巴巴监听订单查看配置信息", zap.Any("err", mySetting))
	if mySetting.Enable == 1 {
		return true
	}
	return false

}

func ManualAlibbOrder(orderID int64) (err error) {
	var orderData model.Order
	err = source.DB().Preload("OrderItems").Preload("ShippingAddress").Where("id = ?", orderID).First(&orderData).Error
	if err != nil {
		return
	}
	if orderData.GatherSupplyID > 0 {
		return
	}
	if orderData.GatherSupplySN != "" {
		err = errors.New("阿里巴巴已经下单成功了，请勿重复下单")
		return
	}

	//var skuMap alimodel.SkuMap
	var OrderMap = make(map[string]alimodel.SkuMap)
	var SkuMap = make(map[string]alimodel.SkuMapSetting)
	for _, item := range orderData.OrderItems {
		var aliProduct alimodel.AliProduct
		err = source.DB().Where("product_id=? and sku_id=?", item.ProductID, item.SkuID).First(&aliProduct).Error
		//if err!=nil && errors.Is(err,gorm.ErrRecordNotFound){
		//	err=errors.New("请先绑定商品")
		//	return
		//}
		if aliProduct.ID > 0 {

			var sku alimodel.Sku

			sku.SpecId = aliProduct.AliSkuID
			sku.Quantity = int64(item.Qty)
			sku.OfferId = aliProduct.AliProductID

			//skuMap = append(skuMap, sku)

			//OrderMap[aliProduct.ShopID] = append(OrderMap[aliProduct.ShopID], sku)
			var companyNames alimodel.CompanyName
			err := source.DB().Where("shop_id=? and uid=?", aliProduct.ShopID, item.SupplierID).First(&companyNames).Error
			if err != nil {
				log.Log().Error("查询阿里商品属于哪个供应商错误", zap.Any("err", err))
				continue
			}

			mapKey := aliProduct.ShopID + "#" + companyNames.AutoPay + "#" + companyNames.OrderType + "#" + companyNames.MemberId
			log.Log().Info("查询阿里商品mapKey", zap.Any("err", mapKey))

			OrderMap[mapKey] = append(OrderMap[mapKey], sku)

			var skuSetting alimodel.SkuSetting
			skuSetting.SpecId = aliProduct.AliSkuID
			skuSetting.Quantity = int64(item.Qty)
			skuSetting.OfferId = aliProduct.AliProductID
			skuSetting.AutoPay = aliProduct.AutoPay
			SkuMap[mapKey] = append(SkuMap[mapKey], skuSetting)

		}

	}
	log.Log().Info("阿里巴巴手动下单监听订单数据集", zap.Any("orderMsg", OrderMap))
	log.Log().Info("阿里巴巴手动下单监听订单数据集", zap.Any("orderMsg1", SkuMap))

	if len(OrderMap) == 0 {
		err = errors.New("无可下单的绑定商品数据，请先绑定")
		return
	}

	for key, item := range OrderMap {
		ConfirmOrder(orderData, item, key, SkuMap[key])
	}

	return
}

func AlibbOrder(orderID int64) {
	var orderData model.Order
	source.DB().Preload("OrderItems").Preload("ShippingAddress").Where("id = ?", orderID).First(&orderData)

	if orderData.GatherSupplyID > 0 {
		return
	}
	if orderData.GatherSupplySN != "" {
		log.Log().Info("阿里巴巴监听订单，已经下单成功了：", zap.Any("orderMsg", orderData.ID))

		return
	}

	//var skuMap alimodel.SkuMap
	var OrderMap = make(map[string]alimodel.SkuMap)
	var SkuMap = make(map[string]alimodel.SkuMapSetting)

	for _, item := range orderData.OrderItems {
		var aliProduct alimodel.AliProduct
		source.DB().Where("product_id=? and sku_id=?", item.ProductID, item.SkuID).First(&aliProduct)
		if aliProduct.ID > 0 {
			if !IsEnable(aliProduct.ShopID) {
				log.Log().Info("阿里巴巴账号已关闭，跳过下单：", zap.Any("data", aliProduct))
				continue
			}

			var sku alimodel.Sku
			sku.SpecId = aliProduct.AliSkuID
			sku.Quantity = int64(item.Qty)
			sku.OfferId = aliProduct.AliProductID
			//skuMap = append(skuMap, sku)

			var companyNames alimodel.CompanyName
			err := source.DB().Where("shop_id=? and uid=?", aliProduct.ShopID, item.SupplierID).First(&companyNames).Error
			if err != nil {
				log.Log().Error("查询阿里商品属于哪个供应商错误", zap.Any("err", err))
				continue
			}

			if companyNames.AutoSubmit == "1" {
				log.Log().Error("当前companyNames AutoSubmit ", zap.Any("info", companyNames), zap.Any("item", item))
				continue

			}

			mapKey := aliProduct.ShopID + "#" + companyNames.AutoPay + "#" + companyNames.OrderType + "#" + companyNames.MemberId
			log.Log().Info("查询阿里商品mapKey", zap.Any("err", mapKey))

			OrderMap[mapKey] = append(OrderMap[mapKey], sku)

			var skuSetting alimodel.SkuSetting
			skuSetting.SpecId = aliProduct.AliSkuID
			skuSetting.Quantity = int64(item.Qty)
			skuSetting.OfferId = aliProduct.AliProductID
			skuSetting.AutoPay = aliProduct.AutoPay
			SkuMap[mapKey] = append(SkuMap[mapKey], skuSetting)

		}

	}
	log.Log().Info("阿里巴巴监听订单数据集：", zap.Any("orderMsg", OrderMap))

	if len(OrderMap) == 0 {
		return
	}

	log.Log().Info("阿里巴巴监听订单：", zap.Any("orderMsg", SkuMap))

	for key, item := range OrderMap {

		ConfirmOrder(orderData, item, key, SkuMap[key])
	}

	//var alibb service.Alibb
	//alibb.ShopID = "33"
	//alibb.Init()
	//var supplyOrderData request.RequestConfirmOrder
	//
	//var orderSn request.OrderSn
	//
	////收货信息
	//var address prequest.ReceivingInformation
	//address.Consignee = orderData.ShippingAddress.Realname
	//address.Phone = orderData.ShippingAddress.Mobile
	//address.Province = orderData.ShippingAddress.Province
	//address.City = orderData.ShippingAddress.City
	//address.Area = orderData.ShippingAddress.County
	//address.Street = orderData.ShippingAddress.Town
	//address.Description = orderData.ShippingAddress.Detail
	//
	//orderSn.OrderSn = strconv.Itoa(int(orderData.OrderSN)) //订单sn
	//supplyOrderData.OrderSn = orderSn
	//supplyOrderData.Address = address //地址
	//
	//err := alibb.ConfirmOrder(supplyOrderData, skuMap)
	//if err != nil {
	//	log.Log().Info("alibb下单错误", zap.Any("info", err))
	//	return
	//}
	//
	//aaa, _ := json.Marshal(skuMap)
	//fmt.Println(string(aaa))

}

func ConfirmOrder(orderData model.Order, skuMap alimodel.SkuMap, key string, SkuMap alimodel.SkuMapSetting) {
	stringArr := strings.Split(key, "#")
	log.Log().Info("alibb ConfirmOrder stringArr", zap.Any("info", stringArr))
	log.Log().Info("alibb ConfirmOrder SkuMap", zap.Any("info", SkuMap))

	if len(stringArr) < 2 {
		return
	}
	shopId := stringArr[0]

	var alibb service.Alibb
	alibb.ShopID = shopId
	alibb.Init()
	var supplyOrderData request.RequestConfirmOrder

	var orderSn request.OrderSn

	//收货信息
	var address prequest.ReceivingInformation
	address.Consignee = orderData.ShippingAddress.Realname
	address.Phone = orderData.ShippingAddress.Mobile
	address.Province = orderData.ShippingAddress.Province
	address.City = orderData.ShippingAddress.City
	address.Area = orderData.ShippingAddress.County
	address.Street = orderData.ShippingAddress.Town
	address.Description = orderData.ShippingAddress.Detail

	orderSn.OrderSn = strconv.Itoa(int(orderData.OrderSN)) //订单sn
	supplyOrderData.OrderSn = orderSn
	supplyOrderData.Address = address //地址

	err := alibb.ConfirmOrder(supplyOrderData, skuMap, stringArr, SkuMap)
	if err != nil {
		log.Log().Info("alibb下单错误", zap.Any("info", err))
		return
	}

}
