package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		CompanyName{},
		AliProduct{},
		AliOrder{},
	)

	if source.DB().Migrator().HasTable(&CompanyName{}) {
		//if !source.DB().Migrator().HasColumn(&CompanyName{}, "shop_id") {
		err = source.DB().Model(&CompanyName{}).Where("shop_id is null or shop_id=? or shop_id=?", "", "null").UpdateColumn("shop_id", "aliOpenSetting").Error
		//}
	}

	if source.DB().Migrator().HasTable(&AliProduct{}) {
		//if !source.DB().Migrator().HasColumn(&CompanyName{}, "shop_id") {
		err = source.DB().Model(&AliProduct{}).Where("shop_id is null or shop_id=? or shop_id=?", "", "null").UpdateColumn("shop_id", "aliOpenSetting").Error
		//}
	}
	// 菜单,权限
	menus := []model.SysMenu{}
	//sysBaseMenu := []model.SysBaseMenu{}

	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	return
}
