package model

import "yz-go/source"

type AlibbUser struct {
	source.Model
	ShopName string `json:"shop_name"`
}

type CallBackData struct {
	Data struct {
		ProductIds  string `json:"productIds"`
		OrderId     int64  `json:"orderId"`
		MsgSendTime string `json:"msgSendTime"`
		MemberId    string `json:"memberId"`
		Status      string `json:"status"`
	} `json:"data"`
	GmtBorn  int64  `json:"gmtBorn"`
	MsgId    int64  `json:"msgId"`
	Type     string `json:"type"`
	UserInfo string `json:"userInfo"`
}

type ResToken struct {
	AccessToken         string `json:"access_token"`
	AliId               string `json:"aliId"`
	RefreshToken        string `json:"refresh_token"`
	ResourceOwner       string `json:"resource_owner"`
	ExpiresIn           string `json:"expires_in"`
	RefreshTokenTimeout string `json:"refresh_token_timeout"`
	MemberId            string `json:"memberId"`
	ErrorInfo           string `json:"error"`
	ErrorDescription    string `json:"error_description"`
}
type CompanyName struct {
	source.Model
	CompanyName string `json:"companyName"`
	LoginId     string `json:"loginId"`
	MemberId    string `json:"memberId"`
	Success     bool   `json:"success"`
	Uid         uint   `json:"uid"`
	Domain      string `json:"domain"`
	ShopID      string `json:"shop_id"`
	AutoPay     string `json:"auto_pay"`
	AutoSubmit  string `json:"auto_submit"`
	OrderType   string `json:"order_type"`
}

type ResErr struct {
	ErrorCode string `json:"errorCode"`
	ErrorInfo string `json:"errorInfo"`
	Success   bool   `json:"success"`
}

type Product struct {
	BizGroupInfos []struct {
		Code        string `json:"code"`
		Description string `json:"description"`
		Support     bool   `json:"support"`
	} `json:"bizGroupInfos"`
	ProductInfo struct {
		ProductID   int64  `json:"productID"`
		ProductType string `json:"productType"`
		CategoryID  int    `json:"categoryID"`
		Attributes  []struct {
			AttributeID   int    `json:"attributeID"`
			AttributeName string `json:"attributeName"`
			Value         string `json:"value"`
			IsCustom      bool   `json:"isCustom"`
		} `json:"attributes"`
		Status           string `json:"status"`
		Subject          string `json:"subject"`
		Description      string `json:"description"`
		Language         string `json:"language"`
		PeriodOfValidity int    `json:"periodOfValidity"`
		BizType          int    `json:"bizType"`
		PictureAuth      bool   `json:"pictureAuth"`
		Image            struct {
			Images []string `json:"images"`
		} `json:"image"`
		SkuInfos []struct {
			Attributes []struct {
				AttributeID    int    `json:"attributeID"`
				AttributeValue string `json:"attributeValue"`
				SkuImageUrl    string `json:"skuImageUrl"`
				AttributeName  string `json:"attributeName"`
			} `json:"attributes"`
			CargoNumber  string  `json:"cargoNumber"`
			AmountOnSale int     `json:"amountOnSale"`
			SkuCode      string  `json:"skuCode"`
			SkuId        int64   `json:"skuId"`
			SpecId       string  `json:"specId"`
			ConsignPrice float64 `json:"consignPrice"`
		} `json:"skuInfos"`
		SaleInfo struct {
			SupportOnlineTrade bool   `json:"supportOnlineTrade"`
			MixWholeSale       bool   `json:"mixWholeSale"`
			SaleType           string `json:"saleType"`
			PriceAuth          bool   `json:"priceAuth"`
			PriceRanges        []struct {
				StartQuantity int     `json:"startQuantity"`
				Price         float64 `json:"price"`
			} `json:"priceRanges"`
			AmountOnSale     float64 `json:"amountOnSale"`
			Unit             string  `json:"unit"`
			MinOrderQuantity int     `json:"minOrderQuantity"`
			QuoteType        int     `json:"quoteType"`
		} `json:"saleInfo"`
		ExtendInfos []struct {
			Key   string `json:"key"`
			Value string `json:"value"`
		} `json:"extendInfos"`
		QualityLevel       int    `json:"qualityLevel"`
		CategoryName       string `json:"categoryName"`
		MainVedio          string `json:"mainVedio"`
		ProductCargoNumber string `json:"productCargoNumber"`
		ReferencePrice     string `json:"referencePrice"`
		CreateTime         string `json:"createTime"`
		LastUpdateTime     string `json:"lastUpdateTime"`
		ExpireTime         string `json:"expireTime"`
		ApprovedTime       string `json:"approvedTime"`
		SellerLoginId      string `json:"sellerLoginId"`
	} `json:"productInfo"`
}

type OrderRes struct {
	Result struct {
		TotalSuccessAmount int    `json:"totalSuccessAmount"`
		OrderId            string `json:"orderId"`
		PostFee            int    `json:"postFee"`
	} `json:"result"`
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

type PayRes struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Message string `json:"message"`
}

type Setting struct {
	Key    string `json:"key"`
	ID     string `json:"id"`
	Secret string `json:"secret"`
	Token  string `json:"token"`
	Domain string `json:"domain"`
	Enable uint   `json:"enable"`
}

type SetSupplierSetting struct {
	AutoPay   string `json:"auto_pay"`
	OrderType string `json:"order_type"`
}

type CallBackOrderData struct {
	BizKey string `json:"bizKey"`
	Data   struct {
		BuyerMemberId  string `json:"buyerMemberId"`
		OrderId        int64  `json:"orderId"`
		CurrentStatus  string `json:"currentStatus"`
		SellerMemberId string `json:"sellerMemberId"`
		MsgSendTime    string `json:"msgSendTime"`
	} `json:"data"`
	GmtBorn  int64  `json:"gmtBorn"`
	MsgId    int64  `json:"msgId"`
	Type     string `json:"type"`
	UserInfo string `json:"userInfo"`
}

type AliProduct struct {
	source.Model
	ProductID    int64  `json:"product_id"`
	SkuID        int64  `json:"sku_id"`
	AliSkuID     string `json:"ali_sku_id"`
	AliProductID int64  `json:"ali_product_id"`
	AutoPay      int64  `json:"auto_pay" gorm:"default:0"`
	ShopID       string `json:"shop_id"`
	Follow       uint   `json:"follow" gorm:"default:0"`
}

type SkuMap []Sku
type SkuMapSetting []SkuSetting

type SkuSetting struct {
	Sku
	AutoPay int64 `json:"auto_pay"`
}
type Sku struct {
	OfferId  int64  `json:"offerId"`
	SpecId   string `json:"specId"`
	Quantity int64  `json:"quantity"`
}

type AliOrder struct {
	source.Model
	OrderID   string `json:"order_id"`
	ProductID int64  `json:"product_id"`
	SkuID     string `json:"sku_id"`
	ShopID    string `json:"shop_id"`
	OrderSN   string `json:"order_sn"`
	PayStatus uint   `json:"pay_status"` //1 支付失败
}
