package request

type Follow struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type ID struct {
	ID     string `json:"id"`
	ShopID string `json:"shop_id"`
}

type OrderID struct {
	ID int64 `json:"id"`
}

type ShopData struct {
	ShopID string `json:"id"`
}

type SupplierDomain struct {
	Domain string `json:"domain"`
	ShopData
}
type Code struct {
	Code string `json:"code"`
	ShopData
}

type AliSupplier struct {
	AliID string `json:"ali_id"`
}
type BindSupplier struct {
	Uid uint `json:"uid"`
	AliSupplier
}

type BindProduct struct {
	ProductID    int64  `json:"product_id"`
	SkuID        int64  `json:"sku_id"`
	AliSkuID     string `json:"ali_sku_id"`
	AliProductID int64  `json:"ali_product_id"`
	AutoPay      int64  `json:"auto_pay"`
	ShopData
}

//	type LogisticsInfo struct {
//		ErrorCode    string `json:"errorCode"`
//		ErrorMessage string `json:"errorMessage"`
//		Success      bool   `json:"success"`
//	}
type LogisticsInfo struct {
	Result []struct {
		Sender struct {
			SenderName         string `json:"senderName"`
			SenderPhone        string `json:"senderPhone"`
			SenderMobile       string `json:"senderMobile"`
			Encrypt            string `json:"encrypt"`
			SenderProvinceCode string `json:"senderProvinceCode"`
			SenderCityCode     string `json:"senderCityCode"`
			SenderCountyCode   string `json:"senderCountyCode"`
			SenderAddress      string `json:"senderAddress"`
			SenderProvince     string `json:"senderProvince"`
			SenderCity         string `json:"senderCity"`
			SenderCounty       string `json:"senderCounty"`
		} `json:"sender"`
		OrderEntryIds   string `json:"orderEntryIds"`
		LogisticsBillNo string `json:"logisticsBillNo"`
		LogisticsId     string `json:"logisticsId"`
		Receiver        struct {
			ReceiverName         string `json:"receiverName"`
			ReceiverPhone        string `json:"receiverPhone"`
			ReceiverMobile       string `json:"receiverMobile"`
			Encrypt              string `json:"encrypt"`
			ReceiverProvinceCode string `json:"receiverProvinceCode"`
			ReceiverCityCode     string `json:"receiverCityCode"`
			ReceiverCountyCode   string `json:"receiverCountyCode"`
			ReceiverAddress      string `json:"receiverAddress"`
			ReceiverProvince     string `json:"receiverProvince"`
			ReceiverCity         string `json:"receiverCity"`
			ReceiverCounty       string `json:"receiverCounty"`
		} `json:"receiver"`
		LogisticsCompanyName string `json:"logisticsCompanyName"`
		Status               string `json:"status"`
		SendGoods            []struct {
			GoodName string `json:"goodName"`
			Quantity string `json:"quantity"`
			Unit     string `json:"unit"`
		} `json:"sendGoods"`
		Remarks            string `json:"remarks"`
		LogisticsCompanyId string `json:"logisticsCompanyId"`
	} `json:"result"`
	Success bool `json:"success"`
}

type ResOrderData struct {
	Result struct {
		BaseInfo struct {
			AllDeliveredTime  string  `json:"allDeliveredTime"`
			PayTime           string  `json:"payTime"`
			Discount          int     `json:"discount"`
			AlipayTradeId     string  `json:"alipayTradeId"`
			SumProductPayment float64 `json:"sumProductPayment"`
			FlowTemplateCode  string  `json:"flowTemplateCode"`
			SellerOrder       bool    `json:"sellerOrder"`
			BuyerLoginId      string  `json:"buyerLoginId"`
			ModifyTime        string  `json:"modifyTime"`
			Id                int64   `json:"id"`
			BuyerContact      struct {
				Phone        string `json:"phone"`
				ImInPlatform string `json:"imInPlatform"`
				Name         string `json:"name"`
				Mobile       string `json:"mobile"`
				CompanyName  string `json:"companyName"`
			} `json:"buyerContact"`
			SellerLoginId    string  `json:"sellerLoginId"`
			BuyerID          string  `json:"buyerID"`
			CloseOperateType string  `json:"closeOperateType"`
			TotalAmount      float64 `json:"totalAmount"`
			SellerID         string  `json:"sellerID"`
			ShippingFee      int     `json:"shippingFee"`
			Refund           int     `json:"refund"`
			Status           string  `json:"status"`
			RefundPayment    int     `json:"refundPayment"`
			SellerContact    struct {
				Phone        string `json:"phone"`
				ImInPlatform string `json:"imInPlatform"`
				Name         string `json:"name"`
				CompanyName  string `json:"companyName"`
			} `json:"sellerContact"`
			CouponFee    int `json:"couponFee"`
			ReceiverInfo struct {
				ToFullName     string `json:"toFullName"`
				ToDivisionCode string `json:"toDivisionCode"`
				ToMobile       string `json:"toMobile"`
				ToPhone        string `json:"toPhone"`
				ToPost         string `json:"toPost"`
				ToArea         string `json:"toArea"`
			} `json:"receiverInfo"`
			TradeType      string   `json:"tradeType"`
			IdOfStr        string   `json:"idOfStr"`
			StepPayAll     bool     `json:"stepPayAll"`
			CreateTime     string   `json:"createTime"`
			BusinessType   string   `json:"businessType"`
			OverSeaOrder   bool     `json:"overSeaOrder"`
			TradeTypeDesc  string   `json:"tradeTypeDesc"`
			PayChannelList []string `json:"payChannelList"`
			TradeTypeCode  string   `json:"tradeTypeCode"`
			PayTimeout     int      `json:"payTimeout"`
			PayTimeoutType int      `json:"payTimeoutType"`
		} `json:"baseInfo"`
		OrderBizInfo struct {
			OdsCyd      bool `json:"odsCyd"`
			CreditOrder bool `json:"creditOrder"`
		} `json:"orderBizInfo"`
		TradeTerms []struct {
			Phase         int64   `json:"phase"`
			PayWayDesc    string  `json:"payWayDesc"`
			ExpressPay    bool    `json:"expressPay"`
			PayTime       string  `json:"payTime"`
			PayStatusDesc string  `json:"payStatusDesc"`
			PayWay        string  `json:"payWay"`
			CardPay       bool    `json:"cardPay"`
			PayStatus     string  `json:"payStatus"`
			PhasAmount    float64 `json:"phasAmount"`
		} `json:"tradeTerms"`
		ProductItems []struct {
			ItemAmount         float64  `json:"itemAmount"`
			Name               string   `json:"name"`
			Price              float64  `json:"price"`
			ProductID          int64    `json:"productID"`
			ProductImgUrl      []string `json:"productImgUrl"`
			ProductSnapshotUrl string   `json:"productSnapshotUrl"`
			Quantity           int      `json:"quantity"`
			Refund             int      `json:"refund"`
			Status             string   `json:"status"`
			SubItemID          int64    `json:"subItemID"`
			Type               string   `json:"type"`
			Unit               string   `json:"unit"`
			GuaranteesTerms    []struct {
				AssuranceInfo        string `json:"assuranceInfo"`
				AssuranceType        string `json:"assuranceType"`
				QualityAssuranceType string `json:"qualityAssuranceType"`
			} `json:"guaranteesTerms"`
			ProductCargoNumber string `json:"productCargoNumber"`
			EntryDiscount      int    `json:"entryDiscount"`
			QuantityFactor     int    `json:"quantityFactor"`
			StatusStr          string `json:"statusStr"`
			LogisticsStatus    int    `json:"logisticsStatus"`
			GmtCreate          string `json:"gmtCreate"`
			GmtModified        string `json:"gmtModified"`
			SubItemIDString    string `json:"subItemIDString"`
		} `json:"productItems"`
		NativeLogistics struct {
			Address        string `json:"address"`
			Area           string `json:"area"`
			AreaCode       string `json:"areaCode"`
			City           string `json:"city"`
			ContactPerson  string `json:"contactPerson"`
			Mobile         string `json:"mobile"`
			Province       string `json:"province"`
			Telephone      string `json:"telephone"`
			Zip            string `json:"zip"`
			LogisticsItems []struct {
				DeliveredTime        string `json:"deliveredTime"`
				LogisticsCode        string `json:"logisticsCode"`
				Type                 string `json:"type"`
				Id                   int64  `json:"id"`
				Status               string `json:"status"`
				GmtModified          string `json:"gmtModified"`
				GmtCreate            string `json:"gmtCreate"`
				FromCity             string `json:"fromCity"`
				FromArea             string `json:"fromArea"`
				FromAddress          string `json:"fromAddress"`
				FromPhone            string `json:"fromPhone"`
				FromPost             string `json:"fromPost"`
				LogisticsCompanyId   int    `json:"logisticsCompanyId"`
				LogisticsCompanyNo   string `json:"logisticsCompanyNo"`
				LogisticsCompanyName string `json:"logisticsCompanyName"`
				LogisticsBillNo      string `json:"logisticsBillNo"`
				SubItemIds           string `json:"subItemIds"`
				ToCity               string `json:"toCity"`
				ToArea               string `json:"toArea"`
				ToAddress            string `json:"toAddress"`
				ToPost               string `json:"toPost"`
				NoLogisticsName      string `json:"noLogisticsName"`
				NoLogisticsTel       string `json:"noLogisticsTel"`
				NoLogisticsBillNo    string `json:"noLogisticsBillNo"`
				NoLogisticsCondition string `json:"noLogisticsCondition"`
			} `json:"logisticsItems"`
		} `json:"nativeLogistics"`
		GuaranteesTerms struct {
			AssuranceInfo        string `json:"assuranceInfo"`
			AssuranceType        string `json:"assuranceType"`
			QualityAssuranceType string `json:"qualityAssuranceType"`
		} `json:"guaranteesTerms"`
		OrderRateInfo struct {
			BuyerRateStatus  int `json:"buyerRateStatus"`
			SellerRateStatus int `json:"sellerRateStatus"`
		} `json:"orderRateInfo"`
		ExtAttributes []interface{} `json:"extAttributes"`
	} `json:"result"`
	Success string `json:"success"`
}
