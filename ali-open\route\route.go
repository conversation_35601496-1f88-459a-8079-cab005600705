package route

import (
	v1 "ali-open/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAliPublicRouter(Router *gin.RouterGroup) {
	AliRouter := Router.Group("aliOpen")
	{
		AliRouter.POST("callBackMessage", v1.CallBack)
		//AliRouter.POST("callBackOrderMessage", v1.CallBackOrder)
	}
}
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	AliRouter := Router.Group("aliOpen")
	{
		// 基础设置

		AliRouter.POST("setSetting", v1.SetSetting)                 //配置设置
		AliRouter.POST("getSetting", v1.GetSetting)                 //获取设置
		AliRouter.GET("getSettingList", v1.GetSettingList)          //获取设置
		AliRouter.POST("getSupplier", v1.GetSupplier)               //获取阿里供应商
		AliRouter.POST("getProduct", v1.GetProduct)                 //获取阿里商品
		AliRouter.POST("bindSupplier", v1.BindSupplier)             //绑定供应商
		AliRouter.POST("createDomain", v1.CreateDomain)             //添加域名
		AliRouter.POST("deleteDomain", v1.DeleteDomain)             //删除域名
		AliRouter.POST("bindProduct", v1.BindProduct)               //绑定商品
		AliRouter.POST("cancelBindProduct", v1.CancelBindProduct)   //取消绑定绑定商品
		AliRouter.GET("deleteBindProduct", v1.DeleteBindProduct)    //删除绑定商品
		AliRouter.GET("getSupplierList", v1.GetSupplierList)        //获取供应商列表
		AliRouter.POST("setToken", v1.SetToken)                     //设置token
		AliRouter.POST("manualAlibbOrder", v1.ManualAlibbOrder)     //手动下单阿里巴巴商品
		AliRouter.POST("setSupplierSetting", v1.SetSupplierSetting) //配置设置

	}
}
