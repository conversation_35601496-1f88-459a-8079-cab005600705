package service

import (
	"ali-open/model"
	"ali-open/request"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	url2 "net/url"
	v1 "order/api/v1"
	omodel "order/model"
	orderRequest2 "order/request"
	"product/service"
	express2 "shipping/express"
	"sort"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

func CallBackService(reqData model.CallBackData) (err error) {

	var aliProduct []model.AliProduct
	ids := strings.Split(reqData.Data.ProductIds, ",")
	log.Log().Info("阿里回调商品下架消息", zap.Any("info", ids), zap.Any("reqData.Data.Status", reqData.Data.Status))
	if len(ids) > 0 {
		err = source.DB().Where("ali_product_id in (?)", ids).Find(&aliProduct).Error
		for _, item := range aliProduct {
			var updateProduct service.ProductForUpdate

			err = source.DB().Where("id=?", item.ProductID).Preload("Skus").First(&updateProduct).Error
			if err != nil {
				log.Log().Error("阿里回调商品消息查询失败", zap.Any("err", err))
				continue
			}

			if updateProduct.StatusLock == 1 {
				log.Log().Info("阿里回调商品下架消息 商品锁定不处理", zap.Any("info", updateProduct.ID))
				continue
			}
			var alibb Alibb
			alibb.ShopID = item.ShopID
			alibb.Init()

			strID := strconv.Itoa(int(item.AliProductID))
			_, product := alibb.GetProduct(strID)
			if reqData.Data.Status == "RELATION_VIEW_PRODUCT_REPOST" {
				updateProduct.IsDisplay = 1
			} else if reqData.Data.Status == "RELATION_VIEW_PRODUCT_EXPIRE" {
				updateProduct.IsDisplay = 0
			} else if reqData.Data.Status == "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY" {

				if product.ProductInfo.Status == "published" {
					updateProduct.IsDisplay = 1
				} else {
					updateProduct.IsDisplay = 0
				}

			}

			for skuIndex, skuItem := range updateProduct.Skus {

				if skuItem.ID == uint(item.SkuID) {

					if item.AliSkuID == "" {
						log.Log().Info("阿里巴巴  单规格商品直接获取sale 库存", zap.Any("skuid", skuItem.ID), zap.Any("info", product.ProductInfo.SaleInfo.AmountOnSale))

						updateProduct.Skus[skuIndex].Stock = int(product.ProductInfo.SaleInfo.AmountOnSale)

					} else {

						stock := GetAlisku(item.AliSkuID, product)
						log.Log().Info("阿里巴巴  多规格商品直接获取sku内库存", zap.Any("skuid", skuItem.ID), zap.Any("info", stock))

						updateProduct.Skus[skuIndex].Stock = stock
					}

				}

			}

			log.Log().Debug("阿里巴巴商品更新数据日志：", zap.Any("info", updateProduct))
			service.UpdateProduct(updateProduct)

		}

	}
	return

}

func GetAlisku(skuId string, product model.Product) int {

	for _, item := range product.ProductInfo.SkuInfos {
		if skuId == item.SpecId {

			return item.AmountOnSale
		}
	}

	return 0

}

func OrderDeliverCallBack(orderSn int64) {
	log.Log().Info("alibb供应链代发货订单查询OrderDelivercallBack")

	var aliOrder model.AliOrder
	source.DB().Where("order_id=?", orderSn).First(&aliOrder)
	if aliOrder.ID <= 0 {
		log.Log().Error("AlibbOrder  订单当前未查出数据---", zap.Any("info", orderSn))
		return
	}

	var deliverOrder []omodel.Order
	err := source.DB().Preload("OrderItems").Where("order_sn=?", aliOrder.OrderSN).Where("status=? and gather_supply_type=?  ", 1, 7).Find(&deliverOrder).Error
	if err != nil {
		log.Log().Info("alibb供应链代发货订单查询3", zap.Any("info", err))
		return
	}

	if len(deliverOrder) == 0 {
		return
	}

	for _, od := range deliverOrder {
		for _, odItem := range od.OrderItems {
			var alibb Alibb

			if odItem.SendStatus == 1 {
				continue
			}
			log.Log().Info("alibb查询订单sku是否发货5", zap.Any("sku", odItem), zap.Any("订单sn", od.OrderSN))

			var aliProduct model.AliProduct
			source.DB().Where("product_id=? and sku_id=?", odItem.ProductID, odItem.SkuID).First(&aliProduct)

			if aliProduct.ID <= 0 {
				log.Log().Error("AlibbOrder product当前未查出绑定数据", zap.Any("info", odItem))
				continue
			}
			var aliOrder model.AliOrder
			source.DB().Where("product_id=? and sku_id=? and order_sn=?", aliProduct.AliProductID, aliProduct.AliSkuID, od.OrderSN).First(&aliOrder)
			if aliOrder.ID <= 0 {
				log.Log().Error("AlibbOrder  订单当前未查出数据9", zap.Any("info", aliProduct))
				continue
			}

			alibb.ShopID = aliProduct.ShopID
			alibb.Init()
			var CompanyName, No string
			err, CompanyName, No = alibb.AlibbOrderSelect(aliOrder.OrderID, aliProduct.AliProductID)
			if err != nil {
				log.Log().Error("AlibbOrderSelecterr当前订单信息未查询到物流A", zap.Any("info", err))
				continue
			}
			var orderRequest v1.HandleOrderRequest
			var code string

			if CompanyName == "" || No == "" {
				log.Log().Error("当前订单信息未查询到物流", zap.Any("info", aliProduct))
				continue
			}

			err, code = ExpressList(CompanyName)

			if err != nil {
				log.Log().Error("alibb查询物流信息错误3", zap.Any("info", err.Error()))
				continue
			}

			if code == "" {
				log.Log().Error("alibb当前订单信息未查询到物流codeN", zap.Any("info", code))
				log.Log().Error("alibb当前订单信息未查询到物流codeM", zap.Any("info", CompanyName))
				continue
			}
			var ids = []orderRequest2.OrderItemSendInfo{{ID: odItem.ID, Num: odItem.Qty}}
			orderRequest.OrderID = od.ID
			orderRequest.ExpressNo = No
			orderRequest.OrderItemIDs = ids
			orderRequest.CompanyCode = code
			log.Log().Info("alibb发货信息", zap.Any("info", orderRequest))
			err = ExpressSent(orderRequest)
			if err != nil {
				continue
			}

		}

	}

}

// 获取快递code
func ExpressList(name string) (err error, code string) {
	log.Log().Info("alibb当前订单查询到物流list", zap.Any("info", express2.GetCompanyList()))

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

func CallBackOrderService(reqData model.CallBackData) (err error) {
	log.Log().Info("阿里aliopen订单发货通知", zap.Any("info", reqData))

	OrderDeliverCallBack(reqData.Data.OrderId)

	return

}

func (ali *Alibb) Init() {

	err, sysSetting := GetSetting(ali.ShopID)
	if err != nil {
		return
	}
	var setting model.Setting
	err = json.Unmarshal([]byte(sysSetting.Value), &setting)
	if err != nil {
		return
	}
	ali.Key = setting.Key
	ali.Secret = setting.Secret
	ali.Token = setting.Token
	ali.Domain = setting.Domain

	return
}

func BindSupplier(bind request.BindSupplier) (err error) {

	var comPanyName model.CompanyName
	comPanyName.Uid = bind.Uid
	err = source.DB().Where("member_id=?", bind.AliID).Updates(&comPanyName).Error

	return

}

func CancelBindProduct(productID, skuID int64) (err error) {
	source.DB().Unscoped().Delete(&model.AliProduct{}, "product_id=?", productID)
	return
}
func BindProduct(param request.BindProduct) (err error) {

	var product, whereProduct model.AliProduct
	product.ProductID = param.ProductID
	product.AliProductID = param.AliProductID
	product.SkuID = param.SkuID
	product.AliSkuID = param.AliSkuID
	product.ShopID = param.ShopData.ShopID
	product.AutoPay = param.AutoPay
	source.DB().Where("product_id=? and sku_id=? ", product.ProductID, product.SkuID).First(&whereProduct)
	if whereProduct.ID > 0 {
		err = source.DB().Where("product_id=? and sku_id=? ", product.ProductID, product.SkuID).Updates(&product).Error
	} else {
		err = source.DB().Create(&product).Error
	}

	return

}

func GetSupplierList(id string) (err error, list []model.CompanyName) {

	//err = source.DB().Where("shop_id=?", id).Order("id desc").Find(&list).Error
	err = source.DB().Where("shop_id=?", id).Order("id desc").Find(&list).Error

	return
}
func DeleteBindProduct(param request.BindProduct) (err error) {
	var product model.AliProduct
	err = source.DB().Where("product_id=?  and sku_id=?", param.ProductID, param.SkuID).Delete(&product).Error
	return

}

func CreateDomain(domain request.SupplierDomain) (err error) {
	var comPanyName model.CompanyName
	comPanyName.Domain = domain.Domain
	comPanyName.ShopID = domain.ShopID
	err = source.DB().Create(&comPanyName).Error
	return

}

func DeleteDomain(id request.ID) (err error) {
	var comPanyName model.CompanyName
	err = source.DB().Where("id=?", id.ID).Delete(&comPanyName).Error
	return

}

func (ali *Alibb) GetProduct(id string) (err error, product model.Product) {

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.simple.get/" + ali.Key

	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	reqData.Add("productID", id) //************
	reqData.Add("webSite", "1688")
	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))

	//log.Log().Info("aliselectproductrequestpost", zap.Any("err", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	//var product model.Product

	json.Unmarshal(resData, &product)
	//log.Log().Info("aliselectproduct", zap.Any("err", string(resData)))

	fmt.Println("product", string(resData))
	return
}

func (ali *Alibb) GetShop(domain string) (err error) {

	var companyList model.CompanyName
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.member.getRelationUserInfo/" + ali.Key
	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	reqData.Add("domain", domain)
	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))
	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	err = json.Unmarshal(resData, &companyList)
	if err != nil {
		return
	}
	if companyList.CompanyName == "" {
		var errorType ErrorType
		err = json.Unmarshal(resData, &errorType)
		err = errors.New(errorType.ErrorInfo)
		return
	}
	if companyList.Success == true {
		var companyName = make(map[string]interface{})
		companyName["company_name"] = companyList.CompanyName
		companyName["login_id"] = companyList.LoginId
		companyName["member_id"] = companyList.MemberId
		err = source.DB().Model(model.CompanyName{}).Where("domain=?", domain).Updates(companyName).Error

		return
	}

	return

}

type ErrorType struct {
	ErrorCode string `json:"errorCode"`
	ErrorInfo string `json:"errorInfo"`
	Success   bool   `json:"success"`
}

//字符串转为16进制

func HmacSha1(data string, secret string) string {
	h := hmac.New(sha1.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

type MapEntryHandler func(string, string)

// 按字母顺序遍历map
func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}
func Sign(urlPath, Secret string, str url2.Values) string {

	if urlPath == "" {
		return ""
	}

	urlpath := strings.Split(urlPath, "openapi/")
	if len(urlpath) < 2 {
		return ""
	}
	path := urlpath[1]
	maplist := make(map[string]string)
	for i, item := range str {
		maplist[i] = item[0]
	}
	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(maplist, func(key string, value string) {
		signature += key + value
	})

	signStr := path + signature
	sign := HmacSha1(signStr, Secret)
	signUpper := strings.ToUpper(sign)
	fmt.Println(string(signUpper))
	return signUpper

	//return "ACD581CC036CC0D2D46FF892D0B9B455F8BE60B7"
}

func (ali *Alibb) CodeGetToken(code string) (err error) {

	//refresh_token:="e85b97e5-1163-4d2f-8712-a39c01c1dd33"

	url := "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/" + ali.Key + "?grant_type=authorization_code&need_refresh_token=true&client_id=" + ali.Key + "&client_secret=" + ali.Secret + "&redirect_uri=" + ali.Domain + "&code=" + code

	reqData := url2.Values{}

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	var token model.ResToken

	err = json.Unmarshal(resData, &token)
	if token.ErrorInfo != "" {
		err = errors.New(token.ErrorDescription)
		fmt.Println("获取错误", token)
		return
	}

	log.Log().Info("info", zap.Any("token", token))
	ali.SetToken(token.AccessToken)
	return
}

func (ali *Alibb) SetToken(token string) {
	err, sysSetting := GetSetting(ali.ShopID)
	if err != nil {
		return
	}
	var setting model.Setting
	err = json.Unmarshal([]byte(sysSetting.Value), &setting)
	setting.Token = token
	sysSetting.SetSetting(ali.ShopID, setting)
}
