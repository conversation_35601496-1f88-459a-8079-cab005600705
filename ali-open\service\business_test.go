package service

import (
	"ali-open/model"
	"testing"
)

func TestCodeGetToken(t *testing.T) {

	var aaa Alibb
	aaa.ShopID = "aliOpenSettingoo"
	aaa.Init()

	//aaa.GetLogisticsInfos()
	aaa.GetProduct("667732761158")
}

func TestCallBackService(t *testing.T) {
	type args struct {
		reqData model.CallBackData
	}
	var aaa model.CallBackData
	aaa.Data.ProductIds = "667732761158"
	aaa.Data.Status = "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY"
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{name: "11", args: args{reqData: aaa}},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CallBackService(tt.args.reqData); (err != nil) != tt.wantErr {
				t.Errorf("CallBackService() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
