package v1

import (
	"ali-selected/component/goods"
	"ali-selected/cron"
	model2 "ali-selected/model"
	request2 "ali-selected/request"
	"ali-selected/service"
	url2 "net/url"
	setting2 "public-supply/setting"
	"strconv"

	"ali-selected/mq"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/url"
	v1 "order/api/v1"
	omodel "order/model"
	orderRequest2 "order/request"
	"product/model"
	model3 "public-supply/model"
	express2 "shipping/express"
	"strings"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func GetChangeRecord(c *gin.Context) {
	var pageInfo request2.SearchShop
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetChangeRecord(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags GetSupplier
// @Summary 阿里巴巴获取供应商
// @Security
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /aliOpen/GetSupplier [put]
func CallBackMessage(c *gin.Context) {

	var reqData model2.CallData
	bytes, _ := c.GetRawData()
	urlStr := "http://yx.cn/?" + string(bytes)
	log.Log().Info("阿里精选回调接受", zap.Any("info", urlStr))
	myUrl, _ := url.Parse(urlStr)
	params, _ := url.ParseQuery(myUrl.RawQuery)
	message := params["message"]
	if len(message) == 0 {
		log.Log().Error("阿里精选回调接受-1")

		yzResponse.FailWithMessage("参数错误", c)
		return
	}

	err := json.Unmarshal([]byte(message[0]), &reqData)
	if err != nil {
		log.Log().Error("阿里精选回调接受-2")
		yzResponse.FailWithMessage("参数解析错误", c)
		return
	}

	err = mq.PublishMessage(1, reqData, 1)
	if err != nil {
		log.Log().Info("阿里aljx回调消息mq send", zap.Any("info", err))
	}

	yzResponse.Ok(c)

}

type QueryString struct {
	Message interface{} `json:"message" query:"message" from:"message"`
}

func CallBackService(reqData model2.AliSale) (err error) {
	var product model.Product
	var aliGoods model2.AliGoods
	source.DB().Where("product_id=?", reqData.OfferId).Delete(&aliGoods)

	err = source.DB().Where("source_goods_id=? and source=116", reqData.OfferId).First(&product).Error
	if err == nil {
		cron.UndercarriageProduct(product.ID)

	}

	return

}

func CallBackProductModifyService(reqData model2.AliSale) (err error) {
	var product []model.Product

	ids := strings.Split(reqData.ProductIds, ",")
	log.Log().Info("阿里精选商品修改", zap.Any("info", ids))
	//var aliGoods model2.AliGoods
	//source.DB().Where("product_id=?", reqData.OfferId).Delete(&aliGoods)
	//
	err = source.DB().Where("source_goods_id=? and source=116", ids).Find(&product).Error
	for _, item := range product {
		log.Log().Info("阿里精选商品修改A", zap.Any("info", item.ID))
	}

	return

}

func CallBackAUDITService(reqData model2.AliSale) (err error) {
	var product model.Product
	var aliGoods model2.AliGoods

	ids := strings.Split(reqData.ProductIds, ",")
	log.Log().Info("阿里回调商品下架消息", zap.Any("info", ids))
	if len(ids) > 0 {
		source.DB().Where("product_id in (?)", ids).Delete(&aliGoods)

		err = source.DB().Where("source_goods_id in (?) and source=116", ids).First(&product).Error
		if err == nil {
			cron.UndercarriageProduct(product.ID)

		}
	}

	return

}

func InitSetting(taskID int) {
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dataSetting)
	if err != nil {

		return
	}
}

func CallBackOrderService(reqData model2.AliSale) (err error) {
	log.Log().Info("aljx供应链代发货订单查询OrderDeliverCron")

	var deliverOrder []omodel.Order
	orderID := strconv.Itoa(int(reqData.OrderId))
	err = source.DB().Preload("OrderItems").Where("note like ?", "%"+orderID+"%").Preload("OrderItems.Product").Where("status=? and gather_supply_type=?  ", 1, 116).Find(&deliverOrder).Error
	if err != nil {
		log.Log().Info("aljx供应链代发货订单查询", zap.Any("info", err))
		return
	}

	if len(deliverOrder) == 0 {
		return
	}

	for _, od := range deliverOrder {
		InitSetting(int(od.GatherSupplyID))
		for _, odItem := range od.OrderItems {
			if odItem.GatherSupplySN != orderID {
				log.Log().Info("aljx查询订单发货处理对应item", zap.Any("sku", odItem), zap.Any("订单sn", od.OrderSN))

				continue
			}

			if odItem.SendStatus == 1 {
				continue
			}
			log.Log().Info("aljx查询订单sku是否发货", zap.Any("sku", odItem), zap.Any("订单sn", od.OrderSN))

			var alproduct model.Product
			source.DB().Where("id=?", odItem.ProductID).First(&alproduct)
			log.Log().Info("Aljx定时查询发货信息", zap.Any("info", od.GatherSupplySN), zap.Any("info", alproduct.SourceGoodsID), zap.Any("orderSN", orderID))

			var CompanyName, No string
			err, CompanyName, No = AlibbOrderSelect(orderID, int64(alproduct.SourceGoodsID))
			if err != nil {
				log.Log().Error("AljxOrderSelecterr当前订单信息未查询到物流", zap.Any("info", err))
				continue
			}
			var orderRequest v1.HandleOrderRequest
			var code string

			if CompanyName == "" || No == "" {
				//log.Log().Error("alxj当前订单信息未查询到物流", zap.Any("info", odItem))
				continue
			}

			err, code = ExpressList(CompanyName)
			if err != nil {
				log.Log().Error("aljx查询物流信息错误3", zap.Any("info", err.Error()))
				continue
			}

			if code == "" {
				log.Log().Error("aljx当前订单信息未查询到物流code", zap.Any("info", code))
				log.Log().Error("aljx当前订单信息未查询到物流code", zap.Any("info", CompanyName))
				continue
			}
			var ids = []orderRequest2.OrderItemSendInfo{{ID: odItem.ID, Num: odItem.Qty}}
			orderRequest.OrderID = od.ID
			orderRequest.ExpressNo = No
			orderRequest.OrderItemIDs = ids
			orderRequest.CompanyCode = code
			log.Log().Info("aljx发货信息", zap.Any("info", orderRequest))
			err = ExpressSent(orderRequest)
			if err != nil {
				continue
			}

		}

	}

	return
}

// 获取快递code
func ExpressList(name string) (err error, code string) {

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

var dataSetting model3.SupplySetting

func AlibbOrderSelect(orderID string, productID int64) (err error, CompanyName, No string) {
	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/" + dataSetting.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", dataSetting.BaseInfo.Token)
	reqData.Add("orderId", orderID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", goods.Sign(url, dataSetting.BaseInfo.AppSecret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	var ResOrderData request2.ResOrderData

	json.Unmarshal(resData, &ResOrderData)
	if ResOrderData.Success != "true" {
		log.Log().Info("alibb供应链代发货订单查询错误", zap.Any("info", string(resData)))

		return
	}

	for _, item := range ResOrderData.Result.ProductItems {

		if item.ProductID == productID {

			for _, LogisticsItems := range ResOrderData.Result.NativeLogistics.LogisticsItems {

				if strings.Contains(LogisticsItems.SubItemIds, item.SubItemIDString) {
					CompanyName = LogisticsItems.LogisticsCompanyName
					No = LogisticsItems.LogisticsBillNo
					return
					//err, CompanyName, No = ali.GetLogisticsInfos(item.SubItemIDString)

				}

			}

			return

		}

	}

	fmt.Println(string(resData))

	return

}
