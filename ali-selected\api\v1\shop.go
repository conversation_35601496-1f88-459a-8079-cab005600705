package v1

import (
	"ali-selected/model"
	"ali-selected/request"
	"ali-selected/service"
	"github.com/gin-gonic/gin"
	"strings"
	yzResponse "yz-go/response"
)

func Create(c *gin.Context) {
	var shop model.AliShop
	err := c.ShouldBindJ<PERSON>N(&shop)
	if err != nil {
		yzResponse.FailWithMessage("解析参数失败", c)
		return
	}
	shop.Name = strings.TrimSpace(shop.Name)
	err = service.Create(shop)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}

}

func Delete(c *gin.Context) {
	var shop model.AliShop
	err := c.ShouldBindJSON(&shop)
	if err != nil {
		yzResponse.FailWithMessage("解析参数失败", c)
		return
	}
	err = service.Delete(shop)
	if err != nil {
		yzResponse.FailWithMessage("操作失败", c)
	} else {
		yzResponse.Ok(c)
	}
}

func List(c *gin.Context) {
	var pageInfo request.SearchShop
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.List(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
