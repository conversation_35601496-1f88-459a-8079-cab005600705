package goods

import (
	model2 "ali-selected/model"
	"fmt"
	"math"
	"public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"testing"
	"yz-go/source"
)

func TestYzh_ImportGoodsRun(t *testing.T) {
	var skuPrice model.Goods

	skuPrice.CostPrice = uint(math.Floor(13.2 * 100)) //成本

	skuPrice.Source = common.ALJX_SOURCE

	var aliShop model2.AliShop
	source.DB().Where("name=?", "kkkk").First(&aliShop)

	err, costPrice, price, originPrice, activityPrice, guidePrice := GetShopPricingPrice(skuPrice, aliShop)
	fmt.Print(guidePrice, activityPrice, originPrice, price, costPrice, err)

	//aaa := &AliJX{}
	//aaa.InitSetting(42)
	//aaa.ProductUpdate()

	//ProductUpdate()

	//var info request.GetGoodsSearch
	//info.SearchWords = "无接缝毛巾圈头绳发圈女皮筋扎头发高弹耐用韩版网红厂家直销发绳"
	//
	//aaa.ImportGoodsRun(info)
	//
	//return

	//var info request.GetCategoryChild

	//aaa.GetCategoryChild(71, info)
	//
	//return

	//aaa.UpdateGoodsRun()

	//var ctx = context.Background()
	//token, redisTokenErr := source.Redis().Get(ctx, YzhTokenKey).Result()

}

func TestGetTestAC(t *testing.T) {
	//GetTestAC()
}

func TestAliJX_getPageList(t *testing.T) {

}

func TestAliJX_SynergeticProcess(t *testing.T) {

	var aljx AliJX
	aljx.InitSetting(47)
	var item []int

	item = append(item, ************)

	aljx.ImportSeparatelySynergeticProcess(item)

}

func TestAliJX_GetCategoryInfo(t *testing.T) {
	var aljx AliJX
	aljx.InitSetting(47)
	var item []int

	item = append(item, ************)

	var info request.GetCategoryChild
	aljx.GetCategoryInfo(54, info)

}
