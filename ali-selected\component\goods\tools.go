package goods

import (
	model3 "ali-selected/model"
	catemodel "category/model"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/xingliuhua/leaf"
	"gorm.io/gorm"
	pmodel "product/model"
	pMq "product/mq"
	"public-supply/common"
	"public-supply/model"
	setting2 "public-supply/setting"
	"sort"
	"strconv"
	"sync"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
)

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

func GetIdArr(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}

func YzhGetIdArr(list []model.YzhGoodsDetail) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.RESULTDATA.PRODUCTDATA.ProductId)
	}
	return

}
func GetSnList(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		sn, _ := strconv.Atoi(elem.SN)
		arrIds = append(arrIds, sn)
	}
	return

}
func splitArray(arr []model.Goods, num int64) [][]model.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]model.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]model.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// 分割数组，根据传入的数组和分割大小，将数组分割为大小等于指定大小的多个数组，如果不够分，则最后一个数组元素小于其他数组
func SplitArray(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

var settingMap = map[string]model.SupplySetting{}

func GetShopPricingPrice(elem model.Goods, aliShop model3.AliShop) (err error, costPrice uint, salePrice, originPrice, activityPrice, guidePrice uint) {

	if aliShop.IsOpen == "0" {
		var intX uint64

		var pricingData model3.PricingData
		err = json.Unmarshal([]byte(aliShop.Strategy), &pricingData)
		if err != nil {
			return
		}

		intX, err = strconv.ParseUint(pricingData.Cost, 10, 32)
		costPrice = elem.CostPrice * uint(intX) / 100

		intX, err = strconv.ParseUint(pricingData.Price, 10, 32)
		salePrice = elem.CostPrice * uint(intX) / 100

		intX, err = strconv.ParseUint(pricingData.Guide, 10, 32)
		guidePrice = elem.CostPrice * uint(intX) / 100

		intX, err = strconv.ParseUint(pricingData.Marketing, 10, 32)
		activityPrice = elem.CostPrice * uint(intX) / 100

		intX, err = strconv.ParseUint(pricingData.Origin, 10, 32)
		originPrice = elem.CostPrice * uint(intX) / 100

	}

	return
}

func GetPricingPrice(elem model.Goods, key string) (err error, costPrice uint, salePrice, originPrice, activityPrice, guidePrice uint) {
	var dat model.SupplySetting
	//if settingMap[key].BaseInfo.AppKey == "" {
	var setting model2.SysSetting

	log.Log().Info("空 dat 从新设置")
	err, setting = setting2.GetSetting(key)
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}
	//	settingMap[key] = dat
	//
	//} else {
	//	dat = settingMap[key]
	//}

	var intX uint64

	if dat.Pricing.Strategy == 2 { //本地定价策略关闭

		if elem.Source == common.ALJX_SOURCE { //ali jingxuan

			//供货价

			if dat.Pricing.YzhNewSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhNewSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.YzhNewSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhNewSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			//建议零售价
			if dat.Pricing.YzhNewSupplyOrigin == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyOriginAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhNewSupplyOrigin == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyOriginMarketing, 10, 32)
				originPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//建议零售价
			if dat.Pricing.YzhNewSupplyActivity == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhNewSupplyActivity == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyActivityMarketing, 10, 32)
				activityPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//建议零售价
			if dat.Pricing.YzhNewSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhNewSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhNewSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

	} else {
		salePrice = elem.GuidePrice
		costPrice = elem.AgreementPrice
	}

	return

}

var (
	category []model.Category
	mutex    sync.Mutex
)

//获取本地策略价格

func GetArrIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}
func GetSkuPrice(skuList []pmodel.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}

func FindChildCategory(id uint, pid uint) {

	for _, elem := range category {
		var paid uint
		if elem.Source == 2 || elem.Source == 6 || elem.Source == 7 || elem.Source == 1 {
			paid = elem.ThirdId
		}
		if elem.Source == 0 {
			paid = elem.Id
		}

		if elem.ParentId == id {
			var catei = new(catemodel.Category)
			catei.Name = elem.Title
			catei.Level = elem.Level + 1
			catei.ParentID = pid
			catei.Source = elem.Source
			catei.Sort = elem.Sort
			catei.IsDisplay = elem.State
			err, cid := CreateCategory(catei)
			fmt.Println("寻找parentid=", id)
			if id > 0 {

				FindChildCategory(paid, cid)
			}
			if err != nil {
				fmt.Println("插入失败", elem.Title, elem.Id, err)
			}

		}

	}

}

func CreateCategory(category *catemodel.Category) (err error, id uint) {

	var fCategory *catemodel.Category
	err = source.DB().Where("level = ? ", category.Level).Where("name = ?", category.Name).First(&fCategory).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = source.DB().Create(&category).Error

		if err != nil {
			return
		}

	}

	return err, category.ID

}

func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func GetKeyValueB(Values []model.SpecsValue, specValueIds string, speceNameId int) (list string) {

	for _, itemValue := range Values {
		if specValueIds == strconv.Itoa(itemValue.ID) && speceNameId == itemValue.SpecNameID {
			list = itemValue.Name
			return
		}
	}
	return
}
func CreateGoods(goodsList []*pmodel.Product) (err error) {

	for _, goods := range goodsList {

		err = source.DB().Create(&goods).Error
		if err != nil {
			fmt.Println("插入失败", err)
		}

		err = source.DB().Create(&model.SupplyGoods{
			SupplyGoodsID:  goods.SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		}).Error

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)

	}

	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}

func GetIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

type MapEntryHandler func(string, string)
type MapEntryHandlerMap func(string, interface{})

// func printKeyValue(key string, value string) {
// 	fmt.Printf("key=%s, value=%s\n", key, value)
// }

// 按字母顺序遍历map
func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}
func traverseMapInStringOrderMap(params map[string]interface{}, handler MapEntryHandlerMap) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}

func HmacSha256(data string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}
