package order

import (
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"public-supply/request"
	"strings"
	"testing"
	"yz-go/component/log"
)

type ATT struct {
	price string
}

func getSupplyOrder(name string) []string {
	// 打开文件
	file, err := excelize.OpenFile("C:\\Users\\<USER>\\" + name)
	if err != nil {
		fmt.Println(err)
		return nil
	}

	// 读取指定的Sheet，这里的Sheet1是默认的Sheet名
	sheetIndex := "Sheet1"
	rows := file.GetRows(sheetIndex)

	var list []string
	//rows := file.GetRows(sheetIndex)
	// 遍历读取到的行
	for index, row := range rows {
		if index == 0 {
			continue
		}
		for _, colCell := range row {
			sn := strings.ReplaceAll(colCell, "-", "")
			list = append(list, sn)

		}

	}
	return list
}

func getExcel(name string) []string {
	// 打开文件
	file, err := excelize.OpenFile(name)
	if err != nil {
		fmt.Println(err)
		return nil
	}

	var list []string
	// 读取指定的Sheet，这里的Sheet1是默认的Sheet名
	sheetIndex := "Sheet1"
	rows := file.GetRows(sheetIndex)
	// 遍历读取到的行
	for i, row := range rows {

		if i == 0 {
			continue
		}
		for index, colCell := range row {

			if index == 0 && colCell != "" {

				list = append(list, colCell)
			}

		}

	}
	return list
}

func TestAliJX_AfterSale(t *testing.T) {

	data1 := getExcel("C:\\Users\\<USER>\\Desktop\\excel\\3.xlsx")
	//data2:=getExcel("C:\\Users\\<USER>\\Desktop\\excel\\2.xls")
	//data3:=getExcel("C:\\Users\\<USER>\\Desktop\\excel\\3.xls")
	aljxdata := getSupplyOrder("supply_orders.xlsx")
	albbdata := getSupplyOrder("supply_orders-albb.xlsx")
	for _, row := range data1 {
		//for _, colCell := range row {
		//	fmt.Print(colCell, "\t")

		if !collection.Collect(aljxdata).Contains(row) && !collection.Collect(albbdata).Contains(row) {

			if row == "" {
				fmt.Println(row)
			}
			log.Log().Error("无效单号", zap.Any("", row))
		}

		//}
		//fmt.Println()
	}

	return
	var aljx AliJX
	aljx.InitSetting(47)

	var requesta request.RequestSaleBeforeCheck

	var address request.ReceivingInformation

	address.Street = ""
	address.Description = ""
	address.City = "常德市"
	address.Area = "武陵区"
	address.Province = "湖南省"
	address.Phone = "18073681633"
	address.Consignee = "郑圣强"
	requesta.Address = address
	var GoodsSpu request.GoodsSpus
	var GoodsSpuA request.GoodsSpu
	GoodsSpuA.Sku.Sku = 4680191
	GoodsSpuA.Number = 1
	GoodsSpu = append(GoodsSpu, GoodsSpuA)
	//var  GoodsSpu  request.GoodsSpus
	requesta.Skus = GoodsSpu

	requesta.LocalSkus = GoodsSpu
	aljx.OrderBeforeCheck(requesta)

}

func TestAliJX_AddShop(t *testing.T) {
	var aljx AliJX
	aljx.InitSetting(47)
	aljx.AddShop()

}
