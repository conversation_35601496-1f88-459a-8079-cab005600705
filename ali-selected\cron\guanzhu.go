package cron

import (
	"ali-selected/component/goods"
	request2 "ali-selected/request"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	url2 "net/url"
	product_model "product/model"
	model3 "public-supply/model"
	setting2 "public-supply/setting"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

var dataSetting model3.SupplySetting

func FollowInitSetting(taskID int) {
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dataSetting)
	if err != nil {

		return
	}
	FollowProduct()
}

func FollowProduct() (err error) {

	var product []product_model.Product
	err = source.DB().Where("source=?", 116).Find(&product).Error
	if len(product) > 0 {
		//FollowInitSetting(int(product[0].GatherSupplyID))
		for _, item := range product {
			productID := strconv.Itoa(int(item.SourceGoodsID))
			AliFollow(productID)
		}
	}

	return
}

func AliFollow(productID string) (err error, CompanyName, No string) {

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.follow/" + dataSetting.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", dataSetting.BaseInfo.Token)
	reqData.Add("productId", productID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", goods.Sign(url, dataSetting.BaseInfo.AppSecret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	log.Log().Info("info", zap.Any("阿里关注商品", string(resData)), zap.Any("商品id", productID))

	var ResOrderData request2.Follow

	json.Unmarshal(resData, &ResOrderData)
	if ResOrderData.Message != "success" {
		log.Log().Error("err", zap.Any("阿里关注商品失败", ResOrderData), zap.Any("商品id", productID))
	}

	return

}
