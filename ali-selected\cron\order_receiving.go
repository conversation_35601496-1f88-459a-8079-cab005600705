package cron

import (
	"ali-selected/component/goods"
	request2 "ali-selected/request"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	url2 "net/url"
	v1 "order/api/v1"
	omodel "order/model"
	orderRequest2 "order/request"
	"public-supply/common"
	"public-supply/model"
	express2 "shipping/express"
	"strings"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func PushAlijxOrderDeliverHandle() {

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.SUPPLY_ALJX).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		OrderCron(int(v.ID))

	}

}
func OrderCron(gid int) {

	InitSetting(gid)
	task := cron.Task{
		Key:  "alijxorderdelivercheckcron",
		Name: "alijx供应链发货查询定时任务cron",
		Spec: "0 0 */2 * * *",
		Handle: func(task cron.Task) {
			OrderDeliverCron()
		},
		Status: cron.ENABLED,
	}

	cron.PushTask(task)
}

func AlibbOrderSelect(orderID string, productID int64) (err error, CompanyName, No string) {
	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/" + datsetting.BaseInfo.AppKey

	reqData := url2.Values{}
	reqData.Add("access_token", datsetting.BaseInfo.Token)
	reqData.Add("orderId", orderID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", goods.Sign(url, datsetting.BaseInfo.AppSecret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	var ResOrderData request2.ResOrderData

	json.Unmarshal(resData, &ResOrderData)
	if ResOrderData.Success != "true" {
		log.Log().Info("alibb供应链代发货订单查询错误", zap.Any("info", string(resData)))

		return
	}

	for _, item := range ResOrderData.Result.ProductItems {

		if item.ProductID == productID {

			for _, LogisticsItems := range ResOrderData.Result.NativeLogistics.LogisticsItems {

				if strings.Contains(LogisticsItems.SubItemIds, item.SubItemIDString) {
					CompanyName = LogisticsItems.LogisticsCompanyName
					No = LogisticsItems.LogisticsBillNo
					return
					//err, CompanyName, No = ali.GetLogisticsInfos(item.SubItemIDString)

				}

			}

			return

		}

	}

	fmt.Println(string(resData))

	return

}

func OrderDeliverCron() {
	log.Log().Info("aljx供应链代发货订单查询OrderDeliverCron")

	var deliverOrder []omodel.Order
	err := source.DB().Preload("OrderItems").Preload("OrderItems.Product").Where("status=? and gather_supply_type=?  ", 1, 116).Find(&deliverOrder).Error
	if err != nil {
		log.Log().Info("aljx供应链代发货订单查询", zap.Any("info", err))
		return
	}

	if len(deliverOrder) == 0 {
		return
	}

	for _, od := range deliverOrder {
		for _, odItem := range od.OrderItems {

			if odItem.SendStatus == 1 {
				continue
			}
			//log.Log().Info("aljx查询订单sku是否发货", zap.Any("sku", odItem), zap.Any("订单sn", od.OrderSN))
			//
			//log.Log().Info("Aljx定时查询发货信息", zap.Any("info", od.GatherSupplySN), zap.Any("info", odItem.Product.SourceGoodsID))

			var CompanyName, No string
			err, CompanyName, No = AlibbOrderSelect(odItem.GatherSupplySN, int64(odItem.Product.SourceGoodsID))
			if err != nil {
				log.Log().Error("AljxOrderSelecterr当前订单信息未查询到物流", zap.Any("info", err))
				continue
			}
			var orderRequest v1.HandleOrderRequest
			var code string

			if CompanyName == "" || No == "" {
				//log.Log().Error("alxj当前订单信息未查询到物流", zap.Any("info", odItem))
				continue
			}

			err, code = ExpressList(CompanyName)
			if err != nil {
				log.Log().Error("aljx查询物流信息错误3", zap.Any("info", err.Error()))
				continue
			}

			if code == "" {
				log.Log().Error("aljx当前订单信息未查询到物流code", zap.Any("info", code))
				log.Log().Error("aljx当前订单信息未查询到物流code", zap.Any("info", CompanyName))
				continue
			}
			var ids = []orderRequest2.OrderItemSendInfo{{ID: odItem.ID, Num: odItem.Qty}}
			orderRequest.OrderID = od.ID
			orderRequest.ExpressNo = No
			orderRequest.OrderItemIDs = ids
			orderRequest.CompanyCode = code
			log.Log().Info("aljx发货信息", zap.Any("info", orderRequest))
			err = ExpressSent(orderRequest)
			if err != nil {
				continue
			}

		}

	}

}

// 获取快递code
func ExpressList(name string) (err error, code string) {

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}
