package listener

import (
	v1 "ali-selected/api/v1"
	model2 "ali-selected/model"
	"ali-selected/mq"
	"go.uber.org/zap"
	"yz-go/component/log"
)

func PushAljxCallBackHandles() {
	log.Log().Info("注册阿里aljx callback队列监听")
	mq.PushHandles("aljxCallBack", func(reqData model2.CallData) (err error) {

		log.Log().Info("阿里aljx回调消息mq 接受", zap.Any("info", reqData))

		if reqData.Type == "ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS" {
			err = v1.CallBackOrderService(reqData.Data)

		} else if reqData.Type == "PRODUCT_RELATION_VIEW_PRODUCT_AUDIT" {
			err = v1.CallBackAUDITService(reqData.Data)
		} else if reqData.Type == "RELATION_VIEW_PRODUCT_NEW_OR_MODIFY" {
			err = v1.CallBackProductModifyService(reqData.Data)
		} else {
			err = v1.CallBackService(reqData.Data)
		}
		log.Log().Info("阿里aljx回调消息mq 完成", zap.Any("info", err))

		return nil
	})
}
