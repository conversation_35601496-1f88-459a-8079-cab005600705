package listener

import (
	goods "ali-selected/component/goods"
	model2 "ali-selected/model"
	"go.uber.org/zap"
	ProductModel "product/model"
	"product/mq"
	"public-supply/common"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

func PushaliGoodsLocalProductImportStatus() {

	mq.PushHandles("aliGoodsLocalProductImportStatus", 1, func(product mq.ProductMessage) error {

		var productModel ProductModel.Product
		err := source.DB().Unscoped().Where("id=?", product.ProductID).First(&productModel).Error
		if err != nil {
			log.Log().Info("aliGoodsLocalProductImportStatus err", zap.Any("err", err), zap.Any("id", product.ProductID))
			return nil
		}

		if productModel.Source != common.ALJX_SOURCE {
			return nil
		}
		if product.MessageType == mq.Create {

			source.DB().Model(&model2.AliGoods{}).Where("product_id=?", productModel.SourceGoodsID).UpdateColumn("is_import", 1)
			var goodsAljx goods.AliJX
			goodsAljx.InitSetting(productModel.GatherSupplyID)
			localID := strconv.Itoa(int(productModel.ID))
			SourceGoodsID := strconv.Itoa(int(productModel.SourceGoodsID))
			goodsAljx.AddRelation(localID, SourceGoodsID)

		}

		if product.MessageType == mq.Delete {

			source.DB().Model(&model2.AliGoods{}).Where("product_id=?", productModel.SourceGoodsID).UpdateColumn("is_import", 0)
		}

		return nil
	})

}
