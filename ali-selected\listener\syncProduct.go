package listener

import (
	"ali-selected/component/goods"
	model2 "ali-selected/model"
	"ali-selected/productSync"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
)

var aljx goods.AliJX

func PushAljxProductSyncHandles() {
	log.Log().Info("注册阿里aljx PushAljxProductSyncHandles队列监听")
	mq.PushHandles("aljxProductSyncHandles", func(reqData model2.SyncProductData) (err error) {

		if aljx.SupplyID == 0 {

			aljx.InitSetting(reqData.SupplyID)

		}

		var Page = 1
		for {

			page := strconv.Itoa(Page)
			err = aljx.RunUpdateGoodsRun(page, reqData)
			if err != nil {
				if err.Error() == "notfind" {
					return nil
				} else {
					continue
				}
			}
			Page++

		}

		log.Log().Info("阿里aljx 同步商品 分类,页", zap.Any("info", reqData))

		return nil
	})
}
