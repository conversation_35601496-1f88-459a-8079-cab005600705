package model

import (
	_ "embed"
	"encoding/json"
	"fmt"
	"gin-vue-admin/admin/model"
	model2 "order/model"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		AliGoods{},
		AliShop{},
		AliProductUpdateRecord{},
	)

	// 菜单,权限
	menus := []model.SysMenu{}
	//sysBaseMenu := []model.SysBaseMenu{}

	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)

	if source.DB().Migrator().HasTable(&model2.Order{}) {
		if source.DB().Migrator().HasColumn(&model2.Order{}, "gather_supply_sn") {
			err = source.DB().Migrator().AlterColumn(&model2.Order{}, "gather_supply_sn")
			if err != nil {
				fmt.Print(err)
			}
		}
	}

	return

}
