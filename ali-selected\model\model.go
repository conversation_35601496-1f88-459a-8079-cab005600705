package model

import (
	"encoding/json"
	"gorm.io/gorm"
	"yz-go/source"
)

type AliProductUpdateRecord struct {
	source.Model
	Type         string `json:"type"`
	ProductID    uint   `json:"product_id"`
	UpdateBefore string `json:"update_before"`
	UpdateAfter  string `json:"update_after"`
}

type CallData struct {
	BizKey   string  `json:"bizKey"`
	Data     AliSale `json:"data"`
	GmtBorn  int64   `json:"gmtBorn"`
	MsgId    int64   `json:"msgId"`
	Type     string  `json:"type"`
	UserInfo string  `json:"userInfo"`
}

type SyncProductData struct {
	Cate      string `json:"cate"`
	Cate1     int    `json:"cate1"`
	Cate2     int    `json:"cate2"`
	Page      string `json:"page"`
	Cate1Name string `json:"Cate1Name"`
	Cate2Name string `json:"Cate2Name"`
	SupplyID  uint   `json:"supply_id"`
}

type Ali<PERSON>ale struct {
	OrderId    int64  `json:"orderId"`
	ProductIds string `json:"productIds"`
	OfferId    string `json:"offerId"`
	Type       string `json:"type"`
	OpenUid    string `json:"openUid"`
}

type ResToken struct {
	AccessToken         string `json:"access_token"`
	AliId               string `json:"aliId"`
	RefreshToken        string `json:"refresh_token"`
	ResourceOwner       string `json:"resource_owner"`
	ExpiresIn           string `json:"expires_in"`
	RefreshTokenTimeout string `json:"refresh_token_timeout"`
	MemberId            string `json:"memberId"`
	ErrorInfo           string `json:"error"`
	ErrorDescription    string `json:"error_description"`
}

type ALIJXGoodsList struct {
	Result struct {
		Success  bool         `json:"success"`
		Code     string       `json:"code"`
		Result   []ALIJXGoods `json:"result"`
		PageInfo struct {
			CurrentPage  int `json:"currentPage"`
			TotalRecords int `json:"totalRecords"`
			PageSize     int `json:"pageSize"`
		} `json:"pageInfo"`
	} `json:"result"`
}

type ALIJXGoods struct {
	ItemId      int64  `json:"itemId"`
	ImgUrl      string `json:"imgUrl"`
	Title       string `json:"title"`
	SalesCnt90D int    `json:"salesCnt90d"`
	MaxPrice    int    `json:"maxPrice"`
	MinPrice    int    `json:"minPrice"`
	ServiceList []struct {
		Code string `json:"code"`
		Name string `json:"name"`
	} `json:"serviceList"`
}

type Setting struct {
	Key    string `json:"key"`
	ID     string `json:"id"`
	Secret string `json:"secret"`
	Token  string `json:"token"`
	Domain string `json:"domain"`
	Enable uint   `json:"enable"`
}

type ALJXProduct struct {
	source.Model
	ApprovedTime string `json:"approvedTime"`
	Attributes   []struct {
		AttributeID   int    `json:"attributeID"`
		AttributeName string `json:"attributeName"`
		IsCustom      bool   `json:"isCustom"`
		Value         string `json:"value"`
	} `json:"attributes"`
	BizType      int    `json:"bizType"`
	CargoNum     string `json:"cargoNum"`
	CategoryID   int    `json:"categoryID"`
	CategoryName string `json:"categoryName"`
	CreateTime   string `json:"createTime"`
	Description  string `json:"description"`
	ExpireTime   string `json:"expireTime"`
	ExtendInfos  []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"extendInfos"`
	Image struct {
		Images []string `json:"images"`
	} `json:"image"`
	Language         string `json:"language"`
	LastUpdateTime   string `json:"lastUpdateTime"`
	MainVedio        string `json:"mainVedio,omitempty"`
	ModifyTime       string `json:"modifyTime"`
	PeriodOfValidity int    `json:"periodOfValidity"`
	PictureAuth      bool   `json:"pictureAuth"`
	ProductID        int64  `json:"productID"`
	ProductType      string `json:"productType"`
	QualityLevel     int    `json:"qualityLevel"`
	ReferencePrice   string `json:"referencePrice"`
	ReserveInfo      struct {
		SupportReserve    bool `json:"supportReserve"`
		MaxQuantity       int  `json:"maxQuantity,omitempty"`
		MinQuantity       int  `json:"minQuantity,omitempty"`
		ReserveRangeInfos []struct {
			Period   int `json:"period"`
			Quantity int `json:"quantity"`
		} `json:"reserveRangeInfos,omitempty"`
	} `json:"reserveInfo"`
	SaleInfo struct {
		AmountOnSale     float64 `json:"amountOnSale"`
		InvReduceType    string  `json:"invReduceType"`
		MinOrderQuantity int     `json:"minOrderQuantity"`
		MixWholeSale     bool    `json:"mixWholeSale"`
		PriceAuth        bool    `json:"priceAuth"`
		PriceRanges      []struct {
			Price         float64 `json:"price"`
			StartQuantity int     `json:"startQuantity"`
		} `json:"priceRanges"`
		QuoteType          int     `json:"quoteType"`
		SaleType           string  `json:"saleType"`
		SupportOnlineTrade bool    `json:"supportOnlineTrade"`
		Unit               string  `json:"unit"`
		ConsignPrice       float64 `json:"consignPrice,omitempty"`
		Retailprice        float64 `json:"retailprice,omitempty"`
		BatchNumber        int     `json:"batchNumber,omitempty"`
		Sellunit           string  `json:"sellunit,omitempty"`
	} `json:"saleInfo"`
	SevenDaysRefunds bool `json:"sevenDaysRefunds"`
	ShippingInfo     struct {
		FreightTemplateID    int     `json:"freightTemplateID"`
		SendGoodsAddressId   int     `json:"sendGoodsAddressId"`
		SendGoodsAddressText string  `json:"sendGoodsAddressText"`
		UnitWeight           float64 `json:"unitWeight,omitempty"`
		OfferSuttleWeight    float64 `json:"offerSuttleWeight,omitempty"`
		OfferHeight          float64 `json:"offerHeight,omitempty"`
		OfferLength          float64 `json:"offerLength,omitempty"`
		OfferWidth           float64 `json:"offerWidth,omitempty"`
	} `json:"shippingInfo"`
	SkuInfos []struct {
		AmountOnSale int `json:"amountOnSale"`
		Attributes   []struct {
			AttributeID    int    `json:"attributeID"`
			AttributeName  string `json:"attributeName"`
			AttributeValue string `json:"attributeValue"`
			SkuImageUrl    string `json:"skuImageUrl,omitempty"`
		} `json:"attributes"`
		CargoNumber  string  `json:"cargoNumber"`
		ConsignPrice float64 `json:"consignPrice"`
		RetailPrice  float64 `json:"retailPrice"`
		SkuCode      string  `json:"skuCode"`
		SkuId        int64   `json:"skuId"`
		SpecId       string  `json:"specId"`
		Price        float64 `json:"price,omitempty"`
	} `json:"skuInfos,omitempty"`
	Status             string `json:"status"`
	Subject            string `json:"subject"`
	DetailVedio        string `json:"detailVedio,omitempty"`
	LastRepostTime     string `json:"lastRepostTime,omitempty"`
	ProcessingOfferId  int64  `json:"processingOfferId,omitempty"`
	PrivateChannelInfo struct {
		OfferPrivatePriceInfo []struct {
			PrivatePriceInfo string `json:"privatePriceInfo"`
			SkuId            int64  `json:"skuId"`
		} `json:"offerPrivatePriceInfo"`
	} `json:"privateChannelInfo,omitempty"`
}

func (o *AliGoods) BeforeCreate(tx *gorm.DB) (err error) {
	if len(o.Image.Images) > 0 {
		jsonImg := "https://cbu01.alicdn.com/" + o.Image.Images[0]
		o.MainImage = jsonImg

		imgArr, _ := json.Marshal(o.Image.Images)
		o.JsonImage = string(imgArr)
	}

	if len(o.ProductSkuInfos) > 0 {
		jsonSku, _ := json.Marshal(o.ProductSkuInfos)
		o.JsonSku = string(jsonSku)
	}
	saleInfo, _ := json.Marshal(o.SaleInfo)
	o.JsonSaleInfo = string(saleInfo)
	//var MaxProfitRate float64
	//for _, item := range o.SkuInfos {
	//	ProfitRate := utils.Decimal((item.RetailPrice - item.ConsignPrice) / item.ConsignPrice)
	//	if ProfitRate > MaxProfitRate {
	//		MaxProfitRate = ProfitRate
	//		o.RetailPrice = item.RetailPrice
	//		o.ConsignPrice = item.ConsignPrice
	//		o.ProfitRate = ProfitRate
	//	}
	//}
	//
	//if MaxProfitRate == 0 && len(o.SkuInfos) > 0 {
	//	o.RetailPrice = o.SkuInfos[0].RetailPrice
	//	o.ConsignPrice = o.SkuInfos[0].ConsignPrice
	//}

	//if o.SaleInfo.ConsignPrice > 0 && o.SaleInfo.Retailprice > 0 {
	//	ProfitRate := utils.Decimal((o.SaleInfo.Retailprice - o.SaleInfo.ConsignPrice) / o.SaleInfo.ConsignPrice)
	//	o.ProfitRate = ProfitRate
	//	o.RetailPrice = o.SaleInfo.Retailprice
	//o.RetailPrice = o.SaleInfo.ConsignPrice
	//o.ConsignPrice = o.SaleInfo.ConsignPrice
	//
	return
}

type Attributes []Attribute
type Attribute struct {
	AttributeID   int    `json:"attributeID"`
	AttributeName string `json:"attributeName"`
	IsCustom      bool   `json:"isCustom"`
	Value         string `json:"value"`
}

type AliGoods struct {
	source.Model
	ImgUrl          string `json:"imgUrl"`
	Title           string `json:"title" gorm:"index"`
	MaxPrice        string `json:"maxPrice"`
	MinPrice        string `json:"minPrice"`
	WangwangAccount string `json:"wangwangAccount" gorm:"index"`
	CateName1       string `json:"CateName1"`
	CateName2       string `json:"CateName2"`
	CateID1         int    `json:"CateID1" gorm:"index"`
	CateID2         int    `json:"CateID2" gorm:"index"`
	//CateName3    string `json:"CateName3"`
	ApprovedTime string `json:"approvedTime"`

	JsonAttributes string     `json:"json_attributes"gorm:"longtext"`
	Attributes     Attributes `json:"attributes"gorm:"-"`

	BizType      int    `json:"bizType"`
	CargoNum     string `json:"cargoNum"`
	CategoryID   int    `json:"categoryID" gorm:"index"`
	CategoryName string `json:"categoryName"`
	CreateTime   string `json:"createTime"`
	Description  string `json:"description" gorm:"type:longtext"`

	JsonSku         string        `json:"json_sku" gorm:"type:longtext"`
	JsonSaleInfo    string        `json:"json_sale_info" gorm:"type:longtext"`
	SaleInfo        SaleInfos     `json:"saleInfo" gorm:"-"`
	ProductSkuInfos SkuInfos      `json:"productSkuInfos" gorm:"-"`
	ExpireTime      string        `json:"expireTime"`
	Image           AliImg        `json:"image" gorm:"-"`
	ExtendInfos     []ExtendInfos `json:"extendInfos" gorm:"-"`

	JsonImage        string `json:"json_image" gorm:"type:longtext"`
	MainImage        string `json:"main_image"`
	Language         string `json:"language"`
	LastUpdateTime   string `json:"lastUpdateTime"`
	MainVedio        string `json:"mainVedio,omitempty"`
	ModifyTime       string `json:"modifyTime"`
	PeriodOfValidity int    `json:"periodOfValidity"`
	PictureAuth      bool   `json:"pictureAuth"`
	ProductID        int64  `json:"productID" gorm:"uniqueIndex"`
	ProductType      string `json:"productType"`
	QualityLevel     int    `json:"qualityLevel"`
	ReferencePrice   string `json:"referencePrice"`

	SevenDaysRefunds bool `json:"sevenDaysRefunds"`

	Status            string  `json:"status"`
	Subject           string  `json:"subject" gorm:"index"`
	DetailVedio       string  `json:"detailVedio,omitempty"`
	LastRepostTime    string  `json:"lastRepostTime,omitempty"`
	ProcessingOfferId int64   `json:"processingOfferId,omitempty"`
	ConsignPrice      float64 `json:"consign_price"`
	RetailPrice       float64 `json:"retail_price"`
	ProfitRate        float64 `json:"profit_rate"`
	IsImport          uint    `json:"is_import" gorm:"default:0"`
}

type AliImg struct {
	Images []string `json:"images"`
}

type SaleInfos struct {
	AmountOnSale     float64 `json:"amountOnSale"`
	ConsignPrice     float64 `json:"consignPrice"`
	InvReduceType    string  `json:"invReduceType"`
	MinOrderQuantity int     `json:"minOrderQuantity"`
	MixWholeSale     bool    `json:"mixWholeSale"`
	PriceAuth        bool    `json:"priceAuth"`
	PriceRanges      []struct {
		Price         float64 `json:"price"`
		StartQuantity int     `json:"startQuantity"`
	} `json:"priceRanges"`
	QuoteType          int     `json:"quoteType"`
	Retailprice        float64 `json:"retailprice"`
	SaleType           string  `json:"saleType"`
	SupportOnlineTrade bool    `json:"supportOnlineTrade"`
	Unit               string  `json:"unit"`
}

type SkuInfos []SkuInfo
type SkuInfo struct {
	CargoNumber  string  `json:"cargoNumber"`
	AmountOnSale int     `json:"amountOnSale"`
	Price        float64 `json:"price"`
	SkuId        int64   `json:"skuId"`
	SpecId       string  `json:"specId"`
	ConsignPrice float64 `json:"consignPrice"`
	JxhyPrice    float64 `json:"jxhyPrice"`
	Attributes   []struct {
		AttributeID    int    `json:"attributeID"`
		AttributeValue string `json:"attributeValue"`
		SkuImageUrl    string `json:"skuImageUrl"`
		AttributeName  string `json:"attributeName"`
	} `json:"attributes"`
	MultipleConsignPrice float64 `json:"multipleConsignPrice"`
}

type ExtendInfos struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ALJXProductList struct {
	InstanceCode    string   `json:"instanceCode"`
	WangwangAccount string   `json:"wangwangAccount"`
	ProductInfo     AliGoods `json:"productInfo"`
}

type ALCategory struct {
	CategoryInfo []struct {
		CategoryID     int    `json:"categoryID"`
		Name           string `json:"name"`
		IsLeaf         bool   `json:"isLeaf"`
		ParentIDs      []int  `json:"parentIDs"`
		ChildCategorys []struct {
			Id           int    `json:"id"`
			Name         string `json:"name"`
			IsLeaf       bool   `json:"isLeaf"`
			CategoryType string `json:"categoryType"`
		} `json:"childCategorys"`
		MinOrderQuantity int    `json:"minOrderQuantity"`
		CategoryType     string `json:"categoryType"`
	} `json:"categoryInfo"`
	Succes string      `json:"succes"`
	Total  interface{} `json:"total"`
}

type ALJXDetail struct {
	Result struct {
		Success bool              `json:"success"`
		Result  []ALJXProductList `json:"result"`
	} `json:"result"`
}

type SkuMap []Sku

//type SkuMapSetting []SkuSetting

//	type SkuSetting struct {
//		Sku
//		AutoPay int64 `json:"auto_pay"`
//	}
type Sku struct {
	OfferId     int64  `json:"offerId"`
	SpecId      string `json:"specId"`
	Quantity    int64  `json:"quantity"`
	OutShopCode string `json:"outShopCode"`
	Channel     string `json:"channel"`
	OutItemCode string `json:"outItemCode"`
}

type ALJXNewDetail struct {
	ProductInfo struct {
		ProductID        int64  `json:"productID"`
		ProductType      string `json:"productType"`
		CategoryID       int    `json:"categoryID"`
		CategoryName     string `json:"categoryName"`
		ProductAttribute []struct {
			AttributeID   int    `json:"attributeID"`
			AttributeName string `json:"attributeName"`
			Value         string `json:"value"`
			IsCustom      bool   `json:"isCustom"`
		} `json:"productAttribute"`
		Status           string `json:"status"`
		Subject          string `json:"subject"`
		Description      string `json:"description"`
		Language         string `json:"language"`
		PeriodOfValidity int    `json:"periodOfValidity"`
		BizType          int    `json:"bizType"`
		PictureAuth      bool   `json:"pictureAuth"`
		SupplierUserId   string `json:"supplierUserId"`
		QualityLevel     int    `json:"qualityLevel"`
		SupplierLoginId  string `json:"supplierLoginId"`
		MainVedio        string `json:"mainVedio"`
		ReferencePrice   string `json:"referencePrice"`
		CreateTime       string `json:"createTime"`
		LastUpdateTime   string `json:"lastUpdateTime"`
		ExpireTime       string `json:"expireTime"`
		ModifyTime       string `json:"modifyTime"`
		ApprovedTime     string `json:"approvedTime"`
		DetailVedio      string `json:"detailVedio"`
		SellerLoginId    string `json:"sellerLoginId"`
		SellerId         int64  `json:"sellerId"`
		ProductImage     struct {
			Images []string `json:"images"`
		} `json:"productImage"`
		ProductSkuInfos []struct {
			CargoNumber  string  `json:"cargoNumber"`
			AmountOnSale int     `json:"amountOnSale"`
			Price        float64 `json:"price"`
			SkuId        int64   `json:"skuId"`
			SpecId       string  `json:"specId"`
			ConsignPrice float64 `json:"consignPrice"`
			JxhyPrice    float64 `json:"jxhyPrice"`
			Attributes   []struct {
				AttributeID    int    `json:"attributeID"`
				AttributeValue string `json:"attributeValue"`
				SkuImageUrl    string `json:"skuImageUrl"`
				AttributeName  string `json:"attributeName"`
			} `json:"attributes"`
			MultipleConsignPrice float64 `json:"multipleConsignPrice"`
		} `json:"productSkuInfos"`
		ProductSaleInfo struct {
			SupportOnlineTrade bool    `json:"supportOnlineTrade"`
			MixWholeSale       bool    `json:"mixWholeSale"`
			PriceAuth          bool    `json:"priceAuth"`
			AmountOnSale       float64 `json:"amountOnSale"`
			Unit               string  `json:"unit"`
			MinOrderQuantity   int     `json:"minOrderQuantity"`
			QuoteType          int     `json:"quoteType"`
			ConsignPrice       float64 `json:"consignPrice"`
			JxhyPrice          float64 `json:"jxhyPrice"`
			PriceRanges        []struct {
				StartQuantity int     `json:"startQuantity"`
				Price         float64 `json:"price"`
			} `json:"priceRanges"`
		} `json:"productSaleInfo"`
		ProductShippingInfo struct {
			FreightTemplateID    int     `json:"freightTemplateID"`
			UnitWeight           float64 `json:"unitWeight"`
			SendGoodsAddressId   int     `json:"sendGoodsAddressId"`
			SendGoodsAddressText string  `json:"sendGoodsAddressText"`
			SuttleWeight         float64 `json:"suttleWeight"`
			Width                float64 `json:"width"`
			Height               float64 `json:"height"`
			Length               float64 `json:"length"`
			FreightTemplate      []struct {
				AddressCodeText    string `json:"addressCodeText"`
				FromAreaCode       string `json:"fromAreaCode"`
				Id                 int    `json:"id"`
				ExpressSubTemplate struct {
					SubTemplateDTO struct {
						ChargeType    int  `json:"chargeType"`
						IsSysTemplate bool `json:"isSysTemplate"`
						ServiceType   int  `json:"serviceType"`
						Type          int  `json:"type"`
					} `json:"subTemplateDTO"`
					RateList []struct {
						IsSysRate      bool   `json:"isSysRate"`
						ToAreaCodeText string `json:"toAreaCodeText"`
						RateDTO        struct {
							FirstUnit    int `json:"firstUnit"`
							FirstUnitFee int `json:"firstUnitFee"`
							NextUnit     int `json:"nextUnit"`
							NextUnitFee  int `json:"nextUnitFee"`
						} `json:"rateDTO"`
					} `json:"rateList"`
				} `json:"expressSubTemplate"`
			} `json:"freightTemplate"`
			DistributionFreePostage bool `json:"distributionFreePostage"`
		} `json:"productShippingInfo"`
		ProductExtendInfos []struct {
			Key   string `json:"key"`
			Value string `json:"value"`
		} `json:"productExtendInfos"`
		SellStartTime    string `json:"sellStartTime"`
		SaleLimitAddress struct {
			LimitAddressCodes string `json:"limitAddressCodes"`
			Status            int    `json:"status"`
		} `json:"saleLimitAddress"`
		EncryptLogisticsOrderSupportChannel struct {
			SupportChannels []string `json:"supportChannels"`
		} `json:"encryptLogisticsOrderSupportChannel"`
	} `json:"productInfo"`
	Success bool `json:"success"`
}
