package route

import (
	v1 "ali-selected/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAliPublicRouter(Router *gin.RouterGroup) {
	AliRouter := Router.Group("ali")
	{
		// 基础设置

		AliRouter.POST("callBackMessage", v1.CallBackMessage) //配置设置

	}
}

func InitAliPrivateRoute(Router *gin.RouterGroup) {
	aliShop := Router.Group("aliShop")
	{
		// 基础设置
		aliShop.POST("create", v1.Create)
		aliShop.POST("delete", v1.Delete)
		aliShop.POST("list", v1.List)
		aliShop.POST("getChangeRecord", v1.GetChangeRecord) //变动记录

	}
}
