package service

import (
	"ali-selected/component/goods"
	request2 "ali-selected/request"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	url2 "net/url"
	"strings"
	"yz-go/component/log"
	"yz-go/utils"
)

func (ali *<PERSON>bb) AlibbOrderSelect(orderID string, productID int64) (err error, CompanyName, No string) {
	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/" + ali.Key

	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	reqData.Add("orderId", orderID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", goods.Sign(url, ali.Secret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	var ResOrderData request2.ResOrderData

	json.Unmarshal(resData, &ResOrderData)
	if ResOrderData.Success != "true" {
		log.Log().Info("alibb供应链代发货订单查询错误", zap.Any("info", string(resData)))

		return
	}

	for _, item := range ResOrderData.Result.ProductItems {

		if item.ProductID == productID {

			for _, LogisticsItems := range ResOrderData.Result.NativeLogistics.LogisticsItems {

				if strings.Contains(LogisticsItems.SubItemIds, item.SubItemIDString) {
					CompanyName = LogisticsItems.LogisticsCompanyName
					No = LogisticsItems.LogisticsBillNo
					return
					//err, CompanyName, No = ali.GetLogisticsInfos(item.SubItemIDString)

				}

			}

			return

		}

	}

	fmt.Println(string(resData))

	return

}
