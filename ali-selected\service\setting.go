package service

import (
	"ali-selected/model"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"strings"
	model2 "yz-go/model"
	"yz-go/source"
)

func SetSetting(setting model.Setting) (err error) {

	//var maps = make(map[string]string)
	strID := setting.ID

	id := strings.Replace(strID, "aliOpenSetting", "", 1)

	strID = id
	fmt.Println(id)

	mapData, _ := json.Marshal(setting)

	var sysSetting model2.SysSetting

	sysSetting.Key = "aliOpenSetting" + strID
	sysSetting.Value = string(mapData)
	err = source.DB().Where("`key` = ?", "aliOpenSetting"+strID).First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		source.DB().Where("`key` = ?", "aliOpenSetting"+strID).Create(&sysSetting)
	} else {
		sysSetting.Value = string(mapData)
		err = source.DB().Where("`key` = ?", "aliOpenSetting"+strID).Updates(&sysSetting).Error
	}
	err = nil

	return
}

func GetSetting(id string) (err error, sysSetting model2.SysSetting) {
	//strID := strconv.Itoa(int(id))
	err = source.DB().Table("sys_settings").Where("`key` = ?", id).First(&sysSetting).Error
	return
}
func GetSettingList() (err error, sysSetting []model2.SysSetting) {
	err = source.DB().Table("sys_settings").Where("`key` like  ?", "aliOpenSetting%").Find(&sysSetting).Error
	return
}
