package service

import (
	"ali-selected/model"
	"ali-selected/request"
	"errors"
	"yz-go/source"
)

func Create(shop model.AliShop) error {
	var selectShop model.AliShop
	source.DB().First(&selectShop)
	if shop.ID == 0 && selectShop.Name == shop.Name {
		return errors.New("店铺已存在,请勿重复添加")

	}

	return source.DB().Save(&shop).Error
}

func Delete(shop model.AliShop) error {
	return source.DB().Delete(&shop).Error

}

func List(info request.SearchShop) (err error, list interface{}, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.AliShop{})
	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}

	var brandss []model.AliShop
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&brandss).Error
	return err, brandss, total

}
