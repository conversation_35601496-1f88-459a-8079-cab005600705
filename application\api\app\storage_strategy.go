package app

import (
	service2 "application/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetStrategyData(c *gin.Context) {
	userID := utils.GetAppUserID(c)
	err, data, categoryData := service2.GetStrategyData(userID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"data": data, "categoryData": categoryData}, c)
}
