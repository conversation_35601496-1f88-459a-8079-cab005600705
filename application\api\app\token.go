package app

import (
	"application/middleware"
	"application/request"
	"application/response"
	gva_model "gin-vue-admin/admin/model"
	gvaRequest "gin-vue-admin/admin/model/request"
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"strconv"
	"time"
	"user/model"
	"user/service"
	"yz-go/component/log"
	"yz-go/config"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func GetToken(c *gin.Context) {
	var L request.GetToken
	err := c.ShouldBindJSON(&L)
	if err = utils.GVerify(L, utils.LoginVerify); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, userID, appID, shopID := middleware.CheckInformation(L)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var user model.User
	err = source.DB().First(&user, userID).Error
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage("登录失败! 用户不存在", c)
		return
	} else {
		tokenNext(c, user, appID, shopID)
	}
}

// 登录以后签发jwt
func tokenNext(c *gin.Context, user model.User, appID uint, shopID uint) {
	j := &middleware.JWT{SigningKey: []byte(config.Config().JWT.SigningKey)} // 唯一签名
	claims := gvaRequest.CustomClaims{
		UUID:       user.UUID,
		ID:         user.ID,
		AppID:      appID,
		ShopID:     shopID,
		BufferTime: config.Config().JWT.BufferTime, // 缓冲时间1天 缓冲时间内会获得新的token刷新令牌 此时一个用户会存在两个有效令牌 但是前端只留一个 另一个会丢失
		StandardClaims: jwt.StandardClaims{
			NotBefore: time.Now().Unix() - 1000,                            // 签名生效时间
			ExpiresAt: time.Now().Unix() + config.Config().JWT.ExpiresTime, // 过期时间 7天  配置文件
			Issuer:    "qmPlus",                                            // 签名的发行者
		},
	}
	token, err := j.CreateToken(claims)
	if err != nil {
		log.Log().Error("获取token失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取token失败", c)
		return
	}
	//开启多点登录后，APP接口将不支持重置秘钥就拉黑的操作，只能等待token到期
	if !config.Config().System.UseMultipoint {
		//支持多点登录，不会将之前存在的token拉黑，直接返回新token,
		yzResponse.OkWithDetailed(response.LoginResponse{
			Token:     token,
			ExpiresAt: claims.StandardClaims.ExpiresAt * 1000,
		}, "登录成功", c)
		return
	}
	if shopID > 0 {
		//多商城
		if err, jwtStr := service.GetRedisJWT("shopTokenKey" + strconv.Itoa(int(shopID))); err == redis.Nil {
			//之前不存在token，正常返回新token
			if err := service.SetRedisJWT(token, "shopTokenKey"+strconv.Itoa(int(shopID))); err != nil {
				log.Log().Error("设置登录状态失败", zap.Any("err", err))
				yzResponse.FailWithMessage("设置登录状态失败", c)
				return
			}
			yzResponse.OkWithDetailed(response.LoginResponse{
				Token:     token,
				ExpiresAt: claims.StandardClaims.ExpiresAt * 1000,
			}, "登录成功", c)
		} else if err != nil {
			//意外错误
			log.Log().Error("设置登录状态失败", zap.Any("err", err))
			yzResponse.FailWithMessage("设置登录状态失败", c)
			return
		} else {
			//之前存在token，将之前存在的token拉黑，并返回新的token，即单点登录
			log.Log().Info("海宏重新获取token，拉黑之前的token")

			var blackJWT gva_model.JwtBlacklist
			blackJWT.Jwt = jwtStr
			if err := service.JsonInBlacklist(blackJWT); err != nil {
				yzResponse.FailWithMessage("jwt作废失败", c)
				return
			}
			if err := service.SetRedisJWT(token, "shopTokenKey"+strconv.Itoa(int(shopID))); err != nil {
				yzResponse.FailWithMessage("设置登录状态失败", c)
				return
			}
			yzResponse.OkWithDetailed(response.LoginResponse{
				Token:     token,
				ExpiresAt: claims.StandardClaims.ExpiresAt * 1000,
			}, "登录成功", c)
		}
	} else {
		if err, jwtStr := service.GetRedisJWT("applicationTokenKey" + strconv.Itoa(int(appID))); err == redis.Nil {
			//之前不存在token，正常返回新token
			if err := service.SetRedisJWT(token, "applicationTokenKey"+strconv.Itoa(int(appID))); err != nil {
				log.Log().Error("设置登录状态失败", zap.Any("err", err))
				yzResponse.FailWithMessage("设置登录状态失败", c)
				return
			}
			yzResponse.OkWithDetailed(response.LoginResponse{
				Token:     token,
				ExpiresAt: claims.StandardClaims.ExpiresAt * 1000,
			}, "登录成功", c)
		} else if err != nil {
			//意外错误
			log.Log().Error("设置登录状态失败", zap.Any("err", err))
			yzResponse.FailWithMessage("设置登录状态失败", c)
			return
		} else {
			//之前存在token，将之前存在的token拉黑，并返回新的token，即单点登录
			log.Log().Info("海宏重新获取token，拉黑之前的token")

			var blackJWT gva_model.JwtBlacklist
			blackJWT.Jwt = jwtStr
			if err := service.JsonInBlacklist(blackJWT); err != nil {
				yzResponse.FailWithMessage("jwt作废失败", c)
				return
			}
			if err := service.SetRedisJWT(token, "applicationTokenKey"+strconv.Itoa(int(appID))); err != nil {
				yzResponse.FailWithMessage("设置登录状态失败", c)
				return
			}
			yzResponse.OkWithDetailed(response.LoginResponse{
				Token:     token,
				ExpiresAt: claims.StandardClaims.ExpiresAt * 1000,
			}, "登录成功", c)
		}
	}

}
