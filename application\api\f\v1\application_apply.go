package v1

import (
	"application/model"
	"application/request"
	"application/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func CreateApplicationApply(c *gin.Context) {
	var application model.Application
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	application.MemberId = int(ufv1.GetUserID(c))
	if err := service.CreateApplicationApply(application); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func FindApplicationApply(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	if err, reapplication := service.GetApplicationApply(userID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reapplication": reapplication}, c)
	}
}

func GetApplicationLevelList(c *gin.Context) {
	var pageInfo request.ApplicationLevelSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetApplicationLevelLFrontList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func FindApplication(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	if err, reapplication := service.GetApplicationByUserID(userID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reapplication": reapplication}, c)
	}
}
