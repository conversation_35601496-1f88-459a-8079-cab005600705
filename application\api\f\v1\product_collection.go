package v1

import (
	applicationModel "application/model"
	"application/request"
	"application/service"
	"github.com/gin-gonic/gin"
	service2 "product/service"
	ufv1 "user/api/f/v1"
	"yz-go/cache"
	"yz-go/model"
	yzResponse "yz-go/response"
	"yz-go/source"
)

func GetProductCollectionList(c *gin.Context) {
	var req request.ProductCollectionListRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// userID := ufv1.GetUserID(c)
	if err, list, total := service.GetProductCollectionList(req); err != nil {
		yzResponse.FailWithMessage("获取选品库失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

type ProductCollectionDetailResult struct {
	yzResponse.PageResult
	SeverRatio    int                                    `json:"sever_ratio"`
	MyCenterTotal int64                                  `json:"my_center_total"`
	Stats         applicationModel.ApplicationCollection `json:"stats"`
}

func GetProductCollectionDetail(c *gin.Context) {
	var pageInfo request.ProductCollectionDetailRequest
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	var user *model.User
	user, err = cache.GetUserFromCache(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").Where("member_id = ?", userID).First(&app).Error
	if err == nil {
		pageInfo.ProductStorageSearch.AppID = app.ID
	} else {
		err = nil
	}
	// 后续传递的都是当前访问会员的信息
	err, list, total := service.GetProductCollectionDetail(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level)
	if err != nil {
		yzResponse.FailWithMessage("获取选品库失败"+err.Error(), c)
		return
	} else {
		var ac applicationModel.ApplicationCollection
		err, ac = service.GetAC(pageInfo.ApplicationID)
		if err != nil {
			yzResponse.FailWithMessage("获取选品库失败"+err.Error(), c)
			return
		}
		yzResponse.OkWithDetailed(ProductCollectionDetailResult{
			PageResult:    yzResponse.PageResult{List: list, Total: total, Page: pageInfo.ProductStorageSearch.Page, PageSize: pageInfo.ProductStorageSearch.PageSize},
			SeverRatio:    app.ApplicationLevel.ServerRadio,
			MyCenterTotal: service2.GetMyCenterTotal(pageInfo.ProductStorageSearch, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level),
			Stats:         ac,
		}, "获取成功", c)
	}
}

type Application struct {
	source.Model
	SupplierID       uint             `json:"supplier_id"`
	AppLevelID       uint             `json:"app_level_id"`
	PetSupplierID    uint             `json:"pet_supplier_id"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}
type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}
func (Application) TableName() string {
	return "application"
}
