package v1

import (
	"application/model"
	request2 "application/request"
	"application/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	v1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func GetStorageStrategyByUserID(c *gin.Context) {

	if err, data := service.GetStorageStrategyByUserID(v1.GetUserID(c)); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func GetCategoryStrategyListByUserID(c *gin.Context) {
	var application request2.CategoryPageInfo
	err := c.ShouldBindQuery(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	application.UserID = v1.GetUserID(c)
	if err, list, total := service.GetCategoryStrategyListByUserID(application); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     application.Page,
			PageSize: application.PageSize,
		}, "获取成功", c)
	}
}

func GetCategoryStrategyList(c *gin.Context) {

	if err, list := service.GetCategoryStrategyList(v1.GetUserID(c)); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(list, c)
	}
}

func SetStorageStrategy(c *gin.Context) {
	var application model.StorageStrategy
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	application.UserID = v1.GetUserID(c)
	if err := service.SetStorageStrategy(application); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func CreateCategoryStrategy(c *gin.Context) {
	var application model.CategoryStrategy
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	application.UserID = v1.GetUserID(c)
	if err := service.CreateCategoryStrategy(application); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func UpdateCategoryStrategy(c *gin.Context) {
	var application model.CategoryStrategy
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	application.UserID = v1.GetUserID(c)
	if err := service.UpdateCategoryStrategy(application); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func DeleteCategoryStrategy(c *gin.Context) {
	var application model.CategoryStrategy
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteCategoryStrategy(application); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}
