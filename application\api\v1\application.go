package v1

import (
	"application/model"
	"application/request"
	"application/service"
	v1 "gin-vue-admin/admin/api/v1"
	gva_model "gin-vue-admin/admin/model"
	service3 "gin-vue-admin/admin/service"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"public-supply/common"
	"strconv"
	"strings"
	model2 "user/model"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
	"yz-go/utils"
)

type ValidateCallbackParams struct {
	Link string `json:"link"`
}

func ValidateCallback(c *gin.Context) {
	var params ValidateCallbackParams

	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err := service.ValidateCallback(params.Link); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	} else {
		yzResponse.OkWithMessage("校验通过：回调地址有效", c)
	}
}

// @Tags 应用
// @Summary 创建Application
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Application true "创建Application"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /application/createApplication [post]
func CreateApplication(c *gin.Context) {
	var application model.Application
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	err, supplier := v1.GetSupplierByUserId(userID)
	if err == nil && userID > 0 {
		if supplier == 0 {
			//总后台
		} else {
			//供应商后台
			supplierID := uint(supplier)
			application.PetSupplierID = supplierID
		}

	}
	if err, id := service.CreateApplication(application); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "新增采购端'"+application.AppName)
		yzResponse.OkWithDetailed(gin.H{"id": id}, "创建成功", c)
	}
}

// @Tags 应用
// @Summary 删除Application
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Application true "删除Application"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /application/deleteApplication [delete]
func DeleteApplication(c *gin.Context) {
	var application model.Application
	err := c.ShouldBindJSON(&application)
	err = source.DB().First(&application, application.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplication(application); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "删除采购端'"+application.AppName)
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func DeleteApplicationExportRecord(c *gin.Context) {
	var application model.AppOrderExportRecord
	err := c.ShouldBindJSON(&application)
	err = source.DB().First(&application, application.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationExport(application); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 应用
// @Summary 批量删除Application
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除Application"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /application/deleteApplicationByIds [delete]
func DeleteApplicationByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteApplicationByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "批量删除采购端'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags 应用
// @Summary 更新Application
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Application true "更新Application"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /application/updateApplication [put]
func UpdateApplication(c *gin.Context) {
	var application model.Application
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateApplication(application); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "编辑采购端'"+application.AppName)
		yzResponse.OkWithMessage("更新成功", c)
	}
}
func UpdateApplicationSzbaoIndependence(c *gin.Context) {
	var application model.Application
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateApplicationSzbaoIndependence(application); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func SaveApplication(c *gin.Context) {
	var application model.Application
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.SaveApplication(application); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags 应用
// @Summary 用id查询Application
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Application true "用id查询Application"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /application/findApplication [get]
func FindApplication(c *gin.Context) {
	var application model.Application
	_ = c.ShouldBindQuery(&application)
	if err, reapplication := service.GetApplication(application.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reapplication": reapplication}, c)
	}
}

func ChangeBanlist(c *gin.Context) {
	var res yzRequest.GetById
	err := c.ShouldBindJSON(&res)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.ChangeBanlist(res.Id)
	if err != nil {
		log.Log().Error("操作黑名单失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("操作黑名单失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

type ApplicationPageResult struct {
	yzResponse.PageResult
	SupplierID  uint
	IsAuthSzbao bool
}

// @Tags 应用
// @Summary 分页获取Application列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationSearch true "分页获取Application列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /application/getApplicationList [get]
func GetApplicationList(c *gin.Context) {
	var pageInfo request.ApplicationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	err, supplier := v1.GetSupplierByUserId(userID)
	if err == nil && userID > 0 {
		if supplier == 0 {
			//总后台
		} else {
			//供应商后台
			supplierID := uint(supplier)
			pageInfo.PetSupplierID = &supplierID
		}

	}
	if err, list, total := service.GetApplicationInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		var isAuthSzbao bool
		if collection.Collect(gva.GlobalAuth.Supply).Contains(common.SUPPLY_SZBAO) == true || utils.LocalEnv() != true {
			isAuthSzbao = true
		} else {
			isAuthSzbao = false
		}
		yzResponse.OkWithDetailed(ApplicationPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SupplierID:  uint(supplier),
			IsAuthSzbao: isAuthSzbao,
		}, "获取成功", c)

	}
}

func GetApplicationOption(c *gin.Context) {
	var petSupplierID *uint
	userID := v1.GetUserID(c)
	err, supplier := v1.GetSupplierByUserId(userID)
	if err == nil && userID > 0 {
		if supplier == 0 {
			//总后台
		} else {
			//供应商后台
			supplierID := uint(supplier)
			petSupplierID = &supplierID
		}

	}

	if err, list := service.GetApplicationOptionList(petSupplierID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// @Tags 应用
// @Summary 获取ApplicationLevel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationSearch true "获取ApplicationLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /application/getApplicationLevelOption [get]
func GetApplicationLevelOption(c *gin.Context) {
	if err, list := service.GetApplicationLevelList(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func CreateApplicationKeySecret(c *gin.Context) {
	var err error
	var IDS yzRequest.GetById
	err = c.ShouldBindQuery(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var application model.Application
	err = source.DB().First(&application, IDS.Id).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var user model2.User
	err = source.DB().First(&user, application.MemberId).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, jwtStr := service3.GetRedisJWT("applicationTokenKey" + strconv.Itoa(int(IDS.Id)))
	if err == nil {
		//将重置密钥之前的token拉黑
		var blackJWT gva_model.JwtBlacklist
		blackJWT.Jwt = jwtStr
		if err := service3.JsonInBlacklist(blackJWT); err != nil {
			yzResponse.FailWithMessage("jwt作废失败", c)
			return
		}
	}
	if err, data := service.CreateKeySecret(IDS.Id); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"appKey": "application" + strconv.Itoa(int(IDS.Id)), "appSecret": data}, c)
	}
}

func CreateShopKeySecret(c *gin.Context) {
	var err error
	var IDS request.ShopSecret
	err = c.ShouldBindQuery(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var application model.Application
	err = source.DB().First(&application, IDS.Id).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var user model2.User
	err = source.DB().First(&user, application.MemberId).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, jwtStr := service3.GetRedisJWT("shopTokenKey" + strconv.Itoa(int(IDS.ShopID)))
	if err == nil {
		//将重置密钥之前的token拉黑
		var blackJWT gva_model.JwtBlacklist
		blackJWT.Jwt = jwtStr
		if err := service3.JsonInBlacklist(blackJWT); err != nil {
			yzResponse.FailWithMessage("jwt作废失败", c)
			return
		}
	}
	if err, data := service.CreateShopKeySecret(IDS.Id, IDS.ShopID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"appKey": "application" + strconv.Itoa(int(IDS.Id)) + "_" + strconv.Itoa(int(IDS.ShopID)), "appSecret": data}, c)
	}
}

func BindSupplier(c *gin.Context) {
	var err error
	var IDS yzRequest.GetById
	err = c.ShouldBindQuery(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.BindSupplier(IDS.Id); err != nil {
		log.Log().Error("失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("解绑成功", c)
	}
}

func ExportOrderList(c *gin.Context) {
	var pageInfo request.ExportApplicationOrderInfo
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.ExportApplicationOrdersSync(pageInfo)
	if err != nil {
		log.Log().Error("导出失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithMessage("导出程序后台处理中，预计10-20分钟导出完成，请于导出列表中查看进度", c)
	}
}

func ExportAppOrderRecordList(c *gin.Context) {
	var pageInfo request.AppOrderExportRecordRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, total, data := service.GetOrderExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func CreateApplicationPetSupplier(c *gin.Context) {
	var info request.CreateApplicationPetSupplier
	var err error
	err = c.ShouldBindJSON(&info)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var newCollectionProducts []model.ApplicationPetSupplier
	for _, productID := range info.PetSupplierIDs {
		newCollectionProducts = append(newCollectionProducts, model.ApplicationPetSupplier{
			ApplicationPetSupplierModel: model.ApplicationPetSupplierModel{
				ApplicationID: info.ApplicationID, PetSupplierID: productID,
			},
		})

	}
	err = source.DB().Where("application_id = ?", info.ApplicationID).Delete(&model.ApplicationPetSupplier{}).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.CreateApplicationPetSupplier(newCollectionProducts)
	if err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("创建成功", c)
}

func DeleteApplicationPetSupplierByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	var err error
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	err = service.DeleteApplicationPetSupplier(IDS.Ids)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	yzResponse.OkWithMessage("批量删除成功", c)
}

func GetCollectionProductList(c *gin.Context) {
	var pageInfo request.ApplicationPetSupplierSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list, total := service.GetApplicationPetSupplierList(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// 采购端导出
func Export(c *gin.Context) {
	var search request.ApplicationSearch
	err := c.ShouldBindJSON(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, link := service.Export(search)
	if err != nil {
		log.Log().Error("导出失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(link, c)
	return
}
