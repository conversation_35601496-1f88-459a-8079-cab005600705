package v1

import (
	"application/model"
	"application/request"
	"application/service"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"notification/mq"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
)

func GetApplicationApplyList(c *gin.Context) {
	var pageInfo request.ApplicationSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetApplicationApplyList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func UpdateApplicationApply(c *gin.Context) {
	var application model.ApplicationApplyRecord
	err := c.ShouldBindJSON(&application)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CheckApplicationApply(application); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		if application.Status == 1 {
			service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "采购端"+strconv.Itoa(int(application.ApplicationID))+"审核申请通过")
			mq.PublishMessage(application.UserID, "applicationSign", 1)
		} else {
			service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "采购端"+strconv.Itoa(int(application.ApplicationID))+"审核申请驳回")
			mq.PublishMessage(application.UserID, "applicationSign", 0)

		}
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func GetApplicationApplyCount(c *gin.Context) {

	total, err := service.GetApplicationApplyCount()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(total, c)

	return
}
