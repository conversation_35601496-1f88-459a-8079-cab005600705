package v1

import (
	"application/model"
	"application/request"
	"application/service"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
)

// @Tags ApplicationGroup
// @Summary 创建ApplicationGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationGroup true "创建ApplicationGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplierGroup/createApplicationGroup [post]
func CreateApplicationGroup(c *gin.Context) {
	var supplierGroup model.ApplicationGroup
	err := c.ShouldBindJSON(&supplierGroup)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateApplicationGroup(supplierGroup); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "新增供应商分类"+supplierGroup.Name)
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags ApplicationGroup
// @Summary 删除ApplicationGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationGroup true "删除ApplicationGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /supplierGroup/deleteApplicationGroup [delete]
func DeleteApplicationGroup(c *gin.Context) {
	var supplierGroup model.ApplicationGroup
	err := c.ShouldBindJSON(&supplierGroup)
	err = source.DB().First(&supplierGroup, supplierGroup.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationGroup(supplierGroup); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "删除供应商分类"+supplierGroup.Name)
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags ApplicationGroup
// @Summary 批量删除ApplicationGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除ApplicationGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /supplierGroup/deleteApplicationGroupByIds [delete]
func DeleteApplicationGroupByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationGroupByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "批量删除供应商分类'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags ApplicationGroup
// @Summary 更新ApplicationGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationGroup true "更新ApplicationGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /supplierGroup/updateApplicationGroup [put]
func UpdateApplicationGroup(c *gin.Context) {
	var supplierGroup model.ApplicationGroup
	err := c.ShouldBindJSON(&supplierGroup)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateApplicationGroup(supplierGroup); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 4, c.ClientIP(), "修改供应商分类"+supplierGroup.Name)
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags ApplicationGroup
// @Summary 用id查询ApplicationGroup
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationGroup true "用id查询ApplicationGroup"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /supplierGroup/findApplicationGroup [get]
func FindApplicationGroup(c *gin.Context) {
	var supplierGroup model.ApplicationGroup
	err := c.ShouldBindQuery(&supplierGroup)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, resupplierGroup := service.GetApplicationGroup(supplierGroup.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"resupplierGroup": resupplierGroup}, c)
	}
}

// @Tags ApplicationGroup
// @Summary 分页获取ApplicationGroup列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationGroupSearch true "分页获取ApplicationGroup列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplierGroup/getApplicationGroupList [get]
func GetApplicationGroupList(c *gin.Context) {
	var pageInfo request.ApplicationGroupSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetApplicationGroupInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
