package v1

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"application/service"
	"application/model"
	yzResponse "yz-go/response"
)



func UpdateApplicationSetting(c *gin.Context) {
	var sysSetting model.ApplicationSetting
	err := c.ShouldBindJSON(&sysSetting)
	if err != nil {
		return
	}
	fmt.Println(sysSetting.Value)
	sysSetting.Key = "application_setting"
	err = service.SaveApplicationSetting(sysSetting)

	if err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	model.ResetApplication()
	yzResponse.OkWithMessage("修改成功", c)

}


func FindApplicationSetting(c *gin.Context) {
	err, tradeSetting := service.GetApplicationSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound){
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": tradeSetting}, c)

}