package v1

import (
	"application/model"
	"application/request"
	"application/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// @Tags ApplicationShop
// @Summary 创建ApplicationShop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationShop true "创建ApplicationShop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplierGroup/createApplicationShop [post]
func CreateApplicationShop(c *gin.Context) {
	var supplierGroup model.ApplicationShop
	err := c.ShouldBindJSON(&supplierGroup)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateApplicationShop(supplierGroup); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags ApplicationShop
// @Summary 删除ApplicationShop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationShop true "删除ApplicationShop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /supplierGroup/deleteApplicationShop [delete]
func DeleteApplicationShop(c *gin.Context) {
	var supplierGroup model.ApplicationShop
	err := c.ShouldBindJSON(&supplierGroup)
	err = source.DB().First(&supplierGroup, supplierGroup.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationShop(supplierGroup); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags ApplicationShop
// @Summary 批量删除ApplicationShop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除ApplicationShop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /supplierGroup/deleteApplicationShopByIds [delete]
func DeleteApplicationShopByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationShopByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags ApplicationShop
// @Summary 更新ApplicationShop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationShop true "更新ApplicationShop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /supplierGroup/updateApplicationShop [put]
func UpdateApplicationShop(c *gin.Context) {
	var supplierGroup model.ApplicationShop
	err := c.ShouldBindJSON(&supplierGroup)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateApplicationShop(supplierGroup); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags ApplicationShop
// @Summary 用id查询ApplicationShop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationShop true "用id查询ApplicationShop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /supplierGroup/findApplicationShop [get]
func FindApplicationShop(c *gin.Context) {
	var supplierGroup model.ApplicationShop
	err := c.ShouldBindQuery(&supplierGroup)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, resupplierGroup := service.GetApplicationShop(supplierGroup.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"resupplierGroup": resupplierGroup}, c)
	}
}

// @Tags ApplicationShop
// @Summary 分页获取ApplicationShop列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationShopSearch true "分页获取ApplicationShop列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /supplierGroup/getApplicationShopList [get]
func GetApplicationShopList(c *gin.Context) {
	var pageInfo request.ApplicationShopSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetApplicationShopInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// 采购端导出
func ExportApplicationShop(c *gin.Context) {
	var search request.ApplicationShopSearch
	err := c.ShouldBindJSON(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, link := service.ExportApplicationShop(search)
	if err != nil {
		log.Log().Error("导出失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(link, c)
	return
}
