package v1

import (
	"application/model"
	"application/request"
	"application/service"
	"github.com/gin-gonic/gin"
	productRequest "product/request"
	yzResponse "yz-go/response"
)

type ProductListResponse struct {
	yzResponse.PageResult
	Stats model.ApplicationCollection `json:"stats"`
}

func GetProductList(c *gin.Context) {
	var pageInfo productRequest.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list, total := service.GetProductList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage("获取选品失败"+err.Error(), c)
		return
	}
	err, stats := service.GetAC(pageInfo.AppID)
	if err != nil {
		yzResponse.FailWithMessage("获取选品统计失败"+err.Error(), c)
		return
	}
	productListResponse := ProductListResponse{
		PageResult: yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		},
		Stats: stats,
	}
	yzResponse.OkWithDetailed(productListResponse, "获取成功", c)
	return
}

type UpdateSelectionRequest struct {
	// 采购端ID
	AppID uint `json:"app_id" form:"app_id" query:"app_id"`
	// 选品库名称
	Name string `json:"name" form:"name" query:"name"`
	// 是否分享 1分享 0不分享
	Share int `json:"share" form:"share" query:"share"`
}

func UpdateSelection(c *gin.Context) {
	var req UpdateSelectionRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.UpdateSelection(req.AppID, req.Name, req.Share)
	if err != nil {
		yzResponse.FailWithMessage("更新选品库失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("更新选品库成功", c)
}

func GetExportRecordList(c *gin.Context) {
	var pageInfo request.GetExportRecordListRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list, total := service.GetExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage("获取导出记录失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func DeleteExportRecord(c *gin.Context) {
	var record model.ApplicationExportRecord
	err := c.ShouldBindJSON(&record)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.DeleteExportRecord(record.ID)
	if err != nil {
		yzResponse.FailWithMessage("删除导出记录失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除导出记录成功", c)
	}
}

func ExportProducts(c *gin.Context) {
	var pageInfo productRequest.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.ExportProducts(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage("导出选品失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("导出程序后台处理中，预计10-20分钟导出完成，请于导出列表中查看进度", c)
	}
}
