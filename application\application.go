package application

import (
	"application/middleware"
	am "application/model"
	"application/service"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"order/model"
	"order/mq"
	common2 "public-supply/common"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

type OrderRequest struct {
	MessageCode uint   `json:"message_code"`
	OrderSn     string `json:"order_sn"`
	MessageType string `json:"message_type"`
	MemberSign  string `json:"member_sign"`
	MessageID   string `json:"message_id"`
}

func PushCustomerHandles() {
	mq.PushHandles("orderQ", func(order mq.OrderMessage) error {

		log.Log().Info("监听订单消息，并向采购端推送消息", zap.Any("info", order))
		err, url := Connection(order)
		if err != nil {
			//var errString = err.Error()
			//order.Level++
			//if order.Level > 5 {
			//	fmt.Println("重新推送次数大于5次，停止推送：", order)
			//	log.Log().Info("重新推送次数大于5次，停止推送：", zap.Any("info", order))
			//	return nil
			//}
			var errMessage am.PushMessageErr
			//var jsonData []byte
			//jsonData, err = json.Marshal(order)
			//errMessage.JsonData = string(jsonData)
			errMessage.Url = url
			//errMessage.Type = 2
			//errMessage.ReSendTime = &source.LocalTime{Time: service.GetNextTime(order.Level)}
			//errMessage.Err = errString
			//err = source.DB().Create(&errMessage).Error
			//if err != nil {
			//	fmt.Println("重新推送消息入库失败：", err)
			//	log.Log().Info("重新推送消息入库失败：", zap.Any("err", err))
			//	return nil
			//}
		}
		return nil
	})
}

type Resp struct {
	Code   int `json:"code"`
	Result int `json:"result"`
}

type CakeOrder struct {
	ID      uint   `json:"id"`
	OrderSN string `json:"order_sn"`
}

func Connection(order mq.OrderMessage) (err error, url string) {
	// 验证消息类型
	if !isValidMessageType(order.MessageType) {
		err = errors.New("消息类型验证失败")
		log.Log().Error("商城通信失败!（消息类型验证失败）", zap.Any("orderMsg", order))
		return
	}

	// 序列化消息数据
	var orderJson []byte
	if orderJson, err = json.Marshal(order); err != nil {
		log.Log().Error("商城通信失败!（消息数据格式出现问题）", zap.Any("err", err))
		return
	}

	// 获取订单
	var orderModel model.Order
	if err = source.DB().Where("id = ?", order.OrderID).First(&orderModel).Error; err != nil {
		log.Log().Error("商城通信失败!（获取订单失败）"+string(orderJson), zap.Any("err", err))
		return
	}

	//租赁订单  -- 租赁订单不使用这个推送消息
	if orderModel.GatherSupplyType == 120 {
		log.Log().Error("商城通信失败!（租赁订单单独推送消息）")
		return
	}

	// 获取回调地址
	var application am.Application
	if err = source.DB().Where("member_id = ?", orderModel.UserID).First(&application).Error; err != nil {
		fmt.Println("与商城通信失败：（获取回调地址失败）"+string(orderJson), err)
		log.Log().Error("与商城通信失败!（获取回调地址失败）"+string(orderJson), zap.Any("err", err))
		return
	}

	// 默认使用供应链回调地址
	url = application.CallBackLink
	memberSign := application.AppSecret
	//存入消息池
	messageType := getMessagePoolTypeWithOrderStatus(order.MessageType)
	if (order.MessageType == mq.Sent || order.MessageType == mq.UpdateSend) && orderModel.ApplicationID > 0 {
		var contentMap = make(map[string]interface{})
		contentMap["order_sn"] = orderModel.ThirdOrderSN
		jsonStr, _ := json.Marshal(contentMap)
		if application.Banlist == 0 {
			err = service.CreateMessagePool(am.MessagePool{
				Type:      messageType,
				Content:   string(jsonStr),
				AppID:     orderModel.ApplicationID,
				AppShopID: uint(orderModel.ApplicationShopID),
				Prefix:    getMessagePoolPrefix(orderModel),
			})
		}

	}

	// 如果是多商城，多商城回调地址存在，则获取多商城回调地址
	if orderModel.ApplicationShopID > 0 {
		var applicationShop am.ApplicationShop
		if err = source.DB().First(&applicationShop, orderModel.ApplicationShopID).Error; err != nil {
			return
		}
		if applicationShop.CallbackLink != "" {
			url = applicationShop.CallbackLink
		}
		if applicationShop.AppSecret != "" {
			memberSign = applicationShop.AppSecret
		}
	}

	//判断消息池是否可用----start
	err = service.GetWhiteCheck(orderModel.ApplicationID, uint(orderModel.ApplicationShopID))
	if (err == nil || application.IsMessagePool == 1) && application.IsMessagePool != 2 {
		// 消息池可用，不继续推送消息
		return
	}
	//判断消息池是否可用----end

	// 插入回调记录
	var ocr = am.OrderConnectionRecord{
		OrderID:     order.OrderID,
		MessageType: int8(order.MessageType),
		Status:      0,
		Content:     string(orderJson),
	}
	err = source.DB().Where("order_id = ? and message_type = ?", order.OrderID, int8(order.MessageType)).FirstOrCreate(&ocr).Error
	if err != nil {
		return
	}

	if orderModel.ApplicationShopID == 0 && application.CallBackLinkValidity == 0 {
		fmt.Println("与商城通信失败：（回调地址无效）"+string(orderJson), err)
		log.Log().Error("与商城通信失败!（回调地址无效）"+string(orderJson), zap.Any("err", err))
		err = errors.New("回调地址无效")
		return
	}

	orderReq := OrderRequest{
		MemberSign:  memberSign,
		MessageID:   "",
		MessageType: getMessageTypePrefix(orderModel) + getMessageTypeSuffix(order.MessageType),
		OrderSn:     orderModel.ThirdOrderSN,
		MessageCode: 0, // 只有福禄订单使用了，默认值1，其他地方未使用，所以此处默认值1
	}
	if strings.Contains(orderReq.MessageType, "fulu") {
		orderReq.MessageCode = 1
	}
	// MessageID计算参数
	messageId := "order" + orderReq.OrderSn + orderReq.MessageType + strconv.Itoa(int(time.Now().Unix()))

	// 计算MessageID
	orderReq.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))

	//生成消息签名
	jsonBodyData, _ := json.Marshal(orderReq)

	header := map[string]string{
		"Content-Type": "application/json",
	}
	header = middleware.SignMessage(string(jsonBodyData), application.AppSecret, map[string]string{}, header)

	//消息签名结束
	if err, _ = post(url, orderReq, header); err != nil {
		fmt.Println("商城通信失败："+string(orderJson), err)
		log.Log().Error("商城通信失败!"+string(orderJson), zap.Any("err", err))
		return
	}

	fmt.Println("商城通信成功：", orderReq)
	log.Log().Info("商城通信成功：", zap.Any("info", order), zap.Any("r", orderReq))

	err = source.DB().Model(&model.Order{}).Where("id = ?", order.OrderID).Update("is_connection", 1).Error
	err = source.DB().Model(&am.OrderConnectionRecord{}).Where("order_id = ?", order.OrderID).Where("message_type = ?", int8(order.MessageType)).Update("status", 1).Error
	if err != nil {
		fmt.Println("修改订单推送消息状态失败：", err)
		log.Log().Info("修改订单推送消息状态失败：", zap.Any("err", err))
	}

	return
}

func isValidMessageType(messageType mq.OrderMessageType) bool {
	validTypes := []mq.OrderMessageType{
		mq.Sent,
		mq.Closed,
		mq.Refunded,
		mq.UpdateSend,
		mq.UpdateShippingAddress,
		mq.Sending,
		mq.OrderFailed,
	}

	for _, v := range validTypes {
		if v == messageType {
			return true
		}
	}

	return false
}

/**
 * 获取消息类型前缀
 */
func getMessageTypePrefix(orderModel model.Order) string {
	// 验证是否属于数字权益订单
	if source.DB().Migrator().HasTable(&am.FuluSupplyOrderResult{}) {
		var fuluOrder am.FuluSupplyOrderResult
		if source.DB().Where("customer_order_no = ? or customer_order_no = ?", orderModel.ThirdOrderSN, orderModel.OrderSN).First(&fuluOrder); fuluOrder.ID != 0 {
			return "fuluOrder."
		}
	}
	// 验证是否属于蛋糕订单
	if orderModel.GatherSupplySN != "" {
		var cakeOrder CakeOrder
		if source.DB().Where("order_sn = ?", orderModel.GatherSupplySN).First(&cakeOrder); cakeOrder.ID > 0 {
			return "CakeOrder."
		}
	}
	return "order."
}

func getMessagePoolPrefix(orderModel model.Order) string {
	// 验证是否属于数字权益订单
	if source.DB().Migrator().HasTable(&am.FuluSupplyOrderResult{}) {
		var fuluOrder am.FuluSupplyOrderResult
		if source.DB().Where("customer_order_no = ? or customer_order_no = ?", orderModel.ThirdOrderSN, orderModel.OrderSN).First(&fuluOrder); fuluOrder.ID != 0 {
			return "fuluOrder."
		}
	}
	// 验证是否属于蛋糕订单
	if orderModel.GatherSupplySN != "" {
		var cakeOrder CakeOrder
		if source.DB().Where("order_sn = ?", orderModel.GatherSupplySN).First(&cakeOrder); cakeOrder.ID > 0 {
			return "CakeOrder."
		}

		if orderModel.GatherSupplyType == 134 {
			return "CakeOrder."
		}
	}

	log.Log().Info("getMessagePoolPrefix", zap.Any("info", orderModel))
	if orderModel.PluginID == 45 || orderModel.GatherSupplyType == common2.SUPPLY_GD {
		return "GuangdianOrder."
	}
	return "order."
}

/**
 * 获取消息类型后缀
 */
func getMessageTypeSuffix(messageType mq.OrderMessageType) string {
	switch messageType {
	case mq.Sent:
		return "delivery"
	case mq.Closed:
		return "cancel"
	case mq.Refunded:
		return "refunded"
	case mq.UpdateSend:
		return "update_send"
	case mq.UpdateShippingAddress:
		return "update_shipping_address"
	case mq.Sending:
		return "sending"
	case mq.OrderFailed:
		return "failed"
	default:
		return ""
	}
}

// 发送POST请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 请求体格式，如：application/json
// content：     请求放回的内容
func post(url string, data interface{}, header map[string]string) (error, Resp) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	var respon Resp
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	err = json.Unmarshal(result, &respon)
	if respon.Code != 0 && respon.Result != 1 {
		err = errors.New("请求成功，但商城返回值为失败")
	}
	return err, respon
}
func getMessagePoolTypeWithOrderStatus(orderStatus mq.OrderMessageType) (messageType int) {
	switch orderStatus {
	case mq.Sent:
		messageType = am.OrderSend
		break
	case mq.Closed:
		messageType = am.OrderClosed
		break
	case mq.Refunded:
		messageType = am.OrderRefunded
		break
	case mq.UpdateSend:
		messageType = am.OrderUpdateSend
		break
	case mq.UpdateShippingAddress:
		messageType = am.OrderUpdateShippingAddress
		break
	case mq.Sending:
		messageType = am.OrderSending
		break
	case mq.OrderFailed:
		messageType = am.OrderFailed
		break
	}
	return messageType
}
