package cron

import (
	"application/model"
	"application/service"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"io/ioutil"
	"net/http"
	mq2 "product/mq"
	"strconv"
	"time"
	"yz-go/cron"
	"yz-go/source"
)

func CheckCallBackLinkHandle() {
	task := cron.Task{
		Key:  "checkCallBack",
		Name: "检查采购端回调地址",
		Spec: "0 */10 * * * *", //1分钟每次 变为30分钟每次。1分钟太频繁了。客户那里全是这个回调信息
		Handle: func(task cron.Task) {
			CheckCallBackLinkCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func CheckCallBackLinkCron() {
	var applications []model.Application
	err := source.DB().Find(&applications).Error
	if err != nil {
		return
	}
	for _, v := range applications {
		header := map[string]string{
			"Content-Type": "application/json",
		}

		var messageId string
		var product mq2.ProductMessage
		messageId = "product" + strconv.Itoa(0) + "goods.alter" + strconv.Itoa(int(time.Now().Unix()))
		product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
		product.MessageType = "goods.alter"
		product.ProductID = 528233
		product.MemberSign = "2d55ea0d3f64a87f7dcdf0658f3e509d"
		var respon service.Resp
		err, respon = post(v.CallBackLink, product, header)
		if err != nil {
			err = source.DB().Model(&model.Application{}).Where("id = ?", v.ID).Update("call_back_link_validity", 0).Error
			if err != nil {
				return
			}
			continue
		} else {
			if respon.Code != 0 && respon.Result != 1 {
				err = source.DB().Model(&model.Application{}).Where("id = ?", v.ID).Update("call_back_link_validity", 0).Error
				if err != nil {
					return
				}
				continue
			} else {
				if v.CallBackLinkValidity == 0 {
					err = source.DB().Model(&model.Application{}).Where("id = ?", v.ID).Update("call_back_link_validity", 1).Error
					if err != nil {
						return
					}
					continue
				}
			}
		}
	}

	var applicationShop []model.ApplicationShop
	err = source.DB().Find(&applicationShop).Error
	if err != nil {
		return
	}
	for _, v := range applicationShop {
		header := map[string]string{
			"Content-Type": "application/json",
		}

		var messageId string
		var product mq2.ProductMessage
		messageId = "product" + strconv.Itoa(0) + "goods.alter" + strconv.Itoa(int(time.Now().Unix()))
		product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
		product.MessageType = "goods.alter"
		var respon service.Resp
		err, respon = post(v.CallbackLink, product, header)
		if err != nil {
			err = source.DB().Model(&model.ApplicationShop{}).Where("id = ?", v.ID).Update("call_back_link_validity", 0).Error
			if err != nil {
				return
			}
			continue
		} else {
			if respon.Code != 0 && respon.Result != 1 {
				err = source.DB().Model(&model.ApplicationShop{}).Where("id = ?", v.ID).Update("call_back_link_validity", 0).Error
				if err != nil {
					return
				}
				continue
			} else {
				if v.CallBackLinkValidity == 0 {
					err = source.DB().Model(&model.ApplicationShop{}).Where("id = ?", v.ID).Update("call_back_link_validity", 1).Error
					if err != nil {
						return
					}
					continue
				}
			}
		}
	}
}

func post(url string, data interface{}, header map[string]string) (error, service.Resp) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, service.Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, service.Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, service.Resp{}
	}
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	var respon service.Resp
	err = json.Unmarshal(result, &respon)
	return err, respon
}
