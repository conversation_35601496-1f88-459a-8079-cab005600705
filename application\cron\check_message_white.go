package cron

import (
	"application/model"
	"application/service"
	"yz-go/cron"
	"yz-go/source"
)

func CheckMessagePoolWhiteHandle() {
	task := cron.Task{
		Key:  "checkMessagePool",
		Name: "定时添加白名单",
		//Spec: "0 */1 * * * *",
		Spec: "37 10 */1 * * *",
		Handle: func(task cron.Task) {
			CheckMessagePool()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func CheckMessagePool() {
	var whiteList []model.MessagePoolWhiteList
	err := source.DB().Find(&whiteList).Error
	if err != nil {
		return
	}
	var whiteIds []uint
	for _, v := range whiteList {
		whiteIds = append(whiteIds, v.AppID)
	}
	var apps []Application
	if len(whiteIds) > 0 {
		err = source.DB().Preload("ApplicationShop").Where("id not in ?", whiteIds).Where("is_message_pool = 0 and banlist = 0").Find(&apps).Error

	} else {
		err = source.DB().Preload("ApplicationShop").Where("is_message_pool = 0 and banlist = 0").Find(&apps).Error

	}
	if err != nil {
		return
	}
	for _, app := range apps {
		if app.IsMultiShop > 0 {
			if len(app.ApplicationShop) > 0 {
				for _, ap := range app.ApplicationShop {
					if service.CheckWhiteUrl(ap.CallbackLink) == nil {
						// 插入白名单
						err = service.InsertWhite(app.ID, int(ap.ID))
						if err != nil {
							continue
						}
					}
				}
			} else {
				if service.CheckWhiteUrl(app.CallBackLink) == nil {
					// 插入白名单
					err = service.InsertWhite(app.ID, 0)
					if err != nil {
						continue
					}
				}
			}

		} else {
			if service.CheckWhiteUrl(app.CallBackLink) == nil {
				// 插入白名单
				err = service.InsertWhite(app.ID, 0)
				if err != nil {
					continue
				}
			}
		}

	}
}

type Application struct {
	source.Model
	CallBackLink    string                  `json:"callBackLink" form:"callBackLink" gorm:"column:call_back_link;comment:;type:varchar(255);size:255;"`
	IsMultiShop     int                     `json:"is_multi_shop" form:"is_multi_shop" gorm:"column:is_multi_shop;default:0;comment:是否多店铺(多密钥) 2关闭 1开启;type:smallint;size:3;"`
	IsMessagePool   int                     `json:"is_message_pool" form:"is_message_pool" gorm:"column:is_message_pool;default:0;comment:是否开启消息池;type:smallint;size:3;"`
	ApplicationShop []model.ApplicationShop `json:"application_shop" gorm:"foreignKey:ApplicationID;references:ID"`
}

func (Application) TableName() string {
	return "application"
}
