package cron

import (
	"application/model"
	"time"
	"yz-go/cron"
	"yz-go/source"
)

func DeleteMessagePoolHandle() {
	task := cron.Task{
		Key:  "deleteMessagePool",
		Name: "定时删除消息池数据",
		Spec: "6 */1 * * * *",
		//Spec: "6 */10 * * * *",
		Handle: func(task cron.Task) {
			DeleteMessagePool()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func DeleteMessagePool() {
	var tm = time.Now().Unix()

	// 获取当前最大ID
	type MaxID struct {
		Max int64 `gorm:"column:max_id"`
	}
	var maxID MaxID
	if err := source.DB().Raw("SELECT MAX(id) as max_id FROM message_pools").Scan(&maxID).Error; err != nil {
		return
	}

	// 如果最大ID超过一定阈值，执行表重建操作
	if maxID.Max > 1000000 { // 设置一个合理的阈值，例如1000万
		// 创建临时表
		if err := source.DB().Exec("CREATE TABLE message_pool_temp LIKE message_pools").Error; err != nil {
			return
		}

		// 复制需要保留的数据到临时表，不包含 id 字段
		if err := source.DB().Exec("INSERT INTO message_pool_temp (type, content, app_id, app_shop_id, prefix, created_at, updated_at, deleted_at) SELECT type, content, app_id, app_shop_id, prefix, created_at, updated_at, deleted_at FROM message_pools WHERE (type in (1,2,3,4,5,6) AND created_at > ?) OR (type in (7,8,9,10,11,12,13) AND created_at > ?)",
			time.Unix(tm-(1*43200), 0).Format("2006-01-02 15:04:05"),
			time.Unix(tm-(30*86400), 0).Format("2006-01-02 15:04:05")).Error; err != nil {
			// 如果失败，删除临时表
			source.DB().Exec("DROP TABLE IF EXISTS message_pool_temp")
			return
		}

		// 替换原表
		if err := source.DB().Exec("DROP TABLE message_pools").Error; err != nil {
			source.DB().Exec("DROP TABLE IF EXISTS message_pool_temp")
			return
		}

		if err := source.DB().Exec("RENAME TABLE message_pool_temp TO message_pools").Error; err != nil {
			return
		}
	} else {
		// 正常删除旧数据
		err := source.DB().Unscoped().Where("`created_at` <= ?", time.Unix(tm-(1*43200), 0).Format("2006-01-02 15:04:05")).Where("type in (1,2,3,4,5,6)").Delete(&model.MessagePool{}).Error
		if err != nil {
			return
		}
		err = source.DB().Unscoped().Where("`created_at` <= ?", time.Unix(tm-(30*86400), 0).Format("2006-01-02 15:04:05")).Where("type in (7,8,9,10,11,12,13)").Delete(&model.MessagePool{}).Error
		if err != nil {
			return
		}
	}
}
