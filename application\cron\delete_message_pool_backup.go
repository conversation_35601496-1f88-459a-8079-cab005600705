package cron

import (
	"application/model"
	"time"
	"yz-go/cron"
	"yz-go/source"
)

func DeleteMessagePoolBackupHandle() {
	task := cron.Task{
		Key:  "deleteMessagePoolBackup",
		Name: "定时删除消息池缓存数据",
		Spec: "6 10 */1 * * *",
		Handle: func(task cron.Task) {
			DeleteMessagePoolBackup()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func DeleteMessagePoolBackup() {
	var tm = time.Now().Unix()

	// 获取当前最大ID
	type MaxID struct {
		Max int64 `gorm:"column:max_id"`
	}
	var maxID MaxID
	if err := source.DB().Raw("SELECT MAX(id) as max_id FROM message_pool_backups").Scan(&maxID).Error; err != nil {
		return
	}

	// 如果最大ID超过一定阈值，执行表重建操作
	if maxID.Max > 50000000 { // 设置一个合理的阈值，例如1000万
		// 创建临时表
		if err := source.DB().Exec("CREATE TABLE message_pool_backup_temp LIKE message_pool_backups").Error; err != nil {
			return
		}

		// 复制最近7天的数据到临时表，不包含 id 字段
		if err := source.DB().Exec("INSERT INTO message_pool_backup_temp (type, content, app_id, app_shop_id, prefix, created_at, updated_at, deleted_at) SELECT type, content, app_id, app_shop_id, prefix, created_at, updated_at, deleted_at FROM message_pool_backups WHERE created_at > ?",
			time.Unix(tm-(7*86400), 0).Format("2006-01-02 15:04:05")).Error; err != nil {
			// 如果失败，删除临时表
			source.DB().Exec("DROP TABLE IF EXISTS message_pool_backup_temp")
			return
		}

		// 替换原表
		if err := source.DB().Exec("DROP TABLE message_pool_backups").Error; err != nil {
			source.DB().Exec("DROP TABLE IF EXISTS message_pool_backup_temp")
			return
		}

		if err := source.DB().Exec("RENAME TABLE message_pool_backup_temp TO message_pool_backups").Error; err != nil {
			return
		}
	} else {
		// 正常删除旧数据
		err := source.DB().Unscoped().Where("`created_at` <= ?", time.Unix(tm-(7*86400), 0).Format("2006-01-02 15:04:05")).Delete(&model.MessagePoolBackup{}).Error
		if err != nil {
			return
		}
	}
}
