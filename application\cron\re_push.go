package cron

import (
	"application"
	"application/model"
	"encoding/json"
	"go.uber.org/zap"
	"order/mq"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func RePushHandle() {
	task := cron.Task{
		Key:  "rePush",
		Name: "重新推送消息",
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			RePushCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func RePushCron() {
	startTime := time.Now()
	log.Log().Info("开始执行重新推送消息任务")

	// 计算24小时前的时间
	timeThreshold := time.Unix(time.Now().Unix()-86400, 0).Format("2006-01-02 15:04:05")

	// 分批处理消息，避免一次性加载所有消息
	const batchSize = 50 // 消息处理批次大小，可根据实际情况调整
	offset := 0
	totalProcessed := 0
	totalSuccess := 0
	totalFailed := 0

	for {
		messages, err := getMessagesBatch(timeThreshold, offset, batchSize)
		if err != nil {
			log.Log().Error("查询消息批次失败", zap.Error(err), zap.Int("offset", offset))
			return
		}

		if len(messages) == 0 {
			break // 没有更多消息了
		}

		// 处理当前批次的消息
		successCount, failedCount := processMessagesBatch(messages)
		totalProcessed += len(messages)
		totalSuccess += successCount
		totalFailed += failedCount

		log.Log().Info("处理消息批次完成",
			zap.Int("批次大小", len(messages)),
			zap.Int("成功数量", successCount),
			zap.Int("失败数量", failedCount),
			zap.Int("累计处理", totalProcessed),
			zap.Int("累计成功", totalSuccess),
			zap.Int("累计失败", totalFailed))

		offset += batchSize

		// 如果返回的消息数少于批次大小，说明已经是最后一批
		if len(messages) < batchSize {
			break
		}

		// 添加短暂延迟，避免对数据库造成过大压力
		time.Sleep(100 * time.Millisecond)
	}

	duration := time.Since(startTime)
	log.Log().Info("重新推送消息任务完成",
		zap.Int("总处理消息数", totalProcessed),
		zap.Int("总成功数量", totalSuccess),
		zap.Int("总失败数量", totalFailed),
		zap.Duration("耗时", duration))
}

// getMessagesBatch 分页查询待重推的消息
func getMessagesBatch(timeThreshold string, offset, limit int) ([]model.OrderConnectionRecord, error) {
	var messages []model.OrderConnectionRecord
	err := source.DB().
		Where("`created_at` >= ?", timeThreshold).
		Where("status = 0").
		Order("created_at ASC"). // 按创建时间升序，优先处理较早的消息
		Offset(offset).
		Limit(limit).
		Find(&messages).Error
	return messages, err
}

// processMessagesBatch 处理消息批次，返回成功和失败的数量
func processMessagesBatch(messages []model.OrderConnectionRecord) (int, int) {
	successCount := 0
	failedCount := 0

	for _, message := range messages {
		if processMessage(message) {
			successCount++
		} else {
			failedCount++
		}
	}

	return successCount, failedCount
}

// processMessage 处理单个消息
func processMessage(message model.OrderConnectionRecord) bool {
	// 解析消息内容
	var orderMessage mq.OrderMessage
	err := json.Unmarshal([]byte(message.Content), &orderMessage)
	if err != nil {
		log.Log().Error("解析消息内容失败",
			zap.Error(err),
			zap.Uint("消息ID", message.ID),
			zap.String("消息内容", message.Content))

		// 标记消息为解析失败状态（可选）
		updateMessageStatus(message.ID, -1) // -1 表示解析失败
		return false
	}

	// 重新推送消息
	err, _ = application.Connection(orderMessage)
	if err != nil {
		log.Log().Error("重新推送消息失败",
			zap.Error(err),
			zap.Uint("消息ID", message.ID),
			zap.Any("订单消息", orderMessage))

		// 增加重试次数记录（可选）
		incrementRetryCount(message.ID)
		return false
	}

	// 推送成功，更新消息状态
	err = updateMessageStatus(message.ID, 1) // 1 表示推送成功
	if err != nil {
		log.Log().Error("更新消息状态失败",
			zap.Error(err),
			zap.Uint("消息ID", message.ID))
		// 虽然推送成功了，但状态更新失败，仍然算作成功
	}

	log.Log().Info("重新推送消息成功",
		zap.Uint("消息ID", message.ID),
		zap.Any("订单消息", orderMessage))

	return true
}

// updateMessageStatus 更新消息状态
func updateMessageStatus(messageID uint, status int) error {
	return source.DB().Model(&model.OrderConnectionRecord{}).
		Where("id = ?", messageID).
		Update("status", status).Error
}

// incrementRetryCount 增加重试次数（如果表中有重试次数字段）
func incrementRetryCount(messageID uint) {
	// 如果 OrderConnectionRecord 表中有 retry_count 字段，可以使用以下代码
	// source.DB().Model(&model.OrderConnectionRecord{}).
	// 	Where("id = ?", messageID).
	// 	Update("retry_count", gorm.Expr("retry_count + 1")).Error

	// 或者记录到日志中
	log.Log().Warn("消息重试失败", zap.Uint("消息ID", messageID))
}

// RePushCronWithRetry 带重试机制的重新推送任务
func RePushCronWithRetry() {
	startTime := time.Now()
	log.Log().Info("开始执行带重试机制的重新推送消息任务")

	// 计算24小时前的时间
	timeThreshold := time.Unix(time.Now().Unix()-86400, 0).Format("2006-01-02 15:04:05")

	const batchSize = 50
	const maxRetries = 3 // 最大重试次数

	offset := 0
	totalProcessed := 0
	totalSuccess := 0
	totalFailed := 0

	for {
		messages, err := getMessagesBatch(timeThreshold, offset, batchSize)
		if err != nil {
			log.Log().Error("查询消息批次失败", zap.Error(err), zap.Int("offset", offset))
			return
		}

		if len(messages) == 0 {
			break
		}

		// 处理当前批次的消息（带重试）
		successCount, failedCount := processMessagesBatchWithRetry(messages, maxRetries)
		totalProcessed += len(messages)
		totalSuccess += successCount
		totalFailed += failedCount

		log.Log().Info("处理消息批次完成",
			zap.Int("批次大小", len(messages)),
			zap.Int("成功数量", successCount),
			zap.Int("失败数量", failedCount),
			zap.Int("累计处理", totalProcessed))

		offset += batchSize

		if len(messages) < batchSize {
			break
		}

		time.Sleep(100 * time.Millisecond)
	}

	duration := time.Since(startTime)
	log.Log().Info("带重试机制的重新推送消息任务完成",
		zap.Int("总处理消息数", totalProcessed),
		zap.Int("总成功数量", totalSuccess),
		zap.Int("总失败数量", totalFailed),
		zap.Duration("耗时", duration))
}

// processMessagesBatchWithRetry 带重试机制的批次处理
func processMessagesBatchWithRetry(messages []model.OrderConnectionRecord, maxRetries int) (int, int) {
	successCount := 0
	failedCount := 0

	for _, message := range messages {
		success := false
		var lastErr error

		// 重试机制
		for retry := 0; retry < maxRetries; retry++ {
			if processMessageWithRetry(message, retry) {
				success = true
				break
			}

			// 重试间隔：第1次重试等待1秒，第2次等待2秒，以此类推
			if retry < maxRetries-1 {
				waitTime := time.Duration(retry+1) * time.Second
				log.Log().Warn("消息处理失败，等待重试",
					zap.Uint("消息ID", message.ID),
					zap.Int("重试次数", retry+1),
					zap.Duration("等待时间", waitTime))
				time.Sleep(waitTime)
			}
		}

		if success {
			successCount++
		} else {
			failedCount++
			log.Log().Error("消息处理最终失败",
				zap.Uint("消息ID", message.ID),
				zap.Int("最大重试次数", maxRetries),
				zap.Error(lastErr))
		}
	}

	return successCount, failedCount
}

// processMessageWithRetry 带重试信息的消息处理
func processMessageWithRetry(message model.OrderConnectionRecord, retryCount int) bool {
	var orderMessage mq.OrderMessage
	err := json.Unmarshal([]byte(message.Content), &orderMessage)
	if err != nil {
		log.Log().Error("解析消息内容失败",
			zap.Error(err),
			zap.Uint("消息ID", message.ID),
			zap.Int("重试次数", retryCount))
		return false
	}

	err, _ = application.Connection(orderMessage)
	if err != nil {
		log.Log().Warn("重新推送消息失败",
			zap.Error(err),
			zap.Uint("消息ID", message.ID),
			zap.Int("重试次数", retryCount))
		return false
	}

	// 推送成功，更新状态
	updateMessageStatus(message.ID, 1)

	if retryCount > 0 {
		log.Log().Info("重新推送消息成功（重试后）",
			zap.Uint("消息ID", message.ID),
			zap.Int("重试次数", retryCount))
	}

	return true
}
