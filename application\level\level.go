package level

import (
	"application/model"
	"errors"
	"fmt"
	"time"
	"yz-go/source"
)

func GetLevelDiscountPercentByFulu(levelID uint) (err error, percent int) {
	err, levels := GetLevels()
	if err != nil {
		return
	}
	for _, level := range levels {
		if level.ID == levelID {
			percent = level.EquityServerRadio
			return
		}
	}
	err = errors.New(fmt.Sprintf("未找到对应等级(%d)的数据", levelID))
	return
}

func GetLevelDiscountPercentByLianLian(levelID uint) (err error, percent int) {
	err, levels := GetLevels()
	if err != nil {
		return
	}
	for _, level := range levels {
		if level.ID == levelID {
			percent = level.LianLianServerRadio
			return
		}
	}
	err = errors.New(fmt.Sprintf("未找到对应等级(%d)的数据", levelID))
	return
}

func GetLevelDiscountPercent(levelID uint) (err error, percent int) {
	err, levels := GetLevels()
	if err != nil {
		return
	}
	for _, level := range levels {
		if level.ID == levelID {
			percent = level.ServerRadio
			return
		}
	}
	err = errors.New(fmt.Sprintf("未找到对应等级(%d)的数据", levelID))
	return
}

var levelsCache []model.ApplicationLevel
var expirationTime int64

func GetLevels() (err error, levels []model.ApplicationLevel) {
	if levelsCache == nil || expirationTime < time.Now().Unix() {
		err, levelsCache = getLevels()
		expirationTime = time.Now().Unix() + 3
	}
	return err, levelsCache
}
func getLevels() (err error, levels []model.ApplicationLevel) {
	err = source.DB().Model(&model.ApplicationLevel{}).Find(&levels).Error
	return
}
