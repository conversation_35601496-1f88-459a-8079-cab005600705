package listener

import (
	"after-sales/model"
	"after-sales/mq"
	"application/middleware"
	am "application/model"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

type AfterSalesRequest struct {
	OrderItemId  uint   `json:"order_item_id"`
	ThirdOrderSn string `json:"third_order_sn"`
	AfterSalesId uint   `json:"after_sales_id"`
	MessageType  string `json:"message_type"`
	MemberSign   string `json:"member_sign"`
	MessageID    string `json:"message_id"`
}

func AfterSalesPushCustomerHandles() {
	mq.PushHandles("afterSalesQ", func(afterSales mq.AfterSalesMessage) error {
		fmt.Println("售后消息接收成功：", afterSales)
		log.Log().Info("售后消息接收成功1", zap.Any("afterSales", afterSales))
		//创建和修改无需推送消息
		if afterSales.MessageType == mq.AfterSalesCreate || afterSales.MessageType == mq.AfterSalesUpdate || afterSales.MessageType == mq.AfterSalesUserSend {
			return nil
		}
		//等级是1的不通知采购端
		if afterSales.Level == 1 {
			log.Log().Info("售后消息接收成功--等级为1不通知采购端", zap.Any("afterSales", afterSales))

			return nil
		}

		var afterSalesPushMessage model.AfterSalesPushMessage

		err, afterSalesModel, orderModel, application, AfterSalesMessageType, applicationShop := AfterSalesPush(afterSales)
		if application.ID == 0 {
			log.Log().Info("售后消息接收成功--无采购端--无需推送消息", zap.Any("afterSales", afterSales))
			return nil
		}

		afterSalesPushMessage.ThirdOrderSN = orderModel.ThirdOrderSN
		afterSalesPushMessage.OrderID = orderModel.ID
		afterSalesPushMessage.OrderItemId = afterSalesModel.OrderItemID
		afterSalesPushMessage.ApplicationID = application.ID
		afterSalesPushMessage.ApplicationShopID = applicationShop.ID
		afterSalesPushMessage.AfterSaleId = afterSalesModel.ID
		afterSalesPushMessage.AfterSalesMessageType = AfterSalesMessageType
		if err != nil {
			afterSalesPushMessage.Status = -1
			afterSalesPushMessage.ErrorMsg = err.Error()
		} else {
			afterSalesPushMessage.Status = 2
		}
		err = source.DB().Create(&afterSalesPushMessage).Error
		if err != nil {
			log.Log().Info("售后消息推送：创建售后推送消息记录失败", zap.Any("err", err), zap.Any("afterSales", afterSales))

		}
		return nil
	})
}

/*
*
售后推送
*/
func AfterSalesPush(afterSales mq.AfterSalesMessage) (err error, afterSalesModel model.AfterSales, orderModel model.Order, application am.Application, pushName string, applicationShop am.ApplicationShop) {
	err = source.DB().Where("id = ?", afterSales.AfterSalesID).First(&afterSalesModel).Error
	if err != nil {
		log.Log().Info("售后消息推送：推送失败售后不存在", zap.Any("err", err), zap.Any("afterSales", afterSales))
		err = errors.New("售后消息推送：推送失败售后不存在")
		return
	}
	err = source.DB().Where("id = ?", afterSalesModel.OrderID).First(&orderModel).Error
	if err != nil {
		log.Log().Error("售后消息推送：与商城通信失败!（获取订单号失败）", zap.Any("err", err), zap.Any("afterSalesModel", afterSalesModel))
		err = errors.New("售后消息推送：与商城通信失败!（获取订单号失败）")
		return
	}
	err, pushName = mq.GetAfterSalesMessagePush(afterSales.MessageType)
	if err != nil {
		return
	}
	if orderModel.GatherSupplyType == 120 {
		pushName = "lease" + pushName
	}
	if orderModel.GatherSupplyType == 9 {
		pushName = "CakeOrder." + pushName
	}
	var url string
	var memberSign string
	err = source.DB().Where("id = ?", orderModel.ApplicationID).First(&application).Error
	if err != nil {
		log.Log().Error("售后消息推送：与商城通信失败!获取回调地址失败）", zap.Any("err", err), zap.Any("afterSalesModel", afterSalesModel))
		err = errors.New("售后消息推送：与商城通信失败!获取回调地址失败）")
		return
	} else {
		if orderModel.ApplicationShopID > 0 {
			err = source.DB().First(&applicationShop, orderModel.ApplicationShopID).Error
			if err != nil {
				err = errors.New("采购端-子采购端不存在" + err.Error())
				return
			}
			url = applicationShop.CallbackLink
			memberSign = applicationShop.AppSecret
		} else {
			//无效地址不在阻止推送
			if application.CallBackLinkValidity == 0 {
				log.Log().Error("售后消息推送：采购端（回调地址无效）", zap.Any("err", err), zap.Any("application", application))
				//err = errors.New("售后消息推送：（回调地址无效）")
				//return
			}
			url = application.CallBackLink
			memberSign = application.AppSecret

		}

	}
	header := map[string]string{
		"Content-Type": "application/json",
	}

	var afterSalesRequest AfterSalesRequest

	var messageId string
	messageId = "after_sales" + strconv.Itoa(int(afterSalesModel.ID)) + pushName + strconv.Itoa(int(time.Now().Unix()))
	afterSalesRequest.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	afterSalesRequest.OrderItemId = afterSalesModel.OrderItemID
	afterSalesRequest.AfterSalesId = afterSalesModel.ID
	afterSalesRequest.ThirdOrderSn = orderModel.ThirdOrderSN
	afterSalesRequest.MemberSign = memberSign
	afterSalesRequest.MessageType = pushName

	//生成消息签名
	jsonBodyData, _ := json.Marshal(afterSalesRequest)
	header = middleware.SignMessage(string(jsonBodyData), application.AppSecret, map[string]string{}, header)
	//消息签名结束

	err, _ = post(url, afterSalesRequest, header)
	return

}

type Resp struct {
	Code   int `json:"code"`
	Result int `json:"result"`
}

// 发送POST请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 请求体格式，如：application/json
// content：     请求放回的内容
func post(url string, data interface{}, header map[string]string) (error, Resp) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	var respon Resp

	log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	err = json.Unmarshal(result, &respon)
	if respon.Code != 0 && respon.Result != 1 {
		err = errors.New("请求成功，但商城返回值为失败")
	}
	return err, respon
}
