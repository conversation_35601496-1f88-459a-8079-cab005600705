package listener

import (
	mq "application/notify-mq"
	"application/service"
	"context"
	"fmt"
	pmq "product/mq"
	service2 "product/service"
	"time"
	"yz-go/component/log"
	"yz-go/source"

	"gorm.io/gorm"

	"go.uber.org/zap"
)

func PushAppNotifyHandles() {
	// 定义消费者配置
	const (
		// workerCount 控制由 KeepConsumerAlive 维护的消费者 goroutine 数量
		consumerCount = 10 // 目标消费者数量 (与你提到的当前数量一致)
		queueName     = "appNotify"
	)

	// 注意：这里的 prefetchCount 参数 (第二个参数) 实际上可能不会被 application.go 中的 ConsumeHandle 使用
	// 因为 ConsumeHandle 内部会重新设置 Qos。保持与 consumerCount 一致或移除此参数，取决于 PushHandles 的实现。
	// 我们假设 application.go 中的 ConsumeHandle 设置的 prefetchCount (50) 是有效的。
	mq.PushHandles(queueName, consumerCount, func(message mq.ApplicationNotifyMessage) error {
		// 移除 workerPool 控制，每个消息由一个独立的 goroutine 处理 (由 RabbitMQ 库管理)

		// 优化数据库查询，只查询必要字段
		var productModel service2.ProductSync
		// Select 和 Preload 逻辑保持不变
		query := source.DB().
			Select("id, supplier_id, gather_supply_id, source, plugin_id, source_goods_id").
			Preload("Storages", func(db *gorm.DB) *gorm.DB {
				return db.Select("id, app_id, product_id")
			})

		if message.MessageType == pmq.Delete {
			query = query.Preload("GatherSupply").Unscoped()
		} else {
			query = query.Preload("GatherSupply")
		}

		// 数据库查询操作
		err := query.Where("id = ?", message.ProductID).First(&productModel).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				log.Log().Info("商品不存在，跳过处理", zap.Uint("product_id", message.ProductID))
				return nil // 确认消息，因为商品不存在无法处理
			}
			log.Log().Error("查询商品失败", zap.Error(err), zap.Uint("product_id", message.ProductID))
			return err // 返回错误，触发 application.go 中的重试逻辑
		}

		// 使用 context 控制 SendNotification 的超时
		// 超时时间可以根据 SendNotification 的典型耗时调整
		ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second) // 增加超时时间
		defer cancel()

		// 直接调用 SendNotification，不再需要额外的 goroutine 和 errChan
		err = callSendNotificationWithContext(ctx, productModel, message.MessageType, message.Level, message.IsStock)

		if err != nil {
			// 记录包含上下文错误的日志
			log.Log().Error("推送消息失败或超时", // 可能是超时错误或 SendNotification 返回的错误
				zap.Any("message", message),
				zap.Uint("product_id", message.ProductID))
			return err // 返回错误给 application.go 进行重试
		}

		// 处理成功
		return nil
	})
}

// 辅助函数，用于在 context 控制下调用 SendNotification
func callSendNotificationWithContext(ctx context.Context, product service2.ProductSync, msgType pmq.ProductMessageType, level int, isStock int) error {
	done := make(chan error, 1)
	go func() {
		// 假设 service.SendNotification 内部有自己的超时或错误处理
		// 如果 SendNotification 可能阻塞很久，需要考虑其内部实现
		done <- service.SendNotification(product, msgType, level, isStock)
	}()

	select {
	case err := <-done:
		return err // 返回 SendNotification 的结果
	case <-ctx.Done():
		return fmt.Errorf("调用 SendNotification 超时: %w", ctx.Err()) // 返回超时错误
	}
}
