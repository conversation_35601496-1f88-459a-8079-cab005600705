package listener

import (
	"application/service"
	"fmt"
	"gorm.io/gorm"
	"product/mq"
	service2 "product/service"
	"yz-go/component/log"
	"yz-go/source"

	"go.uber.org/zap"
)

func PushCustomerHandles() {
	mq.PushHandles("productQ", 10, func(product mq.ProductMessage) error {
		fmt.Println("商品消息接收成功：", product)
		var productModel service2.ProductSync
		var err error
		if product.MessageType == mq.Delete {
			err = source.DB().
				Preload("Supplier").
				Preload("Storages").
				Preload("GatherSupply").
				Unscoped().
				Where("id = ?", product.ProductID).
				First(&productModel).Error
		} else {
			err = source.DB().
				Preload("Storages").
				Preload("Skus").
				Preload("Category1").
				Preload("Category2").
				Preload("Category3").
				Preload("Brand").
				Preload("Supplier").
				Preload("GatherSupply").
				Preload("SmallShopProductSale").
				Preload("AlbumRelations").
				Preload("CollectionRelations").
				Preload("OrderItems", func(db *gorm.DB) *gorm.DB {
					return db.Joins("JOIN orders ON orders.id = order_items.order_id").Where("orders.status >= 1")
				}).
				Preload("OrderItems.Order").
				Where("id = ?", product.ProductID).
				First(&productModel).Error
		}
		if err != nil {
			log.Log().Error("商品不存在"+err.Error(), zap.Any("err", err))
			return nil
		}

		//接收商品变更的队列消息 获得产品id和消息类型，从而获得产品数据
		switch product.MessageType {
		case mq.Create:
			err = service.EsSave(productModel)
			break
		case mq.Edit:
			err = service.EsUpdate(productModel, mq.Edit, product.Level, product.IsStock)
			break
		case mq.StorageChange:
			err = service.EsUpdateOnly(productModel)
			break
		case mq.Undercarriage:
			err = service.EsUpdate(productModel, mq.Undercarriage, product.Level, product.IsStock)
			break
		case mq.OnSale:
			err = service.EsUpdate(productModel, mq.OnSale, product.Level, product.IsStock)
			break
		case mq.Delete:
			err = service.EsDelete(productModel, product.Level)
			break
		case mq.DistributorSync:
			err = service.DistributorSync(productModel, product.Level)
			break
		}

		//if err != nil && productModel.Source != 100 {
		//	var errString = err.Error()
		//	log.Log().Info("处理产品变更的消费者程序报错："+err.Error(), zap.Any("err", err))
		//	product.Level++
		//	if product.Level > 5 {
		//		fmt.Println("重新推送次数大于5次，停止推送：", product)
		//		log.Log().Info("重新推送次数大于5次，停止推送：", zap.Any("info", product))
		//		return nil
		//	}
		//	var errMessage model2.PushMessageErr
		//	var jsonData []byte
		//	jsonData, err = json.Marshal(product)
		//	errMessage.JsonData = string(jsonData)
		//	errMessage.Url = ""
		//	errMessage.Type = 1
		//	errMessage.ReSendTime = &source.LocalTime{Time: service.GetNextTime(product.Level)}
		//	errMessage.Err = errString
		//	err = source.DB().Create(&errMessage).Error
		//	if err != nil {
		//		fmt.Println("重新推送消息入库失败：", err)
		//		log.Log().Info("重新推送消息入库失败：", zap.Any("err", err))
		//		return nil
		//	}
		//}

		return nil
	})
}
