package listener

func PushRePushMessages() {

	//worker_mq.<PERSON>ush<PERSON><PERSON>("rePushMessage", func(data model.RePushProductMessage, i int) error {
	//	var err error
	//	log2.Println("监听重新推送消息" + strconv.Itoa(i))
	//	log2.Println(data)
	//	err = service.Connection(data.ProductMessage, data.Url, nil)
	//	if err != nil {
	//		log.Log().Info("监听重新推送消息处理失败：", zap.Any("err", err))
	//	}
	//	return nil
	//})

}
