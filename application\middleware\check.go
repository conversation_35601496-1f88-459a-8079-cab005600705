package middleware

import (
	"application/request"
	"errors"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

type Application struct {
	source.Model
	MemberId             uint             `json:"memberId" form:"memberId" gorm:"column:member_id;"`
	AppSecret            string           `json:"appSecret"`
	IsCheckSign          int              `json:"is_check_sign" form:"is_check_sign" gorm:"column:is_check_sign;default:0;comment:是否开启签名验证;type:smallint;size:3;"` // 状态 0开启 1开启
	Banlist              int              `json:"banlist" form:"banlist" gorm:"column:banlist;default:0;comment:是否被禁用;type:smallint;size:3;"`
	IsMultiShop          int              `json:"is_multi_shop"`
	AppLevelID           uint             `json:"app_level_id"`
	ApplicationLevel     ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
	CallBackLinkValidity int              `json:"call_back_link_validity"`
	CallBackLink         string           `json:"call_back_link"`
	PetSupplierID        uint             `json:"pet_supplier_id"`
	AppName              string           `json:"app_name"`
}

type ApplicationShop struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:;type:int;size:10;"`
	ShopName      string `json:"shop_name"`
	CallbackLink  string `json:"callback_link"`
	AppSecret     string `json:"app_secret"`
}
type ApplicationLevel struct {
	source.Model
	Sort                int    `json:"sort" form:"sort" gorm:"column:sort;comment:;type:int;size:10;"`
	LevelName           string `json:"levelName" form:"levelName" gorm:"column:level_name;comment:;type:varchar(255);size:255;"`
	ServerRadio         int    `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"`                                      // 手续费比例(万分之一)
	EquityServerRadio   int    `json:"equityServerRadio" form:"equityServerRadio" gorm:"column:equity_server_radio;comment:数字权益商品手续费比例(万分之一);type:int;size:10;"`             // 手续费比例(万分之一)
	LianLianServerRadio int    `json:"lian_lian_server_radio" form:"lian_lian_server_radio" gorm:"column:lian_lian_server_radio;comment:周边游技术服务费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
	NumMax              int    `json:"numMax" form:"numMax" gorm:"column:num_max;comment:;type:int(11);"`
	IsDisplay           int    `json:"is_display"`
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}
func (Application) TableName() string {
	return "application"
}

func CheckInformation(info request.GetToken) (err error, userID uint, appID uint, shopID uint) {

	appKey := info.AppKey
	appSecret := info.AppSecret
	if appKey == "" || appSecret == "" {
		err = errors.New("请检查参数是否填写完整")
		return
	}

	if len(appKey) <= 11 {
		log.Log().Error("application information check failed:", zap.Any("err", err))
		err = errors.New("appKey不正确")
		return
	}

	var appId int
	var shopId int

	appKey = appKey[11:]
	if strings.Contains(appKey, "_") {
		var stringSlice = strings.Split(appKey, "_")
		appId, err = strconv.Atoi(stringSlice[0])
		if err != nil {
			return
		}
		shopId, err = strconv.Atoi(stringSlice[1])
		if err != nil {
			return
		}
	} else {
		appId, err = strconv.Atoi(appKey)
		if err != nil {
			log.Log().Error("application information check failed:", zap.Any("err", err))
			err = errors.New("请检查参数是否填写完整")
			return
		}

	}

	var application Application
	err = source.DB().Model(&Application{}).Where("deleted_at is null").First(&application, appId).Error
	if err != nil {
		log.Log().Error("application information check failed:", zap.Any("err", err))
		err = errors.New("权限验证失败")
		return
	}
	if shopId > 0 {
		if application.IsMultiShop == 2 {
			err = errors.New("多商城功能未开启")
			return
		}
		var applicationShop ApplicationShop
		err = source.DB().Model(&ApplicationShop{}).Where("deleted_at is null").Where("application_id = ?", appId).Where("id = ?", shopId).First(&applicationShop).Error
		if err != nil {
			log.Log().Error("application information check failed:", zap.Any("err", err))
			err = errors.New("权限验证失败")
			return
		}
		if applicationShop.AppSecret != appSecret {
			err = errors.New("权限验证失败")
			return
		} else {
			return nil, application.MemberId, uint(appId), uint(shopId)
		}
	} else {
		if application.AppSecret != appSecret {
			err = errors.New("权限验证失败")
			return
		} else {
			return nil, application.MemberId, uint(appId), uint(0)
		}
	}
}
