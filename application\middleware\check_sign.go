package middleware

import (
	"bytes"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"io/ioutil"
	"math/rand"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"yz-go/cache"
	yzResponse "yz-go/response"
)

func Banlist() gin.HandlerFunc {
	return func(c *gin.Context) {
		appID, exists := c.Get("appID")
		if exists == false {
			return
		}

		// Convert interface{} to uint
		appIDUint, ok := appID.(uint)
		if !ok {
			yzResponse.Result(yzResponse.PURCHASEPERM, gin.H{}, "Invalid application ID format", c)
			c.Abort()
			return
		}

		// Use the converted uint value
		application, err := cache.GetApplicationFromCache(appIDUint)
		if err != nil {
			yzResponse.Result(yzResponse.PURCHASEPERM, gin.H{}, "查询错误，请稍后重试", c)
			c.Abort()
			return
		}

		if application.Banlist == 1 {
			yzResponse.Result(yzResponse.PURCHASEPERM, gin.H{}, "当前采购端已被禁用", c)
			c.Abort()
			return
		}
		c.Next()
	}
}

// 清除指定应用缓存
func ClearApplicationCache(appID interface{}) {
	cache.ClearApplication(appID)
}

// 校验请求签名 (前置)
func RequestSign() gin.HandlerFunc {
	return func(c *gin.Context) {
		//body, _ := ioutil.ReadAll(c.Request.Body)
		////query, _ := ioutil.ReadAll(c.Request.RequestURI)
		//var body1 = make(map[string]interface{})
		//
		//_ = json.Unmarshal(body, &body1)
		appID, _ := c.Get("appID")
		isCheckSign, _ := c.Get("IsCheckSign") //记录是否开启签名
		appSecret, _ := c.Get("AppSecret")     //记录密钥。生成响应签名时使用

		if isCheckSign == 1 {
			//body数据处理
			bodyRequest, _ := io.ReadAll(c.Request.Body)

			//回写这个body 否则 以后的方法读取时会是EOF
			c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyRequest))

			re3 := regexp.MustCompile("\\s+")
			bodyNew := re3.ReplaceAllString(string(bodyRequest), "")
			//query数据处理
			query := c.Request.URL.Query()
			var queryMap = make(map[string]string, len(query))

			for k := range query {
				queryMap[k] = c.Query(k)
			}

			queryMap["App-Timestamp"] = c.Request.Header.Get("App-Timestamp")
			queryMap["App-Nonce-Str"] = c.Request.Header.Get("App-Nonce-Str")

			signRequest := CreateSign(bodyNew, "application"+fmt.Sprint(appID), fmt.Sprint(appSecret), queryMap)
			if signRequest != c.Request.Header.Get("App-Sign") {
				yzResponse.Result(yzResponse.SIGNERROR, gin.H{"reload": true}, "签名不正确", c)
				c.Abort()
				return
			}

		}

		//writer := responseBodyWriter{
		//	ResponseWriter: c.Writer,
		//	body:           &bytes.Buffer{},
		//}

		//c.Writer = writer
		//log.Log().Info("签名验证结束")
		c.Next()
		//log.Log().Info("返回签名生成开始")

		//log.Log().Info("返回结果", zap.Any("sha1String", writer.body.String()))
		//返回签名
		//if application.IsCheckSign == IsCheckSign{
		//bodyRequest, _ := io.ReadAll(c.Request.Body)
		//writer.body.Write(bodyRequest)
		////c.Writer.
		//_, _ = c.Writer.Write(bodyRequest)
		//signResponse := createSign(writer.body.String(),"application"+strconv.Itoa(int(application.ID)),application.AppSecret,queryMapResponse)
		//log.Log().Info("返回结果", zap.Any("signResponse", signResponse))
		//c.Writer.Header().Set("Response-Sign",signResponse)
		//c.Writer.Header().Set("Response-Timestamp",queryMapResponse["Response-Timestamp"])
		//c.Writer.Header().Set("Response-Nonce-Str",queryMapResponse["Response-Nonce-Str"])
		// c.Abort()
		//c.Writer.Header().Set("Response-Sign111","111")
		//
		//return

		//writer.Header().Set("Response-Sign",signResponse)

		//yzResponse.Result(yzResponse.SIGNERROR, gin.H{"reload": true}, signResponse, c)
		//c.Abort()
		//return

		//log.Log().Info("返回结果1", zap.Any("signResponse",c.Writer.Header().Get("Response-Sign")))

		//
		//c.Header("Response-Timestamp",signResponse)
		//c.Header("Response-Nonce-Str",signResponse)

		//}

	}
}

/*
*

	采购端
	生成消息签名
*/
func SignMessage(bodyData string, AppSecret string, queryMap map[string]string, header map[string]string) (reHeader map[string]string) {
	var timeStamp = time.Now().UnixNano() / int64(1e6)
	queryMap["App-Timestamp"] = strconv.FormatInt(timeStamp, 10)
	queryMap["App-Nonce-Str"] = RandString(16)

	signMessage := CreateSign(bodyData, "", AppSecret, queryMap)

	header["App-Timestamp"] = queryMap["App-Timestamp"]
	header["App-Nonce-Str"] = queryMap["App-Nonce-Str"]
	header["App-Sign"] = signMessage
	return header
}

/*
*

	采购端
	生成响应签名
*/
func SignResponse(bodyData interface{}, c *gin.Context) {
	appID, _ := c.Get("appID")
	if appID != nil {
		jsonBodyData, _ := json.Marshal(bodyData)
		AppSecret, _ := c.Get("AppSecret")
		isCheckSign, _ := c.Get("IsCheckSign")
		if isCheckSign == 1 {
			var queryMapResponse = make(map[string]string, 2)
			var timeStamp = time.Now().UnixNano() / int64(1e6)
			queryMapResponse["Response-Timestamp"] = strconv.FormatInt(timeStamp, 10)
			queryMapResponse["Response-Nonce-Str"] = RandString(16)
			signResponse := CreateSign(string(jsonBodyData), "application"+fmt.Sprint(appID), fmt.Sprint(AppSecret), queryMapResponse)

			c.Writer.Header().Set("Response-Timestamp", queryMapResponse["Response-Timestamp"])
			c.Writer.Header().Set("Response-Nonce-Str", queryMapResponse["Response-Nonce-Str"])
			c.Writer.Header().Set("Response-Sign", signResponse)
		}
	}
}
func RandString(len int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	bytes1 := make([]byte, len)
	for i := 0; i < len; i++ {
		b := r.Intn(26) + 65
		bytes1[i] = byte(b)
	}
	return string(bytes1)
}

// 生成签名 (请求时)
func CreateSign(bodyData string, appKey string, appSecret string, queryMap map[string]string) (signRequest string) {
	var paramsString string //生成签名的字符串

	var queryK []string
	for k := range queryMap {
		queryK = append(queryK, k)
	}

	sort.Strings(queryK)
	paramsString = ""
	////拼接
	for _, k := range queryK {
		paramsString += k + "=" + queryMap[k] + "&"
	}
	//消息签名不需要key
	if appKey != "" {
		paramsString += "appKey=" + appKey + "&"
	}
	paramsString += "appSecret=" + appSecret + ""
	//log.Log().Info("APPapi签名生成:paramsString1", zap.Any("paramsString", paramsString))
	//log.Log().Info("APPapi签名生成:paramsString1", zap.Any("body", body))

	paramsString += bodyData
	//log.Log().Info("APPapi签名生成:paramsString2", zap.Any("paramsString", paramsString))

	sha1hash := sha1.New()
	sha1hash.Write([]byte(paramsString))
	sha1String := hex.EncodeToString(sha1hash.Sum([]byte("")))

	md5hash := md5.New()
	md5hash.Write([]byte(sha1String))
	md5String := hex.EncodeToString(md5hash.Sum([]byte("")))
	signRequest = strings.ToUpper(md5String)

	//fmt.Println("参与签名计算的参数：" + paramsString)
	//log.Log().Info("APPapi签名生成:sha1结果", zap.Any("sha1String", sha1String))
	//log.Log().Info("APPapi签名生成:md5结果", zap.Any("md5String", md5String))
	//
	//log.Log().Info("APPapi签名生成:签名结果", zap.Any("sign", signRequest))

	return
}

// 生成签名 (返回时)
//type responseBodyWriter struct {
//	gin.ResponseWriter
//	body *bytes.Buffer
//}
//
//func (r responseBodyWriter) Write(b []byte) (int, error) {
//	r.body.Write(b)
//	return r.ResponseWriter.Write(b)
//}
