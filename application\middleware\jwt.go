package middleware

import (
	"errors"
	"gin-vue-admin/admin/model"
	userModel "gin-vue-admin/admin/model/request"
	"go.uber.org/zap"
	"strconv"
	"time"
	"user/service"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/config"
	model2 "yz-go/model"
	yzResponse "yz-go/response"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 我们这里jwt鉴权取头部信息 x-token 登录时回返回token信息 这里前端需要把token存储到cookie或者本地localStorage中 不过需要跟后端协商过期时间 可以约定刷新令牌或者重新登录
		token := c.Request.Header.Get("x-token")
		if token == "" {
			yzResponse.FailWithDetailed(gin.H{"reload": true}, "非法访问", c)
			c.Abort()
			return
		}
		if service.IsBlacklist(token) {
			yzResponse.Result(yzResponse.EXPIRE, gin.H{"reload": true}, "您的帐户异地登录或令牌失效", c)
			c.Abort()
			return
		}
		j := NewJWT()
		// parseToken 解析token包含的信息
		claims, err := j.ParseToken(token)
		if err != nil {
			if err == TokenExpired {
				yzResponse.Result(yzResponse.EXPIRE, gin.H{"reload": true}, "授权已过期", c)
				c.Abort()
				return
			}
			yzResponse.FailWithDetailed(gin.H{"reload": true}, err.Error(), c)
			c.Abort()
			return
		}
		var application *model2.Application
		// 先尝试从缓存获取
		application, err = cache.GetApplicationFromCache(claims.AppID)
		if err != nil {
			yzResponse.Result(yzResponse.EXPIRE, gin.H{"reload": true}, "查询错误，请稍后重试", c)
			c.Abort()
			return
		}
		if claims.ShopID > 0 {
			if application.IsMultiShop == 2 {
				yzResponse.Result(yzResponse.EXPIRE, gin.H{"reload": true}, "非法访问,采购端未开启多商城功能", c)
				c.Abort()
				return
			}
			_, err = cache.GetApplicationShopFromCache(claims.ShopID)
			if err != nil {
				yzResponse.Result(yzResponse.EXPIRE, gin.H{"reload": true}, "非法访问,此商城不存在", c)
				c.Abort()
				return
			}
		}
		var user *model2.User
		err, user = service.GetUserInfoByID(application.MemberId)
		if err != nil {
			yzResponse.Result(yzResponse.EXPIRE, gin.H{"reload": true}, "非法访问", c)
			c.Abort()
			return
		}
		// token过期时间
		if claims.ExpiresAt-time.Now().Unix() < claims.BufferTime {
			claims.ExpiresAt = time.Now().Unix() + config.Config().JWT.ExpiresTime
			newToken, _ := j.CreateToken(*claims)
			newClaims, _ := j.ParseToken(newToken)
			c.Header("new-token", newToken)
			c.Header("new-expires-at", strconv.FormatInt(newClaims.ExpiresAt, 10))
			if config.Config().System.UseMultipoint {
				if claims.ShopID > 0 {
					err, RedisJwtToken := service.GetRedisJWT("shopTokenKey" + strconv.Itoa(int(claims.ShopID)))
					if err != nil {
						log.Log().Error("get redis jwt failed", zap.Any("err", err))
					} else { // 当之前的取成功时才进行拉黑操作
						_ = service.JsonInBlacklist(model.JwtBlacklist{Jwt: RedisJwtToken})
					}
					// 无论如何都要记录当前的活跃状态
					_ = service.SetRedisJWT(newToken, "shopTokenKey"+strconv.Itoa(int(claims.ShopID)))
				} else {
					err, RedisJwtToken := service.GetRedisJWT("applicationTokenKey" + strconv.Itoa(int(claims.AppID)))
					if err != nil {
						log.Log().Error("get redis jwt failed", zap.Any("err", err))
					} else { // 当之前的取成功时才进行拉黑操作
						_ = service.JsonInBlacklist(model.JwtBlacklist{Jwt: RedisJwtToken})
					}
					// 无论如何都要记录当前的活跃状态
					_ = service.SetRedisJWT(newToken, "applicationTokenKey"+strconv.Itoa(int(claims.AppID)))
				}

			}
		}
		c.Set("claims", claims)
		c.Set("appID", claims.AppID)
		c.Set("shopID", claims.ShopID)
		c.Set("userID", application.MemberId)
		c.Set("user_level_id", user.LevelID)
		//验证签名时使用
		c.Set("IsCheckSign", application.IsCheckSign) //记录是否开启签名
		c.Set("AppSecret", application.AppSecret)     //记录密钥。生成响应签名时使用
		c.Next()
	}
}

type JWT struct {
	SigningKey []byte
}

var (
	TokenExpired     = errors.New("Token is expired")
	TokenNotValidYet = errors.New("Token not active yet")
	TokenMalformed   = errors.New("That's not even a token")
	TokenInvalid     = errors.New("Couldn't handle this token:")
)

func NewJWT() *JWT {
	return &JWT{
		[]byte(config.Config().JWT.SigningKey),
	}
}

// 创建一个token
func (j *JWT) CreateToken(claims userModel.CustomClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.SigningKey)
}

// 解析 token
func (j *JWT) ParseToken(tokenString string) (*userModel.CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &userModel.CustomClaims{}, func(token *jwt.Token) (i interface{}, e error) {
		return j.SigningKey, nil
	})
	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, TokenMalformed
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				// Token is expired
				return nil, TokenExpired
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, TokenNotValidYet
			} else {
				return nil, TokenInvalid
			}
		}
	}
	if token != nil {
		if claims, ok := token.Claims.(*userModel.CustomClaims); ok && token.Valid {
			return claims, nil
		}
		return nil, TokenInvalid

	} else {
		return nil, TokenInvalid

	}

}

// 更新token
//func (j *JWT) RefreshToken(tokenString string) (string, error) {
//	jwt.TimeFunc = func() time.Time {
//		return time.Unix(0, 0)
//	}
//	token, err := jwt.ParseWithClaims(tokenString, &request.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
//		return j.SigningKey, nil
//	})
//	if err != nil {
//		return "", err
//	}
//	if claims, ok := token.Claims.(*request.CustomClaims); ok && token.Valid {
//		jwt.TimeFunc = time.Now
//		claims.StandardClaims.ExpiresAt = time.Now().Unix() + 60*60*24*7
//		return j.CreateToken(*claims)
//	}
//	return "", TokenInvalid
//}
