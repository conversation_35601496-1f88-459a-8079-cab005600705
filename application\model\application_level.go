// 自动生成模板ApplicationLevel
package model

import (
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type ApplicationLevel struct {
	source.Model
	Sort                int    `json:"sort" form:"sort" gorm:"column:sort;comment:;type:int;size:10;"`
	LevelName           string `json:"levelName" form:"levelName" gorm:"column:level_name;comment:;type:varchar(255);size:255;"`
	ServerRadio         int    `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"`                                      // 手续费比例(万分之一)
	EquityServerRadio   int    `json:"equityServerRadio" form:"equityServerRadio" gorm:"column:equity_server_radio;comment:数字权益商品手续费比例(万分之一);type:int;size:10;"`             // 手续费比例(万分之一)
	LianLianServerRadio int    `json:"lian_lian_server_radio" form:"lian_lian_server_radio" gorm:"column:lian_lian_server_radio;comment:周边游技术服务费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
	NumMax              int    `json:"numMax" form:"numMax" gorm:"column:num_max;comment:;type:int(11);"`
	IsDisplay           int    `json:"is_display"`
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}

type ApplicationLevelOption struct {
	Label string `json:"label"`
	Value int    `json:"value"`
}
