package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type ApplicationSetting struct {
	model.SysSetting
	Value ApplicationValue `json:"value"`
}

func (i ApplicationSetting) TableName() string {
	return "sys_settings"
}

type ApplicationValue struct {
	IsOpenApply     int    `json:"is_open_apply"`     // 是否开启前台申请应用
	ApplyDesc       string `json:"apply_desc"`        // 申请说明
	IsOpenAgreement int    `json:"is_open_agreement"` // 是否开启申请协议
	Agreement       string `json:"agreement"`
	EditPrice       int    `json:"edit_price"`    //0可以改价 1不可以改价
	ShowTechFee     int    `json:"show_tech_fee"` //0隐藏 1显示
	MultiPetSupplier int `json:"multi_pet_supplier"`//0单个 1多个
}

func (value ApplicationValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *ApplicationValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var tradeSetting *ApplicationValue

func getApplicationSetting(key string) (err error, sysSetting ApplicationSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func GetApplicationSetting() (err error, setting ApplicationValue) {
	if tradeSetting == nil {
		var sysSetting ApplicationSetting
		err, sysSetting = getApplicationSetting("trade_setting")
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		tradeSetting = &sysSetting.Value
	}
	return err, *tradeSetting
}

func ResetApplication() {
	//重置全局变量 start
	tradeSetting = nil
}
