package model

import (
	"gorm.io/gorm"
	"strings"
	"yz-go/source"
)

const (
	GoodsEdit                  = 1
	GoodsUndercarriage         = 2
	GoodsOnSale                = 3
	GoodsCreate                = 4
	GoodsDelete                = 5
	DistributorSync            = 6
	OrderSend                  = 7
	OrderClosed                = 8
	OrderRefunded              = 9
	OrderUpdateSend            = 10
	OrderUpdateShippingAddress = 11
	OrderSending               = 12
	OrderFailed                = 13
)

type MessagePool struct {
	source.Model
	Type      int    `json:"type" gorm:"type:int;default:0;"`
	Content   string `json:"content" gorm:"type:text(0);size:0;"`
	AppID     uint   `json:"app_id" gorm:"type:int;default:0;index:idx_app_shop"`
	AppShopID uint   `json:"app_shop_id" gorm:"type:int;default:0;index:idx_app_shop"`
	Prefix    string `json:"prefix" gorm:"type:varchar(255);default:'';"`
}
type MessagePoolBackup struct {
	source.Model
	Type      int    `json:"type" gorm:"type:int;default:0;"`
	Content   string `json:"content" gorm:"type:text(0);size:0;"`
	Status    int    `json:"status" gorm:"index:idx_app_status_shop"` //0:未获取 1:已获取
	AppID     uint   `json:"app_id" gorm:"type:int;default:0;index:idx_app_status_shop"`
	AppShopID uint   `json:"app_shop_id" gorm:"type:int;default:0;index:idx_app_status_shop"`
	Prefix    string `json:"prefix" gorm:"type:varchar(255);default:'';"`
}

func (a *MessagePoolResponse) AfterFind(tx *gorm.DB) (err error) {

	a.TypeString = GetMessageTypeString(a.Type)
	if a.Prefix != "" {
		if strings.Contains(a.TypeString, "order.") {
			a.TypeString = strings.Replace(a.TypeString, "order.", a.Prefix, -1)
		} else {
			a.TypeString = a.Prefix + a.TypeString
		}
	}

	a.Prefix = ""
	return
}
func GetMessageTypeString(t int) (messageType string) {
	switch t {

	case GoodsEdit:
		messageType = "goods.alter"
		break
	case GoodsUndercarriage:
		messageType = "goods.undercarriage"
		break
	case GoodsOnSale:
		messageType = "goods.on.sale"
		break
	case GoodsCreate:
		messageType = "goods.create"
		break
	case GoodsDelete:
		messageType = "goods.delete"
		break
	case DistributorSync:
		messageType = "goods.distributorSync"
		break
	case OrderSend:
		messageType = "order.delivery"
		break
	case OrderClosed:
		messageType = "order.cancel"
		break

	case OrderRefunded:
		messageType = "order.refunded"
		break

	case OrderUpdateSend:
		messageType = "order.update_send"
		break
	case OrderUpdateShippingAddress:
		messageType = "order.update_shipping_address"
		break
	case OrderSending:
		messageType = "order.sending"
		break
	case OrderFailed:
		messageType = "order.failed"
		break

	}
	return
}

type MessagePoolResponse struct {
	ID         uint              `json:"id"`
	Type       int               `json:"type"`
	Content    string            `json:"content"`
	CreatedAt  *source.LocalTime `json:"created_at"`
	TypeString string            `json:"type_string" gorm:"-"`
	Prefix     string            `json:"prefix"`
}

func (MessagePoolResponse) TableName() string {
	return "message_pools"
}

type MessagePoolWhiteList struct {
	source.Model
	AppID     uint `json:"app_id" gorm:"type:int;default:0;index;"`
	AppShopID uint `json:"app_shop_id" gorm:"type:int;default:0;index;"`
}
