package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type StorageStrategy struct {
	source.Model
	AppID  uint  `json:"app_id"`
	UserID uint  `json:"user_id"`
	Value  Value `json:"value"`
}
type PriceColumn string

const (
	Price         PriceColumn = "price"
	CostPrice     PriceColumn = "cost_price"
	GuidePrice    PriceColumn = "guide_price"
	OriginPrice   PriceColumn = "origin_price"
	ActivityPrice PriceColumn = "activity_price"
)

type Value struct {
	SalePriceType   uint        `json:"sale_price_type"`   //定价方式 1全局 2区间
	SalePriceColumn PriceColumn `json:"sale_price_column"` //定价依据字段
	SalePriceRatio  int         `json:"sale_price_ratio"`  //定价系数
	SalePriceRange  []Range     `json:"sale_price_range"`  //区间数据（当Type=2时使用）

	GuidePriceType   uint        `json:"guide_price_type"`   //定价方式 1全局 2区间
	GuidePriceColumn PriceColumn `json:"guide_price_column"` //定价依据字段
	GuidePriceRatio  int         `json:"guide_price_ratio"`  //定价系数
	GuidePriceRange  []Range     `json:"guide_price_range"`  //区间数据（当Type=2时使用）

	CostPriceType   uint        `json:"cost_price_type"`   //定价方式 1全局 2区间
	CostPriceColumn PriceColumn `json:"cost_price_column"` //定价依据字段
	CostPriceRatio  int         `json:"cost_price_ratio"`  //定价系数
	CostPriceRange  []Range     `json:"cost_price_range"`  //区间数据（当Type=2时使用）
}
type Range struct {
	From  uint `json:"from"`
	To    uint `json:"to"`
	Ratio int  `json:"ratio"`
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type CategoryStrategy struct {
	source.Model
	Title      string     `json:"title"`
	AppID      uint       `json:"app_id"`
	UserID     uint       `json:"user_id"`
	Value      Value      `json:"value"`
	Categories Categories `json:"categories"`
}
type Categories []Category

func (value Categories) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Categories) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type Category struct {
	ID       int        `json:"id"` // 分类id
	Name     string     `json:"name"`
	Category Categories `json:"category"` //子分类
}
