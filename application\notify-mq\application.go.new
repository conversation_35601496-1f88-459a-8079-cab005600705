package notify_mq

import (
	"encoding/json"
	"fmt"
	"github.com/streadway/amqp"
	"go.uber.org/zap"
	mq2 "product/mq"
	"sync"
	"time"
	"yz-go/component/log"
	"yz-go/mq"
	"yz-go/source"
)

const (
	EXCHANGE = "appNotifyE"
	KEY      = "appNotify"
)

func init() {
	handles = map[string]handleItem{}
	// 保留原始的消费者注册方式
	mq.PushConsumer(ConsumeRabbitMQ)
}

var publishRabbitMQ *source.SafeCh
var mqOnce sync.Once

func getPublishRabbitMQ() *amqp.Channel {
	mqOnce.Do(func() {
		var err error
		publishRabbitMQ = source.SafeMQ(func(ch *amqp.Channel) {
			// 定义话题交换机
			err = ch.ExchangeDeclare(
				EXCHANGE,
				"topic", //话题类型
				true,
				false,
				//true表示这个exchange不可以被client用来推送消息，仅用来进行exchange和exchange之间的绑定
				false,
				false,
				nil,
			)
			if err != nil {
				fmt.Println(err)
			}
		})
	})

	return publishRabbitMQ.GetCh()
}

// PublishMessage
// 订单消息推送
func PublishMessage(productID uint, messageType mq2.ProductMessageType, level int, isStock int) (err error) {

	message, err := json.Marshal(ApplicationNotifyMessage{
		MessageType: messageType,
		ProductID:   productID,
		Level:       level,
		IsStock:     isStock,
	})

	if err != nil {
		return
	}
	key := KEY
	err = getPublishRabbitMQ().Publish(
		EXCHANGE,
		key,
		//如果为true，根据exchange类型和routkey规则，如果无法找到符合条件的队列，那么会把发送的消息返回给发送者
		false,
		//如果为true，当exchange发送消息到队列侯发现队列上没有绑定消费者，则会把消息发还给发送者
		false,
		amqp.Publishing{
			ContentType:  "text/plain",
			Body:         message,
			DeliveryMode: amqp.Persistent,
		})
	return
}

type ApplicationNotifyMessage struct {
	MessageType mq2.ProductMessageType `json:"message_type"`
	ProductID   uint                   `json:"product_id"`
	Level       int                    `json:"level"` //重试次数
	IsStock     int                    `json:"is_stock"`
}
type Handle func(product ApplicationNotifyMessage) error

var handles map[string]handleItem

type handleItem struct {
	ThreadNum int
	Handle    Handle
}

func PushHandles(queueName string, threadNum int, handle Handle) {
	handles[queueName] = handleItem{threadNum, handle}
	getPublishRabbitMQ()
}

// 添加配置常量
const (
	// 显著提高 prefetchCount
	prefetchCount = 50 // 每个消费者的预取消息数量 (从之前的 2 或 100 调整)
	prefetchSize  = 0  // 预取大小（0表示无限制）
	// queueTTL     = 24 * time.Hour // 消息过期时间 (如果需要可以保留)
)

// 恢复 ConsumeRabbitMQ 函数，作为启动入口
func ConsumeRabbitMQ() {
	// 启动后台 goroutine 来维护消费者数量
	go KeepConsumerAlive()
}

// 优化 ConsumeHandle 方法 (保持不变)
func ConsumeHandle(queueName string, handle Handle) {
	consumeRabbitMQ := source.MQ()
	if consumeRabbitMQ == nil {
		log.Log().Error("获取消费通道失败", zap.String("queue", queueName))
		return
	}
	defer consumeRabbitMQ.Close() // 确保通道在使用后关闭

	// 设置 QoS，提高消费效率
	err := consumeRabbitMQ.Qos(
		prefetchCount, // prefetch count
		prefetchSize,  // prefetch size
		false,         // global=false 表示 prefetch 应用于当前 channel
	)
	if err != nil {
		log.Log().Error("设置 QoS 失败", zap.Error(err), zap.String("queue", queueName))
		return
	}

	// 定义队列 (保持不变)
	_, err = consumeRabbitMQ.QueueDeclare(queueName, true, false, false, false, nil)
	if err != nil {
		// 建议使用 log 记录错误，而不是 fmt.Println
		log.Log().Error("定义队列失败", zap.Error(err), zap.String("queue", queueName))
		// 根据错误严重性决定是否 return
	}
	// 绑定队列 (保持不变)
	err = consumeRabbitMQ.QueueBind(
		queueName, // queue name
		KEY,       // routing key
		EXCHANGE,  // exchange
		false,
		nil)
	if err != nil {
		log.Log().Error("队列绑定失败", zap.Error(err), zap.String("queue", queueName))
		return
	}

	// 消费消息 (保持不变)
	msgs, err := consumeRabbitMQ.Consume(
		queueName,
		"",    // consumer tag - 让服务器自动生成
		false, // auto-ack = false
		false, // exclusive
		false, // no-local
		false, // no-wait
		nil,   // args
	)
	if err != nil {
		log.Log().Error("创建消费者失败", zap.Error(err), zap.String("queue", queueName))
		return
	}

	log.Log().Info("消费者启动成功", zap.String("queue", queueName), zap.Int("prefetch", prefetchCount))

	// 使用 select 监听通道关闭和消息 (保持不变)
	done := make(chan error)
	go func() {
		defer func() {
			// 添加 recover 防止 panic
			if r := recover(); r != nil {
				log.Log().Error("消息处理 goroutine panic", zap.Any("panic", r), zap.Stack("stack"), zap.String("queue", queueName))
			}
			log.Log().Info("消息通道关闭，消费者退出", zap.String("queue", queueName))
			done <- nil // 确保即使 panic 也能通知主 select 退出
		}()
		for msg := range msgs {
			processSingleMsgWithRetry(msg, handle)
		}
	}()

	// 监听通道关闭事件 (保持不变)
	notifyClose := make(chan *amqp.Error)
	consumeRabbitMQ.NotifyClose(notifyClose)

	select {
	case err := <-notifyClose:
		log.Log().Error("通道关闭", zap.Error(err), zap.String("queue", queueName))
	case <-done:
		log.Log().Info("消费者正常退出", zap.String("queue", queueName))
	}
	// ConsumeHandle 退出后，KeepConsumerAlive 会在下次检查时重新创建
}

// 添加带重试的单条消息处理逻辑 (保持不变)
func processSingleMsgWithRetry(msg amqp.Delivery, handle Handle) {
	const maxRetries = 3
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		var productMessage ApplicationNotifyMessage
		if err := json.Unmarshal(msg.Body, &productMessage); err != nil {
			log.Log().Error("消息解析失败，将拒绝并不重入队列", zap.Error(err), zap.ByteString("body", msg.Body))
			_ = msg.Reject(false) // false 表示不重新入队
			return
		}

		// 调用业务处理函数
		if err := handle(productMessage); err != nil {
			lastErr = err
			log.Log().Warn("消息处理失败，准备重试",
				zap.Error(err),
				zap.Int("retry", i+1),
				zap.Uint("product_id", productMessage.ProductID),
				zap.String("message_type", string(productMessage.MessageType)))

			// 简单的指数退避
			time.Sleep(time.Duration(1<<i) * time.Second)
			continue // 继续下一次重试
		}

		// 处理成功
		if err := msg.Ack(false); err != nil { // multiple = false
			log.Log().Error("消息 Ack 失败", zap.Error(err), zap.Uint64("deliveryTag", msg.DeliveryTag))
		}
		return // 成功处理，退出循环
	}

	// 所有重试都失败
	log.Log().Error("消息处理最终失败，将拒绝并不重入队列",
		zap.Error(lastErr))
	_ = msg.Reject(false) // false 表示不重新入队
}

// 优化 KeepConsumerAlive 方法 (保持不变)
func KeepConsumerAlive() {
	ticker := time.NewTicker(30 * time.Second) // 检查间隔可以适当调整
	defer ticker.Stop()

	// 首次立即执行一次检查
	createConsumer()

	for range ticker.C { // 使用 range ticker.C 更简洁
		createConsumer()
	}
}

// 优化 createConsumer
func createConsumer() {
	// 添加一个锁防止并发执行 createConsumer
	consumerCreateLock.Lock()
	defer consumerCreateLock.Unlock()

	// 重试机制
	const maxRetries = 3
	var lastErr error

	for retry := 0; retry < maxRetries; retry++ {
		if retry > 0 {
			log.Log().Info("重试获取 RabbitMQ 通道", zap.Int("重试次数", retry))
			// 指数退避策略
			time.Sleep(time.Duration(1<<retry) * time.Second)
		}

		ch := getKeeperCh() // 这个通道用于检查队列状态，不用于消费
		if ch == nil {
			lastErr = fmt.Errorf("无法获取有效的 RabbitMQ 通道")
			log.Log().Warn("无法获取检查通道", zap.Int("重试", retry+1), zap.Int("最大重试", maxRetries))
			continue // 重试
		}

		for queueName, handleConf := range handles {
			// 检查队列状态获取消费者数量
			queue, err := ch.QueueDeclarePassive(
				queueName, // name
				true,      // durable
				false,     // autoDelete
				false,     // exclusive
				false,     // noWait
				nil,       // arguments
			)
			if err != nil {
				log.Log().Warn("检查队列状态失败，尝试创建队列", zap.Error(err), zap.String("queue", queueName))
				// 如果队列不存在，则创建队列
				queue, err = ch.QueueDeclare(
					queueName, // name
					true,      // durable
					false,     // autoDelete
					false,     // exclusive
					false,     // noWait
					nil,       // arguments
				)
				if err != nil {
					log.Log().Error("创建队列失败", zap.Error(err), zap.String("queue", queueName))
					continue
				}
				
				// 创建队列后，绑定到交换机
				err = ch.QueueBind(
					queueName, // queue name
					KEY,       // routing key
					EXCHANGE,  // exchange
					false,     // noWait
					nil,       // arguments
				)
				if err != nil {
					log.Log().Error("绑定队列到交换机失败", zap.Error(err), zap.String("queue", queueName))
					continue
				}
				
				log.Log().Info("成功创建并绑定队列", zap.String("queue", queueName))
			}

			// 检查当前消费者数量是否足够
			if queue.Consumers < handleConf.ThreadNum {
				needed := handleConf.ThreadNum - queue.Consumers
				log.Log().Info("消费者数量不足，需要启动新的消费者",
					zap.String("queue", queueName),
					zap.Int("current", queue.Consumers),
					zap.Int("target", handleConf.ThreadNum),
					zap.Int("needed", needed))

				// 启动所需数量的新消费者
				for i := 0; i < needed; i++ {
					// 每次启动一个新的 goroutine 来运行 ConsumeHandle
					go func(qn string, h Handle) {
						// 添加 recover 防止 ConsumeHandle panic 导致 KeepConsumerAlive 退出
						defer func() {
							if r := recover(); r != nil {
								log.Log().Error("ConsumeHandle goroutine panic", zap.Any("panic", r), zap.Stack("stack"), zap.String("queue", qn))
							}
						}()
						ConsumeHandle(qn, h)
						// ConsumeHandle 退出后，goroutine 结束，等待 KeepConsumerAlive 下次检查
						log.Log().Info("ConsumeHandle goroutine 退出", zap.String("queue", qn))
					}(queueName, handleConf.Handle)
				}
			} else if queue.Consumers > handleConf.ThreadNum {
				log.Log().Warn("消费者数量超过目标值，暂不处理",
					zap.String("queue", queueName),
					zap.Int("current", queue.Consumers),
					zap.Int("target", handleConf.ThreadNum))
			}
		}
		
		// 如果成功处理了队列，则退出重试循环
		return
	}
	
	// 如果所有重试都失败，记录错误
	if lastErr != nil {
		log.Log().Error("所有重试都失败，无法创建消费者", zap.Error(lastErr))
	}
}

// 添加 getKeeperCh 和 keeperCh (如果之前移除了需要加回来)
var keeperCh *source.SafeCh
var keeperOnce sync.Once          // 使用 sync.Once 保证只初始化一次
var consumerCreateLock sync.Mutex // 添加锁

func getKeeperCh() *amqp.Channel {
	// 不使用 Once 模式，每次都重新获取通道，确保通道是有效的
	if keeperCh == nil || !isChannelOpen(keeperCh.GetCh()) {
		log.Log().Info("初始化或重新创建 Keeper Channel")
		keeperCh = source.SafeMQ(func(ch *amqp.Channel) {
			// 声明交换机，确保交换机存在
			err := ch.ExchangeDeclare(
				EXCHANGE,
				"topic", // 话题类型
				true,     // durable
				false,    // autoDelete
				false,    // internal
				false,    // noWait
				nil,      // arguments
			)
			if err != nil {
				log.Log().Error("声明交换机失败", zap.Error(err))
			}
		})
	}
	
	ch := keeperCh.GetCh()
	if ch == nil || !isChannelOpen(ch) {
		log.Log().Error("无法获取有效的 Keeper Channel")
		// 重置 keeperCh，下次尝试时将重新创建
		keeperCh = nil
		return nil
	}
	
	return ch
}

// 检查通道是否打开
// 注意：这个方法不是100%可靠，因为即使通道已关闭，也可能不会立即反映到这个状态中
func isChannelOpen(ch *amqp.Channel) bool {
	if ch == nil {
		return false
	}
	
	// 尝试执行一个无害的操作来检查通道是否打开
	// 使用 Flow 方法，它不会改变通道的状态
	err := ch.Flow(true) // 启用流控，这个操作对大多数应用没有影响
	return err == nil
}

// 移除 StartConsumers 函数 (如果之前添加了)
// func StartConsumers() { ... }
