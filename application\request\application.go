package request

import (
	appm "application/model"
	"payment/model"
	yzRequest "yz-go/request"
)

type ApplicationSearch struct {
	appm.Application
	yzRequest.PageInfo
	MemberName string `json:"member_name" form:"member_name"`
	// 会员手机号
	MemberUserName    string `json:"member_user_name" form:"member_user_name"`
	PetSupplierID     *uint  `json:"pet_supplier_id" form:"pet_supplier_id"`
	APPID             *uint  `json:"appid" form:"appid"`
	SzbaoIndependence *int   `json:"szbao_independence"`
}

type ApplicationPaySortSet struct {
	ApplicationPaySorts []model.ApplicationPaySort `json:"application_pay_sorts"`
}

type GetToken struct {
	AppKey    string `json:"app_key" validate:"required"`
	AppSecret string `json:"app_secret"  validate:"required"`
}

type ApplicationSourceSearch struct {
	appm.ApplicationSource
	yzRequest.PageInfo
}

type ExportApplicationOrderInfo struct {
	ApplicationID uint   `json:"application_id" form:"application_id"`
	TimeType      *int   `json:"time_type" form:"time_type"`
	StartAT       string `json:"start_at" form:"start_at"`
	EndAT         string `json:"end_at" form:"end_at"`
}

type AppOrderExportRecordRequest struct {
	appm.AppOrderExportRecord
	yzRequest.PageInfo
}

type CreateApplicationPetSupplier struct {
	ApplicationID  uint   `json:"application_id"`
	PetSupplierIDs []uint `json:"pet_supplier_ids" form:"pet_supplier_ids"`
	//CollectionProducts []model.CollectionProduct `json:"collection_products" form:"collection_products"`
}

type ApplicationPetSupplierSearch struct {
	yzRequest.PageInfo
	Id uint `json:"id"`
}

type ApplicationGroupSearch struct {
	appm.ApplicationGroup
	yzRequest.PageInfo
}

type ApplicationShopSearch struct {
	appm.ApplicationShop
	yzRequest.PageInfo
}

type ShopSecret struct {
	ShopID uint `json:"shop_id" form:"shop_id"`
	yzRequest.GetById
}
type CategoryPageInfo struct {
	yzRequest.PageInfo
	UserID uint `json:"user_id"`
}
