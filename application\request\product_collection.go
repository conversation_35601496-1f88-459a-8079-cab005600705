package request

import (
	"application/model"
	productRequest "product/request"
	yzRequest "yz-go/request"
)

type ProductCollectionProductListRequest struct {
	productRequest.ProductSearch
}

type ProductCollectionListRequest struct {
	// 排序类型 1商品数量 2订单数量 3订单金额 4最近选品时间 5最新共享时间 6热门访问
	SortType int `json:"sort_type" form:"sort_type" query:"sort_type"`
	// 排序方式 true升序 false降序
	Sort bool `json:"sort" form:"sort" query:"sort"`
	yzRequest.PageInfo
}

type ProductCollectionDetailRequest struct {
	ApplicationID uint `json:"application_id" form:"application_id" query:"application_id"`
	// ProductStorageSearch中有 AppID，这里增加ApplicationID用于区分。两个都用
	ProductStorageSearch productRequest.ProductStorageSearch `json:"product_storage_search" form:"product_storage_search" query:"product_storage_search"`
}

type GetExportRecordListRequest struct {
	yzRequest.PageInfo
	model.ApplicationExportRecord
	StartAt     string `json:"start_at" form:"start_at"`         //开始时间
	EndAt       string `json:"end_at" form:"end_at"`             //结束时间
	ErrorStatus int    `json:"error_status" form:"error_status"` //1导出成功，2导出不成功
}
