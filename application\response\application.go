package response

import (
	"application/model"
	"gorm.io/gorm"
	model2 "user/model"
	"yz-go/response"
	"yz-go/source"
)

type Application struct {
	ID      uint   `json:"id"`
	AppName string `json:"app_name"`
}

type Order struct {
	ID            uint `json:"id"`
	ApplicationID uint `json:"application_id"`
	Amount        uint `json:"amount"`
}

type ApplicationAdminList struct {
	model.Application
	ID            uint                  `json:"id"`
	Order         []Order               `json:"order" gorm:"foreignKey:ApplicationID"`
	OrderCount    int                   `json:"order_count"`
	OrderAmount   int                   `json:"order_amount"`
	User          model2.ChildUser      `json:"user" gorm:"foreignKey:MemberId;"`
	GoinBalance   model2.AccountBalance `json:"goin_balance" gorm:"foreignKey:Uid;references:MemberId"`
	Balance       model2.AccountBalance `json:"balance" gorm:"foreignKey:Uid;references:MemberId"`
	PetSupplier   response.Supplier     `json:"pet_supplier" gorm:"foreignKey:PetSupplierID;references:ID"`
	DaHangErpItem DaHangErpItem         `json:"da_hang_erp_item" gorm:"foreignKey:application_id;references:id"`
}

// 大昌行API项目信息
type DaHangErpItem struct {
	source.Model
	EventCode     string `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"`        //活动编码
	EventName     string `json:"event_name" form:"event_name" gorm:"column:event_name;comment:活动名称;"`              // 活动名称
	ApplicationId uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:采购端id;"` //采购端id
}

func (ApplicationAdminList) TableName() string {
	return "application"
}

func (b *ApplicationAdminList) AfterFind(tx *gorm.DB) (err error) {
	b.OrderCount = len(b.Order)
	for _, v := range b.Order {
		b.OrderAmount += int(v.Amount)
	}
	return
}

type LoginResponse struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expiresAt"`
}

// 用户余额
type UserBalance struct {
	GoinBalance uint `json:"goin_balance" form:"goin_balance"`
}
