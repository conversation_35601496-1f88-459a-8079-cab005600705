package router

import (
	"application/api/app"
	v12 "application/api/f/v1"
	"application/api/v1"
	"github.com/gin-gonic/gin"
)

func InitApplicationRouter(Router *gin.RouterGroup) {
	ApplicationRouter := Router.Group("application")
	{
		ApplicationRouter.POST("validateCallback", v1.ValidateCallback)                                     // 新建Application
		ApplicationRouter.POST("createApplication", v1.CreateApplication)                                   // 新建Application
		ApplicationRouter.DELETE("deleteApplication", v1.DeleteApplication)                                 // 删除Application
		ApplicationRouter.DELETE("deleteApplicationByIds", v1.DeleteApplicationByIds)                       // 批量删除Application
		ApplicationRouter.PUT("updateApplication", v1.UpdateApplication)                                    // 更新Application
		ApplicationRouter.POST("updateApplicationSzbaoIndependence", v1.UpdateApplicationSzbaoIndependence) // 更新Application
		ApplicationRouter.GET("findApplication", v1.FindApplication)                                        // 根据ID获取Application
		ApplicationRouter.POST("changeBanlist", v1.ChangeBanlist)                                           // 加入黑名单or解除黑名单
		ApplicationRouter.GET("getApplicationList", v1.GetApplicationList)                                  // 获取Application列表
		ApplicationRouter.GET("createApplicationKeySecret", v1.CreateApplicationKeySecret)                  // 获取appkey
		ApplicationRouter.GET("createShopKeySecret", v1.CreateShopKeySecret)                                //生成店铺密钥
		ApplicationRouter.GET("getApplicationApplyList", v1.GetApplicationApplyList)                        // 获取审核列表
		ApplicationRouter.POST("updateApplicationApply", v1.UpdateApplicationApply)                         // 审核
		ApplicationRouter.GET("getApplicationApplyCount", v1.GetApplicationApplyCount)                      // 审核
		ApplicationRouter.GET("findApplicationSetting", v1.FindApplicationSetting)                          // 获取商城设置
		ApplicationRouter.POST("updateApplicationSetting", v1.UpdateApplicationSetting)                     // 修改商城设置
		ApplicationRouter.POST("exportOrderList", v1.ExportOrderList)                                       // 修改商城设置
		ApplicationRouter.POST("exportAppOrderRecordList", v1.ExportAppOrderRecordList)                     // 修改商城设置
		ApplicationRouter.DELETE("deleteApplicationExportRecord", v1.DeleteApplicationExportRecord)         // 修改商城设置
		ApplicationRouter.DELETE("deleteApplicationPetSupplierByIds", v1.DeleteApplicationPetSupplierByIds) // 修改商城设置
		ApplicationRouter.POST("createApplicationPetSupplier", v1.CreateApplicationPetSupplier)             // 修改商城设置
		ApplicationRouter.GET("getCollectionProductList", v1.GetCollectionProductList)                      // 修改商城设置
		ApplicationRouter.POST("export", v1.Export)                                                         //采购端导出

		// 采购端-选品库商品列表
		ApplicationRouter.GET("productCollection/getProductList", v1.GetProductList) // 采购端-选品库商品列表
		// 采购端-修改选品库名称与是否共享
		ApplicationRouter.PUT("productCollection/update", v1.UpdateSelection) // 采购端-修改选品库名称与是否共享
		// 采购端选品库导出记录列表
		ApplicationRouter.GET("productCollection/exportRecordList", v1.GetExportRecordList)
		// 采购端选品库删除导出记录
		ApplicationRouter.DELETE("productCollection/deleteExportRecord", v1.DeleteExportRecord)
		// 导出采购端选品库
		ApplicationRouter.GET("productCollection/export", v1.ExportProducts)
	}
	ApplicationLevelRouter := Router.Group("application")
	{
		ApplicationLevelRouter.POST("createApplicationLevel", v1.CreateApplicationLevel)             // 新建ApplicationLevel
		ApplicationLevelRouter.DELETE("deleteApplicationLevel", v1.DeleteApplicationLevel)           // 删除ApplicationLevel
		ApplicationLevelRouter.DELETE("deleteApplicationLevelByIds", v1.DeleteApplicationLevelByIds) // 批量删除ApplicationLevel
		ApplicationLevelRouter.PUT("updateApplicationLevel", v1.UpdateApplicationLevel)              // 更新ApplicationLevel
		ApplicationLevelRouter.GET("findApplicationLevel", v1.FindApplicationLevel)                  // 根据ID获取ApplicationLevel
		ApplicationLevelRouter.GET("getApplicationLevelList", v1.GetApplicationLevelList)            // 获取ApplicationLevel列表
	}
	AdminSupplierRouter := Router.Group("adminSupplier")
	{
		AdminSupplierRouter.POST("createApplication", v1.CreateApplication)             // 新建Application
		AdminSupplierRouter.DELETE("deleteApplication", v1.DeleteApplication)           // 删除Application
		AdminSupplierRouter.DELETE("deleteApplicationByIds", v1.DeleteApplicationByIds) // 批量删除Application
		AdminSupplierRouter.PUT("updateApplication", v1.UpdateApplication)              // 更新Application
		AdminSupplierRouter.GET("findApplication", v1.FindApplication)                  // 根据ID获取Application
		AdminSupplierRouter.GET("getApplicationList", v1.GetApplicationList)            // 获取Application列表
		AdminSupplierRouter.GET("bindSupplier", v1.BindSupplier)
		AdminSupplierRouter.GET("getApplicationOption", v1.GetApplicationOption) // 获取Application列表

	}
	ApplicationGroupRouter := Router.Group("applicationGroup")
	{
		ApplicationGroupRouter.POST("createApplicationGroup", v1.CreateApplicationGroup)             // 新建ApplicationGroup
		ApplicationGroupRouter.DELETE("deleteApplicationGroup", v1.DeleteApplicationGroup)           // 删除ApplicationGroup
		ApplicationGroupRouter.DELETE("deleteApplicationGroupByIds", v1.DeleteApplicationGroupByIds) // 批量删除ApplicationGroup
		ApplicationGroupRouter.PUT("updateApplicationGroup", v1.UpdateApplicationGroup)              // 更新ApplicationGroup
		ApplicationGroupRouter.GET("findApplicationGroup", v1.FindApplicationGroup)                  // 根据ID获取ApplicationGroup
		ApplicationGroupRouter.GET("getApplicationGroupList", v1.GetApplicationGroupList)            // 获取ApplicationGroup列表
	}

	ApplicationShopRouter := Router.Group("applicationShop")
	{
		ApplicationShopRouter.POST("createApplicationShop", v1.CreateApplicationShop)             // 新建ApplicationGroup
		ApplicationShopRouter.DELETE("deleteApplicationShop", v1.DeleteApplicationShop)           // 删除ApplicationGroup
		ApplicationShopRouter.DELETE("deleteApplicationShopByIds", v1.DeleteApplicationShopByIds) // 批量删除ApplicationGroup
		ApplicationShopRouter.PUT("updateApplicationShop", v1.UpdateApplicationShop)              // 更新ApplicationGroup
		ApplicationShopRouter.GET("findApplicationShop", v1.FindApplicationShop)                  // 根据ID获取ApplicationGroup
		ApplicationShopRouter.GET("getApplicationShopList", v1.GetApplicationShopList)            // 获取ApplicationGroup列表
		ApplicationShopRouter.POST("exportApplicationShop", v1.ExportApplicationShop)             // 获取ApplicationGroup列表
	}
}

func InitAdminPublicRouter(Router *gin.RouterGroup) {
	ApplicationRouter := Router.Group("application")
	{
		ApplicationRouter.GET("getApplicationLevelOption", v1.GetApplicationLevelOption) // 获取ApplicationLevel列表
		ApplicationRouter.GET("getApplicationOption", v1.GetApplicationOption)           // 获取Application列表

	}
}

func InitUserPublicRouter(Router *gin.RouterGroup) {
	ApplicationRouter := Router.Group("application")
	{
		ApplicationRouter.GET("getMyGatherSupplyAndSource", app.GetMyGatherSupplyAndSourceFront)
	}
}

func InitUserPrivateRouter(Router *gin.RouterGroup) {
	ApplicationRouter := Router.Group("application")
	{
		ApplicationRouter.POST("createApplicationApply", v12.CreateApplicationApply)         // 提交审核
		ApplicationRouter.GET("getApplicationApply", v12.FindApplicationApply)               // 获取审核状态
		ApplicationRouter.GET("getApplicationStatus", v12.FindApplication)                   // 获取是否申请
		ApplicationRouter.GET("getApplicationLevelList", v12.GetApplicationLevelList)        // 获取应用等级
		ApplicationRouter.GET("findApplicationSetting", v1.FindApplicationSetting)           // 获取应用设置
		ApplicationRouter.GET("createApplicationKeySecret", v1.CreateApplicationKeySecret)   // 获取appkey
		ApplicationRouter.GET("createShopKeySecret", v1.CreateShopKeySecret)                 //生成店铺密钥
		ApplicationRouter.PUT("updateApplication", v1.SaveApplication)                       // 更新Application
		ApplicationRouter.GET("getGatherSupplyAndSource", app.GetGatherSupplyAndSourceFront) // //胜天半子的返回来源 其他的返回供应链id
		//ApplicationRouter.GET("getMyGatherSupplyAndSource", app.GetMyGatherSupplyAndSourceFront) // //胜天半子的返回来源 其他的返回供应链id
		ApplicationRouter.POST("validateCallback", v1.ValidateCallback)
		ApplicationRouter.GET("getStorageStrategyByUserID", v12.GetStorageStrategyByUserID)
		ApplicationRouter.GET("getCategoryStrategyListByUserID", v12.GetCategoryStrategyListByUserID)
		ApplicationRouter.GET("getCategoryStrategyListAll", v12.GetCategoryStrategyList)
		ApplicationRouter.POST("setStorageStrategy", v12.SetStorageStrategy)
		ApplicationRouter.POST("createCategoryStrategy", v12.CreateCategoryStrategy)
		ApplicationRouter.POST("updateCategoryStrategy", v12.UpdateCategoryStrategy)
		ApplicationRouter.POST("deleteCategoryStrategy", v12.DeleteCategoryStrategy)

		// 采购端-选品库列表
		ApplicationRouter.GET("productCollection/list", v12.GetProductCollectionList) // 采购端-选品库列表
		// 采购端-选品库详情
		ApplicationRouter.POST("productCollection/detail", v12.GetProductCollectionDetail) // 采购端-选品库详情
	}
	ApplicationShopRouter := Router.Group("applicationShop")
	{
		ApplicationShopRouter.POST("createApplicationShop", v1.CreateApplicationShop)             // 新建ApplicationGroup
		ApplicationShopRouter.DELETE("deleteApplicationShop", v1.DeleteApplicationShop)           // 删除ApplicationGroup
		ApplicationShopRouter.DELETE("deleteApplicationShopByIds", v1.DeleteApplicationShopByIds) // 批量删除ApplicationGroup
		ApplicationShopRouter.PUT("updateApplicationShop", v1.UpdateApplicationShop)              // 更新ApplicationGroup
		ApplicationShopRouter.GET("findApplicationShop", v1.FindApplicationShop)                  // 根据ID获取ApplicationGroup
		ApplicationShopRouter.GET("getApplicationShopList", v1.GetApplicationShopList)            // 获取ApplicationGroup列表
		ApplicationShopRouter.POST("exportApplicationShop", v1.ExportApplicationShop)             // 获取ApplicationGroup列表
	}
}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	ApplicationRouter := Router.Group("application")
	{
		ApplicationRouter.POST("setPaySort", app.SetPaySort)            // 获取ApplicationLevel列表
		ApplicationRouter.GET("getPaySort", app.GetPaySort)             // 获取Application列表
		ApplicationRouter.GET("cloud/export", app.ExportCloudOrderList) // 获取Application列表

		ApplicationRouter.GET("getSupplySource", app.GetSupplySource)                   // 获取Application列表
		ApplicationRouter.GET("getGatherSupplyAndSource", app.GetGatherSupplyAndSource) // //胜天半子的返回来源 其他的返回供应链id

		ApplicationRouter.POST("getUserGoinBalance", app.GetUserGoinBalance)             // 获取用户站内余额
		ApplicationRouter.POST("getUserPurchasingAccount", app.GetUserPurchasingAccount) // 获取采购端的采购账户
		ApplicationRouter.POST("getApplicationSetting", v1.FindApplicationSetting)       // 获取采购端设置

		ApplicationRouter.POST("getMessage", app.GetMessage)       // 获取消息列表
		ApplicationRouter.POST("deleteMessage", app.DeleteMessage) // 删除消息
		ApplicationRouter.POST("getStrategy", app.GetStrategyData) // 获取采购端设置

	}
}

func InitAppPublicRouter(Router *gin.RouterGroup) {
	ApplicationRouter := Router.Group("application")
	{
		ApplicationRouter.POST("getToken", app.GetToken)                 // 获取ApplicationLevel列表
		ApplicationRouter.POST("getAvailable", app.MessagePoolAvailable) // 获取采购端设置

	}
}
