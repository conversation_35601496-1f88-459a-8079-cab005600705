package service

import (
	"application/model"
	"application/mq"
	"application/request"
	"errors"
	"gorm.io/gorm"
	"strconv"
	"time"
	"yz-go/source"
	"yz-go/utils"
)

func GetApplicationApplyList(info request.ApplicationSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ApplicationApplyRecord{})
	var applicationApplyRecords []model.ApplicationApplyRecord
	// 如果有条件搜索 下方会自动创建搜索语句
	var applicationIds []uint
	adb := source.DB().Unscoped().Model(&model.Application{})
	if info.CompanyName != "" {
		adb = adb.Where("`company_name` LIKE ?", "%"+info.CompanyName+"%")
	}
	if info.ProvinceId != 0 {
		adb = adb.Where("`province_id` = ?", info.ProvinceId)
	}
	if info.CityId != 0 {
		adb = adb.Where("`city_id` = ?", info.CityId)
	}
	if info.DistrictId != 0 {
		adb = adb.Where("`district_id` = ?", info.DistrictId)
	}
	if info.LegalPersonName != "" {
		adb = adb.Where("`legal_person_name` LIKE ?", "%"+info.LegalPersonName+"%")
	}
	if info.AppLevelID != 0 {
		adb = adb.Where("`app_level_id` = ?", info.AppLevelID)
	}
	if info.MemberId != 0 {
		adb = adb.Where("`member_id` = ?", info.MemberId)
	}
	err = adb.Pluck("id", &applicationIds).Error

	db.Where("application_id in ?", applicationIds)
	//db.Preload("Application.ApplicationLevel")

	err = db.Count(&total).Error
	err = db.Order("updated_at desc").Limit(limit).Offset(offset).Find(&applicationApplyRecords).Error
	if err != nil {
		return
	}
	var ids []uint
	err = source.DB().Model(&model.ApplicationApplyRecord{}).Pluck("application_id", &ids).Error
	if err != nil {
		return
	}
	var applications []model.Application
	err = source.DB().Unscoped().Preload("ApplicationLevel").Where("id in ?", ids).Find(&applications).Error
	if err != nil {
		return
	}
	var applicationMap = make(map[uint]model.Application)
	for _, v := range applications {
		applicationMap[v.ID] = v
	}
	for k, vr := range applicationApplyRecords {
		applicationApplyRecords[k].Application = applicationMap[vr.ApplicationID]
	}
	return err, applicationApplyRecords, total
}

func CreateApplicationApply(application model.Application) (err error) {
	var applicationApply []model.ApplicationApplyRecord
	var applicationCheck []model.Application
	err = source.DB().Where("member_id = ?", application.MemberId).Find(&applicationCheck).Error
	if err != nil || len(applicationCheck) > 0 {
		return errors.New("您已注册采购端，无需再次申请")
	}
	err = source.DB().Where("user_id = ?", application.MemberId).Where("status = 0").Find(&applicationApply).Error
	if err != nil || len(applicationApply) > 0 {
		return errors.New("已经提交过申请，请等待审核完成")
	}
	var create = model.Application{
		ApplicationModel: model.ApplicationModel{
			CompanyName:         application.CompanyName,
			CompanyIntro:        application.CompanyIntro,
			ProvinceId:          application.ProvinceId,
			CityId:              application.CityId,
			DistrictId:          application.DistrictId,
			Address:             application.Address,
			CreditCode:          application.CreditCode,
			BusinessLicense:     application.BusinessLicense,
			LegalPersonName:     application.LegalPersonName,
			IdCardNumber:        application.IdCardNumber,
			IdCardFront:         application.IdCardFront,
			IdCardBackend:       application.IdCardBackend,
			ContactsName:        application.ContactsName,
			ContactsPhontnumber: application.ContactsPhontnumber,
			ContactsEmail:       application.ContactsEmail,
			AppName:             application.AppName,
			AppLevelID:          application.AppLevelID,
			CallBackLink:        application.CallBackLink,
			IpList:              application.IpList,
			MemberId:            application.MemberId,
		},
	}
	err = source.DB().Create(&create).Error
	if err != nil {
		return
	}
	err = source.DB().Create(&model.ApplicationApplyRecordModel{UserID: uint(application.MemberId), ApplicationID: create.ID, Status: 0}).Error
	if err != nil {
		return
	}
	err = source.DB().Delete(&application, create.ID).Error
	return err
}

func CheckApplicationApply(applicationApply model.ApplicationApplyRecord) (err error) {
	var applicationApplyCheck model.ApplicationApplyRecord
	err = source.DB().First(&applicationApplyCheck, applicationApply.ID).Error
	if err != nil {
		return
	}
	if applicationApplyCheck.Status != 0 {
		return errors.New("审核状态错误,已经审核完成，请刷新页面")
	}
	err = source.DB().Save(&applicationApply).Error
	if applicationApply.Status == 1 {
		err = source.DB().Unscoped().Model(&model.Application{}).Where("id = ?", applicationApply.ApplicationID).Update("deleted_at", nil).Error
		if err != nil {
			return
		}
		err = mq.PublishMessage(uint(applicationApply.ApplicationID), mq.Created)

	}

	return
}

func GetApplicationApply(userID uint) (err error, record model.ApplicationApplyRecord) {

	err = source.DB().Unscoped().Preload("ApplicationLevel").Where("member_id = ?", userID).Order("id desc").First(&record.Application).Error
	if err != nil {
		return
	}
	err = source.DB().Where("user_id = ?", userID).Preload("ApplicationShops").Order("updated_at desc").First(&record).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			record.ApplicationID = record.Application.ID
			record.UserID = userID
			record.Status = 1
			err = source.DB().Where("application_id = ?", record.ApplicationID).Find(&record.ApplicationShops).Error
			if err != nil {
				return
			}
		} else {
			return
		}
	}
	if record.Application.AppSecret == "" {
		secret := utils.MD5V([]byte("application" + strconv.Itoa(int(record.ApplicationID)) + strconv.Itoa(int(time.Now().Unix()))))
		record.Application.AppSecret = secret
		err = record.Application.BeforeSave(source.DB())
		if err != nil {
			return
		}
		err = source.DB().Updates(&record.Application).Error
	}
	return
}

func GetApplicationApplyCount() (total int64, err error) {
	db := source.DB().Model(&model.ApplicationApplyRecord{})

	db.Where("status = 0")

	err = db.Count(&total).Error
	return
}
