package service

import (
	"application/model"
	"application/request"
	yzRequest "yz-go/request"
	"yz-go/source"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateApplicationGroup
//@description: 创建ApplicationGroup记录
//@param: supplierGroup model.ApplicationGroup
//@return: err error

func CreateApplicationGroup(supplierGroup model.ApplicationGroup) (err error) {
	err = source.DB().Create(&supplierGroup).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationGroup
//@description: 删除ApplicationGroup记录
//@param: supplierGroup model.ApplicationGroup
//@return: err error

func DeleteApplicationGroup(supplierGroup model.ApplicationGroup) (err error) {
	err = source.DB().Delete(&supplierGroup).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationGroupByIds
//@description: 批量删除ApplicationGroup记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteApplicationGroupByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.ApplicationGroup{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateApplicationGroup
//@description: 更新ApplicationGroup记录
//@param: supplierGroup *model.ApplicationGroup
//@return: err error

func UpdateApplicationGroup(supplierGroup model.ApplicationGroup) (err error) {
	err = source.DB().Save(&supplierGroup).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationGroup
//@description: 根据id获取ApplicationGroup记录
//@param: id uint
//@return: err error, supplierGroup model.ApplicationGroup

func GetApplicationGroup(id uint) (err error, supplierGroup model.ApplicationGroup) {
	err = source.DB().Where("id = ?", id).First(&supplierGroup).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationGroupInfoList
//@description: 分页获取ApplicationGroup记录
//@param: info request.ApplicationGroupSearch
//@return: err error, list interface{}, total int64

func GetApplicationGroupInfoList(info request.ApplicationGroupSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ApplicationGroup{})
	var supplierGroups []model.ApplicationGroup
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Name != "" {
		db = db.Where("`name` LIKE ?", "%"+info.Name+"%")
	}
	if info.Sort != 0 {
		db = db.Where("`sort` = ?", info.Sort)
	}
	if info.IsShow != 0 {
		db = db.Where("`is_show` = ?", info.IsShow)
	}
	err = db.Count(&total).Error
	err = db.Order("sort desc").Limit(limit).Offset(offset).Find(&supplierGroups).Error
	return err, supplierGroups, total
}

//@author:
//@function: GetApplicationGroupInfoListNotPage
//@description: 获取ApplicationGroup列表(无分页)
//@param:
//@return: err error, list interface{}
func GetApplicationGroupInfoListNotPage() (err error, list interface{}) {
	db := source.DB().Model(&model.ApplicationGroup{})
	var supplierGroups []model.ApplicationGroup
	err = db.Order("sort desc").Find(&supplierGroups).Error
	return err, supplierGroups
}
