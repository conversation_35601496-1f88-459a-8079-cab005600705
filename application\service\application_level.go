package service

import (
	"application/model"
	"application/request"
	"errors"
	"yz-go/cache"
	yzRequest "yz-go/request"
	"yz-go/source"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateApplicationLevel
//@description: 创建ApplicationLevel记录
//@param: applicationLevel model.ApplicationLevel
//@return: err error

func CreateApplicationLevel(applicationLevel model.ApplicationLevel) (err error) {
	err = source.DB().Create(&applicationLevel).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationLevel
//@description: 删除ApplicationLevel记录
//@param: applicationLevel model.ApplicationLevel
//@return: err error

func DeleteApplicationLevel(applicationLevel model.ApplicationLevel) (err error) {
	var applicationTotal int64
	err = source.DB().Model(&model.Application{}).Where("app_level_id = ?", applicationLevel.ID).Count(&applicationTotal).Error
	if err != nil {
		return
	}
	if applicationTotal > 0 {
		err = errors.New("删除失败,尚有选择此等级的采购端存在")
		return
	}
	err = source.DB().Delete(&applicationLevel).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationLevelByIds
//@description: 批量删除ApplicationLevel记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteApplicationLevelByIds(ids yzRequest.IdsReq) (err error) {
	var applicationTotal int64
	err = source.DB().Model(&model.Application{}).Where("app_level_id in ?", ids.Ids).Count(&applicationTotal).Error
	if err != nil {
		return
	}
	if applicationTotal > 0 {
		err = errors.New("删除失败,尚有选择此等级的采购端存在")
		return
	}
	err = source.DB().Delete(&[]model.ApplicationLevel{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateApplicationLevel
//@description: 更新ApplicationLevel记录
//@param: applicationLevel *model.ApplicationLevel
//@return: err error

func UpdateApplicationLevel(applicationLevel model.ApplicationLevel) (err error) {
	err = source.DB().Save(&applicationLevel).Error
	cache.ClearAllApplications()
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationLevel
//@description: 根据id获取ApplicationLevel记录
//@param: id uint
//@return: err error, applicationLevel model.ApplicationLevel

func GetApplicationLevel(id uint) (err error, applicationLevel model.ApplicationLevel) {
	err = source.DB().Where("id = ?", id).First(&applicationLevel).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationLevelInfoList
//@description: 分页获取ApplicationLevel记录
//@param: info request.ApplicationLevelSearch
//@return: err error, list interface{}, total int64

func GetApplicationLevelInfoList(info request.ApplicationLevelSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ApplicationLevel{})
	var applicationLevels []model.ApplicationLevel
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.LevelName != "" {
		db = db.Where("`level_name` LIKE ?", "%"+info.LevelName+"%")
	}
	if info.NumMax != 0 {
		db = db.Where("`num_max` = ?", info.NumMax)
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&applicationLevels).Error
	return err, applicationLevels, total
}

func GetApplicationLevelLFrontList(info request.ApplicationLevelSearch) (err error, list interface{}) {
	// 创建db
	db := source.DB().Model(&model.ApplicationLevel{})
	var applicationLevels []model.ApplicationLevel
	// 如果有条件搜索 下方会自动创建搜索语句

	db = db.Where("is_display = ?", 1)

	err = db.Find(&applicationLevels).Error
	return err, applicationLevels
}
