package service

import (
	"application/model"
	"yz-go/source"
)

// SaveSysSetting
//@author: [piexlmax](https://github.com/piexlmax)
//@function: SaveSysSetting
//@description: 保存SysTop记录
//@param: data *setting.SysSetting
//@return: err error
func SaveApplicationSetting(data model.ApplicationSetting) (err error) {
	if data.ID != 0{
		err = source.DB().Updates(&data).Error
	}else {
		err = source.DB().Create(&data).Error
	}
	return err
}


func GetApplicationSetting() (err error, sysSetting model.ApplicationSetting) {

	err = source.DB().Where("`key` = ?", "application_setting").First(&sysSetting).Error

	return
}