package service

import (
	"application/model"
	"application/request"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"os"
	"strconv"
	"time"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/config"
	yzRequest "yz-go/request"
	"yz-go/source"
	"yz-go/utils"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateApplicationShop
//@description: 创建ApplicationShop记录
//@param: supplierGroup model.ApplicationShop
//@return: err error

func CreateApplicationShop(supplierGroup model.ApplicationShop) (err error) {
	var application model.Application
	err = source.DB().First(&application, supplierGroup.ApplicationID).Error
	if err != nil {
		return err
	}
	if application.StatusCode == 1 && application.IsMultiShop == 1 {
		if err = ValidateCallback(supplierGroup.CallbackLink); err != nil {
			return
		}
	}

	err = source.DB().Create(&supplierGroup).Error
	if err != nil {
		return err
	}
	err, data := CreateShopKeySecret(supplierGroup.ApplicationID, supplierGroup.ID)
	if err != nil {
		return err
	}
	err = source.DB().Model(&model.ApplicationShop{}).Where("id = ?", supplierGroup.ID).Update("app_secret", data).Error
	if err != nil {
		return err
	}
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationShop
//@description: 删除ApplicationShop记录
//@param: supplierGroup model.ApplicationShop
//@return: err error

func DeleteApplicationShop(supplierGroup model.ApplicationShop) (err error) {
	err = source.DB().Delete(&supplierGroup).Error
	if err != nil {
		return
	}
	cache.ClearApplicationShop(supplierGroup.ID)
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteApplicationShopByIds
//@description: 批量删除ApplicationShop记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteApplicationShopByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.ApplicationShop{}, "id in ?", ids.Ids).Error
	for _, id := range ids.Ids {
		cache.ClearApplicationShop(id)

	}
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateApplicationShop
//@description: 更新ApplicationShop记录
//@param: supplierGroup *model.ApplicationShop
//@return: err error

func UpdateApplicationShop(supplierGroup model.ApplicationShop) (err error) {
	var application model.Application
	err = source.DB().First(&application, supplierGroup.ApplicationID).Error
	if err != nil {
		return err
	}
	if application.StatusCode == 1 && application.IsMultiShop == 1 {
		if err = ValidateCallback(supplierGroup.CallbackLink); err != nil {
			return
		}
	}
	err = source.DB().Updates(&supplierGroup).Error
	if err != nil {
		return
	}
	cache.ClearApplicationShop(supplierGroup.ID)
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationShop
//@description: 根据id获取ApplicationShop记录
//@param: id uint
//@return: err error, supplierGroup model.ApplicationShop

func GetApplicationShop(id uint) (err error, supplierGroup model.ApplicationShop) {
	err = source.DB().Where("id = ?", id).First(&supplierGroup).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationShopInfoList
//@description: 分页获取ApplicationShop记录
//@param: info request.ApplicationShopSearch
//@return: err error, list interface{}, total int64

func GetApplicationShopInfoList(info request.ApplicationShopSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.ApplicationShop{}).Where("application_id = ?", info.ID)
	var supplierGroups []model.ApplicationShop
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.ShopName != "" {
		db = db.Where("`shop_name` LIKE ?", "%"+info.ShopName+"%")
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&supplierGroups).Error
	return err, supplierGroups, total
}

// @author:
// @function: GetApplicationShopInfoListNotPage
// @description: 获取ApplicationShop列表(无分页)
// @param:
// @return: err error, list interface{}
func GetApplicationShopInfoListNotPage() (err error, list interface{}) {
	db := source.DB().Model(&model.ApplicationShop{})
	var supplierGroups []model.ApplicationShop
	err = db.Order("sort desc").Find(&supplierGroups).Error
	return err, supplierGroups
}

type ExportShopStruct struct {
	Search   request.ApplicationShopSearch `json:"search"`
	Total    int64                         `json:"total"`
	ExportDb *gorm.DB                      `json:"exportDb"`
}

func ExportApplicationShop(search request.ApplicationShopSearch) (err error, link string) {
	var exportStruct ExportShopStruct
	exportStruct.Search = search
	// 创建db
	exportStruct.ExportDb = source.DB().Model(&model.ApplicationShop{})
	// 如果有条件搜索 下方会自动创建搜索语句
	err = exportStruct.ExportSearch()
	if err != nil {
		return
	}
	err = exportStruct.ExportDb.Count(&exportStruct.Total).Error
	if err != nil {
		err = errors.New("获取导出数量失败" + err.Error())
		return
	}
	if exportStruct.Total == 0 {
		err = errors.New("没有需要导出的数据" + err.Error())
		return
	}
	err, link = exportStruct.ExportStart()
	return
}
func (exportStruct ExportShopStruct) ExportStart() (err error, link string) {
	var applications []model.ApplicationShop
	err = exportStruct.ExportDb.Find(&applications).Error
	if err != nil {
		log.Log().Error("采购端导出数据错误", zap.Any("search", exportStruct.Search), zap.Any("err", err))
		err = errors.New("导出数据获取错误" + err.Error())
		return
	}

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "id")
	f.SetCellValue("Sheet1", "B1", "创建时间")
	f.SetCellValue("Sheet1", "C1", "商城名称")

	f.SetCellValue("Sheet1", "D1", "回调地址")

	i := 2
	for _, application := range applications {
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), application.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), application.CreatedAt.Format("2006-01-02 15:04:05"))
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), application.ShopName)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), application.CallbackLink)

		i++
	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	path := config.Config().Local.Path + "/plugin_export_application_shop"
	exist, _ := utils.PathExists(path)
	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	timeString := time.Now().Format("20060102150405")

	link = path + "/" + timeString + "采购端店铺导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return
}
func (exportStruct ExportShopStruct) ExportSearch() (err error) {
	exportStruct.ExportDb = exportStruct.ExportDb.Where("`application_id` = ?", exportStruct.Search.ApplicationID)
	if exportStruct.Search.ShopName != "" {
		exportStruct.ExportDb = exportStruct.ExportDb.Where("`shop_name` LIKE ?", "%"+exportStruct.Search.ShopName+"%")
	}

	return
}
