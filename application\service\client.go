package service

import (
	"net"
	"net/http"
	"time"
)

var optimizedHttpClient *http.Client

func init() {
	// 配置 Transport
	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: (&net.Dialer{
			Timeout:   5 * time.Second,  // 连接超时时间
			KeepAlive: 30 * time.Second, // 启用 KeepAlive
		}).DialContext,
		ForceAttemptHTTP2:     true,             // 尝试使用 HTTP/2
		MaxIdleConns:          200,              // 最大空闲连接数 (根据你的并发量调整)
		MaxIdleConnsPerHost:   100,              // 每个目标主机的最大空闲连接数 (应小于等于 MaxIdleConns)
		IdleConnTimeout:       90 * time.Second, // 空闲连接超时时间
		TLSHandshakeTimeout:   10 * time.Second, // TLS 握手超时
		ExpectContinueTimeout: 1 * time.Second,
	}

	// 创建 Client
	optimizedHttpClient = &http.Client{
		Timeout:   30 * time.Second, // 整个请求的超时时间 (包括连接、读写)
		Transport: transport,
	}
}

// GetHttpClient 返回全局的 HTTP Client 实例
func GetHttpClient() *http.Client {
	return optimizedHttpClient
}
