package service

import (
	"application/model"
	"application/request"
	"errors"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

func GetMessagePool(info request.MessagePoolSearch) (err error, messagePool []model.MessagePoolResponse) {
	//var messages []model.MessagePoolResponse
	if info.PageSize > 100 {
		info.PageSize = 100
	}
	db := source.DB().Model(&model.MessagePoolResponse{})
	db = db.Where("app_id = ? and app_shop_id = ?", info.AppID, info.AppShopID)
	if info.Type != 0 {
		db = db.Where("type = ?", info.Type)
	}
	err = db.Order("id asc").Limit(info.PageSize).Find(&messagePool).Error

	return
}

func CreateMessagePool(message model.MessagePool) (err error) {
	err = source.DB().Create(&message).Error
	return
}

func SignMessagePool(ids []uint) (err error) {
	var messages []model.MessagePool
	err = source.DB().Model(&model.MessagePool{}).Where("id in ?", ids).Find(&messages).Error
	if err != nil {
		return
	}
	var messageBackup []model.MessagePoolBackup
	for _, msg := range messages {
		messageBackup = append(messageBackup, model.MessagePoolBackup{
			Model: source.Model{
				CreatedAt: msg.CreatedAt,
				UpdatedAt: &source.LocalTime{
					time.Now(),
				},
			},
			Type:      msg.Type,
			Content:   msg.Content,
			AppID:     msg.AppID,
			AppShopID: msg.AppShopID,
			Prefix:    msg.Prefix,
			Status:    1,
		})
	}
	err = source.DB().Create(&messageBackup).Error
	if err != nil {
		return
	}
	err = source.DB().Unscoped().Delete(&model.MessagePool{}, "id in ?", ids).Error
	if err != nil {
		return
	}
	return
}

func InsertWhite(appID uint, appShopID int) (err error) {
	messagePoolWhiteList := model.MessagePoolWhiteList{
		AppID:     appID,
		AppShopID: uint(appShopID),
	}
	err = source.DB().Model(model.MessagePoolWhiteList{}).Where("app_id = ?", appID).Where("app_shop_id = ?", appShopID).FirstOrCreate(&messagePoolWhiteList).Error
	err = ResetWhiteList()
	return
}

var whiteList []string

func ResetWhiteList() (err error) {
	var list []model.MessagePoolWhiteList
	err = source.DB().Find(&list).Error
	if err != nil {
		return err
	}
	var whiteListString []string
	for _, white := range list {
		whiteListString = append(whiteListString, strconv.Itoa(int(white.AppID))+"+"+strconv.Itoa(int(white.AppShopID)))
	}
	whiteList = whiteListString
	log.Log().Info("白名单重置成功")
	return
}
func GetWhiteCheck(appID uint, appShopID uint) (err error) {
	if whiteList == nil {
		err = ResetWhiteList()
		if err != nil {
			return
		}
	}
	for _, item := range whiteList {
		if item == strconv.Itoa(int(appID))+"+"+strconv.Itoa(int(appShopID)) {
			//log.Log().Info("white list find success")
			//fmt.Println("white list find success", strconv.Itoa(int(appID))+"+"+strconv.Itoa(int(appShopID)))
			return nil
		}
	}
	err = errors.New("not in white list")

	return
}
