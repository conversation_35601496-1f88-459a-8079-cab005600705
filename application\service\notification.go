package service

import (
	"application/middleware"
	model2 "application/model"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"product/mq"
	"product/service"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"

	"sync"

	"go.uber.org/zap"
)

// determineMessageType 确定消息类型
func determineMessageType(product service.ProductSync) mq.ProductMessageType {
	var messageType mq.ProductMessageType

	// 福禄商品
	if product.GatherSupply.CategoryID == 98 {
		return "fuluGoods."
	}

	// 会员权益
	if product.GatherSupply.CategoryID == 111 {
		var equityProduct model2.UserEquityProduct
		if err := source.DB().Unscoped().Where("product_id = ?", product.SourceGoodsID).First(&equityProduct).Error; err == nil && equityProduct.ID > 0 {
			return "equityGoods."
		}
	}

	// 蛋糕叔叔
	if product.Source == 110 {
		var count int64
		if err := source.DB().Table("cake_products").Where("id = ?", product.SourceGoodsID).Count(&count).Error; err == nil && count > 0 {
			return "CakeGoods."
		}
	}

	// 课程
	if product.Source == 0 && product.PluginID == 18 && product.GatherSupplyID == 0 {
		return "Course."
	}

	// 联联周边游
	if product.Source == 109 {
		var count int64
		if err := source.DB().Table("lian_product_news").Where("product_id = ?", product.SourceGoodsID).Count(&count).Error; err == nil && count > 0 {
			return "lianlianGoods."
		}
	}

	// 租赁商品
	if product.GatherSupply.CategoryID == 120 {
		return "leaseGoods."
	}

	return messageType
}

// createProductMessage 创建产品消息
func createProductMessage(product service.ProductSync, messageType mq.ProductMessageType, level int, isStock int, gatherMessageType mq.ProductMessageType) mq.ProductMessage {
	productMessage := mq.ProductMessage{
		ProductID:   product.ID,
		MessageType: messageType,
		Level:       level,
		IsStock:     isStock,
		MessageCode: 0, // 默认设置为 0
	}

	if gatherMessageType != "" {
		// 只有福禄商品和会员权益商品设置 MessageCode 为 1
		if gatherMessageType == "fuluGoods." || gatherMessageType == "equityGoods." {
			productMessage.MessageCode = 1
		}
		productMessage.MessageType = gatherMessageType + productMessage.MessageType
	}

	return productMessage
}

// 使用之前定义的常量，或者重新定义一个更合适的并发数
const workerPoolSize = 50 // 例如，限制为同时处理 50 个应用/店铺的通知

// sendToApplications 发送通知到应用
func sendToApplications(product service.ProductSync, productMessage mq.ProductMessage, gatherMessageType string, messageType mq.ProductMessageType) error {
	// 1. 获取应用列表 (保持不变)
	var applications []model2.Application
	// 优化：同时预加载 ApplicationShop，避免 N+1 查询
	if err := source.DB().Preload("ApplicationLevel").Preload("ApplicationShops").Find(&applications).Error; err != nil {
		// LogInfo(0, product.ID, "查询采购端列表出现错误", err) // 确保 LogInfo 函数存在或替换为 log.Log()
		log.Log().Error("查询采购端列表出现错误", zap.Uint("product_id", product.ID), zap.Error(err))
		return err
	}

	// 2. 准备任务列表
	type notificationTask struct {
		app           model2.Application
		shop          model2.ApplicationShop // shop 可能为空 (ID=0) 代表单店铺应用
		jsonStr       []byte
		productMsg    mq.ProductMessage
		appIds        []uint
		gatherMsgType string
		msgType       mq.ProductMessageType
	}
	var tasks []notificationTask

	// 获取应用ID列表 (保持不变)
	var appIds []uint
	for _, storage := range product.Storages {
		appIds = append(appIds, storage.AppID)
	}

	// 准备消息数据 (只需一次)
	goodsMessageMap := map[string]interface{}{
		"product_id": productMessage.ProductID,
		"is_stock":   productMessage.IsStock,
	}
	jsonStr, _ := json.Marshal(goodsMessageMap) // 忽略错误可能导致后续问题，最好处理一下

	// 填充任务列表
	for _, app := range applications {
		if app.AppLevelID == 0 || app.MemberId == 0 {
			continue
		}

		// 复制 productMessage 以避免并发修改 MemberSign
		currentProductMessage := productMessage
		currentProductMessage.MemberSign = app.AppSecret // 默认使用 App 的 Secret

		if app.IsMultiShop == 1 {
			// 使用预加载的 Shops 数据
			for _, shop := range app.ApplicationShops {
				// 多店铺使用自己的 Secret (如果 shop 有独立 Secret 的话，这里假设沿用 App 的，如果 shop 有独立 secret 需要修改)
				// taskProductMessage := currentProductMessage // 如果 shop 有独立 secret，在这里设置
				// taskProductMessage.MemberSign = shop.AppSecret
				tasks = append(tasks, notificationTask{
					app:           app,
					shop:          shop, // 传递 shop 信息
					jsonStr:       jsonStr,
					productMsg:    currentProductMessage, // 使用设置好 MemberSign 的消息
					appIds:        appIds,
					gatherMsgType: gatherMessageType,
					msgType:       messageType,
				})
			}
		} else {
			tasks = append(tasks, notificationTask{
				app: app,
				// shop:       model2.ApplicationShop{}, // shop 为空代表单店铺
				jsonStr:       jsonStr,
				productMsg:    currentProductMessage,
				appIds:        appIds,
				gatherMsgType: gatherMessageType,
				msgType:       messageType,
			})
		}
	}

	if len(tasks) == 0 {
		return nil // 没有需要处理的任务
	}

	// 3. 创建工作者池和通道
	// 使用 buffered channel 控制并发数量
	pool := make(chan struct{}, workerPoolSize)
	errChan := make(chan error, len(tasks)) // 通道大小为任务数量
	messagePoolChan := make(chan *model2.MessagePool, len(tasks))
	var wg sync.WaitGroup

	// 4. 分发任务给工作者
	wg.Add(len(tasks))
	for _, task := range tasks {
		// 从池中获取一个 "令牌"，如果池满了则阻塞
		pool <- struct{}{}

		go func(t notificationTask) {
			defer func() {
				// 释放令牌回池中
				<-pool
				wg.Done()
				// 添加 recover 防止单个任务 panic 影响整体
				if r := recover(); r != nil {
					log.Log().Error("处理通知任务时发生 Panic",
						zap.Any("panic", r),
						zap.Stack("stack"),
						zap.Uint("app_id", t.app.ID),
						zap.Uint("shop_id", t.shop.ID),
						zap.Uint("product_id", t.productMsg.ProductID))
					errChan <- fmt.Errorf("panic occurred: %v", r)
				}
			}()

			// 调用 processShop 处理任务
			// 注意：product 参数不再需要传递，因为相关信息已在 task 中
			if messagePool, err := processShop(t.app, t.shop, t.productMsg, t.jsonStr, t.appIds, t.gatherMsgType, t.msgType); err != nil {
				errChan <- err // 发送错误
			} else {
				if messagePool != nil {
					messagePoolChan <- messagePool // 发送消息池数据
				}
				// errChan <- nil // 不需要发送 nil 错误，只收集实际错误
			}
		}(task)
	}

	// 5. 等待所有任务完成并收集结果 (保持不变)
	wg.Wait()
	close(messagePoolChan)
	close(errChan)

	// 收集消息池数据并批量写入 (保持不变)
	var messagePools []*model2.MessagePool
	for mp := range messagePoolChan {
		messagePools = append(messagePools, mp) // 不需要检查 nil，因为发送时已检查
	}
	if len(messagePools) > 0 {
		if err := source.DB().CreateInBatches(&messagePools, 100).Error; err != nil {
			// 记录详细错误日志
			log.Log().Error("批量写入消息池失败", zap.Error(err), zap.Int("count", len(messagePools)))
			// 考虑是否将此错误也加入 errs 列表
			// return fmt.Errorf("批量写入消息池失败: %v", err) // 或者只记录日志，让下面的错误处理继续
		} else {
			log.Log().Info("成功批量写入消息池", zap.Int("count", len(messagePools)))
		}
	}

	// 检查错误 (保持不变)
	var errs []error
	for err := range errChan {
		if err != nil { // 确保只收集非 nil 错误
			errs = append(errs, err)
		}
	}

	if len(errs) > 0 {
		// 记录更详细的错误信息
		log.Log().Error("发送通知时发生错误",
			zap.Int("error_count", len(errs)),
			zap.Uint("product_id", productMessage.ProductID),
			zap.Errors("errors", errs[:min(5, len(errs))])) // 只记录前几个错误详情避免日志过长
		return fmt.Errorf("发送通知时发生 %d 个错误，首个错误: %v", len(errs), errs[0])
	}

	return nil
}

// 移除 handleMultiShopApplication 和 handleSingleShopApplication 函数，逻辑已合并到 sendToApplications

// 修改 processShop 函数签名，移除 product 参数
func processShop(app model2.Application, shop model2.ApplicationShop, productMessage mq.ProductMessage, jsonStr []byte, appIds []uint, gatherMessageType string, messageType mq.ProductMessageType) (*model2.MessagePool, error) {
	// 移除 product 参数后，不再需要判断 productMessage.ProductID == 798155
	// if productMessage.ProductID == 798155 { ... } 这类调试日志可以移除或保留

	// 检查 app 是否在 appIds 中 (优化：可以在 sendToApplications 准备 tasks 时就过滤掉)
	if !InIntSlice(appIds, app.ID) {
		// log.Log().Debug("应用不在商品存储列表中，跳过", zap.Uint("app_id", app.ID), zap.Uint("product_id", productMessage.ProductID))
		return nil, nil // 不属于错误，直接返回 nil
	}

	// ... (获取 shopID, callbackLink, appSecret 的逻辑保持不变) ...
	var shopID uint
	var callbackLink string
	var appSecret string
	var callBackLinkValidity int
	if shop.ID != 0 {
		shopID = shop.ID
		callbackLink = shop.CallbackLink
		// 如果 shop 有独立 secret，在这里获取 shop.AppSecret
		// appSecret = shop.AppSecret
		appSecret = shop.AppSecret // 假设沿用 App 的 Secret
		callBackLinkValidity = shop.CallBackLinkValidity
	} else {
		shopID = 0
		callbackLink = app.GetCallBackLink()
		appSecret = app.AppSecret
		callBackLinkValidity = app.CallBackLinkValidity

	}
	// 更新 productMessage 中的 MemberSign，确保使用的是正确的 Secret
	productMessage.MemberSign = appSecret

	// ... (检查白名单 GetWhiteCheck 的逻辑保持不变) ...
	// 考虑缓存 GetWhiteCheck 的结果以提高性能
	err := GetWhiteCheck(app.ID, shopID)
	isMessagePool := shop.ID != 0 && shop.IsMessagePool == 1 || shop.ID == 0 && app.IsMessagePool == 1
	skipMessagePool := shop.ID != 0 && shop.IsMessagePool == 2 || shop.ID == 0 && app.IsMessagePool == 2

	// 处理消息池 (保持不变)
	if (err == nil || isMessagePool) && !skipMessagePool {
		// 确保 getMessagePoolTypeWithProductMessage 函数存在且正确
		poolType := getMessagePoolTypeWithProductMessage(mq.ProductMessageType(messageType))
		// log.Log().Debug("添加到消息池", zap.Uint("app_id", app.ID), zap.Uint("shop_id", shopID), zap.String("type", poolType))
		return &model2.MessagePool{
			Type:      poolType,
			Content:   string(jsonStr),
			AppID:     app.ID,
			AppShopID: shopID,
			Prefix:    gatherMessageType,
		}, nil
	}

	// 直接发送消息 (逻辑微调)
	// 移除重复的 InIntSlice 检查
	if callBackLinkValidity != 0 { // && InIntSlice(appIds, app.ID) { // 已在函数开头检查
		if productMessage.ProductID == 1051634 {
			log.Log().Debug("消息目的地", zap.Any("data", productMessage), zap.String("url", callbackLink))
		}
		// productMessage.MemberSign = appSecret // 已在前面设置
		err := sendMessage(productMessage, callbackLink)
		if err != nil {
			// 返回错误，由上层记录日志和处理
			return nil, fmt.Errorf("发送消息到 %s 失败 (AppID: %d, ShopID: %d): %w", callbackLink, app.ID, shopID, err)
		}
		return nil, nil // 发送成功
	}

	// log.Log().Debug("不满足发送条件，跳过", zap.Uint("app_id", app.ID), zap.Uint("shop_id", shopID), zap.Int("callback_validity", app.CallBackLinkValidity))
	return nil, nil // 不发送也不算错误
}

// sendMessage 发送消息到回调地址 (保持不变, 但需确保 post 使用了优化后的 http client)
func sendMessage(productMessage mq.ProductMessage, callbackLink string) error {
	header := map[string]string{
		"Content-Type": "application/json",
	}
	messageId := "product" + strconv.Itoa(int(productMessage.ProductID)) +
		string(productMessage.MessageType) +
		strconv.Itoa(int(time.Now().Unix()))
	productMessage.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))

	jsonBodyData, _ := json.Marshal(productMessage)
	// 确保 middleware.SignMessage 函数存在且正确
	header = middleware.SignMessage(string(jsonBodyData), productMessage.MemberSign, map[string]string{}, header)

	// 确保 post 函数使用了配置合理的 http.Client (例如设置超时、连接池)
	err, respon := post(callbackLink, productMessage, header) // 假设 post 返回 (error, YourResponseType)
	if err != nil {
		// log.Log().Info("与商城通信失败!", zap.Any("err", err), zap.Any("data", productMessage)) // 由调用者记录日志
		return err // 直接返回错误
	}

	// 假设 respon 是一个结构体，包含 Code 和 Result/Msg 字段
	if respon.Code != 0 && respon.Result != 1 {
		err = fmt.Errorf("推送消息失败：%s (Code: %d)", respon.Msg, respon.Code)
		// log.Log().Info("推送消息失败!"+callbackLink, zap.Any("err", respon), zap.Any("data", productMessage)) // 由调用者记录日志
		return err
	}

	return nil
}

// SendNotification 处理通知操作
func SendNotification(product service.ProductSync, messageType mq.ProductMessageType, level int, isStock int) error {
	// 确定消息类型
	gatherMessageType := determineMessageType(product)
	productMessage := createProductMessage(product, messageType, level, isStock, gatherMessageType)

	// 发送通知
	return sendToApplications(product, productMessage, string(gatherMessageType), messageType)
}

// SendDeleteNotification 处理删除通知操作
func SendDeleteNotification(product service.ProductSync, level int) error {
	// 确定消息类型
	gatherMessageType := determineMessageType(product)
	productMessage := createProductMessage(product, mq.Delete, level, 0, gatherMessageType)

	// 发送通知
	return sendToApplications(product, productMessage, string(gatherMessageType), mq.Delete)
}

// 辅助函数 (如果不存在需要添加)
// 假设 post 函数签名如下，你需要根据你的实际实现调整
type YourResponseType struct {
	Code   int    `json:"code"`
	Result int    `json:"result"`
	Msg    string `json:"msg"`
	// ... 其他字段
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
