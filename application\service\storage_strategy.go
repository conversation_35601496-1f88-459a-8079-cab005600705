package service

import (
	"application/model"
	"application/request"
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
)

func GetStorageStrategyByUserID(userID uint) (err error, data model.StorageStrategy) {
	err = source.DB().Where("user_id = ?", userID).First(&data).Error
	return
}

func SetStorageStrategy(data model.StorageStrategy) (err error) {
	var app model.Application
	err = source.DB().Where("member_id = ?", data.UserID).First(&app).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("没有采购权限")
			return err
		}
		return
	}
	data.AppID = app.ID
	err = source.DB().Delete(&model.StorageStrategy{}, "user_id = ?", data.UserID).Error
	if err != nil {
		return
	}
	err = source.DB().Create(&data).Error
	return
}

func GetCategoryStrategyListByUserID(info request.CategoryPageInfo) (err error, data []model.CategoryStrategy, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	err = source.DB().Model(&model.CategoryStrategy{}).Where("user_id = ?", info.UserID).Count(&total).Error
	err = source.DB().Where("user_id = ?", info.UserID).Limit(limit).Offset(offset).Find(&data).Error
	return
}

func GetCategoryStrategyList(userID uint) (err error, data []model.CategoryStrategy) {
	err = source.DB().Where("user_id = ?", userID).Find(&data).Error
	return
}
func GetStrategyData(userID uint) (err error, data model.StorageStrategy, categoryData []model.CategoryStrategy) {
	err = source.DB().Where("user_id = ?", userID).First(&data).Error
	if err != nil {
		return
	}
	err = source.DB().Where("user_id = ?", userID).Find(&categoryData).Error
	return
}
func CreateCategoryStrategy(data model.CategoryStrategy) (err error) {
	var app model.Application
	err = source.DB().Where("member_id = ?", data.UserID).First(&app).Error
	if err != nil {
		return
	}
	data.AppID = app.ID
	err = source.DB().Create(&data).Error
	return
}

func UpdateCategoryStrategy(data model.CategoryStrategy) (err error) {
	err = source.DB().Updates(&data).Error
	return
}

func DeleteCategoryStrategy(data model.CategoryStrategy) (err error) {
	err = source.DB().Delete(&data).Error
	return
}
