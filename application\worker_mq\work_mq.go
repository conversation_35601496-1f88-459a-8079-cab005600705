package worker_mq

import (
	"application/model"
	"encoding/json"
	"fmt"
	"github.com/streadway/amqp"
	"strconv"
	"sync"
	"yz-go/mq"
	"yz-go/source"
)

const (
	KEY = "rePushMessage"
)

var publishRabbitMQ *source.SafeCh
var mqOnce sync.Once

func init() {
	handles = map[string]Handle{}
	mq.PushConsumer(ConsumeRabbitMQ)
}
func getPublishRabbitMQ() *amqp.Channel {
	mqOnce.Do(func() {
		publishRabbitMQ = source.SafeMQ(func(ch *amqp.Channel) {
			q, err := ch.Queue<PERSON>eclare(
				"rePushMessage", // name
				true,            // durable
				false,           // delete when unused
				false,           // exclusive
				false,           // no-wait
				nil,             // arguments
			)
			if err != nil {
				fmt.Println(err)
			}
			fmt.Println("消费者数量" + strconv.Itoa(q.Consumers))
		})
	})

	return publishRabbitMQ.GetCh()
}

// PublishMessage
// 订单消息推送
func PublishMessage(callBack model.RePushProductMessage) (err error) {
	message, err := json.Marshal(callBack)

	if err != nil {
		return
	}
	key := KEY
	err = getPublishRabbitMQ().Publish(
		"",
		key,
		//如果为true，根据exchange类型和routkey规则，如果无法找到符合条件的队列，那么会把发送的消息返回给发送者
		false,
		//如果为true，当exchange发送消息到队列侯发现队列上没有绑定消费者，则会把消息发还给发送者
		false,
		amqp.Publishing{
			ContentType:  "text/plain",
			Body:         message,
			DeliveryMode: amqp.Persistent,
		})
	return
}

type Handle func(callBack model.RePushProductMessage, i int) error

var handles map[string]Handle

func PushHandles(queueName string, handle Handle) {
	handles[queueName] = handle
	getPublishRabbitMQ()
}
func ConsumeHandle(queueName string, handle Handle) {
	var consumeRabbitMQ *amqp.Channel

	forever := make(chan bool)
	for i := 0; i < 10; i++ {
		go func(routineNum int) {
			var err error
			consumeRabbitMQ = source.MQ()
			// 定义队列
			_, err = consumeRabbitMQ.QueueDeclare(queueName, true, false, false, false, nil)
			if err != nil {
				fmt.Println(err)
			}
			//// 绑定队列
			//err = consumeRabbitMQ.QueueBind(
			//	queueName, // queue name
			//	KEY,       // routing key
			//	"",        // exchange
			//	false,
			//	nil)
			//if err != nil {
			//	fmt.Println(err)
			//}
			err = consumeRabbitMQ.Qos(1, 0, false)
			if err != nil {
				fmt.Println(err)
			}

			msgs, err := consumeRabbitMQ.Consume(
				queueName,
				//用来区分多个消费者
				"",
				//是否自动应答
				false,
				//是否具有排他性
				false,
				//如果设置为true，表示不能将同一个connection中发送的消息传递给这个connection中的消费者
				false,
				//消息队列是否阻塞
				false,
				nil,
			)
			if err != nil {
				fmt.Println(err)
			}
			for msg := range msgs {
				OrderMessage := model.RePushProductMessage{}
				err = json.Unmarshal(msg.Body, &OrderMessage)
				if err != nil {
					fmt.Println(err)
					// 拒绝消息并重试
					err = msg.Reject(true)
					if err != nil {
						fmt.Println(err)
					}
				} else {
					err = handle(OrderMessage, routineNum)
					if err != nil {
						fmt.Println(err)
						// 拒绝消息并重试
						err = msg.Reject(true)
						if err != nil {
							fmt.Println(err)
						}
					} else {
						// 成功接收消息
						err = msg.Ack(false)
						if err != nil {
							fmt.Println(err)
						}
					}
				}
			}
			return
		}(i)
	}
	<-forever
}
func ConsumeRabbitMQ() {
	for queueName, handle := range handles {
		go ConsumeHandle(queueName, handle)
	}
}
