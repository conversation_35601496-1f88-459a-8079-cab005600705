package v1

import (
	"area-agency/model"
	"area-agency/service"
	"github.com/gin-gonic/gin"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

// @Tags Agency
// @Summary 提交区域代理申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AgencyApply true "提交区域代理申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"提交成功"}"
// @Router /api/areaAgency/createAgencyApply [post]
func CreateAgencyApply(c *gin.Context) {
	var apply model.AgencyApply
	err := c.ShouldBindJSON(&apply)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//获取用户ID
	uid := v1.GetUserID(c)
	apply.Uid = uid
	if err := service.CreateAgencyApply(apply); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("提交成功", c)
	}
}

func GetApplyStatus(c *gin.Context) {
	uid := v1.GetUserID(c)
	if err, isApply, again, status, agency := service.GetApplyStatus(uid); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"status": status, "agency": agency, "isApply": isApply, "again": again}, c)
	}
}
