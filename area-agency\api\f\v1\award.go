package v1

import (
	"area-agency/request"
	"area-agency/service"
	"github.com/gin-gonic/gin"
	ufv1 "user/api/f/v1"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetAwardList(c *gin.Context) {
	var err error
	var search request.AwardSearch
	err = c.Should<PERSON>ind<PERSON>uery(&search)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)

	err, agency := service.GetAgencyByUid(userID)
	if err != nil {
		//log.log().Error("未找到区域代理", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	search.UserID = int(userID)
	err, nextUrl := utils.Url(c.<PERSON>(), search)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, todaySum, yesterdaySum, weekSum, monthSum := service.GetStatistic(userID)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAwardListByApi(search); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"agency": agency,
			"statistic": gin.H{
				"today":     todaySum,
				"yesterday": yesterdaySum,
				"week":      weekSum,
				"month":     monthSum,
			},
			"list":     list,
			"total":    total,
			"page":     search.Page,
			"pageSize": search.PageSize,
			"next_url": nextUrl,
		}, "获取成功", c)
	}
}

//获取 存在数据的日期
func GetAgencyDays(c *gin.Context) {
	var err error
	var search request.AwardDays
	err = c.ShouldBindQuery(&search)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)

	search.UserID = userID

	if err, Date := service.GetAgencyDays(search); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(Date, c)
	}
}

//根据日期获取数据
func GetAgencyBydate(c *gin.Context) {
	var err error
	var search request.AwardSearchByDate
	err = c.ShouldBindQuery(&search)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if search.Page == 0 {
		search.Page = 1
	}
	if search.PageSize == 0 {
		search.PageSize = 20
	}

	userID := ufv1.GetUserID(c)

	search.UserID = userID

	if err, Data, total := service.GetAwardListByDate(search); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"list":     Data,
			"total":    total,
			"page":     search.Page,
			"pageSize": search.PageSize,
		}, "获取成功", c)

	}
}
