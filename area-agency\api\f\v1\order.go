package v1

import (
	"area-agency/response"
	"area-agency/service"
	"github.com/gin-gonic/gin"
	"order/order"
	"order/request"
	productService "product/service"
	ufv1 "user/api/f/v1"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

type OrderInfo struct {
	order.OrderInfo
	Operations []order.Operation `json:"operations"`
	DetailUrl  string            `json:"detail_url"`
}

func GetOrderList(c *gin.Context) {
	var err error
	var search request.OrderSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)

	err, agency := service.GetAgencyByUid(userID)
	if err != nil {
		//log.log().Error("未找到区域代理", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, orderIds := service.GetOrderIdsByAid(agency.ID)
	if err != nil {
		//log.log().Error("查询区域代理订单出错", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, orderInfoList, total := order.GetOrderInfoListByOrderIds(orderIds, search)
	var list []OrderInfo
	for _, info := range orderInfoList {
		err, operationList := info.GetOperationList()
		if err != nil {
			return
		}
		err, detailUrl := info.GetDetailUrl()
		if err != nil {
			return
		}
		for k, orderItem := range info.OrderItems {
			info.OrderItems[k].CommentStatus = 0
			var comment productService.Comment
			err = source.DB().Where("product_id = ?", orderItem.ProductID).Where("order_id = ?", orderItem.OrderID).First(&comment).Error
			if err == nil {
				info.OrderItems[k].CommentStatus = 1
			}
			err = nil
		}
		list = append(list, OrderInfo{info, operationList, detailUrl})
	}
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}
	tags := []response.Tag{
		{
			"全部", utils.UnSafeUri(c.FullPath(), ""),
		}, {
			"待付款", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 0}),
		}, {
			"待发货", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 1}),
		}, {
			"待收货", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 2}),
		}, {
			"已完成", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 3}),
		},
	}
	orderList := response.OrderList{
		Tags: tags,
		PageResult: yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
			//NextUrl:  nextUrl,
		},
	}
	yzResponse.OkWithDetailed(orderList, "获取成功", c)
	return
}
