### 获取基础设置
GET {{api}}/areaAgency/findSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzNDI2NzQxNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMzNjYxNjE2fQ.hrL-zkgMEt5u315ENvc_t_pZfET4G-PjleUBanP6nZg
x-User-Id: 1

### 更新基础设置
PUT {{api}}/areaAgency/updateSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzNDI2NzQxNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMzNjYxNjE2fQ.hrL-zkgMEt5u315ENvc_t_pZfET4G-PjleUBanP6nZg
x-User-Id: 1

{
  "id": 0,
  "value": {
    "switch": 1,
    "check_switch": 1,
    "again_apply": 1,
    "settle_mode": 1,
    "settle_type": 1,
    "settle_period": 7,
    "calculate_mode": 1,
    "avg_award_switch": 1,
    "deduct_award_switch": 1,
    "province_ratio": 10,
    "city_ratio": 8,
    "county_ratio": 6,
    "town_ratio": 4,
    "agreement_switch": 1,
    "agreement": "申请协议"
  }
}

### 创建区域代理
POST {{api}}/areaAgency/createAgency
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzNDI2NzQxNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMzNjYxNjE2fQ.hrL-zkgMEt5u315ENvc_t_pZfET4G-PjleUBanP6nZg
x-User-Id: 1

{
  "uid": 14,
  "real_name": "测试代理444",
  "mobile": "13312345678",
  "level": 4,
  "country_id": 86,
  "province_id": 23,
  "city_id": 2301,
  "county_id": 230109,
  "town_id": 230103001,
  "special_switch": 1,
  "special_ratio": 555,
  "order_manage_switch": 1
}

### 删除区域代理
DELETE {{api}}/areaAgency/deleteAgency
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzMjk4NTUzNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMyMzc5NzM2fQ.Ok3A_lTph7C2TOfOnA0L-QH9Msp3jaCZzZHEcgfQO8s
x-User-Id: 1

{
  "id": 1
}

### 修改区域代理
PUT{{api}}/areaAgency/updateAgency
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzMjk4NTUzNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMyMzc5NzM2fQ.Ok3A_lTph7C2TOfOnA0L-QH9Msp3jaCZzZHEcgfQO8s
x-User-Id: 1

{
  "id": 1,
  "uid": 15,
  "real_name": "测试代理1"
}

### 通过id获取区域代理
GET{{api}}/areaAgency/findAgency
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzMjk4NTUzNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMyMzc5NzM2fQ.Ok3A_lTph7C2TOfOnA0L-QH9Msp3jaCZzZHEcgfQO8s
x-User-Id: 1

{
  "id": 5
}

### 分页获取区域代理列表
GET{{api}}/areaAgency/getAgenciesList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzNjUxMjc4OCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM1OTA2OTg4fQ.zMsVjjqaI4RK6q9Y-Go2nlHaKgvW650K_UhXJDyKs-w
x-User-Id: 1

{
  "pageSize": 10,
  "page": 1
}

### 获取区域代理申请
GET{{api}}/areaAgency/findAgencyApply
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzMjk4NTUzNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMyMzc5NzM2fQ.Ok3A_lTph7C2TOfOnA0L-QH9Msp3jaCZzZHEcgfQO8s
x-User-Id: 1

{
  "id": 5
}

### 审核区域代理申请
PUT{{api}}/areaAgency/checkAgencyApply
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzMjk4NTUzNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMyMzc5NzM2fQ.Ok3A_lTph7C2TOfOnA0L-QH9Msp3jaCZzZHEcgfQO8s
x-User-Id: 1

{
  "id": 5,
  "status": 1
}

### 分页获取区域代理申请列表
GET{{api}}/areaAgency/getAgencyAppliesList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzNjUxMjc4OCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM1OTA2OTg4fQ.zMsVjjqaI4RK6q9Y-Go2nlHaKgvW650K_UhXJDyKs-w
x-User-Id: 1

{
  "pageSize": 10,
  "page": 1
}

### 分页获取区域代理奖励列表
GET{{api}}/areaAgency/getAwardsList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYzMjk4NTUzNiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjMyMzc5NzM2fQ.Ok3A_lTph7C2TOfOnA0L-QH9Msp3jaCZzZHEcgfQO8s
x-User-Id: 1

{
  "pageSize": 10,
  "page": 1
}

### 前端获取区域代理订单列表
GET{{api}}/api/areaAgency/getOrderList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWNkZDUwYTAtN2EwNC00ODZjLWJiNDUtMGVkOGUwN2I0MTg4IiwiVXNlcm5hbWUiOiIiLCJJRCI6MTIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2MzQyODU5MzYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTYzMzY4MDEzNn0.b-YNnDW6Edyj4yguRGa5kZb2L4OGq5xzBjb8IquV1d0
x-User-Id: 14

{
  "pageSize": 10,
  "page": 1
}

### 前端获取区域奖励列表
GET{{api}}/api/areaAgency/getAwardList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWNkZDUwYTAtN2EwNC00ODZjLWJiNDUtMGVkOGUwN2I0MTg4IiwiVXNlcm5hbWUiOiIiLCJJRCI6MTIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2MzQyODU5MzYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTYzMzY4MDEzNn0.b-YNnDW6Edyj4yguRGa5kZb2L4OGq5xzBjb8IquV1d0
x-User-Id: 12

{
  "pageSize": 10,
  "page": 1
}

### 前端获取基础设置
GET {{api}}/api/areaAgency/findSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWNkZDUwYTAtN2EwNC00ODZjLWJiNDUtMGVkOGUwN2I0MTg4IiwiVXNlcm5hbWUiOiIiLCJJRCI6MTIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2MzQyODU5MzYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTYzMzY4MDEzNn0.b-YNnDW6Edyj4yguRGa5kZb2L4OGq5xzBjb8IquV1d0
x-User-Id: 12