package v1

import (
	"area-agency/model"
	"area-agency/request"
	"area-agency/service"
	"github.com/gin-gonic/gin"
	yzResponse "yz-go/response"
)

// @Tags Agency
// @Summary 创建区域代理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Agency true "创建区域代理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /areaAgency/createAgency [post]
func CreateAgency(c *gin.Context) {
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateAgency(agency); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags Agency
// @Summary 删除区域代理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Agency true "删除区域代理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /areaAgency/deleteAgency [delete]
func DeleteAgency(c *gin.Context) {
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteAgency(agency); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags Agency
// @Summary 更新区域代理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Agency true "更新区域代理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /areaAgency/updateAgency [put]
func UpdateAgency(c *gin.Context) {
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateAgency(agency); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Agency
// @Summary 用id查询区域代理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Agency true "用id查询区域代理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /areaAgency/findAgency [get]
func FindAgency(c *gin.Context) {
	var agency model.Agency
	err := c.ShouldBindJSON(&agency)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reAgency := service.GetAgency(agency.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"agency": reAgency}, c)
	}
}

// @Tags Agency
// @Summary 分页获取区域代理列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AgencySearch true "分页获取区域代理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /areaAgency/getAgenciesList [get]
func GetAgenciesList(c *gin.Context) {
	var pageInfo request.AgencySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAgenciesList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ExportAgenciesList(c *gin.Context) {
	var pageInfo request.AgencySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportAgenciesList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
