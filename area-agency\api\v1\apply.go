package v1

import (
	"area-agency/model"
	"area-agency/request"
	"area-agency/service"
	"github.com/gin-gonic/gin"
	yzResponse "yz-go/response"
)

// @Tags Agency
// @Summary 区域代理申请审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AgencyApply true "区域代理申请审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /areaAgency/checkAgencyApply [put]
func CheckAgencyApply(c *gin.Context) {
	var agencyApply model.AgencyApply
	err := c.ShouldBindJSON(&agencyApply)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.ChangeAgencyApplyStatus(agencyApply); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Agency
// @Summary 用id查询区域代理申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AgencyApply true "用id查询区域代理申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /areaAgency/findAgencyApply [get]
func FindAgencyApply(c *gin.Context) {
	var agencyApply model.AgencyApply
	err := c.ShouldBindJSON(&agencyApply)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reAgency := service.GetAgencyApply(agencyApply.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"agency": reAgency}, c)
	}
}

// @Tags Agency
// @Summary 分页获取区域代理申请列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AgencySearch true "分页获取区域代理申请列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /areaAgency/getAgencyAppliesList [get]
func GetAgencyAppliesList(c *gin.Context) {
	var pageInfo request.AgencySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAgencyAppliesList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
