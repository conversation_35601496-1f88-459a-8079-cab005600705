package v1

import (
	"area-agency/request"
	"area-agency/service"
	"github.com/gin-gonic/gin"
	yzResponse "yz-go/response"
)

// @Tags Agency
// @Summary 分页获取区域代理奖励列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.AwardSearch true "分页获取区域代理奖励列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /areaAgency/getAwardsList [get]
func GetAwardsList(c *gin.Context) {
	var pageInfo request.AwardSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetAwardsList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func ExportAwardsList(c *gin.Context) {
	var pageInfo request.AwardSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportAwardsList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
