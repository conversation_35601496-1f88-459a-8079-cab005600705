package v1

import (
	"area-agency/model"
	"area-agency/service"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	yzResponse "yz-go/response"
)

// FindSetting
// @Tags AreaAgency
// @Summary 用id查询Setting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Setting true "用id查询Setting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /areaAgency/findSetting [get]
func FindSetting(c *gin.Context) {
	err, setting := service.GetSetting("area_agency_setting")
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

// UpdateSetting
// @Tags AreaAgency
// @Summary 更新Setting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Setting true "更新Setting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /areaAgency/updateSetting [put]
func UpdateSetting (c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = service.SaveSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}