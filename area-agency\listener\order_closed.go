package listener

import (
	"area-agency/model"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func PushOrderClosedHandles() {
	//log.Log().Info("区域代理-订单关闭监听!")
	mq.PushHandles("closedAreaAgencyAward", func(orderMsg mq.OrderMessage) (err error) {
		//log.Log().Info("区域分红监听执行(关闭)订单id[" + strconv.Itoa(int(orderMsg.OrderID)) + "]")
		if orderMsg.MessageType != mq.Closed {
			//log.Log().Info("不是订单关闭事件,返回")
			return nil
		}
		var awardIds []uint
		err = source.DB().Model(&model.CreateAward{}).Where("status = ? AND order_id = ?", 0, orderMsg.OrderID).Pluck("id", &awardIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Info("未找到待结算的奖励奖励,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		err = source.DB().Model(&model.CreateAward{}).Where("`id` in ?", awardIds).Update("status", -1).Error
		if err != nil {
			//log.Log().Info("修改状态失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		//log.Log().Info("修改成已失效状态完成")
		return nil
	})
}
