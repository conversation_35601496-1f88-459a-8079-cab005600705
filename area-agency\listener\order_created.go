package listener

import (
	"area-agency/model"
	"area-agency/service"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	orderModel "order/model"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func PushOrderCreatedHandles() {
	//log.Log().Info("区域代理-订单创建监听!")
	mq.PushHandles("createAreaAgencyAward", func(orderMsg mq.OrderMessage) (err error) {
		//log.Log().Info("区域分红监听执行订单id[" + strconv.Itoa(int(orderMsg.OrderID)) + "]")
		if orderMsg.MessageType != mq.Created {
			//log.Log().Info("不是订单创建事件,返回")
			return nil
		}
		// 区域代理基础设置
		var setting model.Setting
		err = source.DB().Where("`key` = ?", "area_agency_setting").First(&setting).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Info("未设置区域代理基础设置,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		areaSetting := setting.Values
		//log.Log().Info("基础设置", zap.Any("info", areaSetting))
		if areaSetting.Switch != 1 {
			//log.Log().Info("基础设置未开启区域分红,返回")
			return nil
		}

		// 订单
		var order orderModel.Order
		err = source.DB().Model(&orderModel.Order{}).Preload("ShippingAddress").Preload("OrderItems").Where("id = ?", orderMsg.OrderID).First(&order).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Info("未找到订单,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		if order.ShippingAddressID == 0 {
			//log.Log().Info("订单没有收货地址,返回")
			return nil
		}
		// 收货地址
		orderAddress := order.ShippingAddress

		//log.Log().Info("收货地址", zap.Any("info", orderAddress))

		// 添加区域订单
		CreateAgencyOrder(orderAddress, order.ID)

		err, reAgencies := getAgenciesByAddress(orderAddress)
		if err != nil {
			//log.Log().Info("获取参与奖励的区域代理出错,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		// 订单结算金额
		var orderAmount int
		for _, item := range order.OrderItems {
			if areaSetting.CalculateMode == 0 {
				orderAmount += int(item.PaymentAmount)
				//log.Log().Info("计算方式:实付金额;增加金额:", zap.Any("info", int(item.PaymentAmount)))
			} else {
				orderAmount += int(item.PaymentAmount - item.Amount)
				//log.Log().Info("计算方式:利润;增加金额:", zap.Any("info", int(item.PaymentAmount-item.CostAmount)))
			}
		}
		//log.Log().Info("最终订单结算金额:", zap.Any("info", orderAmount))
		// 比例
		townRatio := areaSetting.TownRatio
		countyRatio := areaSetting.CountyRatio
		cityRatio := areaSetting.CityRatio
		provinceRatio := areaSetting.ProvinceRatio
		if countyRatio <= 0 {
			countyRatio = townRatio
		}
		if cityRatio <= 0 {
			cityRatio = countyRatio
		}
		if provinceRatio <= 0 {
			provinceRatio = cityRatio
		}
		//log.Log().Info("基础设置分红比例:省", zap.Any("info", provinceRatio))
		//log.Log().Info("基础设置分红比例:市", zap.Any("info", cityRatio))
		//log.Log().Info("基础设置分红比例:区", zap.Any("info", countyRatio))
		//log.Log().Info("基础设置分红比例:街道", zap.Any("info", townRatio))
		// 下级比例
		var lowerRatio int
		//log.Log().Info("参与奖励的区域代理:", zap.Any("info", reAgencies))
		// 执行顺序
		// 街道 town
		// 区县 county
		// 市 city
		// 省 province
		if len(reAgencies["town"]) > 0 {
			err, lowerRatio = reward(reAgencies["town"], areaSetting, "town", orderAmount, lowerRatio, order)
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("街道代理奖励错误", err))
				return nil
			}
		}
		if len(reAgencies["county"]) > 0 {
			err, lowerRatio = reward(reAgencies["county"], areaSetting, "county", orderAmount, lowerRatio, order)
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("区县代理奖励错误", err))
				return nil
			}
		}
		if len(reAgencies["city"]) > 0 {
			err, lowerRatio = reward(reAgencies["city"], areaSetting, "city", orderAmount, lowerRatio, order)
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("市级代理奖励错误", err))
				return nil
			}
		}
		if len(reAgencies["province"]) > 0 {
			err, lowerRatio = reward(reAgencies["province"], areaSetting, "province", orderAmount, lowerRatio, order)
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("省级代理奖励错误", err))
				return nil
			}
		}
		return nil
	})
}

func reward(agencies []model.Agency, areaSetting model.Value, level string, orderAmount, lowerRatio int, order orderModel.Order) (err error, reLowerRatio int) {
	//log.Log().Info("级别:" + level)
	// 下级奖励比例
	reLowerRatio = areaSetting.TownRatio
	// 当前
	var reRatio int
	if level == "town" {
		reLowerRatio = areaSetting.TownRatio
		reRatio = areaSetting.TownRatio
	} else if level == "county" {
		reLowerRatio = areaSetting.CountyRatio
		reRatio = areaSetting.CountyRatio
	} else if level == "city" {
		reLowerRatio = areaSetting.CityRatio
		reRatio = areaSetting.CityRatio
	} else {
		reLowerRatio = areaSetting.ProvinceRatio
		reRatio = areaSetting.ProvinceRatio
	}
	equalLevelSum := len(agencies)
	// 是否平均
	if areaSetting.AvgAwardSwitch == 1 {
		//log.Log().Info("平均分红开启")
		orderAmount = orderAmount / equalLevelSum
		//log.Log().Info("同级人数", zap.Any("info", equalLevelSum))
	} else {
		//log.Log().Info("平均分红未开启")
		equalLevelSum = 0
		//log.Log().Info("同级人数", zap.Any("info", 0))
	}
	// 奖励
	for _, agency := range agencies {
		//log.Log().Info("区域代理会员id:", zap.Any("info", agency.Uid))
		if areaSetting.DeductAwardSwitch == 1 {
			//log.Log().Info("独立比例开启")
			reRatio = reRatio - lowerRatio
			if agency.SpecialSwitch == 1 {
				if agency.SpecialRatio-lowerRatio > 0 {
					reRatio = agency.SpecialRatio - lowerRatio
				} else {
					reRatio = 0
				}
			}
			//log.Log().Info("最终比例", zap.Any("info", reRatio))
		}
		reAmount := orderAmount * reRatio / 10000
		//log.Log().Info("奖励金额:", zap.Any("info", reAmount))
		if reAmount <= 0 {
			//log.Log().Info("区域分红失败:会员id[" + string(rune(agency.Uid)) + "]奖励金额小于0")
			continue
		}
		err = source.DB().Create(&model.CreateAward{Uid: agency.Uid, Aid: agency.ID, Level: agency.Level, OrderId: int(order.ID), OrderPrice: int(order.Amount), StatementPrice: orderAmount, Ratio: reRatio, LowerRatio: lowerRatio, EqualLevelSum: equalLevelSum, Amount: reAmount, Status: 0, OrderStatus: 0, SettleDays: areaSetting.SettlePeriod, CountryId: 86, ProvinceId: agency.ProvinceId, CityId: agency.CityId, CountyId: agency.CountyId, TownId: agency.TownId}).Error
		if err != nil {
			log.Log().Error(err.Error(), zap.Any("区域分红错误", err))
			return
		}
		// 修改 区域代理 未结算金额 WaitSettleAmount SettleAmountTotal
		agency.SettleAmountTotal += int64(orderAmount)
		agency.WaitSettleAmount += int64(reAmount)
		err = source.DB().Omit("UserInfo").Updates(&agency).Error
		if err != nil {
			log.Log().Error(err.Error(), zap.Any("修改区域代理未结算金额错误", err))
			return
		}
		//log.Log().Info("奖励成功")
	}

	return
}

func CreateAgencyOrder(orderAddress orderModel.ShippingAddress, orderId uint) {
	//log.Log().Info("123123123123123")
	err, AgencyManager := getAgencyByAddress(orderAddress)
	if err != nil {
		//log.Log().Info("获取管理订单权限的代理失败")
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
	if AgencyManager.ID == 0 {
		//log.Log().Info("未找到代理,不保存订单")
		return
	}
	var agencyOrder model.AgencyOrder
	agencyOrder.Aid = AgencyManager.ID
	agencyOrder.OrderId = orderId
	err = source.DB().Create(&agencyOrder).Error
	if err != nil {
		//log.Log().Info("创建代理订单失败")
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
}

func getAgencyByAddress(orderAddress orderModel.ShippingAddress) (err error, reAgency model.Agency) {
	// 街道代理
	if orderAddress.TownId != 0 {
		where := "town_id = ? AND level = ? AND order_manage_switch = ?"
		err, reAgency = service.GetAgencyByManageAndAddressIdAndLevel(where, orderAddress.TownId, 4)
		if err != nil {
			log.Log().Info("error", zap.Any("err", err))
			return
		}
	}
	// 区代理
	if orderAddress.CountyId != 0 && reAgency.ID == 0 {
		where := "county_id = ? AND level = ? AND order_manage_switch = ?"
		err, reAgency = service.GetAgencyByManageAndAddressIdAndLevel(where, orderAddress.CountyId, 3)
		if err != nil {
			log.Log().Info("error", zap.Any("err", err))
			return
		}
	}
	// 市代理
	if orderAddress.CityId != 0 && reAgency.ID == 0 {
		where := "city_id = ? AND level = ? AND order_manage_switch = ?"
		err, reAgency = service.GetAgencyByManageAndAddressIdAndLevel(where, orderAddress.CityId, 2)
		if err != nil {
			log.Log().Info("error", zap.Any("err", err))
			return
		}
	}
	// 省代理
	if orderAddress.ProvinceId != 0 && reAgency.ID == 0 {
		where := "province_id = ? AND level = ? AND order_manage_switch = ?"
		err, reAgency = service.GetAgencyByManageAndAddressIdAndLevel(where, orderAddress.ProvinceId, 1)
		if err != nil {
			log.Log().Info("error", zap.Any("err", err))
			return
		}
	}
	return
}

func getAgenciesByAddress(orderAddress orderModel.ShippingAddress) (err error, reAgencies map[string][]model.Agency) {
	reAgencies = make(map[string][]model.Agency)
	var agencies []model.Agency
	// 省代理
	if orderAddress.ProvinceId != 0 {
		where := "province_id = ? AND level = ?"
		err, agencies = service.GetAgenciesByAddressAndLevel(where, orderAddress.ProvinceId, 1)
		//log.Log().Info("省代理", zap.Any("info", agencies))
		if err == nil {
			reAgencies["province"] = agencies
		}
	}
	// 市代理
	if orderAddress.CityId != 0 {
		where := "city_id = ? AND level = ?"
		err, agencies = service.GetAgenciesByAddressAndLevel(where, orderAddress.CityId, 2)
		//log.Log().Info("市代理", zap.Any("info", agencies))
		if err == nil {
			reAgencies["city"] = agencies
		}
	}
	// 区代理
	if orderAddress.CountyId != 0 {
		where := "county_id = ? AND level = ?"
		err, agencies = service.GetAgenciesByAddressAndLevel(where, orderAddress.CountyId, 3)
		//log.Log().Info("区代理", zap.Any("info", agencies))
		if err == nil {
			reAgencies["county"] = agencies
		}
	}
	// 街道代理
	if orderAddress.TownId != 0 {
		where := "town_id = ? AND level = ?"
		err, agencies = service.GetAgenciesByAddressAndLevel(where, orderAddress.TownId, 4)
		//log.Log().Info("街道代理", zap.Any("info", agencies))
		if err == nil {
			reAgencies["town"] = agencies
		}
	}
	return
}
