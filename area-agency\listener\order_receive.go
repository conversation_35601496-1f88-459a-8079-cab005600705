package listener

import (
	"area-agency/model"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func PushOrderReceivedHandles() {
	//log.Log().Info("区域代理-订单完成监听!")
	mq.PushHandles("receiveAreaAgencyAward", func(orderMsg mq.OrderMessage) (err error) {
		//log.Log().Info("区域分红监听执行(完成)订单id[" + strconv.Itoa(int(orderMsg.OrderID)) + "]")
		if orderMsg.MessageType != mq.Received {
			//log.Log().Info("不是订单完成事件,返回")
			return nil
		}
		var awardIds []uint
		err = source.DB().Model(&model.CreateAward{}).Where("status = ? AND order_status = ?", 0, 0).Pluck("id", &awardIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Info("未找到订单完成的奖励奖励,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		if len(awardIds) == 0 {
			//log.Log().Info("没有可修改的奖励,返回")
			return nil
		}
		err = source.DB().Model(&model.CreateAward{}).Where("`id` in ?", awardIds).Update("order_status", 1).Error
		if err != nil {
			//log.Log().Info("修改状态失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		//log.Log().Info("修改订单状态完成")
		return nil
	})
}
