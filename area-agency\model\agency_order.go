package model

import "yz-go/source"

type AgencyOrderMigration struct {
	source.Model
	Aid     uint `json:"aid" form:"aid" gorm:"column:aid;comment:代理商id;"`
	OrderId uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
}

type AgencyOrder struct {
	source.Model
	Aid     uint `json:"aid" form:"aid" gorm:"column:aid;comment:代理商id;"`
	OrderId uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
}

func (AgencyOrderMigration) TableName() string {
	return "area_agency_order"
}

func (AgencyOrder) TableName() string {
	return "area_agency_order"
}
