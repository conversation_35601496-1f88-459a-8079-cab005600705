package model

import (
	"gorm.io/gorm"
	"region/service"
	userModel "user/model"
	"yz-go/source"
)

type AwardMigration struct {
	source.Model
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Aid            uint              `json:"aid" form:"aid" gorm:"column:aid;comment:代理id;"`
	Level          int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	LevelName      string            `json:"level_name" gorm:"-"`
	OrderId        int               `json:"order_id" gorm:"column:order_id;comment:订单id;type:int;"`
	OrderPrice     int               `json:"order_price" gorm:"column:order_price;comment:订单金额;type:int;"`
	StatementPrice int               `json:"statement_price" gorm:"column:statement_price;comment:结算金额;type:int;"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分红比例;type:int;"`
	LowerRatio     int               `json:"lower_ratio" gorm:"column:lower_ratio;comment:下级分红比例;type:int;"`
	EqualLevelSum  int               `json:"equal_level_sum" gorm:"column:equal_level_sum;comment:同级人数;type:int;"`
	Amount         int               `json:"amount" gorm:"column:amount;comment:奖励金额;type:int;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;type:int;"`
	OrderStatus    int               `json:"order_status" gorm:"column:order_status;comment:订单状态 1:可以结算;type:int;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;type:int;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
	CountryId      int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId     int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId         int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId       int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId         int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province       string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City           string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County         string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town           string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
}

type Award struct {
	source.Model
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Aid            uint              `json:"aid" form:"aid" gorm:"column:aid;comment:代理id;"`
	Level          int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	LevelName      string            `json:"level_name" gorm:"-"`
	OrderId        int               `json:"order_id" gorm:"column:order_id;comment:订单id;type:int;"`
	OrderPrice     int               `json:"order_price" gorm:"column:order_price;comment:订单金额;type:int;"`
	StatementPrice int               `json:"statement_price" gorm:"column:statement_price;comment:结算金额;type:int;"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分红比例;type:int;"`
	LowerRatio     int               `json:"lower_ratio" gorm:"column:lower_ratio;comment:下级分红比例;type:int;"`
	EqualLevelSum  int               `json:"equal_level_sum" gorm:"column:equal_level_sum;comment:同级人数;type:int;"`
	Amount         int               `json:"amount" gorm:"column:amount;comment:奖励金额;type:int;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;type:int;"`
	OrderStatus    int               `json:"order_status" gorm:"column:order_status;comment:订单状态 1:可以结算;type:int;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;type:int;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
	CountryId      int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId     int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId         int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId       int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId         int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province       string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City           string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County         string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town           string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
	FullAddress    string            `json:"full_address" gorm:"-"`
	UserInfo       userModel.User    `json:"user_info" gorm:"foreignKey:Uid"`
	OrderInfo      Order             `json:"order_info" gorm:"foreignKey:OrderId"`
}

type Order struct {
	source.Model
	OrderSN uint `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`
	Amount  uint `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`
}

func (a *Award) AfterFind(tx *gorm.DB) (err error) {
	if a.Status == 0 {
		a.StatusName = "未结算"
	} else if a.Status == -1 {
		a.StatusName = "已结算"
	} else {
		a.StatusName = "已失效"
	}
	a.FullAddress = a.Province
	if a.City != "" {
		a.FullAddress = a.FullAddress + "-" + a.City
	}
	if a.County != "" {
		a.FullAddress = a.FullAddress + "-" + a.County
	}
	if a.Town != "" {
		a.FullAddress = a.FullAddress + "-" + a.Town
	}

	a.LevelName = "无等级"
	if a.Level == 1 {
		a.LevelName = "省级代理"
	} else if a.Level == 2 {
		a.LevelName = "市级代理"
	} else if a.Level == 3 {
		a.LevelName = "区级代理"
	} else if a.Level == 4 {
		a.LevelName = "街级代理"
	}

	return
}

// 创建的时候才用到
type CreateAward struct {
	source.Model
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Aid            uint              `json:"aid" form:"aid" gorm:"column:aid;comment:代理id;"`
	Level          int               `json:"level" gorm:"column:level;comment:代理级别;type:int;"`
	OrderId        int               `json:"order_id" gorm:"column:order_id;comment:订单id;type:int;"`
	OrderPrice     int               `json:"order_price" gorm:"column:order_price;comment:订单金额;type:int;"`
	StatementPrice int               `json:"statement_price" gorm:"column:statement_price;comment:结算金额;type:int;"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分红比例;type:int;"`
	LowerRatio     int               `json:"lower_ratio" gorm:"column:lower_ratio;comment:下级分红比例;type:int;"`
	EqualLevelSum  int               `json:"equal_level_sum" gorm:"column:equal_level_sum;comment:同级人数;type:int;"`
	Amount         int               `json:"amount" gorm:"column:amount;comment:奖励金额;type:int;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;type:int;"`
	OrderStatus    int               `json:"order_status" gorm:"column:order_status;comment:订单状态 1:可以结算;type:int;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;type:int;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
	CountryId      int               `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"`
	ProvinceId     int               `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`
	CityId         int               `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`
	CountyId       int               `json:"county_id" gorm:"column:county_id;comment:区id;type:int;"`
	TownId         int               `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`
	Province       string            `json:"province" gorm:"column:province;comment:省;type:string;"`
	City           string            `json:"city"  gorm:"column:city;comment:市;type:string;"`
	County         string            `json:"county"  gorm:"column:county;comment:区;type:string;"`
	Town           string            `json:"town"  gorm:"column:town;comment:街道;type:string;"`
}

// 结算的时候才用到
type SettleAward struct {
	source.Model
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Aid            uint              `json:"aid" form:"aid" gorm:"column:aid;comment:代理id;"`
	StatementPrice int               `json:"statement_price" gorm:"column:statement_price;comment:结算金额;type:int;"`
	Amount         int               `json:"amount" gorm:"column:amount;comment:奖励金额;type:int;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;type:int;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;type:int;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
	Agency         SettleAgency      `json:"agency" gorm:"foreignKey:Aid"`
}

type SettleAgency struct {
	source.Model
	Uid                uint `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	ConsumeTotal       int  `json:"consume_total" gorm:"column:consume_total;comment:区域消费总额;type:int;"`
	SettleAmountTotal  int  `json:"settle_amount_total" gorm:"column:settle_amount_total;comment:累计结算金额;type:int;"`
	FinishSettleAmount int  `json:"finish_settle_amount" gorm:"column:finish_settle_amount;comment:已结算奖励;type:int;"`
	WaitSettleAmount   int  `json:"wait_settle_amount" gorm:"column:wait_settle_amount;comment:未结算奖励;type:int;"`
}

// 创建的时候才用到
func (a *CreateAward) BeforeSave(tx *gorm.DB) (err error) {
	a.Province = service.GetRegionName(a.ProvinceId)
	a.City = service.GetRegionName(a.CityId)
	a.County = service.GetRegionName(a.CountyId)
	a.Town = service.GetRegionName(a.TownId)
	return
}

func (AwardMigration) TableName() string {
	return "area_agency_award"
}

func (Award) TableName() string {
	return "area_agency_award"
}

func (CreateAward) TableName() string {
	return "area_agency_award"
}

func (SettleAward) TableName() string {
	return "area_agency_award"
}

func (SettleAgency) TableName() string {
	return "area_agency"
}

func (Order) TableName() string {
	return "orders"
}
