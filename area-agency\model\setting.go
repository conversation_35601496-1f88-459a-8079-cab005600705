package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type Setting struct {
	source.Model
	Key    string `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values Value  `json:"value" gorm:"column:value;comment:值;type:json;"`
}

type ApiSetting struct {
	source.Model
	Key    string `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values ApiValue  `json:"value" gorm:"column:value;comment:值;type:json;"`
}

func (Setting) TableName() string {
	return "sys_settings"
}

func (ApiSetting) TableName() string {
	return "sys_settings"
}

type Value struct {
	Switch            int    `json:"switch"`                      // 是否开启区域代理
	CheckSwitch       int    `json:"check_switch"`                // 是否需要审核
	AgainApply        int    `json:"again_apply"`                 // 申请驳回后可再次申请
	SettleMode        int    `json:"settle_mode"`                 // 结算方式:0订单价格1利润
	SettleType        int    `json:"settle_type"`                 // 结算类型:0自动结算1手动结算
	SettlePeriod      int    `json:"settle_period"`               // 结算期
	CalculateMode     int    `json:"calculate_mode"`              // 订单计算方式:0实付金额1利润
	AvgAwardSwitch    int    `json:"avg_award_switch"`            // 是否开启平均分红
	DeductAwardSwitch int    `json:"deduct_award_switch"`         // 是否开启极差分红
	ProvinceRatio     int    `json:"province_ratio"`              // 省:默认分红比例
	CityRatio         int    `json:"city_ratio"`                  // 市:默认分红比例
	CountyRatio       int    `json:"county_ratio"`                // 区:默认分红比例
	TownRatio         int    `json:"town_ratio"`                  // 街道:默认分红比例
	AgreementSwitch   int    `json:"agreement_switch"`            // 申请协议开关
	Agreement         string `json:"agreement" gorm:"type:text;"` // 申请协议
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type ApiValue struct {
	AgreementSwitch   int    `json:"agreement_switch"`            // 申请协议开关
	Agreement         string `json:"agreement" gorm:"type:text;"` // 申请协议
}

func (value ApiValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *ApiValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
