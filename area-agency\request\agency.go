package request

import (
	yzRequest "yz-go/request"
)

type AgencySearch struct {
	Uid         int    `json:"uid" form:"uid"`                   // 会员id
	Member      string `json:"member" form:"member"`             // 会员昵称,姓名,手机号
	AddressName string `json:"address_name" form:"address_name"` // 区域名称
	Level       int    `json:"level" form:"level"`               // 区域等级
	StartAT     string `json:"start_at" form:"start_at"`
	EndAT       string `json:"end_at" form:"end_at"`
	yzRequest.PageInfo
}
