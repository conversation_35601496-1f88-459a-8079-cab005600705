package request

import yzRequest "yz-go/request"

type AwardSearch struct {
	UserID int `json:"user_id" form:"user_id"` // 会员id
	Member string `json:"member" form:"member"` // 会员昵称,姓名,手机号
	AddressName string `json:"address_name" form:"address_name"` // 区域名称
	OrderSn string `json:"order_sn" form:"order_sn"` // 订单编号
	Level int `json:"level" form:"level"` // 区域等级
	Status *int `json:"status" form:"status"` // 奖励状态
	StartAT string    `json:"start_at" form:"start_at"`
	EndAT   string    `json:"end_at" form:"end_at"`
	yzRequest.PageInfo
}

type AwardDays struct {
	UserID uint `json:"user_id" form:"user_id"` // 会员id
	Status *int `json:"status" form:"status"` // 奖励状态
}

type AwardSearchByDate struct {
	UserID uint `json:"user_id" form:"user_id"` // 会员id
	Status *int `json:"status" form:"status"` // 奖励状态
	Date string    `json:"date" form:"date"` //2022-04-21
	yzRequest.PageInfo
}