package response

import (
	"gorm.io/gorm"
	"order/model"
	"payment/service"
	"shipping/address"
	"shipping/method"
	"yz-go/response"
)

type OrderList struct {
	Tags []Tag `json:"tags"`
	response.PageResult
}
type Tag struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}
type User struct {
	ID       uint   `json:"id"`
	NickName string `json:"nickname"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
}

type Application struct {
	ID       uint   `json:"id"`
	AppName string `json:"app_name"`
}
type Order struct {
	model.Order
	User            User                    `json:"user"`
	ShippingAddress address.ShippingAddress `json:"shipping_address" gorm:"foreignKey:ShippingAddressID"`
	ShippingMethod  method.ShippingMethod   `json:"shipping_method" gorm:"foreignKey:ShippingAddressID"`
	Supplier        Supplier                `json:"supplier" gorm:"foreignKey:SupplierID"`
	GatherSupply    GatherSupply            `json:"gather_supply" gorm:"foreignKey:GatherSupplyID"`
	PayInfo         PayInfo                 `json:"pay_info" gorm:"foreignKey:PayInfoID"`
	Button          interface{}             `json:"button" gorm:"-"`
	ShopName        string                  `json:"shop_name"`
	PayType        	string                  `json:"pay_type"`
	Application		Application 			`json:"application"`
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {

	b.Button = model.GetButton(b.Status)
	if b.SupplierID != 0 {
		b.ShopName = "供应商:"+b.Supplier.Name
	} else if b.GatherSupplyID != 0 {
		b.ShopName = "供应链:"+b.GatherSupply.Name
	} else {
		b.ShopName = "平台自营"
	}

	_, payType := service.GetPayType()
	for _,v := range payType {
		if b.PayTypeID == v.Code {
			b.PayType = v.Name
		}
	}
	if b.PayTypeID == -1 {
		b.PayType = "后台支付"
	}

	if b.PayType == "" {
		b.PayType = "未知"
	}
	return
}

type Supplier struct {
	ID     uint   `json:"id"`
	Name   string `json:"name"`
	Mobile string `json:"supplier"`
}
type GatherSupply struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Logo string `json:"logo"` //logo
}

type PayInfo struct {
	ID     uint   `json:"id"`
	PaySn  string `json:"pay_sn"`
	Status string `json:"status"`
}
