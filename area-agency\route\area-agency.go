package route

import (
	fv1 "area-agency/api/f/v1"
	v1 "area-agency/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	AreaAgencyRouter := Router.Group("areaAgency")
	{
		// 基础设置
		AreaAgencyRouter.GET("findSetting", v1.FindSetting)     // 用id查询Setting
		AreaAgencyRouter.PUT("updateSetting", v1.UpdateSetting) // 更新Setting
		// 区域代理
		AreaAgencyRouter.POST("createAgency", v1.CreateAgency)            // 新建区域代理
		AreaAgencyRouter.DELETE("deleteAgency", v1.DeleteAgency)          // 删除区域代理
		AreaAgencyRouter.PUT("updateAgency", v1.UpdateAgency)             // 更新区域代理
		AreaAgencyRouter.GET("findAgency", v1.FindAgency)                 // 根据ID获取区域代理
		AreaAgencyRouter.GET("getAgenciesList", v1.GetAgenciesList)       // 分页获取区域代理列表
		AreaAgencyRouter.GET("exportAgenciesList", v1.ExportAgenciesList) // 导出区域代理
		// 代理申请
		AreaAgencyRouter.PUT("checkAgencyApply", v1.CheckAgencyApply)         // 区域代理申请审核
		AreaAgencyRouter.GET("findAgencyApply", v1.FindAgencyApply)           // 用id查询区域代理申请
		AreaAgencyRouter.GET("getAgencyAppliesList", v1.GetAgencyAppliesList) // 分页获取区域代理申请列表
		// 区域奖励
		AreaAgencyRouter.GET("getAwardsList", v1.GetAwardsList)       // 分页获取区域代理奖励列表
		AreaAgencyRouter.GET("exportAwardsList", v1.ExportAwardsList) // 导出区域分红
	}
}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {
	ApplyRouter := Router.Group("areaAgency")
	{
		ApplyRouter.POST("createAgencyApply", fv1.CreateAgencyApply) // 创建区域代理申请
		ApplyRouter.GET("getOrderList", fv1.GetOrderList)              // 获取区域订单列表
		ApplyRouter.GET("getAwardList", fv1.GetAwardList)              // 获取区域奖励列表
		ApplyRouter.GET("findSetting", fv1.FindSetting)                // 查询Setting
		ApplyRouter.GET("getApplyStatus", fv1.GetApplyStatus)          // 获取申请状态
		ApplyRouter.GET("getAgencyDays", fv1.GetAgencyDays)              // 获取区域奖励日期
		ApplyRouter.GET("getAgencyBydate", fv1.GetAgencyBydate)              // 获取日期获取所有奖励

	}
}


