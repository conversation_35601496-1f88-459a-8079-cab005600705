package service

import (
	"area-agency/model"
	"area-agency/request"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"os"
	"strconv"
	"time"
	userModel "user/model"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func CreateAgency(agency model.Agency) (err error) {
	where, addressId, level := getWhereToCU(agency)
	var viAgency model.Agency
	if agency.TownId == 0 && agency.CountyId == 0 && agency.CityId == 0 && agency.ProvinceId == 0 {
		return errors.New("请选择区域")
	}

	if !errors.Is(source.DB().Where("uid = ?", agency.Uid).First(&viAgency).Error, gorm.ErrRecordNotFound) {
		return errors.New("此会员已经是区域代理")
	}

	if where != "" && agency.OrderManageSwitch == 1 {
		if !errors.Is(source.DB().Where("uid != ? AND order_manage_switch = 1", agency.Uid).Where(where, addressId, level).First(&viAgency).Error, gorm.ErrRecordNotFound) {
			return errors.New("该区域下已存在[订单管理权限]的代理商")
		}
	}

	// 添加的代理商,审核状态默认通过
	agency.Status = 1
	agency.Level = level
	agency.BecomeAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Create(&agency).Error
	return err
}

func DeleteAgency(agency model.Agency) (err error) {
	err = source.DB().Delete(&agency).Error
	return err
}

func UpdateAgencyBySettle(amount int, agency model.SettleAgency) (err error) {
	agency.FinishSettleAmount += amount
	agency.WaitSettleAmount -= amount
	//err = source.DB().Where("id = ?", agency.ID).Updates(model.SettleAgency{FinishSettleAmount: agency.FinishSettleAmount,WaitSettleAmount: agency.WaitSettleAmount}).Error
	err = source.DB().Model(&agency).Updates(map[string]interface{}{"finish_settle_amount": agency.FinishSettleAmount, "wait_settle_amount": agency.WaitSettleAmount}).Error
	return err
}

func UpdateAgency(agency model.Agency) (err error) {
	if agency.TownId == 0 && agency.CountyId == 0 && agency.CityId == 0 && agency.ProvinceId == 0 {
		return errors.New("请选择区域")
	}
	var viAgency model.Agency

	where, addressId, level := getWhereToCU(agency)

	if !errors.Is(source.DB().Where("id != ?", agency.ID).Where("uid = ?", agency.Uid).First(&viAgency).Error, gorm.ErrRecordNotFound) {
		if viAgency.Uid == agency.Uid {
			return errors.New("此会员已经是区域代理")
		}
	}

	if where != "" && agency.OrderManageSwitch == 1 {
		if !errors.Is(source.DB().Where("id != ? AND order_manage_switch = 1", agency.ID).Where(where, addressId, level).First(&viAgency).Error, gorm.ErrRecordNotFound) {
			return errors.New("该区域下已存在[订单管理权限]的代理商")
		}
	}
	agency.Level = level
	err = source.DB().Omit("UserInfo").Updates(&agency).Error
	return err
}

func GetAgency(id uint) (err error, agency model.Agency) {
	err = source.DB().Where("id = ? and status = ?", id, 1).Preload("UserInfo").First(&agency).Error
	return
}

func GetAgencyByUid(uid uint) (err error, agency model.SelectAgency) {
	err = source.DB().Where("uid = ? and status = ?", uid, 1).First(&agency).Error
	return
}

func GetAgencyByManageAndAddressIdAndLevel(where string, aid int, level int) (err error, agency model.Agency) {
	err = source.DB().Where(where, aid, level, 1).Where("status = ?", 1).First(&agency).Error
	return
}

func GetAgenciesByAddressAndLevel(where string, aid int, level int) (err error, agencies []model.Agency) {
	err = source.DB().Where(where, aid, level).Where("status = ?", 1).Find(&agencies).Error
	return
}

// test
func GetProvinceAgencies(provinceId int) (err error, agencies []model.Agency) {
	err = source.DB().Where("province_id = ?", provinceId).Find(&agencies).Error
	return
}

func GetAgenciesList(info request.AgencySearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.SelectAgency{})
	var agencies []model.SelectAgency
	db.Where("`status` = ?", 1)
	if info.Uid != 0 {
		db.Where("`uid` = ?", info.Uid)
	}
	if info.Level != 0 {
		db.Where("`level` = ?", info.Level)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}
	if info.AddressName != "" {
		db.Where("`province` LIKE ?", "%"+info.AddressName+"%").Or("`city` LIKE ?", "%"+info.AddressName+"%").Or("`county` LIKE ?", "%"+info.AddressName+"%").Or("`town` LIKE ?", "%"+info.AddressName+"%")
	}
	if info.StartAT != "" {
		db.Where("`become_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`become_at` <= ?", info.EndAT)
	}

	err = db.Count(&total).Error

	err = db.Preload("UserInfo").Order("created_at desc").Limit(limit).Offset(offset).Find(&agencies).Error
	return err, agencies, total
}

func ExportAgenciesList(info request.AgencySearch) (err error, link string) {
	db := source.DB().Model(&model.SelectAgency{}).Preload(clause.Associations)
	var agencies []model.SelectAgency
	if info.Uid != 0 {
		db.Where("`uid` = ?", info.Uid)
	}
	if info.Level != 0 {
		db.Where("`level` = ?", info.Level)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}
	if info.AddressName != "" {
		db.Where("`province` LIKE ?", "%"+info.AddressName+"%").Or("`city` LIKE ?", "%"+info.AddressName+"%").Or("`county` LIKE ?", "%"+info.AddressName+"%").Or("`town` LIKE ?", "%"+info.AddressName+"%")
	}
	if info.StartAT != "" {
		db.Where("`become_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`become_at` <= ?", info.EndAT)
	}
	err = db.Preload(clause.Associations).Order("id DESC").Find(&agencies).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "ID")
	f.SetCellValue("Sheet1", "B1", "会员ID")
	f.SetCellValue("Sheet1", "C1", "用户名")
	f.SetCellValue("Sheet1", "D1", "成为代理时间")
	f.SetCellValue("Sheet1", "E1", "代理区域")
	f.SetCellValue("Sheet1", "F1", "代理等级")
	f.SetCellValue("Sheet1", "G1", "区域消费总额")
	f.SetCellValue("Sheet1", "H1", "分红比例")
	f.SetCellValue("Sheet1", "I1", "累计结算金额")
	f.SetCellValue("Sheet1", "J1", "已结算奖励")
	f.SetCellValue("Sheet1", "K1", "未结算奖励")
	i := 2
	for _, agency := range agencies {
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), agency.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), agency.Uid)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), agency.UserInfo.Username)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), agency.BecomeAt)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), agency.Province+agency.City+agency.County+agency.Town)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), agency.LevelName)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), agency.ConsumeTotal)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), agency.SpecialRatio)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), agency.SettleAmountTotal)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), agency.FinishSettleAmount)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), agency.WaitSettleAmount)
		i++
	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	format := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_agency"
	exist, _ := utils.PathExists(path)
	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + format + "区域代理导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return err, link
}

func CreateAgencyApply(agencyApply model.AgencyApply) (err error) {
	var reApply model.AgencyApply
	if !errors.Is(source.DB().Where("uid = ?", agencyApply.Uid).First(&reApply).Error, gorm.ErrRecordNotFound) {
		if reApply.Status == 0 {
			return errors.New("已提交申请,等待审核")
		}
		if reApply.Status == 1 {
			return errors.New("此会员已经是区域代理")
		}
		err, setting := GetSetting("area_agency_setting")
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			if reApply.Status == -1 {
				return errors.New("驳回后不可以再次申请")
			}
		}
		if setting.Values.AgainApply != 1 {
			return errors.New("驳回后不可以再次申请")
		}
	}
	agencyApply.Status = 0
	// 区域代理基础设置
	var setting model.Setting
	err = source.DB().Where("`key` = ?", "area_agency_setting").First(&setting).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		//log.Log().Info("基础设置没有设置过不需要审核,默认通过")
		agencyApply.Status = 1
	}
	if setting.Values.CheckSwitch != 1 {
		//log.Log().Info("基础设置不需要审核,默认通过")
		agencyApply.Status = 1
	}

	err = source.DB().Create(&agencyApply).Error
	return err
}

func ChangeAgencyApplyStatus(agencyApply model.AgencyApply) (err error) {
	if agencyApply.Status == 1 {
		agencyApply.BecomeAt = &source.LocalTime{Time: time.Now()}
	}
	err = source.DB().Omit("UserInfo").Updates(&agencyApply).Error
	return err
}

func GetAgencyApply(id uint) (err error, agencyApply model.AgencyApply) {
	err = source.DB().Where("id = ?", id).Preload("UserInfo").Where("status != ?", 1).First(&agencyApply).Error
	return
}

func GetAgencyAppliesList(info request.AgencySearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.AgencyApplyByList{})
	var agencyApplies []model.AgencyApplyByList
	//db.Where("`status` != ?", 1)
	if info.Uid != 0 {
		db.Where("`uid` = ?", info.Uid)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}

	err = db.Count(&total).Error

	err = db.Preload("UserInfo").Order("created_at desc").Limit(limit).Offset(offset).Find(&agencyApplies).Error
	return err, agencyApplies, total
}

func getWhereToCU(agency model.Agency) (where string, addressId int, level int) {
	if agency.TownId != 0 {
		level = model.TownLevel
		where = "town_id = ? AND level = ?"
		addressId = agency.TownId
	} else if agency.CountyId != 0 {
		level = model.CountyLevel
		where = "county_id = ? AND level = ?"
		addressId = agency.CountyId
	} else if agency.CityId != 0 {
		level = model.CityLevel
		where = "city_id = ? AND level = ?"
		addressId = agency.CityId
	} else if agency.ProvinceId != 0 {
		level = model.ProvinceLevel
		where = "province_id = ? AND level = ?"
		addressId = agency.ProvinceId
	}
	return
}

func GetApplyStatus(uid uint) (err error, isApply, again int, status int, agency model.Agency) {
	// isApply 判断可不可以申请  1显示0不显示
	// status 申请状态 0待审核1通过-1驳回   只有status=1 push 区域代理，其他push 申请代理
	// 1. 判断是否开启区域代理
	err, setting := GetSetting("area_agency_setting")
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil // 如果没有找到设置，直接返回
		}
		return
	}
	again = setting.Values.AgainApply
	if setting.Values.Switch != 1 {
		// 如果区域代理功能未开启，直接返回
		return
	}
	// 2. 判断用户是否可以申请
	isApply = 1
	// 3. 查询最新的代理申请状态
	var apply model.AgencyApply
	err = source.DB().Where("uid = ?", uid).Order("id desc").First(&apply).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没有找到代理申请记录，表示用户没有申请过，设置状态为0
			err = nil
			status = 0
		}
		return
	}
	// 4. 根据申请状态设置
	status = apply.Status
	// 查询到申请记录，并且是待审核状态，status=2 用于前端步骤判断
	if apply.Status == 0 {
		status = 2
	}
	if status == 1 {
		// 如果申请通过，查询代理信息
		err = source.DB().Where("uid = ?", uid).First(&agency).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("未查询到代理信息")
			}
			return
		}
	}
	return
}
