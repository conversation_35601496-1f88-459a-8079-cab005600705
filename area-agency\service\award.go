package service

import (
	"area-agency/model"
	"area-agency/request"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"os"
	"strconv"
	"time"
	userModel "user/model"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func SettleAward(award model.SettleAward) (err error) {
	award.Status = 1
	award.StatementAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Omit("Agency").Updates(&award).Error
	return err
}

// 前端 区域代理统计
func GetStatistic(uid uint) (err error, todaySum, yesterdaySum, weekSum, monthSum int) {
	now := time.Now()
	week := now.Weekday()
	offset := int(time.Monday - week)
	if offset > 0 {
		offset = -6
	}
	currentYear, currentMonth, _ := now.Date()
	currentLocation := now.Location()
	// 今天
	firstOfDay := time.Date(currentYear, currentMonth, now.Day(), 0, 0, 0, 0, currentLocation)
	// 昨天开始
	firstOfYesterday := time.Date(currentYear, currentMonth, now.Day()-1, 0, 0, 0, 0, time.Local)
	// 周
	StartDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	weekDate := StartDate.AddDate(0, 0, offset)
	// 月
	firstOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, currentLocation)

	err = source.DB().Model(&model.Award{}).Select("COALESCE(SUM(amount), 0) as total_amount").Where("`uid` = ?", uid).Where("created_at >= ?", firstOfDay).First(&todaySum).Error
	err = source.DB().Model(&model.Award{}).Select("COALESCE(SUM(amount), 0) as total_amount").Where("`uid` = ?", uid).Where("created_at >= ?", firstOfYesterday).Where("created_at <= ?", firstOfDay).First(&yesterdaySum).Error
	err = source.DB().Model(&model.Award{}).Select("COALESCE(SUM(amount), 0) as total_amount").Where("`uid` = ?", uid).Where("created_at >= ?", weekDate).First(&weekSum).Error
	err = source.DB().Model(&model.Award{}).Select("COALESCE(SUM(amount), 0) as total_amount").Where("`uid` = ?", uid).Where("created_at >= ?", firstOfMonth).First(&monthSum).Error

	return
}

// 前端获取奖励列表
func GetAwardListByApi(info request.AwardSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.Award{})
	var awards []model.Award
	db.Where("uid = ?", uint(info.UserID))
	if info.Status != nil {
		db.Where("`status` = ?", info.Status)
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	if info.OrderSn != "" {
		var orderIds []uint
		err = source.DB().Model(model.Order{}).Where("order_sn like ?", "%"+info.OrderSn+"%").Pluck("id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`order_id` in ?", orderIds)
	}

	err = db.Count(&total).Error
	err = db.Preload("UserInfo").Preload("OrderInfo").Order("created_at desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total
}

// 后台获取奖励列表
func GetAwardsList(info request.AwardSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.Award{})
	var awards []model.Award
	if info.UserID != 0 {
		db.Where("`uid` = ?", info.UserID)
	}
	if info.Level != 0 {
		db.Where("`level` = ?", info.Level)
	}
	if info.Status != nil {
		db.Where("`status` = ?", info.Status)
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}
	if info.AddressName != "" {
		db.Where("`province` LIKE ?", "%"+info.AddressName+"%").Or("`city` LIKE ?", "%"+info.AddressName+"%").Or("`county` LIKE ?", "%"+info.AddressName+"%").Or("`town` LIKE ?", "%"+info.AddressName+"%")
	}
	if info.OrderSn != "" {
		var orderIds []uint
		err = source.DB().Model(model.Order{}).Where("order_sn like ?", "%"+info.OrderSn+"%").Pluck("id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`order_id` in ?", orderIds)
	}
	err = db.Count(&total).Error

	err = db.Preload("UserInfo").Preload("OrderInfo").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total
}

func ExportAwardsList(info request.AwardSearch) (err error, link string) {
	db := source.DB().Model(model.Award{}).Preload(clause.Associations)
	var awards []model.Award
	if info.UserID != 0 {
		db.Where("`uid` = ?", info.UserID)
	}
	if info.Level != 0 {
		db.Where("`level` = ?", info.Level)
	}
	if info.Status != nil {
		db.Where("`status` = ?", info.Status)
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}
	if info.Member != "" {
		var userIds []uint
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIds)
	}
	if info.AddressName != "" {
		db.Where("`province` LIKE ?", "%"+info.AddressName+"%").Or("`city` LIKE ?", "%"+info.AddressName+"%").Or("`county` LIKE ?", "%"+info.AddressName+"%").Or("`town` LIKE ?", "%"+info.AddressName+"%")
	}
	if info.OrderSn != "" {
		var orderIds []uint
		err = source.DB().Model(model.Order{}).Where("order_sn like ?", "%"+info.OrderSn+"%").Pluck("id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`order_id` in ?", orderIds)
	}
	err = db.Preload(clause.Associations).Order("id DESC").Find(&awards).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")

	// 设置表头样式
	/*headStyleID, _ := f.NewStyle(`{
	   "font":{
		  "color":"#333333",
		  "bold":true,
		  "size":16,
		  "family":"arial"
	   },
	   "alignment":{
		  "vertical":"center",
		  "horizontal":"center"
	   }
	}`)*/

	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "ID")
	f.SetCellValue("Sheet1", "B1", "分红时间")
	f.SetCellValue("Sheet1", "C1", "区域代理")
	f.SetCellValue("Sheet1", "D1", "会员昵称")
	f.SetCellValue("Sheet1", "E1", "代理区域")
	f.SetCellValue("Sheet1", "F1", "区域等级")
	f.SetCellValue("Sheet1", "G1", "订单号")

	f.SetCellValue("Sheet1", "H1", "订单金额")
	f.SetCellValue("Sheet1", "I1", "分红结算金额")
	f.SetCellValue("Sheet1", "J1", "分红比例")
	f.SetCellValue("Sheet1", "K1", "下级分红比例")
	f.SetCellValue("Sheet1", "L1", "分红金额")
	f.SetCellValue("Sheet1", "M1", "分红状态")

	//f.SetCellStyle("Sheet1", "A1", "M1", headStyleID)

	i := 2
	for _, award := range awards {
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), award.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), award.CreatedAt)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), award.UserInfo.Username)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), award.UserInfo.Username)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), award.Province+award.City+award.County+award.Town)
		var levelName string
		levelName = "无等级"
		if award.Level == 1 {
			levelName = "省级代理"
		} else if award.Level == 2 {
			levelName = "市级代理"
		} else if award.Level == 3 {
			levelName = "区级代理"
		} else if award.Level == 4 {
			levelName = "街级代理"
		}
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), levelName)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), strconv.Itoa(int(award.OrderInfo.OrderSN)))
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), award.OrderPrice)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), award.StatementPrice)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), award.Ratio)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), award.LowerRatio)
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), award.Amount)
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), award.StatusName)
		i++
	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	format := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_agency_award"
	exist, _ := utils.PathExists(path)
	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + format + "区域分红导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return err, link
}

type AwardDays struct {
	Count uint   `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"` // 订单数量
	Date  string `json:"date" form:"date" gorm:"column:date;comment:年月日;type:varchar(30);size:30;"`
}

//获取所有分红的日期
func GetAgencyDays(awardDays request.AwardDays) (err error, Date []AwardDays) {
	db := source.DB().Model(&model.Award{}).Where("uid = ?", awardDays.UserID)

	if awardDays.Status != nil {
		db.Select("LEFT(created_at,10) as date,status,COALESCE(count(id), 0) as count")
		db.Where("status = ?", awardDays.Status)
		err = db.Group("date,status").Order("date DESC").Find(&Date).Error
	} else {
		db.Select("LEFT(created_at,10) as date,COALESCE(count(id), 0) as count")
		err = db.Group("date").Order("date DESC").Find(&Date).Error
	}

	return
}

// 前端获取奖励列表
func GetAwardListByDate(info request.AwardSearchByDate) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(model.Award{})
	var awards []model.Award
	db.Where("uid = ?", uint(info.UserID))
	if info.Status != nil {
		db.Where("`status` = ?", info.Status)
	}
	if info.Date != "" {
		db.Where("`created_at` >= ?", info.Date+" 00:00:00")
		db.Where("`created_at` <= ?", info.Date+" 23:59:59")
	}
	err = db.Count(&total).Error
	err = db.Preload("UserInfo").Preload("OrderInfo").Limit(limit).Offset(offset).Order("created_at desc").Find(&awards).Error
	return err, awards, total
}
