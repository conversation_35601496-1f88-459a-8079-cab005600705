package service

import (
	"area-agency/model"
	"area-agency/request"
	"encoding/json"
	"fmt"
	"github.com/gookit/color"
	"reflect"
	"testing"
)

func TestGetAwardsList(t *testing.T) {
	type args struct {
		info request.AwardSearch
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  interface{}
		wantTotal int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList, gotTotal := GetAwardsList(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.<PERSON><PERSON>("GetAwardsList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetAwardsList() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.<PERSON>rf("GetAwardsList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestGetProvinceAgencies(t *testing.T) {
	var reAgencies = make(map[string][]model.Agency)
	_, gotAgencies := GetProvinceAgencies(23)
	reAgencies["province"] = gotAgencies
	reAgenci, _ := json.Marshal(reAgencies)
	color.Success.Println("===================")
	color.Success.Println(string(reAgenci))
	//map[province:[{{{{0001-01-01 00:00:00 +0000 UTC false}} 5 2021-09-27 15:41:36 +0800 CST 2021-09-27 16:51:33 +0800 CST} 14 测试代理1 13312345678 4 86 23 2301 230109 230103010 黑龙江省 哈尔滨市 松北区 松花江街道 1 2021-09-27 16:51:33 +0800 CST <nil> 1 555 0 0 0 0 {{{{0001-01-01 00:00:00 +0000 UTC false}} 0 <nil> <nil>}      0 00000000-0000-0000-0000-000000000000 0 0    {{{{0001-01-01 00:00:00 +0000 UTC false}} 0 <nil> <nil>} 0 }}} {{{{0001-01-01 00:00:00 +0000 UTC false}} 6 2021-09-27 15:41:57 +0800 CST 2021-09-27 15:41:57 +0800 CST} 15 测试代理2 13312345678 4 86 23 2301 230109 230103010 黑龙江省 哈尔滨市 松北区 松花江街道 1 2021-09-27 15:41:57 +0800 CST <nil> 1 555 0 0 0 0 {{{{0001-01-01 00:00:00 +0000 UTC false}} 0 <nil> <nil>}      0 00000000-0000-0000-0000-000000000000 0 0    {{{{0001-01-01 00:00:00 +0000 UTC false}} 0 <nil> <nil>} 0 }}}]]
}

func TestExportAwardsList(t *testing.T) {
	type args struct {
		info request.AwardSearch
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		wantLink string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotLink := ExportAwardsList(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("ExportAwardsList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if gotLink != tt.wantLink {
				t.Errorf("ExportAwardsList() gotLink = %v, want %v", gotLink, tt.wantLink)
			}
		})
	}
}

func TestGetAwardListByApi(t *testing.T) {
	type args struct {
		info request.AwardSearch
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  interface{}
		wantTotal int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList, gotTotal := GetAwardListByApi(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetAwardListByApi() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetAwardListByApi() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetAwardListByApi() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestGetAwardsList1(t *testing.T) {
	type args struct {
		info request.AwardSearch
	}
	tests := []struct {
		name      string
		args      args
		wantErr   error
		wantList  interface{}
		wantTotal int64
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList, gotTotal := GetAwardsList(tt.args.info)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetAwardsList() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotList, tt.wantList) {
				t.Errorf("GetAwardsList() gotList = %v, want %v", gotList, tt.wantList)
			}
			if gotTotal != tt.wantTotal {
				t.Errorf("GetAwardsList() gotTotal = %v, want %v", gotTotal, tt.wantTotal)
			}
		})
	}
}

func TestGetStatistic(t *testing.T) {
	gotErr, gotTodaySum, gotYesterdaySum, gotWeekSum, gotMonthSum := GetStatistic(14)
	fmt.Println(gotErr,gotTodaySum,gotYesterdaySum,gotWeekSum,gotMonthSum)
}

func TestSettleAward(t *testing.T) {
	type args struct {
		award model.SettleAward
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SettleAward(tt.args.award); (err != nil) != tt.wantErr {
				t.Errorf("SettleAward() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}