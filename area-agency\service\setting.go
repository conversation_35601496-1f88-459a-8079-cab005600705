package service

import (
	"area-agency/model"
	"yz-go/source"
)


func GetSettingByApi(key string) (err error, setting model.ApiSetting) {
	err = source.DB().Where("`key` = ?", key).First(&setting).Error
	return
}

//@function: GetSetting
//@description: 根据id获取setting
//@param: key string
//@return: err error, setting model.Setting
func GetSetting(key string) (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", key).First(&setting).Error
	return
}

//@function: SaveSetting
//@description: 更新setting
//@param: setting *model.Setting
//@return: err error
func SaveSetting(setting model.Setting) (err error) {
	setting.Key = "area_agency_setting"
	if setting.ID != 0{
		err = source.DB().Updates(&setting).Error
	}else {
		err = source.DB().Create(&setting).Error
	}
	return err
}
