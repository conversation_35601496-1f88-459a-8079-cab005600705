package main

import (
	"auto-depploy/util"
	"bytes"
	_ "embed"
	"encoding/json"
	"fmt"
	"github.com/mitchellh/go-homedir"
	"github.com/pkg/sftp"
	"golang.org/x/crypto/ssh"
	"io"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"runtime"
	"time"
)

//go:embed ssh_example.json
var sshJson string

func main() {

	var err error

	sysType = runtime.GOOS

	util.LocalPath, err = os.Getwd()

	var sshConfig []*SSHConfig
	err = json.Unmarshal([]byte(sshJson), &sshConfig)
	if err != nil {
		log.Println("解析json失败")
		return
	}
	if sysType == "windows" {
		err = util.WindowsCompileDaemon()
	} else {
		err = util.MacCompileDaemon()
	}

	if err != nil {
		log.Fatal("打包后台失败", err)
		return
	}

	for index, item := range sshConfig {
		item.SshSupplyPath = "/data" + "/goSupply/"
		item.GoDir = "/data/wwwroot/yzshop/"
		item.SshType = "password"
		item.SshPort = 22
		item.FileName = "supply"
		fmt.Println(index, item.DoMain, item.SshSupplyPath)

		if item.Front {
			err = Front(item) //打包上传前端
			if err != nil {
				log.Println("前端打包上传错误", err)
			}
		}

		if item.Super {
			err = Super(item) //打包上传后端
			if err != nil {
				log.Println("后端打包上传错误", err)
			}
		}

		if item.Supply {
			//上传后台程序
			err = item.FtpUpload()
		}

	}

	fmt.Println("完成")
	return

}

type SSHConfig struct {
	SshHost       string `json:"ssh_host"`
	SshSupplyPath string `json:"ssh_supply_path"`
	SshUser       string `json:"ssh_user"`
	SshPassword   string `json:"ssh_password"`
	SshType       string `json:"ssh_type"`
	SshKeyPath    string `json:"ssh_key_path"`
	SshPort       int    `json:"ssh_port"`
	FileName      string `json:"file_name"`
	DoMain        string `json:"domain"`
	GoDir         string `json:"go_dir"`
	Supply        bool   `json:"supply"`
	Super         bool   `json:"super"`
	Front         bool   `json:"front"`
}

var ShellToUse = "cmd"

func (sshConf *SSHConfig) WindowsCompileFrontVue(dir string, params []string) (err error) {
	var cmd *exec.Cmd
	if sysType == "windows" {
		cmd = exec.Command(ShellToUse, params...)
	} else {
		cmd = exec.Command("bash", "-c", params[0])
	}

	//cmd := exec.Command(ShellToUse, params...)
	in := bytes.NewBuffer(nil)
	cmd.Stdin = in //绑定输入
	var out bytes.Buffer
	cmd.Stdout = &out //绑定输出
	go func() {
		for _, item := range params {
			in.WriteString(item + "\n")
		}

	}()
	cmd.Dir = dir
	err = cmd.Start()
	if err != nil {
		log.Fatal(err)
	}
	log.Println(cmd.Args)
	err = cmd.Wait()
	if err != nil {
		log.Printf("Command finished with error: %v", err)
	}
	//rt := out.String()
	//mahonia.NewDecoder("gbk").ConvertString(out.String()) //
	//fmt.Println("结果：", rt)

	return

}

var session *ssh.Session

func (sshConf *SSHConfig) FtpUpload() (errInfo error) {

	//创建ssh
	config := &ssh.ClientConfig{
		Timeout:         time.Second, //ssh 连接time out 时间一秒钟, 如果ssh验证错误 会在一秒内返回
		User:            sshConf.SshUser,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), //这个可以， 但是不够安全
		//HostKeyCallback: hostKeyCallBackFunc(h.Host),
	}
	if sshConf.SshType == "password" {
		config.Auth = []ssh.AuthMethod{ssh.Password(sshConf.SshPassword)}
	} else {
		config.Auth = []ssh.AuthMethod{publicKeyAuthFunc(sshConf.SshKeyPath)}
	}

	//dial 获取ssh client
	addr := fmt.Sprintf("%s:%d", sshConf.SshHost, sshConf.SshPort)

	sshClient, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		log.Fatal("创建ssh client 失败", err)
	}
	//defer sshClient.Close()

	//创建ssh-session
	session, _ = sshClient.NewSession()

	//command := "pkill -9 supply"
	command := "rm -rf " + sshConf.SshSupplyPath + sshConf.FileName
	fmt.Println("执行命令", command)
	err = session.Run(command)

	//session.Close()

	if err != nil {
		log.Fatal("远程执行cmd 失败1", err)
		return
	}

	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		log.Fatalln(err.Error())
	}
	//defer sftpClient.Close()

	{
		//上传文件(将本地文件通过sftp传到远程服务器)
		remoteFileName := sshConf.FileName

		fmt.Println("文件路径", sftp.Join(sshConf.SshSupplyPath, remoteFileName))
		remoteFile, err := sftpClient.Create(sftp.Join(sshConf.SshSupplyPath, remoteFileName))
		if err != nil {
			log.Fatalln(err.Error())
		}
		//defer remoteFile.Close()

		path := util.LocalPath
		fmt.Println("本地文件路径：", path+"/../supply-chain/supply")

		localFileName := path + "/../supply-chain/" + sshConf.FileName
		//打开本地文件
		localFile, err := os.Open(localFileName)
		if err != nil {
			log.Fatalln(err.Error())
		}
		//defer localFile.Close()

		//本地文件流拷贝到上传文件流
		n, err := io.Copy(remoteFile, localFile)

		if err != nil {
			log.Fatalln(err.Error())
		}

		//获取本地文件大小
		localFileInfo, err := os.Stat(localFileName)
		if err != nil {
			log.Fatalln(err.Error())
		}

		sessions, _ := sshClient.NewSession()
		log.Printf("文件上传成功[%s->%s]本地文件大小：%s，上传文件大小：%s", localFileName, remoteFileName, formatFileSize(localFileInfo.Size()), formatFileSize(n))
		command := "cd " + sshConf.SshSupplyPath + ";chmod 777 " + sshConf.SshSupplyPath + sshConf.FileName + ";pkill -9 supply; fuser -k supply"
		fmt.Println("执行命令", command)
		err = sessions.Run(command)

		if err != nil {
			log.Fatal("远程执行cmd 失败1", err)
			return
		}

		//defer session.Close()

		//后续计算文件MD5验证文件完整度

	}

	return
}

func (sshConf *SSHConfig) FtpUploadVue(zipName string) (errInfo error) {

	//创建ssh
	config := &ssh.ClientConfig{
		Timeout:         time.Second, //ssh 连接time out 时间一秒钟, 如果ssh验证错误 会在一秒内返回
		User:            sshConf.SshUser,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), //这个可以， 但是不够安全
		//HostKeyCallback: hostKeyCallBackFunc(h.Host),
	}
	if sshConf.SshType == "password" {
		config.Auth = []ssh.AuthMethod{ssh.Password(sshConf.SshPassword)}
	} else {
		config.Auth = []ssh.AuthMethod{publicKeyAuthFunc(sshConf.SshKeyPath)}
	}

	//dial 获取ssh client
	addr := fmt.Sprintf("%s:%d", sshConf.SshHost, sshConf.SshPort)

	sshClient, err := ssh.Dial("tcp", addr, config)
	if err != nil {
		log.Fatal("创建ssh client 失败", err)
	}
	//defer sshClient.Close()

	//创建ssh-session
	session, _ = sshClient.NewSession()

	if err != nil {
		log.Fatal("远程执行cmd 失败1", err)
		return
	}

	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		log.Fatalln(err.Error())
	}
	//defer sftpClient.Close()

	{
		//上传文件(将本地文件通过sftp传到远程服务器)
		remoteFileName := zipName

		fmt.Println("远程文件路径", sftp.Join(sftp.Join(sshConf.GoDir, remoteFileName)))
		remoteFile, err := sftpClient.Create(sftp.Join(sshConf.GoDir, remoteFileName))
		if err != nil {
			log.Fatalln(err.Error())
		}
		//defer remoteFile.Close()

		path := util.LocalPath + "/packge"
		fmt.Println("本地文件路径：", path+"/"+zipName)

		localFileName := path + "/" + zipName
		//打开本地文件
		localFile, err := os.Open(localFileName)
		if err != nil {
			log.Fatalln(err.Error())
		}
		//defer localFile.Close()

		//本地文件流拷贝到上传文件流
		n, err := io.Copy(remoteFile, localFile)

		if err != nil {
			log.Fatalln(err.Error())
		}

		//获取本地文件大小
		localFileInfo, err := os.Stat(localFileName)
		if err != nil {
			log.Fatalln(err.Error())
		}

		sessions, _ := sshClient.NewSession()
		log.Printf("文件上传成功[%s->%s]本地文件大小：%s，上传文件大小：%s", localFileName, remoteFileName, formatFileSize(localFileInfo.Size()), formatFileSize(n))

		command := "cd " + sshConf.GoDir + ";unzip -o " + zipName + ";rm -rf static; mv -f  dist/* ./"
		fmt.Println("执行命令", command)
		err = sessions.Run(command)

		if err != nil {
			log.Fatal("远程执行cmd 失败1", err)
			return
		}

		//defer session.Close()

		//后续计算文件MD5验证文件完整度

	}

	return
}

func (sshConf *SSHConfig) ExecRemote(command string) (err error) {

	/*
		如果是可执行程序的话，可以直接使用 pidof
		pkill -9 supply
		pidof supply
	*/
	//执行远程命令
	combo, err := session.CombinedOutput(command)
	if err != nil {
		log.Fatal("远程执行cmd 失败", err, command)
		return
	}
	log.Println("命令输出:", string(combo))
	return

}

func (item *SSHConfig) VueSuper(dirPath string, fileName string) (err error) {

	var dir, strstring string
	var cmdStr []string
	cmdStr = append(cmdStr, "npm run build")
	dir = util.LocalPath + "/../" + dirPath

	strstring = "ENV = 'production'\nVUE_APP_BASE_API = '/supplyapi'"
	if fileName == "front" {
		strstring = "ENV = 'production'\nVUE_APP_BASE_API = '/supplyapi/api/'"
	}

	err = util.WriteFile(strstring, util.LocalPath+"/../"+dirPath+".env.production")
	if err != nil {
		return err
	}
	err = item.WindowsCompileFrontVue(dir, cmdStr) //编译后台
	if err != nil {
		log.Println(dirPath, "打包前端程序失败")
		return
	}

	util.CompressZip(util.LocalPath+"/../"+dirPath+"dist/", fileName)

	return

}

func Front(item *SSHConfig) (err error) {

	fmt.Println("前端pc端打包上传开始")
	err = item.VueSuper("shop-side-shop/", "front")
	item.GoDir = "/data/wwwroot/yzshop/"
	err = item.FtpUploadVue("front.zip")
	return
}

func Super(item *SSHConfig) (err error) {

	fmt.Println("后端pc端打包上传开始")
	err = item.VueSuper("gin-vue-admin/web/", "dist")
	item.GoDir = "/data/wwwroot/yzshop/super/"
	err = item.FtpUploadVue("dist.zip")

	return
}

var sysType string

// 字节的单位转换 保留两位小数
func formatFileSize(s int64) (size string) {
	if s < 1024 {
		return fmt.Sprintf("%.2fB", float64(s)/float64(1))
	} else if s < (1024 * 1024) {
		return fmt.Sprintf("%.2fKB", float64(s)/float64(1024))
	} else if s < (1024 * 1024 * 1024) {
		return fmt.Sprintf("%.2fMB", float64(s)/float64(1024*1024))
	} else if s < (1024 * 1024 * 1024 * 1024) {
		return fmt.Sprintf("%.2fGB", float64(s)/float64(1024*1024*1024))
	} else if s < (1024 * 1024 * 1024 * 1024 * 1024) {
		return fmt.Sprintf("%.2fTB", float64(s)/float64(1024*1024*1024*1024))
	} else { //if s < (1024 * 1024 * 1024 * 1024 * 1024 * 1024)
		return fmt.Sprintf("%.2fEB", float64(s)/float64(1024*1024*1024*1024*1024))
	}
}

func publicKeyAuthFunc(kPath string) ssh.AuthMethod {
	keyPath, err := homedir.Expand(kPath)
	if err != nil {
		log.Fatal("find key's home dir failed", err)
	}
	key, err := ioutil.ReadFile(keyPath)
	if err != nil {
		log.Fatal("ssh key file read failed", err)
	}
	// Create the Signer for this private key.
	signer, err := ssh.ParsePrivateKey(key)
	if err != nil {
		log.Fatal("ssh key signer failed", err)
	}
	return ssh.PublicKeys(signer)
}
