package main

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"golang.org/x/crypto/ssh"
	"net"
)

type ViaSSHDialer struct {
	client *ssh.Client
}

func (self *ViaSSHDialer) Dial(context context.Context, addr string) (net.Conn, error) {
	return self.client.Dial("tcp", addr)
}
func TestSSHMyql() {

	// 一个ClientConfig指针,指向的对象需要包含ssh登录的信息
	config := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.Password("kao)s1DXwo8qvuml"), //用户的密码
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), //这个我也不太清楚，大概是做服务端验证的，按这么写就行
	}

	client, err := ssh.Dial("tcp", "*************:22", config) //xxx那段替换为你服务器的的IP地址
	if err != nil {
		panic("连接失败") //抛出异常
	}
	defer client.Close()

	mysql.RegisterDialContext("mysql+tcp", (&ViaSSHDialer{client}).Dial)
	dsn := "root:uhmisJ9kq8fup[Vh@mysql+tcp(127.0.0.1:3306)/supply?charset=utf8&parseTime=True"

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		panic("连接失败")
	}
	fmt.Println("成功连接到mysql")

	// 查询数据库中的一个表 book
	if rows, err := db.Query("SELECT * FROM products  where id=9557"); err == nil {
		// 使用循环，打印出查询的内容
		for rows.Next() {
			var id int64
			var name string
			rows.Scan(&id, &name)
			fmt.Printf("ID: %d  Name: %s\n", id, name)
		}
		rows.Close()
	} else {
		fmt.Printf("查询失败: %s", err.Error())
	}

	db.Close()
}
