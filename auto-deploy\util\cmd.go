package util

import (
	"bytes"
	"log"
	"os/exec"
)

//编译后台前端vuenp
func WindowsCompileDaemon() (err error) {
	cmd := exec.Command("cmd")
	in := bytes.NewBuffer(nil)
	cmd.Stdin = in //绑定输入
	var out bytes.Buffer
	cmd.Stdout = &out //绑定输出

	/*
		CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o supply main.go
	*/
	go func() {

		in.WriteString("set CGO_ENABLED=0\n")
		in.WriteString("set GOOS=linux\n")
		in.WriteString("set GOARCH=amd64\n")
		in.WriteString("go build -a -o supply main.go\n")
	}()

	cmd.Dir = LocalPath + "/../supply-chain/"
	err = cmd.Start()
	if err != nil {
		log.Fatal(err)
	}
	log.Println(cmd.Args)
	err = cmd.Wait()
	if err != nil {
		log.Printf("Command finished with error: %v", err)
	}
	//rt := out.String()
	//mahonia.NewDecoder("gbk").ConvertString(out.String()) //
	//fmt.Println("结果：", rt)

	return
}

func MacCompileDaemon() (err error) {
	cmd := exec.Command("bash", "-c", "CGO_ENABLED=0 GOOS=linux GOARCH=amd64    go build -a -o supply main.go")
	in := bytes.NewBuffer(nil)
	cmd.Stdin = in //绑定输入
	var out bytes.Buffer
	cmd.Stdout = &out //绑定输出

	/*
		CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o supply main.go
	*/

	cmd.Dir = LocalPath + "/../supply-chain/"
	err = cmd.Start()
	if err != nil {
		log.Fatal(err)
	}
	log.Println(cmd.Args)
	err = cmd.Wait()
	if err != nil {
		log.Printf("Command finished with error: %v", err)
	}
	//rt := out.String()
	//mahonia.NewDecoder("gbk").ConvertString(out.String()) //
	//fmt.Println("结果：", rt)

	return
}
