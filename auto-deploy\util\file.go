package util

import (
	"archive/zip"
	"io"
	"io/ioutil"
	"os"
)

func WriteFile(writeString string, filePath string) (err error) {
	var d1 = []byte(writeString)
	err = ioutil.WriteFile(filePath, d1, 0666) //写入文件(字节数组)
	return
}

func CompressZip(path string,fileName string) {
	f3, err := os.Open(path)
	if err != nil {

	}
	defer f3.Close()
	var files = []*os.File{f3}
	dest := "packge/"+fileName+".zip"
	err = Compress(files, dest)
}

func Compress(files []*os.File, dest string) error {
	d, _ := os.Create(dest)
	defer d.Close()
	w := zip.NewWriter(d)
	defer w.Close()
	for _, file := range files {
		err := compress(file, "", w)
		if err != nil {
			return err
		}
	}
	return nil
}

func compress(file *os.File, prefix string, zw *zip.Writer) error {
	info, err := file.Stat()
	if err != nil {
		return err
	}
	if info.IsDir() {
		prefix = prefix + "/" + info.Name()
		fileInfos, err := file.Readdir(-1)
		if err != nil {
			return err
		}
		for _, fi := range fileInfos {
			f, err := os.Open(file.Name() + "/" + fi.Name())
			if err != nil {
				return err
			}
			err = compress(f, prefix, zw)
			if err != nil {
				return err
			}
		}
	} else {
		header, err := zip.FileInfoHeader(info)
		header.Name = prefix + "/" + header.Name
		if err != nil {
			return err
		}
		writer, err := zw.CreateHeader(header)
		if err != nil {
			return err
		}
		_, err = io.Copy(writer, file)
		file.Close()
		if err != nil {
			return err
		}
	}
	return nil
}
