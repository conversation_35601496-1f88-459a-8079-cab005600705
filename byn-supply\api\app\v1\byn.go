package v1

import (
	"byn-supply/model"
	"byn-supply/request"
	"byn-supply/service"
	"gin-vue-admin/cmd/gva"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"math"
	orderModel "order/model"
	orderPay "order/pay"
	paymentModel "payment/model"
	paymentService "payment/service"
	productRequest "product/request"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"time"
	"trade/checkout"
	"trade/confirm"
	"trade/pay"
	"yz-go/cache"
	"yz-go/source"

	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetProductDetailList(c *gin.Context) {
	var pageInfo productRequest.ProductDetailSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	if err, list := service.GetProductDetailList(pageInfo.Ids, appID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func GetOrderInfo(c *gin.Context) {
	var orderRequest request.OrderInfoRequest
	err := c.ShouldBindJSON(&orderRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var bynOrder model.BynSupplyOrder
	err, bynOrder = service.GetOrderInfo(orderRequest)
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(bynOrder, "获取成功", c)
	return
}

func OrderConfirm(c *gin.Context) {
	var err error
	var confirmRequest request.ConfirmRequest
	err = c.ShouldBindJSON(&confirmRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetAppUserID(c)
	appID := utils.GetAppID(c)

	one := 1
	zero := 0
	var orders []orderModel.Order
	var confirmInfo confirm.Confirm
	err = source.DB().Where("third_order_sn = ?", confirmRequest.OrderSn).Find(&orders).Error
	if err == nil && len(orders) > 0 {
		//已下单
		for _, orderM := range orders {
			if orderM.Status == 1 {
				log.Log().Error("订单号重复", zap.Any("err", err))
				yzResponse.FailWithMessage("订单号重复", c)
				return
			}
			confirmInfo.Orders = append(confirmInfo.Orders, orderM)
		}
	} else {
		//未下单
		id, err := cache.GetID("api_buy")
		if err != nil {
			log.Log().Error("buy_id生成失败", zap.Any("err", err))
			yzResponse.FailWithMessage("buy_id生成失败", c)
			return
		}
		buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
		//插入购物车记录
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:        userID,
			SkuID:         uint(confirmRequest.SpecID),
			Qty:           uint(confirmRequest.Count),
			Status:        &zero,
			Checked:       &one,
			BuyID:         uint(buyID),
			ApplicationID: utils.GetAppID(c),
			BuyWay:        gva.BYNCONFIRMBUYWAY,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("添加失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("添加失败", c)
			return
		}
		// 读取购物车记录
		err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: gva.BYNCONFIRMBUYWAY})
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if len(shoppingCarts) == 0 {
			//  没有购物车记录
			log.Log().Error("请选择要结算的商品", zap.Any("err", err))
			yzResponse.FailWithMessage("请选择要结算的商品", c)
			return
		}
		// 结算信息
		err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		// 下单
		checkoutInfo.ThirdOrderSn = confirmRequest.OrderSn
		err, confirmInfo = confirm.ShoppingCartConfirm(checkoutInfo)
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: gva.BYNCONFIRMBUYWAY})
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}
	for _, v := range confirmInfo.Orders {
		if v.Status > 0 {
			log.Log().Error("订单已支付，无需再次支付!", zap.Any("err", err))
			yzResponse.FailWithMessage("订单已支付，无需再次支付!", c)
			return
		} else {
			err = service.CreateOrderRequest(v.ID, confirmRequest)
			if err != nil {
				log.Log().Error("操作失败!", zap.Any("err", err))
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
			//获取支付信息
			err, payInfo := orderPay.GetPayInfo(v.UserID, []uint{v.ID})
			if err != nil {
				log.Log().Error("操作失败!", zap.Any("err", err))
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
			var payTypeSort []paymentModel.ApplicationPaySort
			err, payTypeSort = paymentService.GetPaySort(appID)
			if err != nil {
				log.Log().Error("支付方式获取失败!", zap.Any("err", err))
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
			//支付
			err = pay.PaidBySort(v, payInfo, payTypeSort)
			if err != nil {
				log.Log().Error("支付扣款失败!", zap.Any("err", err))
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
		}
	}

	yzResponse.OkWithDetailed(confirmInfo, "操作成功", c)
	return
}
