package v1

import (
	"byn-supply/model"
	"byn-supply/request"
	"byn-supply/service"
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	publicModel "public-supply/model"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func GetBynSupplyId(c *gin.Context) {
	var (
		err    error
		supply publicModel.GatherSupply
	)
	err, supply = service.GetBynSupply()
	if err != nil {
		err = errors.New("获取必应鸟供应链id失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"supply_id": supply.ID}, c)
}

func GetCategories(c *gin.Context) {
	var (
		err        error
		categories []model.BynSupplyCategory
		brands     []model.BynSupplyBrand
	)
	err, categories = service.GetCategories()
	if err != nil {
		err = errors.New("获取二级分类失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, brands = service.GetBrands()
	if err != nil {
		err = errors.New("获取三级分类失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"categories": categories, "brands": brands}, c)
}

func ImportAllGoods(c *gin.Context) {
	var err error
	err = service.ImportAllGoods()
	if err != nil {
		err = errors.New("同步商品失败")
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("同步商品成功", c)
}

func GetOrderList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
