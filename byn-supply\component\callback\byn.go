package callback

import (
	"go.uber.org/zap"
	orderModel "order/model"
	"order/order"
	pubmodel "public-supply/model"
	"yz-go/component/log"
	"yz-go/source"
)

type Byn struct{}

func (*Byn) CallBackMessage(request pubmodel.RequestCallBackType) (err error) {
	if request.Status == 2 {
		var shopOrder orderModel.Order
		err = source.DB().Where("`order_sn` = ?", request.OutTradeNo).First(&shopOrder).Error
		if err != nil {
			return
		}
		if err = order.Send(shopOrder.ID); err != nil {
			log.Log().Error("必应鸟回调2:订单发货操作失败!", zap.Any("err", err))
			return
		}
	} else {
		log.Log().Error("必应鸟回调-1:订单发货操作失败!", zap.Any("err", err))
	}
	return
}
