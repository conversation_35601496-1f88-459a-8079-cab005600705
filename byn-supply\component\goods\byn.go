package goods

import (
	"byn-supply/model"
	categoryModel "category/model"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
	log2 "log"
	productModel "product/model"
	productMq "product/mq"
	publicCallback "public-supply/callback"
	"public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	"public-supply/service"
	publicSetting "public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	yzGoModel "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

//type Goods interface {
// 	获取设置 InitSetting(gatherSupplyID uint) (err error) ✅
//	获取商品列表 GetGoods(info pubreq.GetGoodsSearch) (err error, data interface{}, total int64) ✅
//	同步商品 ImportGoodsRun(info pubreq.GetGoodsSearch) (err error, data interface{}) ✅
//	获取分类 GetCategory(info pubreq.GetCategorySearch) (err error, data interface{}) ❎ 不需要
//	获取分组 GetGroup() (err error, data interface{}) ❎ 不需要
//	获取分类(三级)GetCategoryChild(pid int, info pubreq.GetCategoryChild) (err error, data interface{}) ❎ 不需要
//	api获取分类 RunConcurrent(wg *sync.WaitGroup, info pubreq.GetCategorySearch, i int) (err error) ❎ 不需要
//	拼接商品数据存表 CommodityAssembly(list []pubmodel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product) ❎ 不需要
//	选品库 GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) ❎ 不需要
//	存商品时计算价格 GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) ❎ 不需要
//	定时:同步商品 InitGoods() (err error)
//}

const (
	//URL             = "http://v3.wef2.top"
	URL             = "https://v3.biyingniao.com"
	GOODS_METHOD    = "/api/v3/resource/goods/list"
	CATEGORY_METHOD = "/api/v3/resource/category/list"
	BRAND_METHOD    = "/api/v3/resource/brand/list"

	CONFIRMORDER_METHOD = "/api/v3/resource/order/pre"
	PAYORDER_METHOD     = "/api/v3/resource/order/pay"
)

var defaultCategory2, defaultCategory3 categoryModel.Category

type Byn struct {
	SupplyID uint
}

func (self *Byn) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *Byn) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func (self *Byn) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(productModel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}

func (self *Byn) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*productModel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (st *Byn) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {

	return
}

func (b *Byn) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (b *Byn) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

type BynSupplySetting struct {
	BaseInfo   BaseInfoData   `json:"base_info"`
	UpdateInfo UpdateInfoData `json:"update"`
	Pricing    PricingData    `json:"pricing"`
}

type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

var bynData *BynSupplySetting

// UpdateInfoData 更新设置
type UpdateInfoData struct {
	// 自动更新品牌
	Brand uint `json:"brand"`
	// 自动更新分类
	Goods uint `json:"goods"`
}

// PricingData 定价策略
type PricingData struct {
	// 成本价
	CostPrice PricingType `json:"cost_price"`
	// 供货价
	Price PricingType `json:"price"`
	// 市场价
	OriginPrice PricingType `json:"origin_price"`
	// 指导价
	GuidePrice PricingType `json:"guide_price"`
	// 营销价
	ActivityPrice PricingType `json:"activity_price"`
}

// PricingType 定价详情
type PricingType struct {
	// 1:成本价2:零售价3:面值
	ComputeType int `json:"compute_type"`
	// 成本价定价系数
	CostPriceRatio int `json:"cost_price_ratio"`
	// 零售价定价系数
	PriceRatio int `json:"price_ratio"`
	// 面值定价系数
	DenominationRatio int `json:"denomination_ratio"`
}

func GetSetting(gatherSupplyID uint) (err error, bynSettingData BynSupplySetting) {
	var setting yzGoModel.SysSetting
	err, setting = publicSetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &bynSettingData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	return
}

func (b *Byn) InitSetting(gatherSupplyID uint) (err error) {
	var setting yzGoModel.SysSetting
	err, setting = publicSetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	b.SupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(setting.Value), &bynData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if bynData.BaseInfo.AppKey == "" || bynData.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	return
}

func (b *Byn) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	var goods model.Product
	db := source.DB().Model(&model.Product{})
	db.Where("gather_supply_id = ?", info.GatherSupplyID)
	var bynGoodsIds []uint
	bynDB := source.DB().Model(&model.BynSupplyGoods{})
	if info.CouponType != 0 {
		bynDB.Where("coupon_type = ?", info.CouponType)
	}
	err = bynDB.Pluck("id", &bynGoodsIds).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if len(bynGoodsIds) > 0 {
		db.Where("source_goods_id IN ?", bynGoodsIds)
	}
	if info.Title != "" {
		db.Where("title like ?", "%"+info.Title+"%")
	}
	if info.IsDisplay != nil {
		db.Where("is_display = ?", &info.IsDisplay)
	}
	if info.IsStock != 0 {
		if info.IsStock == 1 {
			db.Where("stock > ?", 0)
		} else {
			db.Where("stock <= ?", 0)
		}
	}
	if len(info.CategoryIds) > 0 {
		db.Where("category2_id IN ?", info.CategoryIds)
	}

	if len(info.BrandIds) > 0 {
		db.Where("category3_id IN ?", info.BrandIds)
	}

	err = db.Count(&total).Error
	err = db.Preload("BynGoodsInfo").Order("created_at desc").Limit(limit).Offset(offset).Find(&goods).Error

	return err, goods, total, serverRatio
}

func (b *Byn) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	//url := URL + GOODS_METHOD + "?app_key=nkvdqe&page=1&page_size=100000"
	////url := "https://v3.biyingniao.com/api/v3/resource/goods/list?app_key=eydmkn"
	//err, httpResData := utils.Get(url)
	//if err != nil {
	//	return err, data
	//}
	//var response model.BynGoodsResponse
	//err = json.Unmarshal(httpResData, &response)
	//if err != nil {
	//	return err, data
	//}
	//if response.Code != 0 {
	//	err = errors.New(response.Message)
	//	return err, data
	//}
	//var page int
	//if len(response.Data.Data) > 1000 {
	//	page = int(math.Ceil(float64(len(response.Data.Data) / 1000)))
	//} else {
	//	page = 1
	//}

	var page int
	page = 5
	// 查询已经导入的products
	var importedProducts []productModel.Product
	err = source.DB().Preload("Skus").Where("`gather_supply_id` = ?", b.SupplyID).Find(&importedProducts).Error
	if err != nil {
		return err, data
	}
	// 已导入的products map
	var importedProductMap = make(map[uint]productModel.Product)
	// 已导入的sku map
	var importedSkuMap = make(map[uint]productModel.Sku)
	for _, goods := range importedProducts {
		importedProductMap[goods.SourceGoodsID] = goods
		importedSkuMap[goods.ID] = goods.Skus[0]
	}
	// 一级分类
	var category1 categoryModel.Category
	err, category1 = createCreategoey1()
	if err != nil {
		return err, data
	}
	// 同步分类
	err = UpdateCategory()
	if err != nil {
		return err, data
	}
	// 同步品牌
	var importedBrandMap = make(map[uint]categoryModel.Brand)
	err, importedBrandMap = UpdateBrand(b.SupplyID)
	if err != nil {
		return err, data
	}
	// 已存在的byn分类数据转成map
	var existedBynCategoriesMap = make(map[uint]model.BynSupplyCategory)
	err, existedBynCategoriesMap = getImportedBynCategoryMap()
	if err != nil {
		return err, data
	}
	// 已经导入到本地的byn分类
	for i := 1; i < page+1; i++ {
		url := URL + GOODS_METHOD + "?app_key=" + string(bynData.BaseInfo.AppKey) + "&page=" + strconv.Itoa(i) + "&page_size=1000"
		//url := URL + GOODS_METHOD + "?app_key=nkvdqe&page=" + strconv.Itoa(i) + "&page_size=1000"
		err, httpResData := utils.Get(url, nil)
		if err != nil {
			return err, data
		}
		var response model.BynGoodsResponse
		err = json.Unmarshal(httpResData, &response)
		if err != nil {
			return err, data
		}
		if response.Code != 0 {
			err = errors.New(response.Message)
			return err, data
		}
		if len(response.Data.Data) <= 0 {
			return err, data
		}
		// 已经保存的byn商品, 要修改的byn商品, 要创建的byn商品
		var bynGoodsList, updateBynGoodsList, insertBynGoodsList []model.BynSupplyGoods
		// 查询已经保存的byn商品,
		err = source.DB().Find(&bynGoodsList).Error
		if err != nil {
			return err, data
		}
		// 已经保存的byn商品 转 map
		var existedBynGoodsMap = make(map[uint]model.BynSupplyGoods)
		// api返回的byn商品 转 map
		var responseBynGoodsMap = make(map[uint]model.BynSupplyGoods)
		for _, bynGoods := range bynGoodsList {
			existedBynGoodsMap[bynGoods.ID] = bynGoods
		}
		// 比对api返回数据,存在并且无变化,跳过,否则添加到product表
		// 存在,有变化, 修改product表
		for _, item := range response.Data.Data {
			// api返回的byn商品 转 map
			responseBynGoodsMap[item.ID] = item
			var jsonResult []byte
			jsonResult, err = json.Marshal(item)
			h := md5.New()
			h.Write(jsonResult)
			item.MD5 = hex.EncodeToString(h.Sum(nil))
			// 验证api返回的byn商品是否已经保存到byn_supply_goods表
			if _, existed := existedBynGoodsMap[item.ID]; existed {
				// 验证是否相同:相同, 跳出 else 不相同, 修改
				if item.MD5 == existedBynGoodsMap[item.ID].MD5 {
					continue
				}
				// 附加 要修改的byn商品
				updateBynGoodsList = append(updateBynGoodsList, item)
			} else {
				// 附加 要创建的byn商品
				insertBynGoodsList = append(insertBynGoodsList, item)
			}
		}
		// 要删除的byn商品ids, 要删除的中台商品ids
		var deleteBynGoodsIds, deleteProductIds []uint
		// 循环已存在的byn商品数据, 对比api返回byn商品数据, 不存在的删除
		for _, goods := range existedBynGoodsMap {
			if _, existed := responseBynGoodsMap[goods.ID]; !existed {
				deleteBynGoodsIds = append(deleteBynGoodsIds, goods.ID)
				deleteProductIds = append(deleteProductIds, importedProductMap[goods.ID].ID)
			}
		}
		// 要修改的byn商品 map, 要修改的product map, 要修改的sku map
		var updateBynGoodsMap, updateProductMap, updateSkuMap []map[string]interface{}
		// 循环 要修改的byn商品(updateBynGoodsList), 修改byn_supply_goods 和 product 表
		for _, goods := range updateBynGoodsList {
			updateBynGoodsMap = append(updateBynGoodsMap, assembleBynGoods(goods))

			productId := importedProductMap[goods.ID].ID
			updateProductMap = append(updateProductMap, assembleProduct(goods, productId))

			skuId := importedSkuMap[importedProductMap[goods.ID].ID].ID
			updateSkuMap = append(updateSkuMap, assembleSku(goods, skuId))
		}
		// 循环 要添加的byn商品, 组装insertProductList
		var insertProductList []productModel.Product
		for _, goods := range insertBynGoodsList {
			var Category2ID, Category3ID uint
			if goods.CategoryId == 0 {
				Category2ID = defaultCategory2.ID
				Category3ID = defaultCategory3.ID
			} else {
				if _, existed := existedBynCategoriesMap[goods.CategoryId]; !existed {
					continue
				}
				Category2ID = existedBynCategoriesMap[goods.CategoryId].ShopCategoryId
			}
			if _, existed := importedBrandMap[goods.BrandId]; !existed {
				continue
			}

			var resCategory3 categoryModel.Category
			var display int
			display = 1
			resCategory3.Name = importedBrandMap[goods.BrandId].Name
			resCategory3.Image = importedBrandMap[goods.BrandId].Logo
			resCategory3.Level = 3
			resCategory3.ParentID = Category2ID
			resCategory3.IsDisplay = &display

			source.DB().Where("name=? and level=? and parent_id=?", resCategory3.Name, resCategory3.Level, resCategory3.ParentID).FirstOrCreate(&resCategory3)
			Category3ID = resCategory3.ID

			insertProductRow := productModel.Product{}
			insertProductRow.Title = goods.Name
			insertProductRow.DetailImages = goods.Description
			insertProductRow.ImageUrl = goods.Logo
			insertProductRow.Stock = goods.Stock
			insertProductRow.BrandID = importedBrandMap[goods.BrandId].ID
			insertProductRow.Category1ID = category1.ID
			insertProductRow.Category2ID = Category2ID
			insertProductRow.Category3ID = Category3ID
			insertProductRow.IsDisplay = goods.Status
			insertProductRow.SourceGoodsID = goods.ID
			insertProductRow.Source = model.BYNID
			insertProductRow.GatherSupplyID = b.SupplyID
			var desc string
			if goods.Validity > 24 {
				desc = "有效期:" + strconv.Itoa(goods.Validity/24) + "天"
			} else {
				desc = "有效期:" + strconv.Itoa(goods.Validity) + "小时"
			}
			insertProductRow.Desc = desc

			var costPrice, price, originPrice, guidePrice, activityPrice uint
			// 成本价 CostPrice
			costPrice = goods.DiscountPrice
			if bynData.Pricing.CostPrice.ComputeType == 1 {
				if bynData.Pricing.CostPrice.CostPriceRatio > 0 {
					costPrice = goods.CostPrice * uint(bynData.Pricing.CostPrice.CostPriceRatio) / 10000
				}
			}
			if bynData.Pricing.CostPrice.ComputeType == 2 {
				if bynData.Pricing.CostPrice.PriceRatio > 0 {
					costPrice = goods.DiscountPrice * uint(bynData.Pricing.CostPrice.PriceRatio) / 10000
				}
			}
			if bynData.Pricing.CostPrice.ComputeType == 3 {
				if bynData.Pricing.CostPrice.DenominationRatio > 0 {
					costPrice = goods.DiscountPrice * uint(bynData.Pricing.CostPrice.DenominationRatio) / 10000
				}
			}
			// 供货价 Price
			price = goods.DiscountPrice
			if bynData.Pricing.Price.ComputeType == 1 {
				if bynData.Pricing.Price.CostPriceRatio > 0 {
					price = goods.CostPrice * uint(bynData.Pricing.Price.CostPriceRatio) / 10000
				}
			}
			if bynData.Pricing.Price.ComputeType == 2 {
				if bynData.Pricing.Price.PriceRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.Price.PriceRatio) / 10000
				}
			}
			if bynData.Pricing.Price.ComputeType == 3 {
				if bynData.Pricing.Price.DenominationRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.Price.DenominationRatio) / 10000
				}
			}
			// 市场价 OriginPrice
			originPrice = goods.DiscountPrice
			if bynData.Pricing.OriginPrice.ComputeType == 1 {
				if bynData.Pricing.OriginPrice.CostPriceRatio > 0 {
					price = goods.CostPrice * uint(bynData.Pricing.OriginPrice.CostPriceRatio) / 10000
				}
			}
			if bynData.Pricing.OriginPrice.ComputeType == 2 {
				if bynData.Pricing.OriginPrice.PriceRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.OriginPrice.PriceRatio) / 10000
				}
			}
			if bynData.Pricing.OriginPrice.ComputeType == 3 {
				if bynData.Pricing.OriginPrice.DenominationRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.OriginPrice.DenominationRatio) / 10000
				}
			}
			// 指导价 GuidePrice
			guidePrice = goods.DiscountPrice
			if bynData.Pricing.GuidePrice.ComputeType == 1 {
				if bynData.Pricing.GuidePrice.CostPriceRatio > 0 {
					price = goods.CostPrice * uint(bynData.Pricing.GuidePrice.CostPriceRatio) / 10000
				}
			}
			if bynData.Pricing.GuidePrice.ComputeType == 2 {
				if bynData.Pricing.GuidePrice.PriceRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.GuidePrice.PriceRatio) / 10000
				}
			}
			if bynData.Pricing.GuidePrice.ComputeType == 3 {
				if bynData.Pricing.GuidePrice.DenominationRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.GuidePrice.DenominationRatio) / 10000
				}
			}
			// 营销价 ActivityPrice
			activityPrice = goods.DiscountPrice
			if bynData.Pricing.ActivityPrice.ComputeType == 1 {
				if bynData.Pricing.ActivityPrice.CostPriceRatio > 0 {
					price = goods.CostPrice * uint(bynData.Pricing.ActivityPrice.CostPriceRatio) / 10000
				}
			}
			if bynData.Pricing.ActivityPrice.ComputeType == 2 {
				if bynData.Pricing.ActivityPrice.PriceRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.ActivityPrice.PriceRatio) / 10000
				}
			}
			if bynData.Pricing.ActivityPrice.ComputeType == 3 {
				if bynData.Pricing.ActivityPrice.DenominationRatio > 0 {
					price = goods.DiscountPrice * uint(bynData.Pricing.ActivityPrice.DenominationRatio) / 10000
				}
			}
			insertProductRow.Price = price
			insertProductRow.CostPrice = costPrice
			insertProductRow.OriginPrice = originPrice
			insertProductRow.GuidePrice = guidePrice
			insertProductRow.ActivityPrice = activityPrice

			if len(goods.Specs) > 0 {
				for _, spec := range goods.Specs {
					var sku productModel.Sku
					var options productModel.Options
					var option productModel.Option
					option.SpecName = spec.Name
					option.SpecItemName = spec.Name
					options = append(options, option)
					sku.Title = spec.Name
					sku.Options = options
					sku.Weight = 0
					sku.Stock = int(goods.Stock)
					sku.IsDisplay = display
					sku.Price = price
					sku.OriginPrice = originPrice
					sku.CostPrice = costPrice
					sku.GuidePrice = guidePrice
					sku.ActivityPrice = activityPrice
					sku.OriginalSkuID = int64(spec.Id)
					insertProductRow.Skus = append(insertProductRow.Skus, sku)
				}
			} else {
				var sku productModel.Sku
				var options productModel.Options
				var option productModel.Option
				option.SpecName = "规格"
				option.SpecItemName = "默认"
				options = append(options, option)
				sku.Title = "默认"
				sku.Options = options
				sku.Weight = 0
				sku.CostPrice = goods.CostPrice
				sku.Stock = int(goods.Stock)
				sku.IsDisplay = display
				sku.Price = price
				sku.OriginPrice = originPrice
				sku.CostPrice = costPrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = activityPrice
				insertProductRow.Skus = append(insertProductRow.Skus, sku)
			}
			insertProductList = append(insertProductList, insertProductRow)
		}
		// 批量修改byn商品
		if len(updateBynGoodsMap) > 0 {
			err = source.BatchUpdate(updateBynGoodsMap, "byn_supply_goods", "")
			if err != nil {
				return err, data
			}
		}
		// 批量修改中台商品
		if len(updateProductMap) > 0 {
			for _, row := range updateProductMap {
				log.Log().Debug("BYN修改商品", zap.Any("id", row["id"]))
				err = productMq.PublishMessage(row["id"].(uint), productMq.Edit, 0)
				if err != nil {
					fmt.Println("mq插入失败", err)
				}
			}
			err = source.BatchUpdate(updateProductMap, "products", "")
			if err != nil {
				return err, data
			}
		}
		// 批量修改中台商品规格
		if len(updateSkuMap) > 0 {
			err = source.BatchUpdate(updateSkuMap, "skus", "")
			if err != nil {
				return err, data
			}
		}
		// 批量创建byn商品
		if len(insertBynGoodsList) > 0 {
			err = source.DB().CreateInBatches(&insertBynGoodsList, 500).Error
			if err != nil {
				return err, data
			}
		}
		// 批量创建中台商品
		if len(insertProductList) > 0 {
			for _, goods := range insertProductList {
				err = source.DB().Create(&goods).Error
				if err != nil {
					fmt.Println("插入失败", err)
				}
				err = productMq.PublishMessage(goods.ID, productMq.Create, 0)
				if err != nil {
					fmt.Println("mq插入失败", err)
				}
			}

			//err = source.DB().CreateInBatches(&insertProductList, 500).Error
			//if err != nil {
			//	return err, data
			//}
		}
		// 删除byn商品
		if len(deleteBynGoodsIds) > 0 {
			err = source.DB().Delete(&model.BynSupplyGoods{}, deleteBynGoodsIds).Error
			if err != nil {
				return err, data
			}
		}
		// 删除中台商品
		if len(deleteProductIds) > 0 {
			err = source.DB().Delete(&productModel.Product{}, deleteProductIds).Error
			if err != nil {
				return err, data
			}
		}
	}
	return err, data
}

func assembleSku(goods model.BynSupplyGoods, id uint) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = id
	// 指导价
	row["guide_price"] = goods.DiscountPrice
	// 市场价
	row["origin_price"] = goods.DiscountPrice
	// 成本价
	row["cost_price"] = goods.CostPrice
	// 供货价
	row["price"] = goods.SalePrice
	row["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

	return
}

func assembleBynGoods(goods model.BynSupplyGoods) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = goods.ID
	row["name"] = goods.Name
	row["description"] = goods.Description
	row["logo"] = goods.Logo
	row["sale_price"] = goods.SalePrice
	row["stock"] = goods.Stock
	row["brand_id"] = goods.BrandId
	row["category_id"] = goods.CategoryId
	row["status"] = goods.Status
	row["validity"] = goods.Validity
	row["coupon_type"] = goods.CouponType
	row["sub_coupon_type"] = goods.SubCouponType
	res, _ := json.Marshal(goods.Specs)
	row["specs"] = string(res)
	row["cost_price"] = goods.CostPrice
	row["discount_price"] = goods.DiscountPrice
	row["md_5"] = goods.MD5
	return
}

func assembleProduct(goods model.BynSupplyGoods, id uint) (row map[string]interface{}) {
	row = make(map[string]interface{})
	row["id"] = id
	row["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
	row["title"] = goods.Name
	row["detail_images"] = goods.Description
	row["image_url"] = goods.Logo
	row["stock"] = goods.Stock
	row["is_display"] = goods.Status

	var desc string
	if goods.Validity > 24 {
		desc = "有效期:" + strconv.Itoa(goods.Validity/24) + "天"
	} else {
		desc = "有效期:" + strconv.Itoa(goods.Validity) + "小时"
	}
	row["desc"] = desc

	var costPrice, price, originPrice, guidePrice, activityPrice uint
	// 成本价 CostPrice
	costPrice = goods.DiscountPrice
	if bynData.Pricing.CostPrice.ComputeType == 1 {
		if bynData.Pricing.CostPrice.CostPriceRatio > 0 {
			costPrice = goods.CostPrice * uint(bynData.Pricing.CostPrice.CostPriceRatio) / 10000
		}
	}
	if bynData.Pricing.CostPrice.ComputeType == 2 {
		if bynData.Pricing.CostPrice.PriceRatio > 0 {
			costPrice = goods.DiscountPrice * uint(bynData.Pricing.CostPrice.PriceRatio) / 10000
		}
	}
	if bynData.Pricing.CostPrice.ComputeType == 3 {
		if bynData.Pricing.CostPrice.DenominationRatio > 0 {
			costPrice = goods.DiscountPrice * uint(bynData.Pricing.CostPrice.DenominationRatio) / 10000
		}
	}
	// 供货价 Price
	price = goods.DiscountPrice
	if bynData.Pricing.Price.ComputeType == 1 {
		if bynData.Pricing.Price.CostPriceRatio > 0 {
			price = goods.CostPrice * uint(bynData.Pricing.Price.CostPriceRatio) / 10000
		}
	}
	if bynData.Pricing.Price.ComputeType == 2 {
		if bynData.Pricing.Price.PriceRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.Price.PriceRatio) / 10000
		}
	}
	if bynData.Pricing.Price.ComputeType == 3 {
		if bynData.Pricing.Price.DenominationRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.Price.DenominationRatio) / 10000
		}
	}
	// 市场价 OriginPrice
	originPrice = goods.DiscountPrice
	if bynData.Pricing.OriginPrice.ComputeType == 1 {
		if bynData.Pricing.OriginPrice.CostPriceRatio > 0 {
			price = goods.CostPrice * uint(bynData.Pricing.OriginPrice.CostPriceRatio) / 10000
		}
	}
	if bynData.Pricing.OriginPrice.ComputeType == 2 {
		if bynData.Pricing.OriginPrice.PriceRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.OriginPrice.PriceRatio) / 10000
		}
	}
	if bynData.Pricing.OriginPrice.ComputeType == 3 {
		if bynData.Pricing.OriginPrice.DenominationRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.OriginPrice.DenominationRatio) / 10000
		}
	}
	// 指导价 GuidePrice
	guidePrice = goods.DiscountPrice
	if bynData.Pricing.GuidePrice.ComputeType == 1 {
		if bynData.Pricing.GuidePrice.CostPriceRatio > 0 {
			price = goods.CostPrice * uint(bynData.Pricing.GuidePrice.CostPriceRatio) / 10000
		}
	}
	if bynData.Pricing.GuidePrice.ComputeType == 2 {
		if bynData.Pricing.GuidePrice.PriceRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.GuidePrice.PriceRatio) / 10000
		}
	}
	if bynData.Pricing.GuidePrice.ComputeType == 3 {
		if bynData.Pricing.GuidePrice.DenominationRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.GuidePrice.DenominationRatio) / 10000
		}
	}
	// 营销价 ActivityPrice
	activityPrice = goods.DiscountPrice
	if bynData.Pricing.ActivityPrice.ComputeType == 1 {
		if bynData.Pricing.ActivityPrice.CostPriceRatio > 0 {
			price = goods.CostPrice * uint(bynData.Pricing.ActivityPrice.CostPriceRatio) / 10000
		}
	}
	if bynData.Pricing.ActivityPrice.ComputeType == 2 {
		if bynData.Pricing.ActivityPrice.PriceRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.ActivityPrice.PriceRatio) / 10000
		}
	}
	if bynData.Pricing.ActivityPrice.ComputeType == 3 {
		if bynData.Pricing.ActivityPrice.DenominationRatio > 0 {
			price = goods.DiscountPrice * uint(bynData.Pricing.ActivityPrice.DenominationRatio) / 10000
		}
	}
	row["price"] = price
	row["cost_price"] = costPrice
	row["origin_price"] = originPrice
	row["guide_price"] = guidePrice
	row["activity_price"] = activityPrice

	return
}

// UpdateCategory 同步byn分类
func UpdateCategory() (err error) {
	//url := URL + CATEGORY_METHOD + "?app_key=nkvdqe&page=1&page_size=100000"
	//err, httpResData := utils.Get(url)
	//if err != nil {
	//	return err
	//}
	//var response model.BynCategoryResponse
	//err = json.Unmarshal(httpResData, &response)
	//if err != nil {
	//	return err
	//}
	//if response.Code != 0 {
	//	err = errors.New(response.Message)
	//	return err
	//}
	//var page int
	//if len(response.Data.Data) > 1000 {
	//	page = int(math.Ceil(float64(len(response.Data.Data) / 1000)))
	//} else {
	//	page = 1
	//}

	// 暂时5000条
	var page int
	page = 5

	var category1 categoryModel.Category
	err, category1 = createCreategoey1()
	if err != nil {
		return err
	}
	// 已存在的分类数据转成map
	var existedBynCategoriesMap = make(map[uint]model.BynSupplyCategory)
	err, existedBynCategoriesMap = getImportedBynCategoryMap()
	for i := 1; i < page+1; i++ {
		url := URL + CATEGORY_METHOD + "?app_key=" + string(bynData.BaseInfo.AppKey) + "&page=" + strconv.Itoa(i) + "&page_size=1000"
		//url := URL + CATEGORY_METHOD + "?app_key=nkvdqe&page=" + strconv.Itoa(i) + "&page_size=1000"
		err, httpResData := utils.Get(url, nil)
		if err != nil {
			return err
		}
		var response model.BynCategoryResponse
		err = json.Unmarshal(httpResData, &response)
		if err != nil {
			return err
		}
		if response.Code != 0 {
			err = errors.New(response.Message)
			return err
		}
		if len(response.Data.Data) <= 0 {
			return err
		}
		var bynCategories []model.BynSupplyCategory
		var display int
		for _, category := range response.Data.Data {
			// 如果本地分类数据已存在,跳出本次循环
			if _, existed := existedBynCategoriesMap[uint(category.Id)]; existed {
				continue
			}
			var resCategory categoryModel.Category
			resCategory.Name = category.Name
			resCategory.Level = 2
			resCategory.ParentID = category1.ID
			resCategory.IsDisplay = &display
			source.DB().Where("name=? and level=? and parent_id=?", category.Name, 2, category1.ID).FirstOrCreate(&resCategory)
			var res model.BynSupplyCategory
			res.ID = uint(category.Id)
			res.Name = category.Name
			res.ShopCategoryId = resCategory.ID
			bynCategories = append(bynCategories, res)

		}
		// 创建默认分类
		//var defaultCategory2, defaultCategory3 categoryModel.Category
		defaultCategory2.Name = "默认分类"
		defaultCategory2.Level = 2
		defaultCategory2.ParentID = category1.ID
		defaultCategory2.IsDisplay = &display
		source.DB().Where("name=? and level=? and parent_id=?", defaultCategory2.Name, 2, category1.ID).FirstOrCreate(&defaultCategory2)
		defaultCategory3.Name = "默认分类"
		defaultCategory3.Level = 3
		defaultCategory3.ParentID = defaultCategory2.ID
		defaultCategory3.IsDisplay = &display
		source.DB().Where("name=? and level=? and parent_id=?", defaultCategory3.Name, 3, defaultCategory2.ID).FirstOrCreate(&defaultCategory3)

		if len(bynCategories) > 0 {
			source.DB().Model(&model.BynSupplyCategory{}).CreateInBatches(bynCategories, 500)
		}
	}
	return err
}

// GetCategory 获取分类
func (b *Byn) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {
	return
}

// GetGroup 获取分组
func (b *Byn) GetGroup() (err error, data interface{}) {
	return
}

// GetCategoryChild 获取子分类
func (b *Byn) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	return
}

// api获取分类 RunConcurrent(wg *sync.WaitGroup, info pubreq.GetCategorySearch, i int) (err error)
func (b *Byn) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {
	return
}

// 拼接商品数据存表 CommodityAssembly(list []pubmodel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product)
func (b *Byn) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*productModel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {
	return
}

// 选品库 GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{})
func (b *Byn) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	return
}

// 存商品时计算价格 GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error)
func (b *Byn) GoodsPriceAlert(GoodsData publicCallback.GoodsCallBack) (err error) {
	return
}

// 定时:同步商品 InitGoods() (err error)
func (b *Byn) InitGoods() (err error) {
	return
}

// UpdateBrand 同步byn品牌
func UpdateBrand(supplyId uint) (err error, importedBrandMap map[uint]categoryModel.Brand) {
	//url := URL + BRAND_METHOD + "?app_key=nkvdqe&page=1&page_size=100000"
	//err, httpResData := utils.Get(url)
	//if err != nil {
	//	return err
	//}
	//var response model.BynBrandResponse
	//err = json.Unmarshal(httpResData, &response)
	//if err != nil {
	//	return err
	//}
	//if response.Code != 0 {
	//	err = errors.New(response.Message)
	//	return err
	//}
	//var page int
	//if len(response.Data.Data) > 1000 {
	//	page = int(math.Ceil(float64(len(response.Data.Data) / 1000)))
	//} else {
	//	page = 1
	//}

	importedBrandMap = make(map[uint]categoryModel.Brand)

	// 暂时5000条
	var page int
	page = 5

	//  bynData

	// 已存在的品牌数据转成map
	var existedBynBrandsMap = make(map[uint]model.BynSupplyBrand)
	err, existedBynBrandsMap = getImportedBynBrandMap()
	if err != nil {
		return err, importedBrandMap
	}
	for i := 1; i < page+1; i++ {
		url := URL + BRAND_METHOD + "?app_key=" + bynData.BaseInfo.AppKey + "&page=" + strconv.Itoa(i) + "&page_size=1000"
		//url := URL + BRAND_METHOD + "?app_key=nkvdqe&page=" + strconv.Itoa(i) + "&page_size=1000"
		err, httpResData := utils.Get(url, nil)
		if err != nil {
			return err, importedBrandMap
		}
		var response model.BynBrandResponse
		err = json.Unmarshal(httpResData, &response)
		if err != nil {
			return err, importedBrandMap
		}
		if response.Code != 0 {
			err = errors.New(response.Message)
			return err, importedBrandMap
		}
		if len(response.Data.Data) <= 0 {
			return err, importedBrandMap
		}
		var bynBrands []model.BynSupplyBrand
		for _, brand := range response.Data.Data {
			// 如果本地品牌数据已存在,跳出本次循环
			if _, existed := existedBynBrandsMap[uint(brand.Id)]; existed {
				var resBrand categoryModel.Brand
				resBrand.Name = brand.Name
				resBrand.Logo = brand.Logo
				resBrand.Desc = brand.Description
				resBrand.Source = int(supplyId)
				source.DB().Where("name=?", brand.Name).FirstOrCreate(&resBrand)
				importedBrandMap[uint(brand.Id)] = resBrand
				continue
			}
			var resBrand categoryModel.Brand
			resBrand.Name = brand.Name
			resBrand.Logo = brand.Logo
			resBrand.Desc = brand.Description
			resBrand.Source = int(supplyId)
			source.DB().Where("name=?", brand.Name).FirstOrCreate(&resBrand)
			importedBrandMap[uint(brand.Id)] = resBrand
			var res model.BynSupplyBrand
			res.ID = uint(brand.Id)
			res.ShopBrandId = resBrand.ID
			res.Name = brand.Name
			res.Logo = brand.Logo
			res.Description = brand.Description
			bynBrands = append(bynBrands, res)
		}
		if len(bynBrands) > 0 {
			source.DB().Model(&model.BynSupplyBrand{}).CreateInBatches(bynBrands, 500)
		}
	}
	return err, importedBrandMap
}

func createCreategoey1() (err error, category1 categoryModel.Category) {
	category1.Name = "卡券资源"
	category1.Level = 1
	category1.ParentID = 0
	display := 1
	category1.IsDisplay = &display
	// 创建一级分类 卡券资源
	err = source.DB().Where("name = ? and level = ? and parent_id = ?", category1.Name, category1.Level, category1.ParentID).FirstOrCreate(&category1).Error
	return err, category1
}

func getImportedBynCategoryMap() (err error, existedBynCategoriesMap map[uint]model.BynSupplyCategory) {
	// 查询已经同步到本地的分类数据
	var existedBynCategories []model.BynSupplyCategory
	err = source.DB().Find(&existedBynCategories).Error
	if err != nil {
		return err, existedBynCategoriesMap
	}
	// 已存在的分类数据转成map
	existedBynCategoriesMap = make(map[uint]model.BynSupplyCategory)
	for _, bynCategory := range existedBynCategories {
		existedBynCategoriesMap[bynCategory.ID] = bynCategory
	}
	return err, existedBynCategoriesMap
}

func getImportedBynBrandMap() (err error, existedBynBrandsMap map[uint]model.BynSupplyBrand) {
	// 查询已经同步到本地的品牌数据
	var existedBynBrands []model.BynSupplyBrand
	err = source.DB().Find(&existedBynBrands).Error
	if err != nil {
		return err, existedBynBrandsMap
	}
	// 已存在的品牌数据转成map
	existedBynBrandsMap = make(map[uint]model.BynSupplyBrand)
	for _, bynBrand := range existedBynBrands {
		existedBynBrandsMap[bynBrand.ID] = bynBrand
	}
	return err, existedBynBrandsMap
}

func getImportedCategoryMap(parentId uint) (err error, importedCategoryMap map[uint]categoryModel.Category) {
	importedCategoryMap = make(map[uint]categoryModel.Category)
	var importedCategories []categoryModel.Category
	err = source.DB().Where("parent_id = ? AND level = ?", parentId, 2).Find(&importedCategories).Error
	if err != nil {
		return err, importedCategoryMap
	}
	for _, category := range importedCategories {
		importedCategoryMap[category.ID] = category
	}
	return err, importedCategoryMap
}
