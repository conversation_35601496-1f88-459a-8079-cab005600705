package goods

import (
	"fmt"
	"public-supply/request"
	"testing"
)

func TestByn_ImportGoodsRun(t *testing.T) {
	type args struct {
		info request.GetGoodsSearch
	}
	tt := struct {
		args args
	}{
		// TODO: Add test cases.
	}
	b := &Byn{
		SupplyID: 45,
	}
	var err error
	err = b.InitSetting(6)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	//err, _, _ = b.GetGoods(tt.args.info)
	//if err != nil {
	//	fmt.Println(err.Error())
	//}
	//return
	err, _ = b.ImportGoodsRun(tt.args.info)
	if err != nil {
		fmt.Println(err.Error())
	}
	fmt.Println("11111111111111")
	// _ = b.UpdateCategory()
	//_ = b.UpdateBrand()
}
