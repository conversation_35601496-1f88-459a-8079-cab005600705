package goods

import (
	"fmt"
	"github.com/xingliuhua/leaf"
	url2 "net/url"
	publicModel "public-supply/model"
	"sort"
	"strconv"
	"time"
	"yz-go/source"
)

func GetIdArr(list []publicModel.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}
func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}
func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}
func splitArray(arr []publicModel.Goods, num int64) [][]publicModel.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]publicModel.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]publicModel.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}
func GetRequestParams(params map[string]string, data *BynSupplySetting) (result url2.Values) {
	reqData := url2.Values{}
	for k, v := range params {
		reqData.Add(k, v)
	}
	reqData.Add("app_key", data.BaseInfo.AppKey)
	reqData.Add("app_secret", data.BaseInfo.AppSecret)
	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
	return reqData
}

type MapEntryHandler func(string, string)

// 按字母顺序遍历map
func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}
