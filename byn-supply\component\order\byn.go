package order

import (
	afterSalesModel "after-sales/model"
	byn "byn-supply/component/goods"
	"byn-supply/model"
	"encoding/json"
	"errors"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	url2 "net/url"
	orderModel "order/model"
	"order/order"
	callback2 "public-supply/callback"
	"public-supply/request"
	"public-supply/response"
	publicSetting "public-supply/setting"
	"strconv"
	"time"
	"yz-go/component/log"
	yzGoModel "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Byn struct{}

func (b *Byn) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (b *Byn) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (b *Byn) GetAllAddress() (err error, data interface{}) {
	return
}

var bynData *byn.BynSupplySetting

type ConfirmRequest struct {
	request.BynPreOrder
}

type PayRequest struct {
	// 预下单返回的订单号
	OrderNo string `json:"order_no" form:"order_no"`
	// success成功fail失败
	Status string `json:"status" form:"status"`
}

type BynPreOrderResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderNo string `json:"order_no"`
	} `json:"data"`
}

type BynOrderPayResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		GoodsId        int     `json:"goods_id"`
		GoodsName      string  `json:"goods_name"`
		GoodsCover     string  `json:"goods_cover"`
		SalePrice      float64 `json:"sale_price"`
		Count          int     `json:"count"`
		CreatedAt      string  `json:"created_at"`
		UpdatedAt      string  `json:"updated_at"`
		OrderPrice     float64 `json:"order_price"`
		OrderNo        string  `json:"order_no"`
		OutTradeNo     string  `json:"out_trade_no"`
		Instruction    string  `json:"instruction"`
		UseNotice      string  `json:"use_notice"`
		Params         string  `json:"params"`
		StraightParams struct {
			RechargeNumber string `json:"recharge_number"`
			Username       string `json:"username"`
			Card           string `json:"card"`
		} `json:"straight_params"`
		Status     int `json:"status"`
		CouponId   int `json:"coupon_id"`
		CouponInfo struct {
			Coupons []struct {
				GoodsType     string `json:"goods_type"`
				GoodsLink     string `json:"goods_link"`
				GoodsNumber   string `json:"goods_number"`
				GoodsPassword string `json:"goods_password"`
				EffectiveTime string `json:"effective_time"`
			} `json:"coupons"`
		} `json:"coupon_info"`
	} `json:"data"`
}

func (*Byn) InitSetting(gatherSupplyID uint) (err error) {
	var setting yzGoModel.SysSetting
	err, setting = publicSetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &bynData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if bynData.BaseInfo.AppKey == "" || bynData.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	return
}

// ConfirmOrder 下单
func (*Byn) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	var confirmRequest ConfirmRequest
	var shopOrder orderModel.Order
	err = source.DB().Where("`order_sn` = ?", request.OrderSn).First(shopOrder).Error
	//err = source.DB().Where("`order_sn` = ?", 180693399673).First(&shopOrder).Error
	if err != nil {
		return
	}
	var orderRequest model.BynSupplyOrderRequest
	err = source.DB().Where("order_id = ?", shopOrder.ID).First(&orderRequest).Error
	if err != nil {
		return
	}

	reqData := url2.Values{}
	reqData.Add("app_key", bynData.BaseInfo.AppKey)
	reqData.Add("app_secret", bynData.BaseInfo.AppSecret)
	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
	reqData.Add("coupon_id", strconv.Itoa(orderRequest.CouponID))
	reqData.Add("count", strconv.Itoa(orderRequest.Count))
	reqData.Add("out_trade_no", strconv.Itoa(int(shopOrder.OrderSN)))
	reqData.Add("recharge_number", orderRequest.RechargeNumber)
	reqData.Add("card", orderRequest.Card)
	reqData.Add("username", orderRequest.Username)
	reqData.Add("coupon_type", strconv.Itoa(orderRequest.CouponType))
	reqData.Add("goods_id", strconv.Itoa(int(orderRequest.GoodsID)))
	reqData.Add("spec_id", strconv.Itoa(orderRequest.SpecID))

	err, result := utils.PostForm(byn.URL+byn.CONFIRMORDER_METHOD, reqData, nil)
	var bynPreOrderResponse BynPreOrderResponse
	err = json.Unmarshal(result, &bynPreOrderResponse)
	if err != nil {
		return
	}
	if bynPreOrderResponse.Code != 0 {
		err = errors.New(bynPreOrderResponse.Message)
		return
	}
	// 支付
	var payRequest PayRequest
	payRequest.OrderNo = bynPreOrderResponse.Data.OrderNo
	payRequest.Status = "success"
	var jsonPayData []byte
	jsonPayData, _ = json.Marshal(&payRequest)
	payParams := make(map[string]string)
	err = json.Unmarshal(jsonPayData, &payParams)
	if err != nil {
		return
	}
	requestPayParams := byn.GetRequestParams(payParams, bynData)
	err, payResult := utils.PostForm(byn.URL+byn.PAYORDER_METHOD, requestPayParams, nil)
	var bynOrderPayResponse BynOrderPayResponse
	err = json.Unmarshal(payResult, &bynOrderPayResponse)
	if err != nil {
		return
	}
	if bynOrderPayResponse.Code != 0 {
		err = errors.New(bynOrderPayResponse.Message)
		return
	}
	if bynOrderPayResponse.Data.Status != 2 {
		if bynOrderPayResponse.Data.Status == -1 {
			err = errors.New("必应鸟订单支付失败")
			return
		}
		if bynOrderPayResponse.Data.Status == 3 {
			err = errors.New("处理中,请联系客服")
			return
		}
	}
	// 保存订单关联表信息
	var bynSupplyOrder model.BynSupplyOrder
	bynSupplyOrder.GoodsId = bynOrderPayResponse.Data.GoodsId
	bynSupplyOrder.GoodsName = bynOrderPayResponse.Data.GoodsName
	bynSupplyOrder.GoodsCover = bynOrderPayResponse.Data.GoodsCover
	bynSupplyOrder.SalePrice = uint(bynOrderPayResponse.Data.SalePrice * 100)
	bynSupplyOrder.Count = bynOrderPayResponse.Data.Count
	bynSupplyOrder.OrderPrice = uint(bynOrderPayResponse.Data.OrderPrice * 100)
	bynSupplyOrder.OrderNo = bynOrderPayResponse.Data.OrderNo
	bynSupplyOrder.OutTradeNo = bynOrderPayResponse.Data.OutTradeNo
	bynSupplyOrder.Instruction = bynOrderPayResponse.Data.Instruction
	bynSupplyOrder.UseNotice = bynOrderPayResponse.Data.UseNotice
	bynSupplyOrder.Params = bynOrderPayResponse.Data.Params
	bynSupplyOrder.StraightParams.RechargeNumber = bynOrderPayResponse.Data.StraightParams.RechargeNumber
	bynSupplyOrder.StraightParams.Username = bynOrderPayResponse.Data.StraightParams.Username
	bynSupplyOrder.StraightParams.Card = bynOrderPayResponse.Data.StraightParams.Card
	bynSupplyOrder.Status = bynOrderPayResponse.Data.Status
	bynSupplyOrder.CouponId = bynOrderPayResponse.Data.CouponId
	if len(bynOrderPayResponse.Data.CouponInfo.Coupons) > 0 {
		for _, info := range bynOrderPayResponse.Data.CouponInfo.Coupons {
			var couponInfo model.BynSupplyOrderCoupon
			couponInfo.GoodsType = info.GoodsType
			couponInfo.GoodsLink = info.GoodsLink
			couponInfo.GoodsNumber = info.GoodsNumber
			couponInfo.GoodsPassword = info.GoodsPassword
			couponInfo.EffectiveTime = info.EffectiveTime
			bynSupplyOrder.CouponInfo = append(bynSupplyOrder.CouponInfo, couponInfo)
		}
	}
	err = source.DB().Create(&bynSupplyOrder).Error
	if err != nil {
		return
	}

	// 修改订单 供应链订单编号
	err = source.DB().Model(&orderModel.Order{}).Where("`order_sn` = ?", bynOrderPayResponse.Data.OutTradeNo).Update("gather_supply_sn", bynOrderPayResponse.Data.OrderNo).Error
	if err != nil {
		return
	}

	// 如果商品为卡券, 修改订单状态为发货
	if confirmRequest.CouponType == 2 {
		var shopOrder orderModel.Order
		err = source.DB().Where("`order_sn` = ?", bynOrderPayResponse.Data.OutTradeNo).First(&shopOrder).Error
		if err != nil {
			return
		}
		if err = order.Send(shopOrder.ID); err != nil {
			log.Log().Error("必应鸟:订单发货操作失败!", zap.Any("err", err))
			return
		}
	}

	info = &stbz.APIResult{
		Code: 1,
		Msg:  bynOrderPayResponse.Message,
		Data: bynOrderPayResponse.Data,
	}
	return
}

// OrderBeforeCheck 要实现
func (*Byn) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	data.Freight = 0
	data.Msg = "成功"
	data.Code = 1
	return
}

func (*Byn) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (*Byn) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	return
}

func (*Byn) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	return
}

func (*Byn) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (*Byn) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (*Byn) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (*Byn) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}
