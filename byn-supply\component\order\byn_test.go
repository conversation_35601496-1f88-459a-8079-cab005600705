package order

import (
	"fmt"
	"public-supply/request"
	"testing"
)

func TestByn_InitSetting(t *testing.T) {
	by := &Byn{}
	_ = by.InitSetting(6)
}

func TestByn_ConfirmOrder(t *testing.T) {
	type args struct {
		request request.RequestConfirmOrder
	}
	tests := struct {
		args args
	}{
		// TODO: Add test cases.
	}
	by := &Byn{}
	var err error
	err = by.InitSetting(6)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	err, _ = by.ConfirmOrder(tests.args.request)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
}
