package cron

import (
	"byn-supply/component/goods"
	"byn-supply/service"
	publicModel "public-supply/model"
	"public-supply/request"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
)

func PushBynGoodsSyncHandle() {
	task := cron.Task{
		Key:  "bynSupplyGoodsSync",
		Name: "必应鸟供应链商品同步",
		// 每十分钟执行一次
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			GoodsSync()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func PushBynBrandSyncHandle() {
	task := cron.Task{
		Key:  "bynSupplyBrandSync",
		Name: "必应鸟供应链品牌同步",
		// 每三十分钟执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			BrandSync()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func BrandSync() {
	return
	log.Log().Info("必应鸟供应链品牌同步,开始")
	var (
		err            error
		supply         publicModel.GatherSupply
		bynGoods       goods.Byn
		bynSettingData goods.BynSupplySetting
	)
	err, supply = service.GetBynSupply()
	if err != nil {
		return
	}
	err, bynSettingData = goods.GetSetting(supply.ID)
	if err != nil {
		log.Log().Info("id为" + strconv.Itoa(int(supply.ID)) + "的供应链没有设置1：" + err.Error())
		return
	}
	if bynSettingData.UpdateInfo.Brand != 1 {
		return
	}
	log.Log().Info("必应鸟设置开启自动品牌同步,继续")
	err = bynGoods.InitSetting(supply.ID)
	if err != nil {
		log.Log().Info("id为" + strconv.Itoa(int(supply.ID)) + "的供应链没有设置2：" + err.Error())
		return
	}
	err, _ = goods.UpdateBrand(supply.ID)
	if err != nil {
		log.Log().Info("id为" + strconv.Itoa(int(supply.ID)) + "的供应链同步品牌失败：" + err.Error())
		return
	}
	log.Log().Info("必应鸟供应链品牌同步,成功")
}

func GoodsSync() {
	return
	log.Log().Info("必应鸟供应链商品同步,开始")
	var (
		err            error
		supply         publicModel.GatherSupply
		bynGoods       goods.Byn
		info           request.GetGoodsSearch
		bynSettingData goods.BynSupplySetting
	)
	err, supply = service.GetBynSupply()
	if err != nil {
		return
	}
	err, bynSettingData = goods.GetSetting(supply.ID)
	if err != nil {
		log.Log().Info("id为" + strconv.Itoa(int(supply.ID)) + "的供应链没有设置1：" + err.Error())
		return
	}
	if bynSettingData.UpdateInfo.Goods != 1 {
		return
	}
	log.Log().Info("必应鸟设置开启自动商品同步,继续")
	err = bynGoods.InitSetting(supply.ID)
	if err != nil {
		log.Log().Info("id为" + strconv.Itoa(int(supply.ID)) + "的供应链没有设置：" + err.Error())
		return
	}
	err, _ = bynGoods.ImportGoodsRun(info)
	if err != nil {
		log.Log().Info("id为" + strconv.Itoa(int(supply.ID)) + "的供应链导入商品失败：" + err.Error())
		return
	}
	log.Log().Info("必应鸟供应链商品同步,成功")
}
