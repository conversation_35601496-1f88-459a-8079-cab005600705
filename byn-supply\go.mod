module byn-supply

go 1.21

require (
	category v1.0.0
	gin-vue-admin v1.0.0
	github.com/gin-gonic/gin v1.6.3
	github.com/writethesky/stbz-sdk-golang v1.0.1
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	order v1.0.0
	payment v1.0.0
	product v1.0.0
	public-supply v1.0.0
	shopping-cart v1.0.0
	trade v1.0.0
	yz-go v1.0.0
)

replace (
	after-sales => ../after-sales
	application => ../application
	category => ../category
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product => ../product
	public-supply => ../public-supply
	purchase-account => ../purchase-account
	region => ../region
	sales => ../sales
	shipping => ../shipping
	shop => ../shop
	shopping-cart => ../shopping-cart
	supplier => ../supplier
	trade => ../trade
	user => ../user
	wechatpay => ../wechat-pay
	wechatpay-go-main => ../wechatpay-go-main
	yz-go => ../yz-go
)
