package model

import (
	"database/sql/driver"
	"encoding/json"
	productModel "product/model"
	"yz-go/source"
)

const BYNID = 99

type Product struct {
	productModel.Product
	AgreementPrice uint           `json:"agreement_price" gorm:"-"` //协议价
	SalePrice      uint           `json:"sale_price" gorm:"-"`      //销售价
	Brand          Brand          `json:"brand"  form:"brand"`
	Category1      Category       `json:"category_1" form:"category_1"`
	Category2      Category       `json:"category_2" form:"category_2"`
	Category3      Category       `json:"category_3" form:"category_3"`
	BynGoodsInfo   BynSupplyGoods `json:"byn_goods_info" gorm:"foreignKey:SourceGoodsID;references:ID"`
}

type Brand struct {
	ID   uint   `json:"id"`
	Name string `json:"name" form:"name"`
}
type Category struct {
	ID    uint   `json:"id"`
	Name  string `json:"name" form:"name"`
	Image string `json:"image" form:"image"`
}

type WithBynSupplyGoods struct {
	source.Model
	ProductSourceGoodsID uint `json:"product_source_goods_id" gorm:"column:product_source_goods_id;comment:商城商品来源;"`
	Validity             int  `json:"validity" form:"validity" gorm:"column:validity;comment:有效期：小时;"`
	// ValidityName     string            `json:"validity_name" gorm:"-"`
}

func (WithBynSupplyGoods) TableName() string {
	return "byn_supply_goods"
}

type BynSupplyGoods struct {
	source.Model
	Name          string `json:"name" gorm:"name;comment:卡券商品名称;"`
	Description   string `json:"description" form:"description" gorm:"column:description;comment:使用说明;type:text;"`
	Logo          string `json:"logo" form:"logo" gorm:"column:logo;comment:图片url;"`
	SalePrice     uint   `json:"sale_price" form:"sale_price" gorm:"column:sale_price;comment:售价：分;"`
	Stock         uint   `json:"stock" form:"stock" gorm:"column:stock;comment:库存;"`
	BrandId       uint   `json:"brand_id" form:"brand_id" gorm:"column:brand_id;comment:品牌id;index;"`
	CategoryId    uint   `json:"category_id" form:"category_id" gorm:"column:category_id;comment:分类id;index;"`
	Status        int    `json:"status" form:"status" gorm:"column:status;comment:状态-1下架1上架;"`
	Validity      int    `json:"validity" form:"validity" gorm:"column:validity;comment:有效期：小时;"`
	CouponType    int    `json:"coupon_type" form:"coupon_type" gorm:"column:coupon_type;comment:1直冲2卡券;"`
	SubCouponType int    `json:"sub_coupon_type" form:"sub_coupon_type" gorm:"column:sub_coupon_type;comment:直冲时：1手机号2qq号3中石化4中石油;"`
	Specs         Specs  `json:"specs" gorm:"column:specs;type:text"`
	CostPrice     uint   `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价：分;"`
	DiscountPrice uint   `json:"discount_price" form:"discount_price" gorm:"column:discount_price;comment:建议零售价：分;"`
	MD5           string `json:"md_5" form:"md_5" gorm:"column:md_5;type:varchar(255);"`
}

func (BynSupplyGoods) TableName() string {
	return "byn_supply_goods"
}

func (value Specs) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Specs) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type Specs []Spec

type Spec struct {
	Id int `json:"id"`
	// 名称
	Name string `json:"name"`
	// -1下架1上架
	Status int `json:"status"`
}

type BynGoodsResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data    []BynSupplyGoods `json:"data"`
		Ext     interface{}      `json:"ext"`
		HasNext bool             `json:"has_next"`
		Page    int              `json:"page"`
	} `json:"data"`
}
