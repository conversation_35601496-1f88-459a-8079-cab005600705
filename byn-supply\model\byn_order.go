package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type BynSupplyOrderRequest struct {
	source.Model
	OrderID uint `json:"order_id" form:"order_id" gorm:"order_id;comment:订单id;"`
	// 商品ID
	GoodsID uint `json:"goods_id" form:"goods_id" gorm:"goods_id;comment:商品id;"`
	// 规格ID
	SpecID int `json:"spec_id" form:"spec_id" gorm:"spec_id;comment:规格ID;"`
	// 优惠券ID，不传走默认优惠券
	CouponID int `json:"coupon_id" form:"coupon_id" gorm:"coupon_id;comment:优惠券ID，不传走默认优惠券;"`
	// count 数量，至少为1
	Count int `json:"count" form:"count" gorm:"count;comment:数量，至少为1;"`
	// out_trade_no 外部订单号
	OutTradeNo string `json:"out_trade_no" form:"out_trade_no" gorm:"out_trade_no;comment:外部订单号;"`
	// recharge_number 账号，直冲
	RechargeNumber string `json:"recharge_number" form:"recharge_number" gorm:"recharge_number;comment:账号，直冲;"`
	// card 卡号，油卡使用
	Card string `json:"card" form:"card" gorm:"card;comment:卡号，油卡使用;"`
	// username 用户名，油卡使用
	Username string `json:"username" form:"username" gorm:"username;comment:用户名，油卡使用;"`
	// coupon_type 卡券类型 1直冲2卡券
	CouponType int `json:"coupon_type" form:"coupon_type" gorm:"coupon_type;comment:卡券类型 1直冲2卡券;"`
}

type Order struct {
	source.Model
	OrderSN      uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"` // 编号
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`
}

type BynSupplyOrderMigration struct {
	source.Model
	GoodsId        int    `json:"goods_id" gorm:"column:goods_id;comment:商品id;"`
	GoodsName      string `json:"goods_name" gorm:"column:goods_name;comment:商品名称;"`
	GoodsCover     string `json:"goods_cover" gorm:"column:goods_cover;comment:商品的logo;"`
	SalePrice      uint   `json:"sale_price" gorm:"column:sale_price;comment:售价，分;"`
	Count          int    `json:"count" gorm:"column:count;comment:数量;"`
	OrderPrice     uint   `json:"order_price" gorm:"column:order_price;comment:订单金额,分;"`
	OrderNo        string `json:"order_no" gorm:"column:order_no;comment:订单号;"`
	OutTradeNo     string `json:"out_trade_no" gorm:"column:out_trade_no;comment:外部订单号;"`
	Instruction    string `json:"instruction" gorm:"column:instruction;comment:产品说明;type:text"`
	UseNotice      string `json:"use_notice" gorm:"column:use_notice;comment:使用说明;type:text"`
	Params         string `json:"params" gorm:"column:params;comment:自定义参数，透传;"`
	StraightParams Params `json:"straight_params" gorm:"column:straight_params;comment:直充参数;"`
	Status         int    `json:"status" gorm:"column:status;comment:状态：0默认状态-1失败2成功3处理中，如果一直是状态3请联系客服;"`
	CouponId       int    `json:"coupon_id" gorm:"coupon_id;comment:优惠券ID;"`
}

func (BynSupplyOrderMigration) TableName() string {
	return "byn_supply_orders"
}

type BynSupplyOrder struct {
	source.Model
	GoodsId        int                    `json:"goods_id" gorm:"column:goods_id;comment:商品id;"`
	GoodsName      string                 `json:"goods_name" gorm:"column:goods_name;comment:商品名称;"`
	GoodsCover     string                 `json:"goods_cover" gorm:"column:goods_cover;comment:商品的logo;"`
	SalePrice      uint                   `json:"sale_price" gorm:"column:sale_price;comment:售价，分;"`
	Count          int                    `json:"count" gorm:"column:count;comment:数量;"`
	OrderPrice     uint                   `json:"order_price" gorm:"column:order_price;comment:订单金额,分;"`
	OrderNo        string                 `json:"order_no" gorm:"column:order_no;comment:订单号;"`
	OutTradeNo     string                 `json:"out_trade_no" gorm:"column:out_trade_no;comment:外部订单号;"`
	Instruction    string                 `json:"instruction" gorm:"column:instruction;comment:产品说明;type:text"`
	UseNotice      string                 `json:"use_notice" gorm:"column:use_notice;comment:使用说明;type:text"`
	Params         string                 `json:"params" gorm:"column:params;comment:自定义参数，透传;"`
	StraightParams Params                 `json:"straight_params" gorm:"column:straight_params;comment:直充参数;"`
	Status         int                    `json:"status" gorm:"column:status;comment:状态：0默认状态-1失败2成功3处理中，如果一直是状态3请联系客服;"`
	CouponId       int                    `json:"coupon_id" gorm:"coupon_id;comment:优惠券ID;"`
	CouponInfo     []BynSupplyOrderCoupon `json:"coupon_info"`
}

type BynSupplyOrderCoupon struct {
	source.Model
	BynSupplyOrderID uint   `json:"byn_supply_order_id" gorm:"column:byn_supply_order_id:comment:BynSupplyOrderID;"`
	GoodsType        string `json:"goods_type" gorm:"column:goods_type;comment:卡券类型:LINK 链接，PICTURE 图片，NUMBER_PASSWORD 卡号和密码，PASSWORD 密码;"`
	GoodsLink        string `json:"goods_link" gorm:"column:goods_link;comment:链接值，goods_type为LINK或PICTURE时有值;"`
	GoodsNumber      string `json:"goods_number" gorm:"column:goods_number;comment:卡号，goods_type为NUMBER_PASSWORD时有值;"`
	GoodsPassword    string `json:"goods_password" gorm:"column:goods_password;comment:卡密，goods_type为NUMBER_PASSWORD或PASSWORD时有值;"`
	EffectiveTime    string `json:"effective_time" gorm:"column:effective_time;comment:过期时间;"`
}

type Params struct {
	// 充值账号
	RechargeNumber string `json:"recharge_number"`
	// 用户名：一般商品为油卡时需要
	Username string `json:"username"`
	// 卡号：一般商品为油卡时需要
	Card string `json:"card"`
}

func (value Params) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Params) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
