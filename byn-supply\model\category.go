package model

import "yz-go/source"

// 三级分类
type BynSupplyBrand struct {
	source.Model
	ShopBrandId uint   `json:"shop_brand_id" gorm:"column:shop_brand_id;comment:商城品牌id;index;"`
	Name        string `json:"name" gorm:"column:name;comment:name;"`
	Logo        string `json:"logo" gorm:"column:logo;comment:logo;"`
	Description string `json:"description" gorm:"column:description;comment:描述;"`
}

type BynBrandResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data []struct {
			Id          int    `json:"id"`
			Name        string `json:"name"`
			Logo        string `json:"logo"`
			Description string `json:"description"`
		} `json:"data"`
		Ext     interface{} `json:"ext"`
		HasNext bool        `json:"has_next"`
		Page    int         `json:"page"`
	} `json:"data"`
}

// 二级分类
type BynSupplyCategory struct {
	source.Model
	ShopCategoryId uint   `json:"shop_category_id" gorm:"column:shop_category_id;comment:商城分类id;index;"`
	Name           string `json:"name" gorm:"column:name;comment:name;"`
}

type BynCategoryResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data []struct {
			Id   int    `json:"id"`
			Name string `json:"name"`
		} `json:"data"`
		Ext     interface{} `json:"ext"`
		HasNext bool        `json:"has_next"`
		Page    int         `json:"page"`
	} `json:"data"`
}
