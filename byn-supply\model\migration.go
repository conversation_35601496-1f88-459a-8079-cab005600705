package model

import (
	_ "embed"
	"encoding/json"
	"errors"
	"gin-vue-admin/admin/model"
	"gorm.io/gorm"
	publicModel "public-supply/model"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		BynSupplyCategory{},
		BynSupplyBrand{},
		BynSupplyGoods{},
		BynSupplyOrderMigration{},
		BynSupplyOrderCoupon{},
		BynSupplyOrderRequest{},
	)

	var menus []model.SysMenu
	err = json.Unmarshal([]byte(menu), &menus)
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)

	// 供应链表是否存在
	res := source.DB().Migrator().HasTable(&publicModel.GatherSupply{})
	if res == true {
		var supply publicModel.GatherSupply
		err = source.DB().Unscoped().Where("category_id = ?", BYNID).First(&supply).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
			return
		}
		if supply.ID == 0 {
			supply.Name = "必应鸟供应链"
			supply.CategoryID = 99
			err = source.DB().Create(&supply).Error
			if err != nil {
				err = nil
				return
			}
			err = source.DB().Delete(&supply).Error
			if err != nil {
				err = nil
				return
			}
		}
	}

	return
}
