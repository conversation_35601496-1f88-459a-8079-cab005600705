package request

import yzRequest "yz-go/request"

type OrderAdminSearch struct {
	// 订单状态
	Status *int `json:"status" form:"status"`
	// 下单时间
	StartAT string `json:"start_at" form:"start_at"`
	EndAT   string `json:"end_at" form:"end_at"`
	// 订单编号
	OrderSN string `json:"order_sn" form:"order_sn"`
	// 第三号订单编号
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn"`
	// 支付单号
	PaySN string `json:"pay_sn" form:"pay_sn"`
	// 必应鸟订单编号
	BynOrderSN string `json:"byn_order_sn" form:"byn_order_sn"`
	// 支付方式
	PayTypeID int `json:"pay_type_id" form:"pay_type_id"`
	// 会员id
	UserID uint `json:"user_id" form:"user_id"`
	// 会员昵称 会员姓名
	NickName string `json:"nick_name" form:"nick_name"`
	// 采购端id
	ApplicationID uint `json:"application_id" form:"application_id"`
	// 商品名称
	ProductTitle string `json:"product_title" form:"product_title"`
	yzRequest.PageInfo
}

type OrderInfoRequest struct {
	OrderSn string `json:"order_sn"`
}

type ConfirmRequest struct {
	OrderSn string `json:"order_sn"`
	BynPreOrder
}

// BynPreOrder byn下单参数
type BynPreOrder struct {
	// 商品ID
	GoodsID uint `json:"goods_id" form:"goods_id"`
	// 规格ID
	SpecID int `json:"spec_id" form:"spec_id"`
	// 优惠券ID，不传走默认优惠券
	CouponID int `json:"coupon_id" form:"coupon_id"`
	// count 数量，至少为1
	Count int `json:"count" form:"count"`
	// out_trade_no 外部订单号
	OutTradeNo string `json:"out_trade_no" form:"out_trade_no"`
	// recharge_number 账号，直冲
	RechargeNumber string `json:"recharge_number" form:"recharge_number"`
	// card 卡号，油卡使用
	Card string `json:"card" form:"card"`
	// username 用户名，油卡使用
	Username string `json:"username" form:"username"`
	// coupon_type 卡券类型 1直冲2卡券
	CouponType int `json:"coupon_type" form:"coupon_type"`
}
