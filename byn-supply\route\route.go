package route

import (
	av1 "byn-supply/api/app/v1"
	v1 "byn-supply/api/v1"
	"github.com/gin-gonic/gin"
)

func InitBynSupplyRouter(Router *gin.RouterGroup) {
	byn := Router.Group("bynSupply")
	{
		// 获取供应链id
		byn.GET("getBynSupplyId", v1.GetBynSupplyId)
		// 获取品牌和分类
		byn.GET("getCategories", v1.GetCategories)
		// 导入商品
		byn.POST("importGoods", v1.ImportAllGoods)
		// 订单列表 0:待支付 1:处理中 3:已完成 -1:已关闭 6:已退款
		byn.GET("getOrderList", v1.GetOrderList)
		// 采购端列表  application/getApplicationOption
		// 支付类型  finance/getPayType

		// 获取设置 gatherSupply/getSetting
		// 更新设置 gatherSupply/setSetting
		// 获取商品列表 gatherSupply/getGoods
	}
}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("bynSupply")
	{
		ProductRouter.POST("storage/detailList", av1.GetProductDetailList)
		ProductRouter.POST("order", av1.OrderConfirm)
		ProductRouter.POST("orderInfo", av1.GetOrderInfo)
		//ProductRouter.POST("storage/addStorage", av1.AddProductStorage)
	}
}
