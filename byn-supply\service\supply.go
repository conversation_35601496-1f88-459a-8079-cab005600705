package service

import (
	"byn-supply/component/goods"
	"byn-supply/model"
	bynRequest "byn-supply/request"
	"errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"math"
	orderModel "order/model"
	"payment/service"
	publicModel "public-supply/model"
	"public-supply/request"
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type Application struct {
	source.Model

	AppLevelID       uint             `json:"appLevelId" form:"appLevelId" gorm:"column:app_level_id;comment:;type:int;size:10;"`
	MemberId         int              `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}

func (Application) TableName() string {
	return "application"
}

// 如果含有time.Time 请自行import time包
type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:;type:int;size:10;"`
	NumMax      int `json:"numMax" form:"numMax" gorm:"column:num_max;comment:;type:int;size:10;"`
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}

func CreateOrderRequest(orderID uint, confirmRequest bynRequest.ConfirmRequest) (err error) {
	var bynSupplyOrderRequest model.BynSupplyOrderRequest
	bynSupplyOrderRequest.OrderID = orderID
	bynSupplyOrderRequest.GoodsID = confirmRequest.GoodsID
	bynSupplyOrderRequest.SpecID = confirmRequest.SpecID
	bynSupplyOrderRequest.CouponID = confirmRequest.CouponID
	bynSupplyOrderRequest.Count = confirmRequest.Count
	bynSupplyOrderRequest.OutTradeNo = confirmRequest.OrderSn
	bynSupplyOrderRequest.RechargeNumber = confirmRequest.RechargeNumber
	bynSupplyOrderRequest.RechargeNumber = confirmRequest.RechargeNumber
	bynSupplyOrderRequest.Card = confirmRequest.Card
	bynSupplyOrderRequest.Username = confirmRequest.Username
	bynSupplyOrderRequest.CouponType = confirmRequest.CouponType
	err = source.DB().Create(&bynSupplyOrderRequest).Error
	return
}

func GetProductDetailList(ids []int, appID uint) (err error, list []model.Product) {
	var application Application
	err = source.DB().Preload("ApplicationLevel").Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}
	db := source.DB().Preload(clause.Associations)
	db = db.Where("id in ?", ids)
	err = db.Find(&list).Error

	var productList []model.Product
	for _, product := range list {
		product.AgreementPrice = product.Price
		product.SalePrice = uint(math.Floor(float64(product.GuidePrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000))) // 建议销售价
		productList = append(productList, product)
	}

	return err, productList
}

func GetBynSupply() (err error, supply publicModel.GatherSupply) {
	err = source.DB().Unscoped().Where("category_id = ?", model.BYNID).First(&supply).Error
	return
}

func GetCategories() (err error, categories []model.BynSupplyCategory) {
	err = source.DB().Where("shop_category_id != ?", 0).Order("id DESC").Find(&categories).Error
	return
}

func GetBrands() (err error, brands []model.BynSupplyBrand) {
	err = source.DB().Where("shop_brand_id != ?", 0).Order("id DESC").Find(&brands).Error
	return
}

func ImportAllGoods() (err error) {
	var (
		supply   publicModel.GatherSupply
		bynGoods goods.Byn
		info     request.GetGoodsSearch
	)
	err, supply = GetBynSupply()
	if err != nil {
		return
	}
	err = bynGoods.InitSetting(supply.ID)
	if err != nil {
		return
	}
	err, _ = bynGoods.ImportGoodsRun(info)
	if err != nil {
		return
	}
	return
}

func GetOrderInfo(orderRequest bynRequest.OrderInfoRequest) (err error, bynOrder model.BynSupplyOrder) {
	var shopOrder model.Order
	err = source.DB().Where("third_order_sn = ?", orderRequest.OrderSn).First(&shopOrder).Error
	if err != nil {
		return
	}
	err = source.DB().Preload("CouponInfo").Where("out_trade_no = ?", shopOrder.OrderSN).First(&bynOrder).Error
	return
}

type Order struct {
	orderModel.Order
	User         User                 `json:"user"`
	PayInfo      PayInfo              `json:"pay_info" gorm:"foreignKey:PayInfoID"`
	Button       interface{}          `json:"button" gorm:"-"`
	ShopName     string               `json:"shop_name"`
	PayType      string               `json:"pay_type"`
	BynOrderInfo model.BynSupplyOrder `json:"byn_order_info" gorm:"foreignKey:OrderSN;references:OutTradeNo"`
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {
	b.Button = GetButton(b.Status)
	b.ShopName = "卡券商品"
	_, payType := service.GetPayType()
	for _, v := range payType {
		if b.PayTypeID == v.Code {
			b.PayType = v.Name
		}
	}
	if b.PayTypeID == -1 {
		b.PayType = "后台支付"
	}
	if b.PayType == "" {
		b.PayType = "未知"
	}
	return
}

func GetButton(status orderModel.OrderStatus) interface{} {
	buttonMap := map[orderModel.OrderStatus][]Button{
		orderModel.WaitSend: {{Title: "确认发货", Url: "order/send"}, {Title: "强制退款", Url: "order/orderRefund"}},
		//orderModel.WaitReceive: {{Title: "确认收货", Url: "order/receive"}, {Title: "修改物流", Url: "order/updateOrderExpress"}, {Title: "全额退款", Url: "order/orderRefund"}},
		orderModel.Completed: {{Title: "强制退款", Url: "order/orderRefund"}},
		//orderModel.Closed:    {{Title: "已关闭", Url: ""}},
	}
	return buttonMap[status]
}

type Button struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}

type User struct {
	ID       uint   `json:"id"`
	NickName string `json:"nickname"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
}

type PayInfo struct {
	ID     uint   `json:"id"`
	PaySn  string `json:"pay_sn"`
	Status string `json:"status"`
}

func GetOrderList(info bynRequest.OrderAdminSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	order := Order{}
	db := source.DB().Model(&order).Preload(clause.Associations)

	var supply publicModel.GatherSupply
	err, supply = GetBynSupply()
	if err != nil {
		return
	}
	db.Where("`gather_supply_id` = ?", supply.ID)

	var orders []Order
	// 订单编号
	if info.OrderSN != "" {
		db.Where("`order_sn` = ?", info.OrderSN)
	}
	// 支付单号
	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`pay_info_id` in ?", payInfoIds)
	}
	// 会员id
	if info.UserID > 0 {
		db.Where("`user_id` = ?", info.UserID)
	}
	// 会员昵称
	if info.NickName != "" {
		var userIds []uint
		err = source.DB().Model(User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`user_id` in ?", userIds)
	}
	// 第三方订单单号
	if info.ThirdOrderSN != "" {
		db.Where("`third_order_sn` = ?", info.ThirdOrderSN)
	}
	// 必应鸟订单编号
	if info.ApplicationID > 0 {
		db.Where("`application_id` = ?", info.ApplicationID)
	}
	// 商品名称
	if info.ProductTitle != "" {
		var orderIds []uint
		err = source.DB().Model(orderModel.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`id` in ?", orderIds)
	}
	// 支付方式
	if info.PayTypeID != 0 {
		db.Where("`pay_type_id` = ?", info.PayTypeID)
	}
	// 订单状态
	if info.Status != nil {
		db.Where("`status` = ?", info.Status)
	}
	// 采购端
	if info.ApplicationID > 0 {
		db.Where("`application_id` = ?", info.ApplicationID)
	}
	if info.StartAT != "" {
		db.Where("`created_at` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`created_at` <= ?", info.EndAT)
	}

	err = db.Count(&total).Error
	err = db.Order("created_at DESC").Limit(limit).Offset(offset).Find(&orders).Error
	return err, orders, total
}
