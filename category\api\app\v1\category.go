package v1

import (
	_ "category/model"
	"category/request"

	"category/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// @Tags Category
// @Summary 通过parentid获取Category列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "通过parentid获取Category列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /category/getCategoryListWithParentId [get]
func GetCategoryListWithParentId(c *gin.Context) {
	var search request.CategoryChildrenSearch
	_ = c.ShouldBindQuery(&search)
	if err, list, total := service.GetCategoryInfoListWithParentId(search); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
		}, "获取成功", c)
	}
}

// @Tags Category
// @Summary 通过parentid获取Category列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "通过parentid获取Category列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /category/getCategoryListWithParentId [get]
func GetCategoryAllParentId(c *gin.Context) {
	var search request.CategoryChildrenSearch
	_ = c.ShouldBindQuery(&search)
	if err, list := service.GetCategoryAllWithParentId(search); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

func GetCategoryList(c *gin.Context) {
	var pageInfo request.CategorySearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetCategoryList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
