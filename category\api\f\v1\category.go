package v1

import (
	"category/model"
	"category/request"
	"category/response"
	"category/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// @Tags 分类
// @Summary 获取Category树
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"code":0,"data":{},"msg":"获取成功"}"
// @Success 200 {object} []response.Category
// @Router /api/category/tree [post]
func GetCategoryTree(c *gin.Context) {
	isDisplay := 1

	search := request.CategorySearch{
		Category: model.Category{
			CategoryModel: model.CategoryModel{
				IsDisplay: &isDisplay,
			},
		},
	}
	if err, list := service.GetCategoryInfoTree(search); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"categories": list}, "获取成功", c)
		return
	}
}

// @Tags 分类
// @Summary 分类首页
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "分类首页"
// @Success 200 {object} response.CategoryIndex
// @Router /api/category/index [post]
func Index(c *gin.Context) {
	var err error
	var reqId yzRequest.GetById
	err = c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	categoryIndex := response.CategoryIndex{}
	if reqId.Id != 0 {
		err, categoryIndex.Category = service.GetSimpleCategory(reqId.Id)
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}

		err, categoryIndex.CateGoryParents = service.CategoryParents(categoryIndex.Category)
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}

	search := request.CategorySearch{}
	search.ParentID = uint(reqId.Id)

	err, categoryIndex.Children = service.GetCategorySimpleList(search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(categoryIndex, c)
	return
}
