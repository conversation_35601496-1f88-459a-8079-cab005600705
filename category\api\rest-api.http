### 分类列表
GET {{api}}/category/getCategoryList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiM2VkNjljNmEtNmM2Yi00YWZiLTk5MmYtYjlkOTkyYmVjNmRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MTkzMDQ5NDEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcxODY5OTE0MX0.x8gNBbxl9fjYWC1sryZ1NDGisVvFDqvWshkdttHxlxY
x-user-id: 1



### 分类移动排序
POST {{api}}/category/moveCategory
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiM2VkNjljNmEtNmM2Yi00YWZiLTk5MmYtYjlkOTkyYmVjNmRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MTkzMDQ5NDEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcxODY5OTE0MX0.x8gNBbxl9fjYWC1sryZ1NDGisVvFDqvWshkdttHxlxY
x-user-id: 1

{
  "id": 428,
  "parent_id": 423,
  "move_operate": "down"
}


###
GET {{api}}/api/category/index?id=0
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1


###
GET {{api}}/api/category/tree
Content-Type: application/json


###
GET {{api}}/category/getCategoryListWithParentId?parent_id=2
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZWY0OWQyM2UtOWQwYi00ZWEzLThmZDEtNjc3YzI3MTNjYjJhIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhjExMTEiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2MTg5MDU3MDMsImlzcyI6InFtUGx1cyIsIm5iZiI6MTYxODI5OTkwM30.rpw415Qzx4khIFZK1IvLWW9CV8ar9p_msrnsx5BQV88
x-user-id: 1

{}

###
GET {{api}}/category/getCategorysWithLevelAndName?level=3&parent_id=0
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZWY0OWQyM2UtOWQwYi00ZWEzLThmZDEtNjc3YzI3MTNjYjJhIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhjExMTEiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2MTg5MDU3MDMsImlzcyI6InFtUGx1cyIsIm5iZiI6MTYxODI5OTkwM30.rpw415Qzx4khIFZK1IvLWW9CV8ar9p_msrnsx5BQV88
x-user-id: 1

{}
###
POST {{api}}/category/putRecommendCategory
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMjUxODAzMSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIxOTEyMjMxfQ.xzXKzYIdg3brv8AVcbwm2qJ7KMv_kbBJvZBE2s1nBRo
x-user-id: 1

{
  "data": [
    {
      "cate_id": 1,
      "icon": "asdasd"
    },{
      "cate_id": 2,
      "icon": "asdasd"
    }
  ]
}

###
GET {{api}}/category/getRecommendCategory
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYjdlZjhmNWQtNzMwZS00ZWUzLTgxZGUtZjMyZjUxYTFiNDY5IiwiSUQiOjEsIkFwcElEIjoxLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3Mjc0MDc5NjgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcyNjgwMjE2OH0.P7IhZxbQFwIWU2DQ4IWhq42roVIXGWDNvmhpoqSriKo
x-user-id: 1


### APP 分类分页数据（支持部分字段搜索）
GET {{api}}/app/category/lists
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYjdlZjhmNWQtNzMwZS00ZWUzLTgxZGUtZjMyZjUxYTFiNDY5IiwiSUQiOjEsIkFwcElEIjoxLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3Mjc0MDc5NjgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcyNjgwMjE2OH0.P7IhZxbQFwIWU2DQ4IWhq42roVIXGWDNvmhpoqSriKo
x-user-id: 1

{
  "page": 1,
  "pageSize": 10,
  "name": "",
  "level": 0,
  "source": 0,
  "is_plugin": 0,
  "parent_id": 0,
  "is_display": 0
}

### APP 树形结构全部分类（不支持搜索）
GET {{api}}/app/category/tree
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYjdlZjhmNWQtNzMwZS00ZWUzLTgxZGUtZjMyZjUxYTFiNDY5IiwiSUQiOjEsIkFwcElEIjoxLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3Mjc0MDc5NjgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcyNjgwMjE2OH0.P7IhZxbQFwIWU2DQ4IWhq42roVIXGWDNvmhpoqSriKo
x-user-id: 1


### APP 获取全部分类（仅支持“parent_id”、“name”搜索）
GET {{api}}/app/category/getCategory
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYjdlZjhmNWQtNzMwZS00ZWUzLTgxZGUtZjMyZjUxYTFiNDY5IiwiSUQiOjEsIkFwcElEIjoxLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3Mjc0MDc5NjgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcyNjgwMjE2OH0.P7IhZxbQFwIWU2DQ4IWhq42roVIXGWDNvmhpoqSriKo
x-user-id: 1

{
  "parent_id": 0,
  "name": ""
}

### APP 根据"id"获取分类
GET {{api}}/app/category/findCategory
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYjdlZjhmNWQtNzMwZS00ZWUzLTgxZGUtZjMyZjUxYTFiNDY5IiwiSUQiOjEsIkFwcElEIjoxLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3Mjc0MDc5NjgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcyNjgwMjE2OH0.P7IhZxbQFwIWU2DQ4IWhq42roVIXGWDNvmhpoqSriKo
x-user-id: 1

{
  "id": 1036
}

### APP 根据"name"获取分类
GET {{api}}/app/category/findCategoryWithName
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYjdlZjhmNWQtNzMwZS00ZWUzLTgxZGUtZjMyZjUxYTFiNDY5IiwiSUQiOjEsIkFwcElEIjoxLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3Mjc0MDc5NjgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcyNjgwMjE2OH0.P7IhZxbQFwIWU2DQ4IWhq42roVIXGWDNvmhpoqSriKo
x-user-id: 1

{
  "name": ""
}
