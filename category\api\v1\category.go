package v1

import (
	"category/model"
	"category/request"
	"category/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func MoveCategory(c *gin.Context) {
	var params request.MoveParams

	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := utils.Verify(params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.MoveCategory(params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
}

// @Tags Category
// @Summary 创建Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "创建Category"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"获取成功"}"
// @Router /category/createCategory [post]
func CreateCategory(c *gin.Context) {
	var category model.Category
	err := c.ShouldBindJSON(&category)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(category); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateCategory(category); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags Category
// @Summary 删除Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "删除Category"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /category/deleteCategory [delete]
func DeleteCategory(c *gin.Context) {
	var category model.Category
	err := c.ShouldBindJSON(&category)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteCategory(category); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags Category
// @Summary 批量删除Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除Category"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /category/deleteCategoryByIds [delete]
func DeleteCategoryByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteCategoryByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags Category
// @Summary 更新Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "更新Category"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /category/updateCategory [put]
func UpdateCategory(c *gin.Context) {
	var category model.Category
	err := c.ShouldBindJSON(&category)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateCategory(category); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Category
// @Summary 用id查询Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "用id查询Category"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /category/findCategory [get]
func FindCategory(c *gin.Context) {
	var category model.Category
	_ = c.ShouldBindQuery(&category)
	if err, recategory := service.GetCategory(category.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"recategory": recategory}, c)
	}
}

// @Tags Category
// @Summary 用id查询Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "用id查询Category"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /category/findCategory [get]
func FindCategoryWithName(c *gin.Context) {
	var category model.Category
	_ = c.ShouldBindQuery(&category)

	if category.Name == "" {
		yzResponse.FailWithMessage("请提交分类名称", c)
		return
	}

	if err, recategory := service.GetCategoryByName(category.Name); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"recategory": recategory}, c)
	}
}

// @Tags Category
// @Summary 分页获取Category列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CategorySearch true "分页获取Category列表"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"获取成功"}"
// @Router /category/getCategoryList [get]
func GetCategoryList(c *gin.Context) {
	var pageInfo request.CategorySearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCategoryList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags Category
// @Summary 通过parentid获取Category列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "通过parentid获取Category列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /category/getCategoryListWithParentId [get]
func GetCategoryListWithParentId(c *gin.Context) {
	var search request.CategoryChildrenSearch
	_ = c.ShouldBindQuery(&search)
	if err, list, total := service.GetCategoryInfoListWithParentId(search); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
		}, "获取成功", c)
	}
}

// @Tags Category
// @Summary 获取Category列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "获取Category列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /category/getCategoriesWithLevelAndName [get]
func GetCategoriesWithLevelAndName(c *gin.Context) {
	var search request.CategorySearch
	if err := c.ShouldBindQuery(&search); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetCategoriesWithLevelAndName(search); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// @Tags Category
// @Summary 获取Category列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "获取Category列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /category/getCategoriesWithLevelAndName [get]
func GetRecommendCategory(c *gin.Context) {

	if err, list := service.GetRecommendCategory(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// @Tags Category
// @Summary 更新Category
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Category true "更新Category"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /category/updateCategory [put]
func UpdateRecommendCategory(c *gin.Context) {
	var category request.RecommendCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.PutRecommendCategory(category.Data); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Product
// @Summary 批量删除Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /category/DisplayCategoryByIds [delete]
func DisplayCategoryByIds(c *gin.Context) {
	var IDS yzRequest.IdsReqWithValue
	if err := c.ShouldBindJSON(&IDS); err != nil {
		log.Log().Error("批量修改失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改失败", c)
		return
	}

	if err := utils.Verify(IDS); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	failMessage := "修改失败"
	successMessage := "修改成功"
	if len(IDS.Ids) > 1 {
		failMessage = "批量修改失败"
		successMessage = "批量修改成功"
	}

	if err := service.DisplayCategoryByIds(IDS); err != nil {
		log.Log().Error("批量修改失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(failMessage, c)
		return
	} else {
		yzResponse.OkWithMessage(successMessage, c)
	}
}
