// 自动生成模板Category
package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type CategoryModel struct {
	source.Model
	Name         string `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"`         //分类名称
	Sort         int    `json:"sort" form:"sort" gorm:"column:sort;default:0;comment:排序;"`                                                   //排序
	Level        int    `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`                 //等级
	Desc         string `json:"desc" form:"desc" gorm:"column:desc;comment:简介;type:varchar(255);size:255;"`                                  //描述
	IsDisplay    *int   `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;default:0;type:tinyint;size:1;index;"` //是否显示
	Image        string `json:"image" form:"image" gorm:"column:image;comment:图片;"`                                                          //分类图片
	Icon         string `json:"icon" form:"icon" gorm:"column:icon;comment:icon;"`                                                           //分类图片
	HasChildrens bool   `json:"hasChildrens"`
	Url          string `json:"url"`
	Source       int    `json:"source" form:"source" gorm:"column:source"`
	ParentID     uint   `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"` //父级id
	IsNew        int    `json:"is_new" gorm:"column:is_new;default:0;comment:;"`
	IsPlugin     int    `json:"is_plugin" form:"is_plugin" gorm:"column:is_plugin;type:smallint;default:0;comment:0平台显示，1插件显示;"`
	IsAuto       int    `json:"is_auto" form:"is_auto" gorm:"column:is_auto;type:smallint;default:0;comment:1手动创建，0自动创建;"`
}

func (CategoryModel) TableName() string {
	return "categories"
}

type Category struct {
	CategoryModel
	Childrens []Category `json:"childrens" gorm:"foreignKey:ParentID;references:ID"` //子分类
}

func (b *Category) AfterFind(tx *gorm.DB) (err error) {
	if b.Level < 3 {
		b.HasChildrens = true
	} else {
		b.HasChildrens = false
	}
	return
}

type RecommendCateModel struct {
	source.Model
	CateID uint   `json:"cate_id" validate:"required" form:"cate_id" gorm:"column:cate_id;comment:分类id;type:int(11);size:100;"`
	Icon   string `json:"icon" validate:"required" form:"icon" gorm:"column:icon;comment:;"`
}

func (RecommendCateModel) TableName() string {
	return "recommend_cates"
}

type RecommendCate struct {
	RecommendCateModel
	Category Category `json:"category" gorm:"foreignKey:CateID"`
}
