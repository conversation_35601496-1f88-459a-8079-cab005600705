package model

import (
	_ "embed"
	"encoding/json"
	"io/ioutil"
	"time"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		CategoryModel{},
		RecommendCateModel{},
		Brand{},
	)
	if err != nil {
		return
	}
	initCategory()
	//handleRepeatCategory()
	return
}

func initCategory() {
	db := source.DB().Model(&Category{})
	var categorys []Category

	err := db.Find(&categorys).Error
	if err != nil {
		return
	}
	if len(categorys) <= 0 {
		Json, err := ioutil.ReadFile("./category.json")
		var category []Category
		json.Unmarshal([]byte(Json), &category)
		err = source.DB().Create(&category).Error
		if err != nil {
			return
		}
	} else {
		return
	}

}

func handleRepeatCategory() {
	var allCategory []Category
	source.DB().Find(&allCategory)
	var oneCategory []Category
	var twoCategory = make(map[uint][]Category)
	var threeCategory = make(map[uint][]Category)
	for _, cate := range allCategory {
		if cate.Level == 1 {
			oneCategory = append(oneCategory, cate)
		}
		if cate.Level == 2 {
			twoCategory[cate.ParentID] = append(twoCategory[cate.ParentID], cate)
		}
		if cate.Level == 3 {
			threeCategory[cate.ParentID] = append(threeCategory[cate.ParentID], cate)
		}
	}
	for _, oneCate := range oneCategory {
		children := twoCategory[oneCate.ID]

		for _, child := range children {
			var repeatChild []Category
			source.DB().Where("name = ?", child.Name).Where("parent_id = ?", child.ParentID).Where("is_new = 0").Find(&repeatChild)
			if len(repeatChild) > 1 {
				//有重复分类
				var repeatIds []uint //获取id数组
				for _, rc := range repeatChild {
					repeatIds = append(repeatIds, rc.ID)
				}
				//生成一个新的分类
				var newChild Category
				newChild = child
				newChild.ID = 0
				newChild.IsNew = 1
				newChild.CreatedAt = nil
				newChild.UpdatedAt = nil
				source.DB().Create(&newChild)
				//修改属于该分类组的所有商品和下级分类
				var thirdChild []Category
				source.DB().Where("parent_id in ?", repeatIds).Find(&thirdChild)
				var updateThirdCate []map[string]interface{}
				for tck, tc := range thirdChild {
					updateThirdCateRow := make(map[string]interface{})
					updateThirdCateRow["id"] = tc.ID
					updateThirdCateRow["parent_id"] = newChild.ID
					updateThirdCateRow["updated_at"] = newChild.ID
					updateThirdCate = append(updateThirdCate, updateThirdCateRow)
					thirdChild[tck].ParentID = newChild.ID
				}
				source.BatchUpdate(updateThirdCate, "categories", "")

				var products []Product
				source.DB().Where("category2_id in ?", repeatIds).Find(&products)
				var updateproduct []map[string]interface{}
				for pk, product := range products {
					updateproductRow := make(map[string]interface{})
					updateproductRow["id"] = product.ID
					updateproductRow["category2_id"] = newChild.ID
					updateproductRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
					updateproduct = append(updateproduct, updateproductRow)
					products[pk].Category2ID = newChild.ID
				}
				source.BatchUpdate(updateproduct, "products", "")
				//修改属于该分类组的所有商品和下级分类end

				source.DB().Delete(&Category{}, "id in ?", repeatIds) //删除原来重复的分类

				//处理三级分类
				for _, thirdC := range thirdChild {
					var repeatChildThird []Category
					source.DB().Where("name = ?", thirdC.Name).Where("parent_id = ?", thirdC.ParentID).Where("is_new = 0").Find(&repeatChildThird)
					if len(repeatChildThird) > 1 {
						//有重复分类
						var repeatThirdIds []uint //获取id数组
						for _, rc := range repeatChildThird {
							repeatThirdIds = append(repeatThirdIds, rc.ID)
						}
						//生成一个新的分类
						var newThirdChild Category
						newThirdChild = thirdC
						newThirdChild.ID = 0
						newThirdChild.IsNew = 1
						newThirdChild.CreatedAt = nil
						newThirdChild.UpdatedAt = nil
						source.DB().Create(&newThirdChild)

						var thirdProducts []Product
						source.DB().Where("category3_id in ?", repeatThirdIds).Find(&thirdProducts)
						var updateproductThird []map[string]interface{}
						for pk, product := range thirdProducts {
							updateproductThirdRow := make(map[string]interface{})
							updateproductThirdRow["id"] = product.ID
							updateproductThirdRow["category3_id"] = newChild.ID
							updateproductThirdRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
							updateproductThird = append(updateproductThird, updateproductThirdRow)
							thirdProducts[pk].Category3ID = newThirdChild.ID
						}
						source.BatchUpdate(updateproductThird, "products", "")
						source.DB().Delete(&Category{}, "id in ?", repeatThirdIds) //删除原来重复的分类

					}
				}
			}

		}
	}
}

type Product struct {
	source.Model
	Category1ID uint `json:"category1_id" form:"category1_id" gorm:"index"` // 一级分类
	Category2ID uint `json:"category2_id" form:"category2_id" gorm:"index"` // 二级分类
	Category3ID uint `json:"category3_id" form:"category3_id" gorm:"index"`
}
