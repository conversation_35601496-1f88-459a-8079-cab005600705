package model

import (
	"strconv"
	"testing"
	"yz-go/source"
)

func TestName(t *testing.T) {
	err := Migrate()
	if err != nil {
		t.<PERSON>al(err)
	}
}
func TestUpdateCategoryWhichCreatedAtIsNull(t *testing.T) {
	var categories []Category
	err := source.DB().Find(&categories, "created_at is null").Error
	if err != nil {
		t.Fatal(err)
	}
	for _, category := range categories {
		err = source.DB().Model(&category).Update("created_at", category.UpdatedAt).Error
		if err != nil {
			t.Fatal(err)
		}
	}
}
func Test1LevelCategory(t *testing.T) {
	var categories []Category
	i := 0
	sum := 0
	for {
		i += 1
		sum += 1
		isDisplay := 1
		categories = append(categories, Category{
			CategoryModel: CategoryModel{
				Name:      strconv.Itoa(i),
				Level:     1,
				ParentID:  0,
				IsDisplay: &isDisplay,
			},
		})
		if i == 7 {
			err := source.DB().Create(&categories).Error
			if err != nil {
				t.Fatal(err)
			}
			categories = []Category{}
			i = 0
		}
		if sum == 7 {
			break
		}
	}
}
func Test2LevelCategory(t *testing.T) {
	var categories []Category
	var chirdren []Category
	isDisplay := 1
	err := source.DB().Find(&categories, "level = ?", 1).Error
	for _, category := range categories {
		for i := 0; i < 7; i++ {
			chirdren = append(chirdren, Category{
				CategoryModel: CategoryModel{
					Level:     2,
					Name:      category.Name + strconv.Itoa(i),
					IsDisplay: &isDisplay,
				},
			})
		}
		err = source.DB().Model(&category).Association("Childrens").Append(chirdren)
		chirdren = []Category{}
		if err != nil {
			t.Fatal(err)
		}
	}
	if err != nil {
		t.Fatal(err)
	}
}
func TestLevel3Category(t *testing.T) {
	var categories []Category
	var chirdren []Category
	isDisplay := 1
	err := source.DB().Find(&categories, "level = ?", 2).Error
	for _, category := range categories {
		for i := 0; i < 20; i++ {
			chirdren = append(chirdren, Category{
				CategoryModel: CategoryModel{
					Level:     3,
					Name:      category.Name + strconv.Itoa(i),
					IsDisplay: &isDisplay,
				},
			})
		}
		err = source.DB().Model(&category).Association("Childrens").Append(chirdren)
		chirdren = []Category{}
		if err != nil {
			t.Fatal(err)
		}
	}
	if err != nil {
		t.Fatal(err)
	}
}
