package request

import (
	"category/model"
	yzRequest "yz-go/request"
)

type CategorySearch struct {
	model.Category
	yzRequest.PageInfo
}

type CategoryChildrenSearch struct {
	yzRequest.PageInfo
	ParentId uint   `json:"parent_id" form:"parent_id"`
	Name     string `json:"name" form:"name"`
	Source   int    `json:"source" form:"source"`
	IsPlugin int    `json:"is_plugin" form:"is_plugin"`
}

type BrandSearch struct {
	model.Brand
	yzRequest.PageInfo
}

// MoveParams 移动分类参数
type MoveParams struct {
	ID          uint   `json:"id" validate:"required"`                         // 分类ID
	ParentId    uint   `json:"parent_id"`                                      // 父级分类ID
	Source      int    `json:"source"`                                         // 分类来源
	IsPlugin    int    `json:"is_plugin"`                                      // 0平台分类，1插件分类
	MoveOperate string `json:"move_operate" validate:"required,oneof=up down"` // up上移 down下移
}
