package request

import yzRequest "yz-go/request"

type ProductCardListSearch struct {
	Category1ID uint   `json:"category1_id" query:"category1_id"` // 一级分类
	Category2ID uint   `json:"category2_id" query:"category2_id"` // 二级分类
	Category3ID uint   `json:"category3_id" query:"category3_id"` // 三级分类
	SortBy      int    `json:"sort_by" query:"sort_by"`           // 1综合降2价格降3价格升4销量降5发布时间降
	MinPrice    int    `json:"min_price" query:"min_price"`       // 最低售价（分）
	MaxPrice    int    `json:"max_price" query:"max_price"`       // 最高售价（分）
	Title       string `json:"title" query:"title"`               // 产品标题模糊
	IsNew       int    `json:"is_new" query:"is_new"`             // 新品
	IsRecommend int    `json:"is_recommend" query:"is_recommend"` // 推荐
	IsHot       int    `json:"is_hot" query:"is_hot"`             // 热卖
	IsPromotion int    `json:"is_promotion" query:"is_promotion"` // 促销
	yzRequest.PageInfo
}
