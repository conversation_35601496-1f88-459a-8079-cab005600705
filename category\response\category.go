package response

import (
	"category/request"
	"gorm.io/gorm"
	yzRequest "yz-go/request"
	"yz-go/source"
	"yz-go/utils"
)

type CategoryIndex struct {
	Category        Category   `json:"category"`            // 分类
	CateGoryParents []Category `json:"category_parents"`    // 分类路径
	Children        []Category `json:"childrens,omitempty"` // 子分类
}

type Category struct {
	source.SoftDel
	ID          uint   `json:"id"`
	Name        string `json:"name"` // 分类名
	ParentID    uint   `json:"parent_id"`
	Image       string `json:"image_url"`             // 图片地址
	Icon        string `json:"icon"`                  // icon名
	CategoryUrl string `json:"category_url" gorm:"-"` // 分类详情url
	ProductsUrl string `json:"products_url" gorm:"-"` // 商品列表url

	Level    int        `json:"-"`
	Children []Category `json:"children" gorm:"foreignKey:ParentID;references:ID"` //子分类
}

func (b *Category) AfterFind(tx *gorm.DB) (err error) {
	if b.Level != 0 {
		var search request.ProductCardListSearch
		switch b.Level {
		case 1:
			search.Category1ID = b.ID
		case 2:
			search.Category2ID = b.ID
		case 3:
			search.Category3ID = b.ID
		default:
			return
		}
		err, b.ProductsUrl = utils.Url("api/product/list", search)

		err, b.CategoryUrl = utils.Url("api/category/index", yzRequest.GetById{Id: b.ID})
		if err != nil {
			return
		}
	}
	return
}
