package route

import (
	av1 "category/api/app/v1"
	fv1 "category/api/f/v1"
	"category/api/v1"
	"github.com/gin-gonic/gin"
)

// 后台私有
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	BrandsRouter := Router.Group("brand")
	{
		BrandsRouter.POST("createBrand", v1.CreateBrand)              // 新建Brands
		BrandsRouter.DELETE("deleteBrand", v1.DeleteBrand)            // 删除Brands
		BrandsRouter.DELETE("deleteBrandsById", v1.DeleteBrandByIds)  // 批量删除Brands
		BrandsRouter.PUT("updateBrand", v1.UpdateBrand)               // 更新Brands
		BrandsRouter.GET("findBrand", v1.FindBrand)                   // 根据ID获取Brands
		BrandsRouter.GET("getBrandList", v1.GetBrandList)             // 获取Brands列表
		BrandsRouter.GET("getBrandOptionList", v1.GetBrandOptionList) // 获取BrandsOption列表
	}
	CategoryRouter := Router.Group("category")
	{
		CategoryRouter.POST("moveCategory", v1.MoveCategory)                                 // 分类上移、下移
		CategoryRouter.POST("createCategory", v1.CreateCategory)                             // 新建Category
		CategoryRouter.DELETE("deleteCategory", v1.DeleteCategory)                           // 删除Category
		CategoryRouter.DELETE("deleteCategoryByIds", v1.DeleteCategoryByIds)                 // 批量删除Category
		CategoryRouter.PUT("updateCategory", v1.UpdateCategory)                              // 更新Category
		CategoryRouter.GET("findCategory", v1.FindCategory)                                  // 根据ID获取Category
		CategoryRouter.GET("getCategoryList", v1.GetCategoryList)                            // 获取Category列表
		CategoryRouter.GET("getCategoryListWithParentId", v1.GetCategoryListWithParentId)    // 获取Category子列表
		CategoryRouter.GET("getCategorysWithLevelAndName", v1.GetCategoriesWithLevelAndName) // 名称和级别获取Category列表
		CategoryRouter.GET("getRecommendCategory", v1.GetRecommendCategory)                  // 获取Category子列表
		CategoryRouter.POST("putRecommendCategory", v1.UpdateRecommendCategory)              // 名称和级别获取Category列表
		CategoryRouter.POST("displayCategoryByIds", v1.DisplayCategoryByIds)                 // 批量上下架
		CategoryRouter.GET("tree", fv1.GetCategoryTree)                                      // 获取Category树

	}
}

// 后台公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {

}

// 前端公共
func InitUserPublicRouter(Router *gin.RouterGroup) {
	CategoryRouter := Router.Group("category")
	{
		CategoryRouter.GET("tree", fv1.GetCategoryTree)                    // 获取Category树
		CategoryRouter.GET("index", fv1.Index)                             // 获取Category首页
		CategoryRouter.GET("getCategory", av1.GetCategoryListWithParentId) // 获取Category首页
		CategoryRouter.GET("lists", av1.GetCategoryList)                   // 获取Category首页
	}
}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {

}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	CategoryRouter := Router.Group("category")
	{
		CategoryRouter.GET("tree", fv1.GetCategoryTree)                     // 获取Category树
		CategoryRouter.GET("lists", av1.GetCategoryList)                    // 获取Category首页
		CategoryRouter.GET("getCategory", av1.GetCategoryAllParentId)       // 获取Category首页  //变更为获取所有分类
		CategoryRouter.GET("findCategory", v1.FindCategory)                 // 根据ID获取Category
		CategoryRouter.GET("findCategoryWithName", v1.FindCategoryWithName) // 根据ID获取Category

	}
}
