package service

import (
	"category/model"
	"category/request"
	"errors"
	"strconv"
	yzRequest "yz-go/request"
	"yz-go/response"
	"yz-go/source"
)

//
//@function: CreateBrands
//@description: 创建Brands记录
//@param: brands model.Brands
//@return: err error

func CreateBrands(brands model.Brand) (err error) {
	err = source.DB().Create(&brands).Error
	return err
}

//
//@function: DeleteBrands
//@description: 删除Brands记录
//@param: brands model.Brands
//@return: err error

func DeleteBrands(brands model.Brand) (err error) {
	if err = checkBrandsCanBeDeleted([]uint{brands.ID}); err != nil {
		return err
	}

	return source.DB().Delete(&brands).Error
}

//
//@function: DeleteBrandsByIds
//@description: 批量删除Brands记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteBrandsByIds(ids yzRequest.IdsReq) (err error) {
	if err = checkBrandsCanBeDeleted(ids.Ids); err != nil {
		return err
	}

	return source.DB().Delete(&[]model.Brand{}, "id in ?", ids.Ids).Error
}

// 验证品牌是否可以删除

func checkBrandsCanBeDeleted(ids []uint) (err error) {
	var total int64
	for _, id := range ids {
		// 查询品牌下商品数量是否大于0
		if err = source.DB().Model(&response.Product{}).Where("deleted_at is null").Where("brand_id = ?", id).Count(&total).Error; err != nil {
			return
		}
		if total > 0 {
			return errors.New("请删除此品牌下的商品以后再试")
		}
	}
	return
}

//
//@function: UpdateBrands
//@description: 更新Brands记录
//@param: brands *model.Brands
//@return: err error

func UpdateBrands(brands model.Brand) (err error) {
	err = source.DB().Save(&brands).Error
	return err
}

//
//@function: GetBrands
//@description: 根据id获取Brands记录
//@param: id uint
//@return: err error, brands model.Brands

func GetBrands(id uint) (err error, brands model.Brand) {
	err = source.DB().Where("id = ?", id).First(&brands).Error
	return
}

//
//@function: GetBrandsInfoList
//@description: 分页获取Brands记录
//@param: info request.BrandsSearch
//@return: err error, list interface{}, total int64

func GetBrandsInfoList(info request.BrandsSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.Brand{})
	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}
	if info.ID > 0 {
		db.Where("id = ?", info.ID)
	}
	if info.SupplierID != 0 {
		db.Where("supplier_id = ?", info.SupplierID)
	}
	var brandss []model.Brand
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&brandss).Error
	return err, brandss, total
}

// 查询供应商品牌列表
func GetSupplierBrandsInfoList(info request.BrandsSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.Brand{})
	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}
	if info.SupplierID != 0 {
		db.Where("supplier_id = " + strconv.Itoa(int(info.SupplierID)) + " or supplier_id = 0")
	}
	var brandss []model.Brand
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&brandss).Error
	return err, brandss, total
}

func GetBrandsList(info model.Brand) (err error, list interface{}) {

	// 创建db
	db := source.DB().Model(&model.Brand{})
	if info.SupplierID != 0 {
		var supplierIds []uint
		supplierIds = append(supplierIds, 0)
		supplierIds = append(supplierIds, info.SupplierID)
		db = db.Where("supplier_id in ?", supplierIds)
	}
	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}
	var brandss []model.Brand
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Find(&brandss).Error
	return err, brandss
}
