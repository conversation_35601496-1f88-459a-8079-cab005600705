package service

import (
	"category/model"
	"category/request"
	"category/response"
	"errors"
	"gorm.io/gorm"
	model2 "product/model"
	"product/mq"
	yzRequest "yz-go/request"
	response2 "yz-go/response"
	"yz-go/source"
)

func MoveCategory(params request.MoveParams) (err error) {
	// 分类搜索模型
	db := searchCategoryGorm(request.CategorySearch{
		Category: model.Category{
			CategoryModel: model.CategoryModel{
				ParentID: params.ParentId,
				Source:   params.Source,
				IsPlugin: params.IsPlugin,
			},
		},
	})

	// 格式排序
	if err = formatSort(db); err != nil {
		return
	}

	// 上移、下移
	var currentCategory, replaceCategory model.Category
	if err = source.DB().Find(&currentCategory, params.ID).Error; err != nil {
		return
	}

	if params.MoveOperate == "up" {
		err, replaceCategory = getReplaceCategory(db, currentCategory.Sort+1)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("已经是第一个了")
			return
		}
	} else {
		err, replaceCategory = getReplaceCategory(db, currentCategory.Sort-1)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("已经是最后一个了")
			return
		}
	}

	// 执行操作
	sortMap := []map[string]interface{}{
		{
			"id":   currentCategory.ID,
			"sort": replaceCategory.Sort,
		},
		{
			"id":   replaceCategory.ID,
			"sort": currentCategory.Sort,
		},
	}
	if err = source.BatchUpdate(sortMap, "categories", "id"); err != nil {
		return
	}

	return
}

// 格式排序
func formatSort(db *gorm.DB) (err error) {
	var categories []model.Category
	if err = db.Find(&categories).Error; err != nil {
		return
	}

	// 重新排序(格式化排序)
	sort := len(categories)
	var sortMap []map[string]interface{}
	for _, item := range categories {
		sortMap = append(sortMap, map[string]interface{}{
			"id":   item.ID,
			"sort": sort,
		})
		sort--
	}

	if err = source.BatchUpdate(sortMap, "categories", ""); err != nil {
		return
	}

	return
}

// 获取被替换位置分类
func getReplaceCategory(db *gorm.DB, sort int) (err error, category model.Category) {
	err = db.Where("`sort` = ?", sort).First(&category).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateCategory
//@description: 创建Category记录
//@param: category model.Category
//@return: err error

func CreateCategory(category model.Category) (err error) {
	category.IsAuto = 1
	err = source.DB().Create(&category).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteCategory
//@description: 删除Category记录
//@param: category model.Category
//@return: err error

func DeleteCategory(category model.Category) (err error) {
	var total int64
	err = source.DB().Model(&response2.Product{}).Where("category1_id = ? or category2_id = ? or category3_id = ?", category.ID, category.ID, category.ID).Where("category1_id > 0 AND category2_id > 0 AND category3_id > 0").Where("deleted_at is null").Count(&total).Error
	if total > 0 {
		return errors.New("请删除此分类下的商品以后再试")
	}
	err = source.DB().Delete(&category).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteCategoryByIds
//@description: 批量删除Category记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteCategoryByIds(ids yzRequest.IdsReq) (err error) {
	var total int64
	err = source.DB().Model(&response2.Product{}).Where("category1_id in ?", ids.Ids).Or("category2_id in ?", ids.Ids).Or("category3_id in ?", ids.Ids).Count(&total).Error
	if total > 0 {
		return errors.New("请删除此分类下的商品以后再试")
	}
	err = source.DB().Delete(&[]model.Category{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateCategory
//@description: 更新Category记录
//@param: category *model.Category
//@return: err error

func UpdateCategory(category model.Category) (err error) {
	var oldCategory model.Category
	err = source.DB().Where("id", category.ID).First(&oldCategory).Error
	if err != nil {
		return
	}
	err = nil
	err = source.DB().Omit("created_at").Save(&category).Error
	//修改成功 并且分类上级id不一致则进行更新分类
	if err == nil && oldCategory.ParentID != category.ParentID {
		var ids []uint
		switch oldCategory.Level {
		case 2:
			err = source.DB().Model(&model2.Product{}).Where("category2_id", category.ID).Pluck("id", &ids).Error
			//代表该分类下没有商品 无需操作
			if err != nil {
				return nil
			}
			err = source.DB().Where("id in ?", ids).Updates(model2.ProductModel{
				Category1ID: category.ParentID,
			}).Error
			if err != nil {
				err = errors.New("商品分类更新失败")
				return
			}
			break
		case 3:
			err = source.DB().Model(&model2.Product{}).Where("category3_id", category.ID).Pluck("id", &ids).Error
			//代表该分类下没有商品 无需操作
			if err != nil {
				return nil
			}

			var oneCategory model.Category
			//获取二级分类
			source.DB().Where("id", category.ParentID).First(&oneCategory)
			err = source.DB().Where("id in ?", ids).Updates(&model2.ProductModel{
				Category1ID: oneCategory.ParentID,
				Category2ID: category.ParentID,
			}).Error
			if err != nil {
				err = errors.New("商品分类更新失败")
				return
			}
			break
		}
		for _, item := range ids {
			_ = mq.PublishMessage(item, mq.Edit, 0)
		}
	}
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetCategory
//@description: 根据id获取Category记录
//@param: id uint
//@return: err error, category model.Category

func GetCategoryByName(name string) (err error, category model.Category) {
	search := request.CategorySearch{
		Category: model.Category{
			CategoryModel: model.CategoryModel{
				Name: name,
			},
		},
	}
	err = searchCategoryGorm(search).First(&category).Error
	return
}

func GetCategory(id uint) (err error, category model.Category) {
	err = source.DB().Where("id = ?", id).First(&category).Error
	return
}

func searchCategoryGorm(search request.CategorySearch) *gorm.DB {
	db := source.DB().Model(&model.Category{})

	// 默认查询平台分类
	db.Where("`is_plugin` = ?", search.IsPlugin)

	// 默认排序
	db.Order("sort desc, id desc")

	if search.Name != "" {
		db.Where("`name` LIKE ?", "%"+search.Name+"%")
	}
	if search.Level != 0 {
		db.Where("`level` = ?", search.Level)
	}
	if search.Source > 0 {
		db.Where("`source` = ?", search.Source)
	}
	if search.ParentID > 0 {
		db.Where("`parent_id` = ?", search.ParentID)
	}
	if search.IsDisplay != nil {
		db.Where("`is_display` = ?", &search.IsDisplay)
	}

	return db
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetCategoryInfoList
//@description: 分页获取Category记录
//@param: info request.CategorySearch
//@return: err error, list interface{}, total int64

func GetCategoryList(search request.CategorySearch) (err error, list []model.Category, total int64) {
	db := searchCategoryGorm(search).Where("`parent_id` = 0")

	if err = db.Count(&total).Error; err != nil {
		return
	}

	limit := search.PageSize
	offset := search.PageSize * (search.Page - 1)

	err = db.Limit(limit).Offset(offset).Find(&list).Error

	return
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetCategoriesWithLevelAndName
// @description: 获取Category记录
// @param: info request.CategorySearch
// @return: err error, list interface{}
func GetCategoriesWithLevelAndName(search request.CategorySearch) (err error, categories []model.Category) {
	err = searchCategoryGorm(search).Find(&categories).Error

	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetCategoryInfoTree
//@description: 获取Category树
//@param: category model.Category
//@return: err error, list []response.Category

func GetCategoryInfoTree(search request.CategorySearch) (err error, list []response.Category) {
	var categories []response.Category
	if err = searchCategoryGorm(search).Find(&categories).Error; err != nil {
		return
	}

	list = paresCategory(categories, 0, 0, 3)
	return
}

func paresCategory(categories []response.Category, parentId uint, depth int, maxDepth int) (list []response.Category) {
	for _, v1 := range categories {
		if v1.ParentID == parentId {
			v1.Children = []response.Category{}
			if depth+1 < maxDepth {
				v1.Children = paresCategory(categories, v1.ID, depth+1, maxDepth)
				if v1.Children == nil {
					v1.Children = []response.Category{}
				}
			}
			list = append(list, v1)
		}

	}
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetCategoryInfoList
//@description: 分页获取Category记录
//@param: info request.CategorySearch
//@return: err error, list interface{}, total int64

func GetCategoryInfoListWithParentId(search request.CategoryChildrenSearch) (err error, list interface{}, total int64) {
	limit := search.PageSize
	offset := search.PageSize * (search.Page - 1)
	// 创建db
	db := source.DB().Model(&model.Category{})
	var categories []model.Category
	if search.Name != "" {
		db = db.Where("`name` LIKE ?", "%"+search.Name+"%")
	}
	err = db.Where("parent_id = ?", search.ParentId).Count(&total).Error

	err = db.Where("parent_id = ?", search.ParentId).Limit(limit).Offset(offset).Order("sort desc,id desc").Find(&categories).Error

	return err, categories, total
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetCategoryInfoList
//@description: 分页获取Category记录
//@param: info request.CategorySearch
//@return: err error, list interface{}, total int64

func GetCategoryAllWithParentId(search request.CategoryChildrenSearch) (err error, categories []model.Category) {
	db := source.DB().Model(&model.Category{})

	if search.Name != "" {
		db = db.Where("`name` LIKE ?", "%"+search.Name+"%")
	}

	// 插件分类搜索
	if search.IsPlugin == 1 {
		db = db.Where("`is_plugin` = ?", 1)
	} else {
		db = db.Where("`is_plugin` = ?", 0)
	}

	// 插件源搜索
	if search.Source > 0 {
		db = db.Where("`source` = ?", search.Source)
	}

	err = db.Where("parent_id = ?", search.ParentId).
		Where("is_display = 1").
		Order("sort desc,id desc").
		Find(&categories).
		Error
	return
}

// @author: [piexlmax](https://github.com/piexlmax)
// @description: 分页获取Category记录
// @param: info request.CategorySearch
// @return: err error, list []response.Category, total int64
func GetCategorySimpleList(info request.CategorySearch) (err error, list []response.Category) {
	// 创建db
	db := source.DB().Model(&response.Category{})
	var categories []response.Category
	// 默认返回所有pid=0的分类（1级分类）
	var joinWhere string
	joinWhere = "INNER join products on products.category1_id = categories.id"
	db.Select("categories.*,count(`products`.id) as product_count").Joins(joinWhere).Where("categories.parent_id = ?", info.ParentID).Where("categories.is_display", 1)

	err = db.Group("categories.id").Order("categories.sort desc,categories.id desc").Find(&categories).Error
	return err, categories
}

func GetSupplierCategorySimpleList(info request.CategorySearch) (err error, list []response.Category) {
	// 创建db
	db := source.DB().Model(&response2.Product{})
	var categories []response.Category
	//var products []response2.Product
	var categoryIds []uint
	err = db.Select("category1_id").Where("supplier_id = ?", info.ParentID).Where("deleted_at is NULL").Group("category1_id").Pluck("category1_id", &categoryIds).Error
	if err != nil {
		return
	}

	//for _, v := range products {
	//	categoryIds = append(categoryIds, v.Category1ID)
	//}
	err = source.DB().Where("id in ?", categoryIds).Find(&categories).Error
	if err != nil {
		return
	}
	return err, categories
}

func CategoryParents(category response.Category) (err error, parents []response.Category) {
	parent := response.Category{}
	if category.ParentID > 0 {
		err = source.DB().Model(&category).First(&parent, category.ParentID).Error
		if err != nil {
			return
		}
		if parent.ParentID == 0 || parent.Level == 1 {
			parents = append(parents, parent)
		} else {
			err1, parentsOfParent := CategoryParents(parent)
			err = err1
			if err != nil {
				return
			}
			parents = append(parents, parentsOfParent...)

		}
	}

	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetSimpleCategory
//@description: 根据id获取基本信息
//@param: id uint
//@return: err error, category response.Category

func GetSimpleCategory(id uint) (err error, category response.Category) {
	err = source.DB().Where("id = ?", id).First(&category).Error
	return
}

func GetRecommendCategory() (err error, category []model.RecommendCate) {
	err = source.DB().Preload("Category").Find(&category).Error
	return
}

func PutRecommendCategory(data []model.RecommendCate) (err error) {
	source.DB().Exec("truncate table recommend_cates")
	err = source.DB().Create(&data).Error
	return
}

// DisplayProductByIds
// @author: [piexlmax](https://github.com/piexlmax)
// @function: DeleteProductByIds
// @description: 批量删除Product记录
// @param: ids yzRequest.IdsReq
// @return: err error
func DisplayCategoryByIds(ids yzRequest.IdsReqWithValue) (err error) {
	err = source.DB().Model(&model.Category{}).Where("`id` in ?", ids.Ids).Update("is_display", ids.Value).Error
	return err
}

type CategoryIds struct {
	Category1ID uint `json:"category1_id" form:"category1_id" gorm:"category1_id"` // 一级分类
	Category2ID uint `json:"category2_id" form:"category2_id" gorm:"category2_id"` // 二级分类
	Category3ID uint `json:"category3_id" form:"category3_id" gorm:"category3_id"`
}

// @author: [piexlmax](https://github.com/piexlmax)
// @description: 分页获取Category记录
// @param: info request.CategorySearch
// @return: err error, list []response.Category, total int64
func GetCategoryListSupplierAndGatherSupply(info yzRequest.GetStoreDetatilInformation) (err error, list []response.Category) {

	pdb := source.DB().Model(&model2.Product{}).Where("is_display", 1)
	switch info.Type {
	case 0: //供应商
		pdb.Where("supplier_id = ?", info.Id)
		break
	case 1: //供应链
		pdb.Where("gather_supply_id = ?", info.Id)
		break
	default: //自营
		pdb.Where("supplier_id = 0 and gather_supply_id = 0 and source = 0")
		break
	}

	switch info.Level {
	case 2:
		if info.Pid != 0 {
			pdb.Where("category1_id = ?", info.Pid)
		}
		pdb.Select("DISTINCT(category2_id) as category_id")
		break
	case 3:
		if info.Pid != 0 {
			pdb.Where("category2_id = ?", info.Pid)
		}
		pdb.Select("DISTINCT(category3_id) as category_id")
		break
	default:
		pdb.Select("DISTINCT(category1_id) as category_id")
		info.Level = 1
		break
	}

	var categoryIds []uint
	err = pdb.Find(&categoryIds).Error
	//没有查询到相关分类
	if err != nil {
		err = nil
		return
	}

	categoryDb := source.DB().Model(&response.Category{}).Where("level = ?", info.Level).Where("id in ?", categoryIds)

	if info.Pid != 0 {
		categoryDb = categoryDb.Where("parent_id = ?", info.Pid)
	}
	err = categoryDb.Find(&list).Error
	return
}

func GetCategoryListSupplierAndGatherSupplyAll(info yzRequest.GetStoreDetatilInformation) (err error, Categories []response.Category) {
	var categoryIdProducts []CategoryIds

	var categoryIds []uint

	db := source.DB().Model(&model2.Product{}).Where("is_display = 1")

	switch info.Type {
	case 0: //供应商
		db.Where("supplier_id = ?", info.Id)
		break
	case 1: //供应链
		db.Where("gather_supply_id = ?", info.Id)
		break
	default: //自营
		db.Where("supplier_id = 0 and gather_supply_id = 0 and source = 0")
		break
	}

	err = db.Find(&categoryIdProducts).Error

	if err != nil {
		err = errors.New("暂无任何分类")
		return
	}
	for _, item := range categoryIdProducts {
		var isCategoryIds1 = 1
		var isCategoryIds2 = 1
		var isCategoryIds3 = 1
		for _, item1 := range categoryIds {
			if item1 == item.Category1ID {
				isCategoryIds1 = 2
			}
			if item1 == item.Category2ID {
				isCategoryIds2 = 2
			}
			if item1 == item.Category3ID {
				isCategoryIds3 = 2
			}
		}
		if isCategoryIds1 == 1 {
			categoryIds = append(categoryIds, item.Category1ID)
		}
		if isCategoryIds2 == 1 {
			categoryIds = append(categoryIds, item.Category2ID)
		}
		if isCategoryIds3 == 1 {
			categoryIds = append(categoryIds, item.Category3ID)
		}
	}

	categoryDb := source.DB().Model(&response.Category{}).Where("id in ?", categoryIds)

	err = categoryDb.Find(&Categories).Error

	Categories = paresCategory(Categories, 0, 0, 3)
	return
}
