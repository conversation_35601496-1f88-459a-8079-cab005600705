package service

import (
	"category/model"
	"category/request"
	"reflect"
	"testing"
)

func TestGetCategoryInfoTree(t *testing.T) {
	isDisplay := 1

	search := request.CategorySearch{
		Category: model.Category{
			CategoryModel: model.CategoryModel{
				IsDisplay: &isDisplay,
			},
		},
	}

	type args struct {
		request.CategorySearch
	}

	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "正常",
			args: args{
				CategorySearch: search,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotList := GetCategoryInfoTree(tt.args.CategorySearch)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetCategoryInfoTree() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if len(gotList) == 0 {
				t.<PERSON><PERSON><PERSON>("GetCategoryInfoTree() gotList = %v", gotList)
			}
		})
	}
}
