package v1

import (
	"cloud/common"
	model2 "cloud/model"
	"cloud/request"
	"cloud/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/frame/g"
	"go.uber.org/zap"
	"product/model"
	"strconv"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// GetProductsList
// @Tags 云仓
// @Summary 获取可推送至云仓的商品，与推送状态
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取获取可推送至云仓的商品，与推送状态列表"
// @Success 200 {object} []model.Product
// @Router /api/cloud/getProductsList [get]
func GetProductsList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetProductList(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetFreightList
// @Tags 云仓
// @Summary 获取运费模板列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "获取运费模板列表"
// @Success 200 {object}  request.GetFreightList
// @Router /cloud/getFreightList [get]
func GetFreightList(c *gin.Context) {
	var pageInfo request.FreightSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, list := service.GetFreightList(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list.Data,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetCateBusinessList
// @Tags 云仓
// @Summary 获取云仓分类
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "获取云仓分类"
// @Success 200 {object} []model.Product
// @Router /cloud/getCateBusinessList [get]
func GetCateBusinessList(c *gin.Context) {
	var pageInfo request.CloudCommon
	err := c.ShouldBindQuery(&pageInfo)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)
	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, list := service.GetCateBusinessList(config); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.OptionResult{
			List: list.Data,
		}, "获取成功", c)
	}
}

// GetCateBusinessList
// @Tags 云仓
// @Summary 获取云仓分类
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "获取云仓分类"
// @Success 200 {object} []model.Product
// @Router /cloud/getCateBusinessList [get]
func GetStagsList(c *gin.Context) {
	var pageInfo request.CloudCommon
	err := c.ShouldBindQuery(&pageInfo)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)
	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, list := service.GetStagsList(config); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.OptionResult{
			List: list.Data,
		}, "获取成功", c)
	}
}

// GetGoodsList
// @Tags 云仓
// @Summary 获取商品列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "获取商品列表"
// @Success 200 {object}  request.CloudGoodsSearch
// @Router /cloud/getGoodsList [get]
func GetGoodsList(c *gin.Context) {
	var pageInfo request.CloudGoodsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//本地id检索
	if pageInfo.LocGoodsId != 0 {
		var CloudGoods model2.CloudGoods
		err = source.DB().Where("product_id = ?", pageInfo.LocGoodsId).Find(&CloudGoods).Error
		if err != nil {
			yzResponse.FailWithMessage("商品未推送"+err.Error(), c)
			return
		}
		pageInfo.ThirdGoodsId = int(CloudGoods.CloudProductId)

	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, list, total := service.GetGoodsList(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list.Data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetGoodsList
// @Tags 云仓
// @Summary 获取云仓下架的商品与中台上架的进行比对如果中台上架则进行更新
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "获取云仓下架的商品与中台上架的进行比对如果中台上架则进行更新"
// @Success 200 {object}  request.CloudGoodsSearch
// @Router /cloud/getGoodsList [get]
func GetGoodsOn(c *gin.Context) {
	var pageInfo request.CloudGoodsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//本地id检索
	if pageInfo.LocGoodsId != 0 {
		var CloudGoods model2.CloudGoods
		err = source.DB().Where("product_id = ?", pageInfo.LocGoodsId).Find(&CloudGoods).Error
		if err != nil {
			yzResponse.FailWithMessage("商品未推送"+err.Error(), c)
			return
		}
		pageInfo.ThirdGoodsId = int(CloudGoods.CloudProductId)

	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}

	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 20
	}
	var IsOnsale = 0
	pageInfo.IsOnsale = &IsOnsale

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	go service.GetGoodsOn(config, pageInfo)
	yzResponse.Ok(c)

}

// UpdateGoodsOnsale
// @Tags 云仓
// @Summary 修改云仓商品上架下架状态
// @accept application/json
// @Produce application/json
// @Param data body request.CloudGoodsSearch true "修改云仓商品上架下架状态"
// @Success 200 {object}  request.CloudGoodsSearch
// @Router /cloud/UpdateGoodsOnsale [post]
func UpdateGoodsOnsale(c *gin.Context) {
	var pageInfo request.GoodsOnsale
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err := service.UpdateGoodsOnsale(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

// DeleteGoods
// @Tags 云仓
// @Summary 删除云仓商品
// @accept application/json
// @Produce application/json
// @Param data body request.DeleteGoods true "删除云仓商品"
// @Success 200 {object}  request.DeleteGoods
// @Router /cloud/deleteGoods [post]
func DeleteGoods(c *gin.Context) {
	var pageInfo request.DeleteGoods
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err := service.DeleteGoods(config, pageInfo, pageInfo.GatherSuppliesId); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

// GetFreight
// @Tags 云仓
// @Summary 获取运费模板详情
// @accept application/json
// @Produce application/json
// @Param data body request.GetFreight true "获取运费模板详情"
// @Success 200 {object}  request.GetFreight
// @Router /cloud/getFreight [get]
func GetCloudFreight(c *gin.Context) {
	var pageInfo request.FreightDetail
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, freight := service.GetFreight(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(freight, c)
	}
}

// GetFreight
// @Tags 云仓
// @Summary 删除运费模板
// @accept application/json
// @Produce application/json
// @Param data body request.DeleteFreight true "删除运费模板"
// @Success 200 {object}  request.DeleteFreight
// @Router /cloud/deleteFreight [post]
func DeleteFreight(c *gin.Context) {
	var pageInfo request.DeleteFreight
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, _ := service.DeleteFreight(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// AddFreight
// @Tags 云仓
// @Summary 添加运费模板
// @accept application/json
// @Produce application/json
// @Param data body request.SaveFreight true "添加运费模板"
// @Success 200 {object}  request.SaveFreight
// @Router /cloud/addFreight [post]
func AddFreight(c *gin.Context) {
	var addFreight request.SaveFreight
	err := c.ShouldBindJSON(&addFreight)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if addFreight.GatherSuppliesId == 0 {
		yzResponse.FailWithMessage("云仓id不可为空", c)
		return
	}
	if addFreight.Name == "" {
		yzResponse.FailWithMessage("模板名称不可为空", c)
		return
	}

	err, config := common.InitCloudSetting(addFreight.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, _ := service.AddFreight(config, addFreight); err != nil {
		log.Log().Error("创建失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// UpdateFreight
// @Tags 云仓
// @Summary 添加运费模板
// @accept application/json
// @Produce application/json
// @Param data body request.SaveFreight true "添加运费模板"
// @Success 200 {object}  request.SaveFreight
// @Router /cloud/updateFreight [post]
func UpdateFreight(c *gin.Context) {
	var addFreight request.SaveFreight
	err := c.ShouldBindJSON(&addFreight)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if addFreight.GatherSuppliesId == 0 {
		yzResponse.FailWithMessage("云仓id不可为空", c)
		return
	}
	if addFreight.Id == 0 {
		yzResponse.FailWithMessage("运费模板id不可为空", c)
		return
	}
	if addFreight.Name == "" {
		yzResponse.FailWithMessage("模板名称不可为空", c)
		return
	}

	err, config := common.InitCloudSetting(addFreight.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, _ := service.UpdateFreight(config, addFreight); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// GetCloudOrderList
// @Tags 云仓
// @Summary 获取订单列表
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "获取订单列表"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/getCloudOrderList [get]
func GetCloudOrderList(c *gin.Context) {
	var orderSearch request.CloudOrderSearch
	err := c.ShouldBindQuery(&orderSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(orderSearch.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, list, total := service.GetCloudOrderList(config, orderSearch); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list.Data,
			Total:    total,
			Page:     orderSearch.Page,
			PageSize: orderSearch.PageSize,
		}, "获取成功", c)
	}
}

// GetDeliverList
// @Tags 云仓
// @Summary 获取物流公司列表
// @accept application/json
// @Produce application/json
// @Param data body request.CloudCommon true "获取物流公司列表"
// @Success 200 {object}  request.CloudCommon
// @Router /cloud/getDeliverList [get]
func GetDeliverList(c *gin.Context) {
	var cloudCommon request.CloudCommon
	err := c.ShouldBindQuery(&cloudCommon)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudCommon.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, list := service.GetDeliverList(config); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list.Data, c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 获取订单详情
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "获取订单详情"
// @Success 200 {object}  request.CloudOrderDetatil
// @Router /cloud/getCloudOrder [get]
func GetCloudOrder(c *gin.Context) {
	var pageInfo request.CloudOrderDetatil
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, freight := service.GetCloudOrder(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(freight.Data, c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 获取订单详情
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "获取订单详情"
// @Success 200 {object}  request.CloudOrderDetatil
// @Router /cloud/getCloudDeliver [get]
func GetCloudDeliver(c *gin.Context) {
	var cloudGetDeliver request.CloudGetDeliver
	err := c.ShouldBindQuery(&cloudGetDeliver)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(cloudGetDeliver.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, deliver := service.CloudGetDeliver(config, cloudGetDeliver); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(deliver.Data, c)
	}
}

// CloudOrderSend
// @Tags 云仓
// @Summary 云仓订单发货
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSend true "云仓订单发货"
// @Success 200 {object}  request.CloudOrderSend
// @Router /cloud/cloudOrderSend [post]
func CloudOrderSend(c *gin.Context) {
	var cloudOrderSend request.CloudOrderSend
	err := c.ShouldBindJSON(&cloudOrderSend)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudOrderSend.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, data := service.CloudOrderSend(config, cloudOrderSend); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

// GetRefundList
// @Tags 云仓
// @Summary 获取售后列表
// @accept application/json
// @Produce application/json
// @Param data body request.CloudRefundSearch true "获取售后列表"
// @Success 200 {object}  request.CloudRefundSearch
// @Router /cloud/getRefundList [get]
func GetRefundList(c *gin.Context) {
	var pageInfo request.CloudRefundSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service.GetRefundList(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list.Data,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetRefundDetatil
// @Tags 云仓
// @Summary 获取订单详情
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "获取订单详情"
// @Success 200 {object}  request.CloudOrderDetatil
// @Router /cloud/getRefundDetatil [get]
func GetRefundDetatil(c *gin.Context) {
	var cloudRefund request.CloudRefund
	err := c.ShouldBindQuery(&cloudRefund)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudRefund.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, refund := service.GetRefundDetatil(config, cloudRefund); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(refund.Data, c)
	}
}

// GetAddressList
// @Tags 云仓
// @Summary 获取售后地址列表
// @accept application/json
// @Produce application/json
// @Param data body request.CloudRefundAddressList true "获取售后地址列表"
// @Success 200 {object}  request.CloudRefundAddressList
// @Router /cloud/getAddressList [get]
func GetAddressList(c *gin.Context) {
	var pageInfo request.CloudRefundAddressList
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list := service.GetAddressList(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list.Data,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetRegionList
// @Tags 云仓
// @Summary 获取订单详情
// @accept application/json
// @Produce application/json
// @Param data body request.CloudRegion true "获取订单详情"
// @Success 200 {object}  request.CloudRegion
// @Router /cloud/getRegionList [get]
func GetRegionList(c *gin.Context) {
	var cloudRegion request.CloudRegion
	err := c.ShouldBindQuery(&cloudRegion)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudRegion.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, region := service.GetRegionList(config, cloudRegion); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(region.Data, c)
	}
}

// AddRefundAddress
// @Tags 云仓
// @Summary 添加运费模板
// @accept application/json
// @Produce application/json
// @Param data body request.SaveFreight true "添加运费模板"
// @Success 200 {object}  request.SaveFreight
// @Router /cloud/addRefundAddress [post]
func AddRefundAddress(c *gin.Context) {
	var saveRefundAddress request.SaveRefundAddress
	err := c.ShouldBindJSON(&saveRefundAddress)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(saveRefundAddress.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, _ := service.AddRefundAddress(config, saveRefundAddress); err != nil {
		log.Log().Error("添加失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("添加成功", c)
	}
}

// AddRefundAddress
// @Tags 云仓
// @Summary 添加运费模板
// @accept application/json
// @Produce application/json
// @Param data body request.SaveFreight true "添加运费模板"
// @Success 200 {object}  request.SaveFreight
// @Router /cloud/addRefundAddress [post]
func UpdateRefundAddress(c *gin.Context) {
	var saveRefundAddress request.SaveRefundAddress
	err := c.ShouldBindJSON(&saveRefundAddress)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(saveRefundAddress.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, _ := service.UpdateRefundAddress(config, saveRefundAddress); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// GetRefundAddress
// @Tags 云仓
// @Summary 获取订单详情
// @accept application/json
// @Produce application/json
// @Param data body request.SaveRefundAddress true "获取订单详情"
// @Success 200 {object}  request.SaveRefundAddress
// @Router /cloud/getRefundAddress [get]
func GetRefundAddress(c *gin.Context) {
	var saveRefundAddress request.SaveRefundAddress
	err := c.ShouldBindQuery(&saveRefundAddress)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(saveRefundAddress.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, region := service.GetRefundAddress(config, saveRefundAddress); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(region.Data, c)
	}
}

// DeleteRefundAddress
// @Tags 云仓
// @Summary 删除退货地址
// @accept application/json
// @Produce application/json
// @Param data body request.SaveRefundAddress true "删除退货地址"
// @Success 200 {object}  request.SaveRefundAddress
// @Router /cloud/deleteRefundAddress [get]
func DeleteRefundAddress(c *gin.Context) {
	var saveRefundAddress request.SaveRefundAddress
	err := c.ShouldBindQuery(&saveRefundAddress)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(saveRefundAddress.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, region := service.DeleteRefundAddress(config, saveRefundAddress); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(region.Data, c)
	}
}

// CloudRefundAgree
// @Tags 云仓
// @Summary 同意售后
// @accept application/json
// @Produce application/json
// @Param data body request.CloudRefundAgree true "同意售后"
// @Success 200 {object}  request.CloudRefundAgree
// @Router /cloud/cloudRefundAgree [post]
func CloudRefundAgree(c *gin.Context) {
	var cloudRefundAgree request.CloudRefundAgree
	err := c.ShouldBindJSON(&cloudRefundAgree)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//yzResponse.OkWithData(cloudRefundAgree,c)
	err, config := common.InitCloudSetting(cloudRefundAgree.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, _ := service.CloudRefundAgree(config, cloudRefundAgree); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// CloudRefundReject
// @Tags 云仓
// @Summary 拒绝售后
// @accept application/json
// @Produce application/json
// @Param data body request.CloudRefundReject true "拒绝售后"
// @Success 200 {object}  request.CloudRefundReject
// @Router /cloud/cloudRefundReject [post]
func CloudRefundReject(c *gin.Context) {
	var cloudRefundReject request.CloudRefundReject
	err := c.ShouldBindJSON(&cloudRefundReject)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//yzResponse.OkWithData(cloudRefundReject,c)
	err, config := common.InitCloudSetting(cloudRefundReject.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, _ := service.CloudRefundReject(config, cloudRefundReject); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// CloudPushGoods
// @Tags 云仓
// @Summary 推送商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudPushGoods true "推送商品"
// @Success 200 {object}  request.CloudPushGoods
// @Router /cloud/cloudPushGoods [post]
func CloudPushGoods(c *gin.Context) {
	var cloudPushGoods request.CloudPushProductIds
	err := c.ShouldBindJSON(&cloudPushGoods)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if cloudPushGoods.CloudCategory3Id == 0 && cloudPushGoods.CloudCategory2Id == 0 && cloudPushGoods.CloudCategory1Id == 0 {
		yzResponse.FailWithMessage("请选择推送分类", c)
		return
	}
	if cloudPushGoods.ProductIds == nil || len(cloudPushGoods.ProductIds) == 0 {
		yzResponse.FailWithMessage("请选择推送的商品", c)
		return
	}
	if cloudPushGoods.GoodsDes == "" {
		yzResponse.FailWithMessage("请添写商品关键字", c)
		return
	}
	if cloudPushGoods.ProducingArea == "" {
		yzResponse.FailWithMessage("请添写产地", c)
		return
	}
	if cloudPushGoods.DeliverArea == "" {
		yzResponse.FailWithMessage("请填写发货地", c)
		return
	}
	if cloudPushGoods.FreightId == 0 {
		yzResponse.FailWithMessage("请选择运费模板", c)
		return
	}
	if cloudPushGoods.AftersaleTime == 0 {
		yzResponse.FailWithMessage("请选择售后时长", c)
		return
	}
	if cloudPushGoods.DelayCompensate == 0 {
		yzResponse.FailWithMessage("请选择发货延期", c)
		return
	}
	if cloudPushGoods.Stags == "" {
		yzResponse.FailWithMessage("请选择服务标签", c)
		return
	}
	//yzResponse.OkWithData(cloudPushGoods,c)
	err, config := common.InitCloudSetting(cloudPushGoods.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, pushGoods := service.CloudPushGoods(config, cloudPushGoods); err != nil {
		log.Log().Error("推送失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(pushGoods, c)
	}
}

// CloudUpdateGoods
// @Tags 云仓
// @Summary 更新商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudUpdateCloudGoods true "更新商品"
// @Success 200 {object}  request.CloudUpdateCloudGoods
// @Router /cloud/cloudUpdateGoods [post]
func CloudUpdateGoodsBySource(c *gin.Context) {
	var cloudUpdateCloudGoods request.CloudUpdateGoodsBySource
	err := c.ShouldBindJSON(&cloudUpdateCloudGoods)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudUpdateCloudGoods.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}
	source.DB().Model(&model2.CloudGoods{}).Joins("INNER join  products on products.id = cloud_goods.product_id").Where("products.source = ?", cloudUpdateCloudGoods.ProductSource).Order("products.id desc").Pluck("products.id", &cloudUpdateCloudGoods.ProductIds)

	var cloudUpdateCloudGoodStart request.CloudUpdateCloudGoods
	cloudUpdateCloudGoodStart.ProductIds = cloudUpdateCloudGoods.ProductIds
	cloudUpdateCloudGoodStart.CloudCommon = cloudUpdateCloudGoods.CloudCommon
	cloudUpdateCloudGoodStart.GatherSuppliesId = cloudUpdateCloudGoods.GatherSuppliesId

	if len(cloudUpdateCloudGoods.ProductIds) > 5 {
		go service.CloudUpdateGoods(config, cloudUpdateCloudGoodStart)
		yzResponse.OkWithData("更新中", c)
	} else {
		if err, pushGoods := service.CloudUpdateGoods(config, cloudUpdateCloudGoodStart); err != nil {
			log.Log().Error("推送失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		} else {
			yzResponse.OkWithData(pushGoods, c)
		}
	}
}

// CloudUpdateGoods
// @Tags 云仓
// @Summary 更新商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudUpdateCloudGoods true "更新商品"
// @Success 200 {object}  request.CloudUpdateCloudGoods
// @Router /cloud/cloudUpdateGoods [post]
func CloudUpdateGoods(c *gin.Context) {
	var cloudUpdateCloudGoods request.CloudUpdateCloudGoods
	err := c.ShouldBindJSON(&cloudUpdateCloudGoods)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudUpdateCloudGoods.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}
	if len(cloudUpdateCloudGoods.ProductIds) > 5 {
		go service.CloudUpdateGoods(config, cloudUpdateCloudGoods)
		yzResponse.OkWithData("更新中", c)
	} else {
		if err, pushGoods := service.CloudUpdateGoods(config, cloudUpdateCloudGoods); err != nil {
			log.Log().Error("推送失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		} else {
			yzResponse.OkWithData(pushGoods, c)
		}
	}
}

// CloudUpdateGoods
// @Tags 云仓
// @Summary 更新所有标记为待更新的商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudUpdateCloudGoods true "更新所有标记为待更新的商品"
// @Success 200 {object}  request.CloudUpdateCloudGoods
// @Router /cloud/cloudUpdateGoods [post]
func CloudUpdateAllGoods(c *gin.Context) {
	var cloudUpdateCloudGoods request.CloudUpdateCloudGoods
	err := c.ShouldBindJSON(&cloudUpdateCloudGoods)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudUpdateCloudGoods.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	go service.CloudUpdateAllGoods(config, cloudUpdateCloudGoods)
	yzResponse.OkWithData("更新中", c)

}

// cloudAllPushGoods
// @Tags 云仓
// @Summary 推送全部商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudAllPush true "推送全部商品"
// @Success 200 {object}  request.CloudAllPush
// @Router /cloud/cloudAllPushGoods [post]
func CloudAllPushGoods(c *gin.Context) {
	var cloudAllPush request.CloudAllPush
	err := c.ShouldBindJSON(&cloudAllPush)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if cloudAllPush.CloudCategory3Id == 0 && cloudAllPush.CloudCategory2Id == 0 && cloudAllPush.CloudCategory1Id == 0 {
		yzResponse.FailWithMessage("请选择推送分类", c)
		return
	}
	if cloudAllPush.GoodsDes == "" {
		yzResponse.FailWithMessage("请添写商品关键字", c)
		return
	}
	if cloudAllPush.ProducingArea == "" {
		yzResponse.FailWithMessage("请添写产地", c)
		return
	}
	if cloudAllPush.DeliverArea == "" {
		yzResponse.FailWithMessage("请填写发货地", c)
		return
	}
	if cloudAllPush.FreightId == 0 {
		yzResponse.FailWithMessage("请选择运费模板", c)
		return
	}
	if cloudAllPush.AftersaleTime == 0 {
		yzResponse.FailWithMessage("请选择售后时长", c)
		return
	}
	if cloudAllPush.DelayCompensate == 0 {
		yzResponse.FailWithMessage("请选择发货延期", c)
		return
	}
	if cloudAllPush.Stags == "" {
		yzResponse.FailWithMessage("请选择服务标签", c)
		return
	}
	//yzResponse.OkWithMessage("开始推送(时间仅供参考),"+"00000000",c)
	err, config := common.InitCloudSetting(cloudAllPush.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, totalTime := service.CloudAllPushGoods(config, cloudAllPush); err != nil {
		log.Log().Error("推送失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("开始推送(时间仅供参考),"+totalTime, c)
	}
}

// UpdateCloudGoodsStock
// @Tags 云仓
// @Summary 一键更新云仓库存
// @accept application/json
// @Produce application/json
// @Param data body request.CloudCommon true "一键更新云仓库存"
// @Success 200 {object}  request.CloudCommon
// @Router /cloud/updateCloudGoodsStock [post]
func UpdateCloudGoodsStock(c *gin.Context) {
	var cloudCommon request.CloudCommon
	err := c.ShouldBindJSON(&cloudCommon)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(cloudCommon.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, total := service.UpdateCloudGoodsStock(config, cloudCommon); err != nil {
		log.Log().Error("推送失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(total, c)
	}
}

// GetCloudGoodsDetail
// @Tags 云仓
// @Summary 获取云仓商品详情
// @accept application/json
// @Produce application/json
// @Param data body request.GetFreight true "获取运费模板详情"
// @Success 200 {object}  request.GetFreight
// @Router /cloud/getCloudGoodsDetail [get]
func GetCloudGoodsDetail(c *gin.Context) {
	var getCloudGoodsDetail request.GetCloudGoodsDetail
	err := c.ShouldBindQuery(&getCloudGoodsDetail)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(getCloudGoodsDetail.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, Goods := service.CloudGetGoods(config, getCloudGoodsDetail); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(Goods, c)
	}
}

// getGatherSupplies
// @Tags 云仓
// @Summary 获取供应链
// @accept application/json
// @Produce application/json
// @Param data body request.GetFreight true "获取供应链"
// @Success 200 {object}  request.GetFreight
// @Router /cloud/getGatherSupplies [get]
func GetGatherSupplies(c *gin.Context) {
	if err, gatherSupplys := service.GetGatherSupplies(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gatherSupplys, c)
	}
}

// GetProductsList
// @Tags 云仓
// @Summary 获取全部推送记录
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取获取全部推送记录列表"
// @Success 200 {object} []model.Product
// @Router /api/cloud/getCloudPushGoodsMessageList [get]
func GetCloudPushGoodsMessageList(c *gin.Context) {
	var pageInfo request.CloudPushGoodsMessage
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCloudPushGoodsMessageList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 云仓订单导出
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "云仓订单导出"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/exportCloudOrder [get]
func ExportCloudOrder(c *gin.Context) {
	var orderSearch request.CloudOrderSearch
	err := c.ShouldBindQuery(&orderSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, config := common.InitCloudSetting(orderSearch.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, link := service.ExportCloudOrder(config, orderSearch); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(link, c)
	}
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 获取云仓订单在中单下单，定时版
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "获取云仓订单在中单下单，定时版"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/cronCloudOrderCreate [get]
func CronCloudOrderCreate(c *gin.Context) {
	var gatherSupplys []model.GatherSupply
	err := source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()
	//执行这个时 如果mq挂了会一直往mq里面放消息 导致重复下单 这个去掉（这个时配合定时任务下单使用的,队列不需要这个了）

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.Cloud.CloudIsSynOrder == 2 {
			log.Log().Error("云仓下单:已关闭同步订单!")
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		//go service.CloudOrderCreateStep1(config, 1, uint(item.ID))
		service.CloudOrderCreateNewStep1(config, 1, uint(item.ID))
		log.Log().Error("云仓:同步云仓发货信息到中台开始执行配置id" + strconv.Itoa(item.ID))
	}
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 获取云仓订单在中单下单，定时版
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "获取云仓订单在中单下单，定时版"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/cronCloudOrderCreate [get]
func CloudOrderCreateNewStep1APi(c *gin.Context) {
	var orderSearch request.CloudOrderCreateNewStep1APi
	err := c.ShouldBindJSON(&orderSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var gatherSupplys []model.GatherSupply
	err = source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()
	//执行这个时 如果mq挂了会一直往mq里面放消息 导致重复下单 这个去掉（这个时配合定时任务下单使用的,队列不需要这个了）

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.Cloud.CloudIsSynOrder == 2 {
			log.Log().Error("云仓下单:已关闭同步订单!")
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		//go service.CloudOrderCreateStep1(config, 1, uint(item.ID))
		go service.CloudOrderCreateNewStep1APi(config, 1, uint(item.ID), orderSearch.CreatedStartTime, orderSearch.CreatedEndTime)
		log.Log().Error("云仓:同步云仓发货信息到中台开始执行配置id" + strconv.Itoa(item.ID))
	}
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 获取云仓订单支付，定时版
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "获取云仓订单在中单下单，定时版"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/cronCloudOrdePay [get]
func CronCloudOrderPay(c *gin.Context) {
	var gatherSupplys []model.GatherSupply
	err := source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓定时支付:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓定时支付:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓定时支付:没有选择云仓下单用户!")
			continue
		}
		//go service.CloudOrderCreateStep1(config, 1, uint(item.ID))
		service.CronOrderPay(config, uint(item.ID))
		log.Log().Error("云仓:定时支付中台开始执行配置id" + strconv.Itoa(item.ID))
	}
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 同步中台与云仓商品下架状态，定时版
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "同步中台与云仓商品下架状态，定时版"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/cronCloudOrderCreate [get]
func CronCloudProductSoldOut(c *gin.Context) {
	var gatherSupplys []model.GatherSupply
	err := source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		go service.CronCloudProductSoldOutStep1(config, 1, uint(item.ID))
		log.Log().Error("云仓:同步中台与云仓商品下架状态" + strconv.Itoa(item.ID))
	}
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 同步中台与云仓商品下架状态，定时版
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "同步中台与云仓商品下架状态，定时版"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/cronCloudOrderCreate [get]
func CloudProductSoldOut(c *gin.Context) {
	var gatherSupplys []model.GatherSupply
	err := source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		go service.CronCloudProductSoldOutStep1(config, 1, uint(item.ID))
		log.Log().Error("云仓:同步中台与云仓商品下架状态" + strconv.Itoa(item.ID))
	}
	yzResponse.OkWithMessage("同步中，请勿频繁点击", c)
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 同步中台与云仓商品下架状态，中台删除product记录也没有发消息导致云仓没有下架 同步这部分下架状态
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "同步中台与云仓商品下架状态，中台删除product记录也没有发消息导致云仓没有下架 同步这部分下架状态"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/cronCloudOrderCreate [get]
func CloudProductDeleteSoldOutStep1(c *gin.Context) {
	var gatherSupplys []model.GatherSupply
	err := source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		go service.CloudProductDeleteSoldOutStep1(config, 1, uint(item.ID))
		log.Log().Error("云仓:同步中台与云仓商品下架状态" + strconv.Itoa(item.ID))
	}
	yzResponse.OkWithMessage("同步中，请勿频繁点击", c)
}

// ExportCloudOrder
// @Tags 云仓
// @Summary 同步中台与云仓商品上架状态，一键上架中台所以上架的云仓商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSearch true "同步中台与云仓商品下架状态，中台删除product记录也没有发消息导致云仓没有下架 同步这部分下架状态"
// @Success 200 {object}  request.CloudOrderSearch
// @Router /cloud/CloudProductOnStep1 [get]
func CloudProductOnStep1(c *gin.Context) {
	var orderSearch request.CloudProductOnStep1
	err := c.ShouldBindQuery(&orderSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if orderSearch.ProductSource == nil {
		yzResponse.FailWithMessage("来源参数必须填写", c)
		return
	}
	if orderSearch.Page == 0 {
		orderSearch.Page = 1
	}
	var gatherSupplys []model.GatherSupply
	err = source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("云仓下单:请启用mq服务")
		return
	}
	//service.CloudOrderCreateSystemError()

	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		go service.CloudProductOnStep1(config, orderSearch.Page, uint(item.ID), *orderSearch.ProductSource)
		log.Log().Error("云仓:同步中台与云仓商品下架状态" + strconv.Itoa(item.ID))
	}
	yzResponse.OkWithMessage("同步中，请勿频繁点击", c)
}

// cronCloudSendOrder
// @Tags 云仓
// @Summary 获取中台订单（是云仓下单的）未发货的查询云仓是否发货 如果发货则中台发货 定时任务
// @accept application/json
// @Produce application/json
// @Router /cloud/cronCloudSendOrder [get]
func CronCloudSendOrder(c *gin.Context) {
	var gatherSupplys []model.GatherSupply
	err := source.DB().Where("category_id = ?", 1).Where("deleted_at is NULL").Find(&gatherSupplys).Error
	if err != nil {
		log.Log().Error("云仓下单:没有胜天半子的配置!", zap.Any("err", err))
		return
	}
	for _, item := range gatherSupplys {
		err, config := common.InitCloudSetting(uint(item.ID))

		if err != nil {
			log.Log().Error("云仓下单:定时执行获取设置失败!"+strconv.Itoa(item.ID), zap.Any("err", err))
			continue
		}
		if config.BaseInfo.AppSecret == "" || config.BaseInfo.AppKey == "" {
			log.Log().Error("云仓下单:没有配置密钥和key!")
			continue
		}
		if config.Cloud.CloudUserId == 0 {
			log.Log().Error("云仓下单:没有选择云仓下单用户!")
			continue
		}
		go service.CronCloudSendOrder(config, uint(item.ID))
		log.Log().Error("云仓:同步云仓发货信息到中台开始执行配置id" + strconv.Itoa(item.ID))
	}
}

// GetOrder
// @Tags 云仓
// @Summary 选择云仓订单下单到中台
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "选择云仓订单下单到中台"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/cloudOrderCreate [post]
func CloudOrderCreate(c *gin.Context) {
	var cloudOrderCreate request.CloudOrderCreate
	err := c.ShouldBindJSON(&cloudOrderCreate)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("mq", zap.Any("err", err))
		yzResponse.FailWithMessage("请启用mq服务", c)
		return
	}
	err, config := common.InitCloudSetting(cloudOrderCreate.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//手动下单不限制
	//if config.Cloud.CloudIsSynOrder == 2 {
	//	log.Log().Error("云仓下单:已关闭同步订单!")
	//	yzResponse.FailWithMessage("已关闭同步订单", c)
	//
	//	return
	//}
	if config.Cloud.CloudUserId == 0 {
		log.Log().Error("请去配置中设置云仓下单用户!")
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage("请去配置中设置云仓下单用户", c)
		return
	}

	if err = service.CloudOrderCreate(config, cloudOrderCreate); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 监听中台订单发货 中台订单发货 云仓订单随之一起发货
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "监听中台订单发货 中台订单发货 云仓订单随之一起发货"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/lisOrderSend [post]
func LisOrderSend(c *gin.Context) {
	var CeshiSendCloudOrder request.CeshiLisOrderSend
	err := c.ShouldBindJSON(&CeshiSendCloudOrder)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.LisOrderSend(uint(CeshiSendCloudOrder.OrderId), 2); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 获取自动发货失败的订单重新发货
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "获取自动发货失败的订单重新发货"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/getSendErrorOrder [post]
func GetSendErrorOrder(c *gin.Context) {
	if err := service.GetSendErrorOrder(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 指定code重新发货
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "指定code重新发货"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/getSendErrorOrder [post]
func UpdateSendErrorOrder(c *gin.Context) {
	var updateSendErrorOrder request.UpdateSendErrorOrder
	_ = c.ShouldBindJSON(&updateSendErrorOrder)
	if updateSendErrorOrder.CompanyCode == "" {
		yzResponse.FailWithMessage("请提交需要重新发货的快递code", c)
		return
	}
	if err := service.UpdateSendErrorOrder(updateSendErrorOrder.CompanyCode); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
	}
}

// GetProductsList
// @Tags 云仓
// @Summary 获取云仓订单记录表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取获取云仓订单记录表列表"
// @Success 200 {object} []model.Product
// @Router /api/cloud/getCloudOrderRecordList [get]
func GetCloudOrderRecordList(c *gin.Context) {
	var pageInfo request.CloudOrderRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCloudOrderRecordList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CloudOrderSend
// @Tags 云仓
// @Summary 云仓订单发货新（选择指定商品进行发货）
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSend true "云仓订单发货新（选择指定商品进行发货）"
// @Success 200 {object}  request.CloudOrderSend
// @Router /cloud/cloudOrderSendNew [post]
func CloudOrderSendNew(c *gin.Context) {
	var cloudOrderSendNew request.CloudOrderSendNew
	err := c.ShouldBindJSON(&cloudOrderSendNew)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(cloudOrderSendNew.CloudOrderSendApi) == 0 {
		log.Log().Error("请填写发货参数", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择商品添加发货参数", c)
		return
	}
	for _, item := range cloudOrderSendNew.CloudOrderSendApi {
		if item.GoodsOrderSn == "" {
			log.Log().Error("请选择发货商品", zap.Any("err", err))
			yzResponse.FailWithMessage("请选择发货商品", c)
			return
		}
		if item.ExpressName == "" {
			log.Log().Error("请选择物流公司名称", zap.Any("err", err))
			yzResponse.FailWithMessage("请选择物流公司名称", c)
			return
		}
		if item.ExpressSn == "" {
			log.Log().Error("请添写物流单号", zap.Any("err", err))
			yzResponse.FailWithMessage("请添写物流单号", c)
			return
		}
	}
	//yzResponse.OkWithData(cloudOrderSendNew,c)
	err, config := common.InitCloudSetting(cloudOrderSendNew.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, data := service.CloudOrderSendNew(config, cloudOrderSendNew); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

// CloudOrderSend
// @Tags 云仓
// @Summary 云仓订单补发货
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderSend true "云仓订单补发货"
// @Success 200 {object}  request.CloudOrderSend
// @Router /cloud/cloudOrderSendNew [post]
func UpdateCloudOrderSend(c *gin.Context) {
	var cloudOrderSendNew request.CloudOrderSendNew
	err := c.ShouldBindJSON(&cloudOrderSendNew)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(cloudOrderSendNew.CloudOrderSendApi) == 0 {
		log.Log().Error("请填写发货参数", zap.Any("err", err))
		yzResponse.FailWithMessage("请选择商品添加发货参数", c)
		return
	}
	for _, item := range cloudOrderSendNew.CloudOrderSendApi {
		if item.GoodsOrderSn == "" {
			log.Log().Error("请选择发货商品", zap.Any("err", err))
			yzResponse.FailWithMessage("请选择发货商品", c)
			return
		}
		if item.ExpressName == "" {
			log.Log().Error("请选择物流公司名称", zap.Any("err", err))
			yzResponse.FailWithMessage("请选择物流公司名称", c)
			return
		}
		if item.ExpressSn == "" {
			log.Log().Error("请添写物流单号", zap.Any("err", err))
			yzResponse.FailWithMessage("请添写物流单号", c)
			return
		}
	}
	//yzResponse.OkWithData(cloudOrderSendNew,c)
	err, config := common.InitCloudSetting(cloudOrderSendNew.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err, c)
		return
	}

	if err, data := service.UpdateCloudOrderSend(config, cloudOrderSendNew); err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 云仓-手动下单时的订单详情
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "云仓-手动下单时的订单详情"
// @Success 200 {object}  request.CloudOrderDetatil
// @Router /cloud/getCloudOrderProduct [get]
func GetCloudOrderProduct(c *gin.Context) {
	var pageInfo request.CloudOrderDetatil
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(pageInfo.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, Data, cloudOrder := service.GetCloudOrderProduct(config, pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(g.Map{"data": Data, "cloud_order": cloudOrder}, c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 选择云仓订单下单到中台
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "选择云仓订单下单到中台"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/manualCloudOrderCreate [post]
func ManualCloudOrderCreate(c *gin.Context) {
	var cloudOrderCreate request.ManualCloudOrderCreate
	err := c.ShouldBindJSON(&cloudOrderCreate)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	//var expressNo = "3213213213:3085"
	//if strings.Index(expressNo,":") != -1{
	//	expressNo = expressNo[0:strings.Index(expressNo,":")]
	//}
	//yzResponse.OkWithData(expressNo,c)
	channel := source.MQ()
	if channel == nil {
		log.Log().Error("mq", zap.Any("err", err))
		yzResponse.FailWithMessage("请启用mq服务", c)
		return
	}
	err, config := common.InitCloudSetting(cloudOrderCreate.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if config.Cloud.CloudUserId == 0 {
		log.Log().Error("请去配置中设置云仓下单用户!")
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage("请去配置中设置云仓下单用户", c)
		return
	}
	if cloudOrderCreate.Goods == nil {
		yzResponse.FailWithMessage("请提交下单商品参数", c)
		return
	}
	var code int
	err, code = service.ManualCloudOrderCreate(config, cloudOrderCreate)

	if err != nil {
		log.Log().Error("云仓手动下单错误：", zap.Any("err", err))
		if code == 2 {
			yzResponse.OkWithMessage("下单成功,支付失败"+err.Error(), c)
		} else {
			yzResponse.FailWithMessage(err.Error(), c)
		}
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 手动关联云仓与中台商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "选择云仓订单下单到中台"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/cloudOrderCreate [post]
func ProductRelevanceCloudGoods(c *gin.Context) {
	var cloudOrderCreate request.ProductRelevanceCloudGoods
	err := c.ShouldBindJSON(&cloudOrderCreate)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(cloudOrderCreate.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = service.ProductRelevanceCloudGoods(config, cloudOrderCreate); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("成功", c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 删除中台推送到云仓重复的商品
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "删除中台推送到云仓重复的商品"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/deleteCloudGoods [post]
func DeleteCloudGoods(c *gin.Context) {
	var cloudCommon request.DeleteProductRelevanceCloudGoods
	err := c.ShouldBindJSON(&cloudCommon)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, config := common.InitCloudSetting(cloudCommon.GatherSuppliesId)

	if err != nil {
		log.Log().Error("获取设置失败!", zap.Any("err", err))
		//err := fmt.Sprintf("%s", err)
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	errs := service.DeleteCloudGoods(config, cloudCommon)
	if errs != "" {
		log.Log().Error("删除失败", zap.Any("err", errs))
		yzResponse.FailWithMessage(errs, c)
		return
	}
	yzResponse.OkWithMessage("成功", c)

}

// GetOrder
// @Tags 云仓
// @Summary 修改云仓订单记录的状态变为已下线处理
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "修改云仓订单记录的状态变为已下线处理"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/updateCloudOrderIsOffline [GET]
func UpdateCloudOrderIsOffline(c *gin.Context) {
	var cloudCommon request.UpdateCloudOrderIsOffline
	err := c.ShouldBindQuery(&cloudCommon)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.UpdateCloudOrderIsOffline(cloudCommon)
	if err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)

}

// GetOrder
// @Tags 云仓
// @Summary 对应中台与云仓快递公司的关系
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "对应中台与云仓快递公司的关系"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/createMiddlegroundCloudExpressMatching [POST]
func CreateMiddlegroundCloudExpressMatching(c *gin.Context) {
	var middlegroundCloudExpressMatching model2.MiddlegroundCloudExpressMatching
	err := c.ShouldBindJSON(&middlegroundCloudExpressMatching)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.CreateMiddlegroundCloudExpressMatching(middlegroundCloudExpressMatching)
	if err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)

}

// GetOrder
// @Tags 云仓
// @Summary 修改对应关系
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "修改对应关系"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/updateMiddlegroundCloudExpressMatching [POST]
func UpdateMiddlegroundCloudExpressMatching(c *gin.Context) {
	var middlegroundCloudExpressMatching model2.MiddlegroundCloudExpressMatching
	err := c.ShouldBindJSON(&middlegroundCloudExpressMatching)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.UpdateMiddlegroundCloudExpressMatching(middlegroundCloudExpressMatching)
	if err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)

}

// GetOrder
// @Tags 云仓
// @Summary 删除
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "删除"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/deleteMiddlegroundCloudExpressMatching [POST]
func DeleteMiddlegroundCloudExpressMatching(c *gin.Context) {
	var middlegroundCloudExpressMatching model2.MiddlegroundCloudExpressMatching
	err := c.ShouldBindJSON(&middlegroundCloudExpressMatching)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.DeleteMiddlegroundCloudExpressMatching(middlegroundCloudExpressMatching)
	if err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)

}

// GetProductsList
// @Tags 云仓
// @Summary 获取可推送至云仓的商品，与推送状态
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取获取可推送至云仓的商品，与推送状态列表"
// @Success 200 {object} []model.Product
// @Router /api/cloud/getMiddlegroundCloudExpressMatchingList [get]
func GetMiddlegroundCloudExpressMatchingList(c *gin.Context) {
	var pageInfo request.MiddlegroundCloudExpressMatchinSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetMiddlegroundCloudExpressMatchingList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetOrder
// @Tags 云仓
// @Summary 修改推送/更新商品记录变为已处理
// @accept application/json
// @Produce application/json
// @Param data body request.CloudOrderDetatil true "修改推送/更新商品记录变为已处理"
// @Success 200 {object}  request.CloudOrderCreate
// @Router /cloud/updateCloudPushGoodsMessageType [GET]
func UpdateCloudPushGoodsMessageType(c *gin.Context) {
	var cloudCommon request.UpdateCloudPushGoodsMessageType
	err := c.ShouldBindQuery(&cloudCommon)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.UpdateCloudPushGoodsMessageType(cloudCommon)
	if err != nil {
		log.Log().Error("修改失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功", c)

}

// 清除一个月之前的商品同步记录日志
func ClearCloudPushGoodsMessages(c *gin.Context) {
	service.ClearCloudPushGoodsMessages()

	yzResponse.OkWithMessage("成功", c)

}
