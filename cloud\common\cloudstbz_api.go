package common

import (
	"bytes"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"io/ioutil"
	"math/rand"
	"net/http"
	gsetting "public-supply/setting"
	"regexp"
	model3 "region/model"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
)

type RequestUrl string

var Request RequestData

type RequestData struct {
	Query  map[string]string `json:"query"`  //请求query参数
	Body   []byte            `json:"body"`   //请求body参数
	Url    string            `json:"Url"`    //请求链接
	Method string            `json:"method"` //请求方式
	Config SupplySetting     `json:"config"`
	Host   string            `json:"host"` //请求链接
}

// 云仓订单列表返回数据结构体
type CloudOrderList struct {
	Count int          `json:"count" form:"count"` //分页
	List  []CloudOrder `json:"list" form:"list"`   //分页
}
type CloudOrder struct {
	Order      CloudOrderData        `json:"order" form:"order"`             //分页
	OrderGoods []CloudOrderGoodsData `json:"order_goods" form:"order_goods"` //分页
}
type CloudOrderData struct {
	Id              int           `json:"id" form:"id"`                               //分页
	OrderSn         string        `json:"order_sn" form:"order_sn"`                   //订单号
	Status          int           `json:"status" form:"status"`                       //订单状态 0待支付 1待发货2待收货3已完成
	CreatedTime     int           `json:"created_time" form:"created_time"`           //创建时间
	CancelTime      int           `json:"cancel_time" form:"cancel_time"`             //取消时间
	FinishTime      int           `json:"finish_time" form:"finish_time"`             //完成时间
	SendTime        int           `json:"send_time" form:"send_time"`                 //发货时间
	DispatchPrice   int           `json:"dispatch_price" form:"dispatch_price"`       //运费 单位为分
	ThirdOrdersn    int           `json:"third_order_sn" form:"third_order_sn"`       //第三方订单号
	OrderTotalPrice uint          `json:"order_total_price" form:"order_total_price"` //订单总金额 单位为分
	ServiceFee      int           `json:"service_fee" form:"service_fee"`             //服务费？
	Remark          string        `json:"remark" form:"remark"`                       //订单备注
	SellerRemark    string        `json:"seller_remark" form:"seller_remark"`         //商家备注
	RealName        string        `json:"real_name" form:"real_name"`                 //收货人姓名
	Mobile          string        `json:"mobile" form:"mobile"`                       //手机号
	Provice         string        `json:"provice" form:"provice"`                     //省
	City            string        `json:"city" form:"city"`                           //市
	District        string        `json:"district" form:"district"`                   //区
	Street          string        `json:"street" form:"street"`                       //县
	Address         string        `json:"address" form:"address"`                     //详细地址
	ProviceModel    model3.Region `json:"provice_model" form:"provice_model"`         //省 中台数据
	CityModel       model3.Region `json:"city_model" form:"city_model"`               //市 中台数据
	CountyModel     model3.Region `json:"county_model" form:"county_model"`           //区 中台数据
	TownModel       model3.Region `json:"town_model" form:"town_model"`               //县 中台数据
}
type CloudOrderGoodsData struct {
	Id               int    `json:"id" form:"id"`                                 //分页
	GoodsOrderSn     string `json:"goods_order_sn" form:"goods_order_sn"`         //子订单号
	OrderId          int    `json:"order_id" form:"order_id"`                     //父订单id
	GoodsId          int    `json:"goods_id" form:"goods_id"`                     //商品Id
	Total            uint   `json:"total" form:"total"`                           //商品数量
	GoodsOptionId    uint   `json:"goods_option_id" form:"goods_option_id"`       //sku
	Title            string `json:"title" form:"title"`                           //商品名称
	GoodsOptionTitle string `json:"goods_option_title" form:"goods_option_title"` //商品规格名称
	GoodsPrice       int    `json:"goods_price" form:"goods_price"`               //商品金额 单位为分
	GoodsStatus      int    `json:"goods_status" form:"goods_status"`             //发货状态 0:未发货,1:已发货,2:已收货,3:配货中,4：拒收
	PayStatus        int    `json:"pay_status" form:"pay_status"`                 //订单状态 0:未付款,1:已付款,2申请退款,3,退款中,4已退款5退款申请失败
	CreatedTime      int    `json:"created_time" form:"created_time"`             //创建时间
}

const (
	GetFreightList       RequestUrl = "/v2/freight/getFreightList"           //获取运费模板
	GetCateBusinessList  RequestUrl = "/v2/catebusiness/getCateBusinessList" //获取分类
	GetStagsList         RequestUrl = "/v2/goods/getStagsList"               //获取服务标签
	GetGoodsList         RequestUrl = "/v2/goods/getGoodsList"               //获取商品列表
	UpdateGoodsOnsale    RequestUrl = "/v2/goods/updateGoodsOnsale"          //云仓商品上下架
	DeleteGoods          RequestUrl = "/v2/goods/deleteGoods"                //云仓商品删除
	GetFreight           RequestUrl = "/v2/freight/getFreight"               //获取运费模板详情
	DeleteFreight        RequestUrl = "/v2/freight/deleteFreight"            //删除运费模板
	AddFreight           RequestUrl = "/v2/freight/addFreight"               //添加运费模板
	UpdateFreight        RequestUrl = "/v2/freight/updateFreight"            //修改运费模板
	GetCloudOrderList    RequestUrl = "/v2/order/getOrderList"               //获取订单列表
	GetDeliverList       RequestUrl = "/v2/deliver/getDeliverList"           //获取物流公司
	GetOrder             RequestUrl = "/v2/order/getOrder"                   //获取订单详情
	CloudOrderSend       RequestUrl = "/v2/order/sends"                      //订单发货
	CloudOrderAgainSends RequestUrl = "/v2/order/againSends"                 //重新发货

	GetRefundList       RequestUrl = "/v2/refund/getRefundList"   //售后列表
	GetRefund           RequestUrl = "/v2/refund/getRefund"       //获取售后详情
	RefundAgree         RequestUrl = "/v2/refund/agree"           //售后同意
	RefundReject        RequestUrl = "/v2/refund/reject"          //售后同意
	GetAddressList      RequestUrl = "/v2/refund/getAddressList"  //获取退货地址
	GetRegionList       RequestUrl = "/v2/address/getAddressList" //获取省市区县
	AddRefundAddress    RequestUrl = "/v2/refund/addAddress"      //添加退货地址
	GetRefundAddress    RequestUrl = "/v2/refund/getAddress"      //获取退货地址详情
	UpdateRefundAddress RequestUrl = "/v2/refund/updateAddress"   //修改退货地址
	DeleteRefundAddress RequestUrl = "/v2/refund/deleteAddress"   //删除退货地址
	CloudAddGoods       RequestUrl = "/v2/goods/addGoods"         //添加云仓商品
	CloudUpdateGoods    RequestUrl = "/v2/goods/updateGoods"      //修改云仓商品
	CloudGetGoods       RequestUrl = "/v2/goods/getGoods"         //获取云仓商品详情

	CloudUpdateGoodsStock RequestUrl = "/v2/goods/updateGoodsStock" //更新云仓库存

	CloudGetDeliver RequestUrl = "/v2/deliver/getDeliver" //获取物流详情

)

// 云仓不显示的供应链商品分类
func ExcludeGatherSupplysCategoryIds() (ids []int) {
	ids = append(ids, 1)  //胜天半子
	ids = append(ids, 98) //福禄
	return
}

type SupplySetting struct {
	BaseInfo   BaseInfoData            `json:"baseInfo"`
	UpdateInfo gsetting.UpdateInfoData `json:"update"`
	Pricing    gsetting.PricingData    `json:"pricing"`
	Management gsetting.Management     `json:"management"`
	Cloud      gsetting.Cloud          `json:"cloud"`
}
type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}
type APIResult struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 云仓初始化
func InitCloudSetting(gatherSupplyID uint) (err error, config SupplySetting) {
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		log.Log().Error("云仓设置：获取供应链key设置失败", zap.Any("err", err))
		err = errors.New("云仓设置：获取供应链key设置失败" + err.Error())

		return
	}
	err = json.Unmarshal([]byte(setting.Value), &config)

	if err != nil {
		log.Log().Error("云仓设置：获取失败", zap.Any("err", err))
		err = errors.New("云仓设置：获取失败" + err.Error())
		return
	}
	return
}

func RequestApi(url string, method string, query map[string]string, body map[string]interface{}, config SupplySetting) (res *APIResult, err error) {
	Request.Query = query
	bodyData, _ := json.Marshal(body)
	Request.Body = bodyData
	Request.Url = url
	Request.Method = method
	Request.Config = config
	Request.Host = "http://sapi.jxhh.com"
	if method == "POST" {
		res, err = ClientPost()

	}
	return
}
func RequestApiJson(url string, method string, query map[string]string, body []byte, config SupplySetting) (res *APIResult, err error) {
	Request.Query = query
	Request.Body = body
	Request.Url = url
	Request.Method = method
	Request.Config = config
	Request.Host = "http://sapi.jxhh.com"
	if method == "POST" {
		res, err = ClientPost()
	}
	return
}

// Api-App-Keyrvvnyabm2nv1ywneApi-NonceWDCREWKTApi-Time-Stamp164635984064247544C17D113E9D397C91F3CF3CD7105
// Api-App-Keyrvvnyabm2nv1ywneApi-Nonce47cd8b0fddaaa8844174b9f75802e338Api-Time-Stamp164636008424947544C17D113E9D397C91F3CF3CD7105
func ClientPost() (result *APIResult, err error) {
	client := &http.Client{Timeout: 5 * time.Minute}
	//if Request.Body = nil
	body := bytes.NewReader(Request.Body)

	req, _ := http.NewRequest("POST", Request.Host+Request.Url, body)

	req = SetClentHeader(req)

	resp, err := client.Do(req)
	//err = errors.New("错误");
	//resp = nil
	if err != nil {
		//if resp != nil{
		//	defer resp.Body.Close()
		//}
		return
	}
	defer resp.Body.Close()

	res, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Log().Error("云仓:返回参数异常", zap.Any("err", err))
		err = errors.New("云仓:返回参数异常" + err.Error())
		return
	}

	err = json.Unmarshal(res, &result)

	if err != nil {
		log.Log().Error("云仓:JSON异常", zap.Any("err", err))
		err = errors.New("云仓:JSON异常" + err.Error())
		//fmt.Println("云仓:JSON异常"+err.Error())
		return
	}
	if result.Message == "" {
		result.Message = "API异常"
	}
	//log.Log().Info("云仓返回", zap.Any("res",res))
	return
}
func SetClentHeader(request *http.Request) (req *http.Request) {
	var timeStamp = time.Now().UnixNano() / int64(1e6)
	var Nonce = RandString(16)
	var item = make(map[string]string)
	item["Api-App-Key"] = Request.Config.BaseInfo.AppKey
	item["Api-Nonce"] = Nonce
	item["Api-Time-Stamp"] = strconv.FormatInt(timeStamp, 10)
	sign := createSign(item)

	request.Header.Add("Api-App-Key", Request.Config.BaseInfo.AppKey)
	request.Header.Add("Api-Time-Stamp", item["Api-Time-Stamp"])
	request.Header.Add("Api-Nonce", Nonce)
	request.Header.Add("Api-Sign", sign)

	return request
}

func RandString(len int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	bytes1 := make([]byte, len)
	for i := 0; i < len; i++ {
		b := r.Intn(26) + 65
		bytes1[i] = byte(b)
	}
	return string(bytes1)
}

// 生成签名
func createSign(item map[string]string) (sign string) {

	//allMaps := make(map[string]string)
	//
	//for k, v := range Request.Query {
	//	allMaps[k] = v
	//}
	//
	//keys := make([]string, 0)
	//for k := range allMaps {
	//	keys = append(keys, k)
	//
	//}
	//
	//sort.Strings(keys)
	//
	paramsString := ""
	//
	//
	////ksort
	//var itemKeys []string
	//for k := range item {
	//	itemKeys = append(itemKeys, k)
	//}
	//sort.Strings(itemKeys)
	//
	////拼接
	//for _, k := range itemKeys {
	//	//fmt.Println("key:", k, "Value:", item[k])
	//	paramsString +=  k  + item[k]
	//}
	//
	//for _, k := range keys {
	//	paramsString += k + allMaps[k]
	//}

	//item["Api-App-Key"] = Request.Config.BaseInfo.AppKey
	//item["Api-Nonce"] = Nonce
	//item["Api-Time-Stamp"] = strconv.FormatInt(timeStamp, 10)

	paramsString = "Api-App-Key" + item["Api-App-Key"] + "Api-Nonce" + item["Api-Nonce"] + "Api-Time-Stamp" + item["Api-Time-Stamp"]

	//fmt.Println("paramsString1::"+paramsString)
	//re3, _ := regexp.Compile(`\s`)
	re3 := regexp.MustCompile("\\s+")
	body := re3.ReplaceAllString(string(Request.Body), "")

	paramsString += Request.Config.BaseInfo.AppSecret + body
	//log.Log().Info("importCloudPushGoods:paramsString2", zap.Any("paramsString", paramsString))
	sha1hash := sha1.New()
	sha1hash.Write([]byte(paramsString))
	sha1String := hex.EncodeToString(sha1hash.Sum([]byte("")))

	md5hash := md5.New()
	md5hash.Write([]byte(sha1String))
	md5String := hex.EncodeToString(md5hash.Sum([]byte("")))
	sign = strings.ToUpper(md5String)

	// fmt.Println("参与签名计算的参数：" + paramsString)
	// fmt.Println("sha1结果：" + sha1String)
	// fmt.Println("md5结果：" + md5String)
	// fmt.Println("签名结果：" + sign)

	return
}
