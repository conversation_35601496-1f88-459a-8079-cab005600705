package cron

import (
	v1 "cloud/api/v1"
	"github.com/gin-gonic/gin"
	"yz-go/cron"
)

// /获取推送到云仓的中台下架商品，在云仓进行下架（避免因为监听出现问题而导致部分没有下架的问题）关闭这个定时任务 现在队列比较稳定很少出现不下架的情况
// 获取云仓订单，在中台进行下单
func SynCloudProductSoldOut() {
	var c *gin.Context
	task := cron.Task{
		Key:  "synCloudProductSoldOut",
		Name: "获取推送到云仓的中台下架商品，在云仓进行下架（避免因为监听出现问题而导致部分没有下架的问题）",
		Spec: "0 0 3 * * *",
		Handle: func(task cron.Task) {
			v1.CronCloudProductSoldOut(c)
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}
