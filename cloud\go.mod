module cloud

go 1.21

require (
	gin-vue-admin v1.0.0
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/gin-gonic/gin v1.6.3
	github.com/gogf/gf v1.16.9
	github.com/olivere/elastic/v7 v7.0.24
	github.com/shopspring/decimal v1.3.1
	github.com/streadway/amqp v1.1.0
	go.uber.org/zap v1.16.0
	golang.org/x/net v0.0.0-20220722155237-a158d28d115b
	gorm.io/gorm v1.25.5
	order v1.0.0
	payment v1.0.0
	product v1.0.0
	public-supply v1.0.0
	region v1.0.0
	shipping v1.0.0
	shopping-cart v1.0.0
	trade v1.0.0
	yz-go v1.0.0
)

replace (
	after-sales => ../after-sales
	application => ../application
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product v1.0.0 => ../product
	public-supply => ../public-supply
	purchase-account => ../purchase-account
	region v1.0.0 => ../region
	shipping => ../shipping
	shop => ../shop
	shopping-cart => ../shopping-cart
	trade => ../trade
	user => ../user
	wechatpay-go-main => ../wechatpay-go-main
	yz-go v1.0.0 => ../yz-go
)
