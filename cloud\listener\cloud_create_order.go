package listener

import (
	"cloud/common"
	"cloud/model"
	"cloud/mq"
	"cloud/service"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	orderModel "order/model"
	"yz-go/component/log"
	"yz-go/source"
)

// 云仓监听订单下单 往中台下单
func CloudCreateOrderListenerHandles() {

	mq.PushHandles("CloudCreateOrder", func(cloudMsg mq.CloudMessage) error {
		var err error
		log.Log().Debug("云仓自动下单", zap.Any("云仓表订单id", cloudMsg.CloudOrderId))
		//云仓有新的订单 需要在中台下单
		if cloudMsg.MessageType == mq.CloudCreateOrder {
			var config common.SupplySetting
			//赋值请求第三方需要的key和密钥 以及下单用户id
			config.BaseInfo.AppKey = cloudMsg.AppKey
			config.BaseInfo.AppSecret = cloudMsg.AppSecret
			config.Cloud.CloudUserId = cloudMsg.UserId
			var cloudOrder model.CloudOrder
			err = source.DB().Model(&model.CloudOrder{}).Where("id = ?", cloudMsg.CloudOrderId).First(&cloudOrder).Error
			if err != nil {
				return nil
			}
			if cloudOrder.ID == 0 {
				log.Log().Error("云仓下单:下单错误下单记录不存在不执行", zap.Any("CloudOrderId", cloudMsg.CloudOrderId))
				return nil
			}
			var orderData orderModel.Order
			err = source.DB().Model(&orderModel.Order{}).Where("third_order_sn = ?", cloudOrder.CloudOrderSn).First(&orderData).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("云仓下单:下单错误,数据库错误", zap.Any("err", err), zap.Any("CloudOrderSn", cloudOrder.CloudOrderSn))
				return nil
			}
			if orderData.ID != 0 {
				log.Log().Error("云仓下单:下单错误,已下单直接跳过", zap.Any("CloudOrderSn", cloudOrder.CloudOrderSn))
				return nil
			}

			var code int
			//此处变为队列执行
			err, code = service.CloudOrderCreateNewStep4(config, cloudMsg.Data, cloudOrder)
			if err != nil {
				log.Log().Error("云仓下单:下单错误", zap.Any("err", err), zap.Any("CloudOrderSn", cloudOrder.CloudOrderSn))
				var cloudOrderUpdate model.CloudOrder
				//支付错误 不变为-1 再次请求时直接执行支付 (变为定时任务支付 已下单不就不会再走这个)
				if code == 1 {
					cloudOrderUpdate.Status = -1
					cloudOrderUpdate.ErrorMsg = err.Error()
					//cloudOrder.SynStatus = 1 //因为同步失败状态变为待同步
					source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrderUpdate)
				} else {
					cloudOrderUpdate.ErrorMsg = err.Error()
					source.DB().Where("id = ?", cloudOrder.ID).Updates(&cloudOrderUpdate)
				}
				return nil
			}
		}
		return nil
	})
}
