package listener

import (
	"cloud/service"
	"fmt"
	"order/mq"
)

func OrderSendListenerHandles() {
	fmt.Println("云仓监听订单发货")

	mq.PushHandles("CloudOrderSend", func(orderMsg mq.OrderMessage) error {
		//云仓发货   监听中台发货与部分发货
		if orderMsg.MessageType == mq.Sent || orderMsg.MessageType == mq.Sending{
			_ = service.LisOrderSend(orderMsg.OrderID, 2) //错误内部都有记录
		}
		//修改云仓发货 （云仓API仅能修改一次发货信息。多次请自行去云仓后台修改）
		if orderMsg.MessageType == mq.UpdateSend {
			_ = service.LisOrderSend(orderMsg.OrderID, 1)
		}
		return nil
	})
}
