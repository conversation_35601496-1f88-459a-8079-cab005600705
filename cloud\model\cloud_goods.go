package model

import (
	"yz-go/model"
	"yz-go/source"
)

// 云仓商品关联表
type CloudGoods struct {
	source.Model
	ProductId      uint                 `json:"product_id" form:"product_id" gorm:"column:product_id;comment:中台商品id;index"`                   //中台商品id
	CloudProductId uint                 `json:"cloud_product_id" form:"cloud_product_id" gorm:"column:cloud_product_id;comment:云仓商品id;index"` //云仓商品id
	IsUpdate       int                  `json:"is_update"  form:"is_update" gorm:"column:is_update;comment:是否修改1是0否;"`                        // 是否修改1是0否
	IsDelete       int                  `json:"is_delete"  form:"is_delete" gorm:"column:is_delete;comment:是否待删除1是0否;"`                       // 是否待删除1是0否
	Title          string               `json:"title" form:"title" gorm:"column:title;comment:中台商品名称;"`                                       //中台商品名称
	Imgs           model.JsonStringList `json:"imgs" form:"imgs" gorm:"column:imgs;comment:推送到云仓的相册图json数组;type:text;"`                       // 相册图json数组

	GatherSuppliesId uint `json:"gather_supplies_id" form:"gather_supplies_id" gorm:"column:gather_supplies_id;comment:供应链id;index"` //供应链id
}

/*
*

	中台与云仓快递对应表
*/
type MiddlegroundCloudExpressMatching struct {
	source.Model
	Code      string `json:"code" form:"code" gorm:"column:code;comment:中台快递code;"`                    //中台快递code
	Name      string `json:"name" form:"name" gorm:"column:name;comment:中台快递名称;"`                      //中台快递名称
	CloudCode string `json:"cloud_code"  form:"cloud_code" gorm:"column:cloud_code;comment:云仓快递code;"` // 云仓快递code
	CloudName string `json:"cloud_name"  form:"cloud_name" gorm:"column:cloud_name;comment:云仓快递名称;"`   // 云仓快递名称
}

/*
*

	云仓有的城市的街道是当区级返回的 需要处理否则匹配不上 这里存需要处理的城市
*/
type CloudAreaName struct {
	source.Model
	Name  string `json:"name" form:"name" gorm:"column:name;comment:云仓地区名称;"`           //云仓地区名称
	Level int    `json:"level"  form:"level" gorm:"column:level;default:2;comment:级别;"` // 级别 暂时级别只有2 后续如果有调整 代码逻辑也需要调整
}

/*
*

	地址匹配失败的 后来手动对应的 这里记录一下 以后直接查询这里的
*/
type CloudAreaMatching struct {
	source.Model
	Province string `json:"province"` //省
	City     string `json:"city"`     //市
	Area     string `json:"area"`     //区
	Street   string `json:"street"`   //街道

	CloudProvince string `json:"cloud_province"` //云仓省
	CloudCity     string `json:"cloud_city"`     //云仓市
	CloudArea     string `json:"cloud_area"`     //云仓区
	CloudStreet   string `json:"cloud_street"`   //云仓街道
}

// 推送商品至云仓消息表
type CloudPushGoodsMessage struct {
	ID               uint              `json:"id" form:"id" gorm:"primarykey"`
	CreatedAt        *source.LocalTime `json:"created_at"`
	UpdatedAt        *source.LocalTime `json:"updated_at"`
	Batch            string            `json:"batch" form:"batch" gorm:"column:batch;comment:批次（同样的属于 同一批次）;"`                               //批次（同样的属于 同一批次）
	ProductIds       string            `json:"product_ids" form:"product_ids" gorm:"column:product_ids;comment:本次导入的商品id;type:text;"`        //商城商品id
	Status           int               `json:"status"  form:"status" gorm:"column:status;comment:0执行中1成功2错误"`                                // 0开始1成功2错误
	ErrorMsg         string            `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"`                             //错误内容
	GatherSuppliesId uint              `json:"gather_supplies_id" form:"gather_supplies_id" gorm:"column:gather_supplies_id;comment:供应链id;"` //供应链id
	Type             int               `json:"type"  form:"type" gorm:"column:type;default:0;comment:0待处理1已处理"`                              // 0待处理1已处理

}

// 云仓订单定时任务在商城下单记录表 (记录表状态只是记录第一次同步订单的状态，不代表真正的云仓订单状态)
type CloudOrder struct {
	source.Model
	OrderId              string `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id，以子表的为准;type:varchar(255);size:255;"`                //中台订单id //舍弃 因为 可能中台拆单 这里无法跟着拆分订单 以子表的为准
	OrderSn              string `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:中台订单编号，以子表的为准;type:varchar(255);size:255;"`                //中台订单编号 // 舍弃 因为 可能中台拆单 这里无法跟着拆分订单 以子表的为准
	CloudOrderSn         string `json:"cloud_order_sn" form:"cloud_order_sn" gorm:"column:cloud_order_sn;comment:云仓订单号;"`                                 //云仓订单号 (与订单表关联使用云仓订单号 中台订单第三方订单号== 云仓订单号)
	CloudOrderId         int    `json:"cloud_order_id"  form:"cloud_order_id" gorm:"column:cloud_order_id;comment:云仓订单id"`                                // 云仓订单id
	Status               int    `json:"status" form:"status" gorm:"column:status;comment:-1订单错误0待付款1待发货2已发货3完成;"`                                         //-1订单错误0待支付1待发货2待收货3已完成 -2自动发货失败//云仓订单发货之后就算完成
	IsOffline            int    `json:"is_offline" form:"is_offline" gorm:"column:is_offline;comment:是否线下操作下单 1是0否;default:0;type:smallint;size:3;"`      //是否线下操作下单 1是0否
	ErrorMsg             string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;type:text;"`                                       //错误内容
	CloudOrderTotalPrice uint   `json:"cloud_order_total_price" form:"cloud_order_total_price" gorm:"column:cloud_order_total_price;comment:云仓订单金额;"`     //云仓订单金额
	GatherSuppliesId     uint   `json:"gather_supplies_id" form:"gather_supplies_id" gorm:"column:gather_supplies_id;comment:供应链id;"`                     //供应链id
	SynStatus            int    `json:"syn_status" form:"syn_status" gorm:"column:syn_status;default:3;comment:同步状态1待同步2部分同步3全部同步;type:smallint;size:3;"` // 发货状态

}

// 云仓订单定时任务在商城下单记录子表 (记录表状态只是记录第一次同步订单的状态，不代表真正的云仓订单状态)
type CloudOrderItem struct {
	source.Model
	CloudOrderId          uint   `json:"cloud_order_id" form:"cloud_order_id" gorm:"column:cloud_order_id;comment:cloudOrder表id;index"`                     //cloudOrder表id
	OrderId               uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id;index"`                                              //中台订单id
	OrderSn               uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:中台订单编号;index"`                                              //中台订单编号 //
	OrderItemId           uint   `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:中台子订单id;index"`                              //中台子订单id
	CloudGoodsOrderSn     string `json:"cloud_goods_order_sn" form:"cloud_goods_order_sn" gorm:"column:cloud_goods_order_sn;comment:云仓子订单号;"`               //云仓子订单号
	Status                int    `json:"status" form:"status" gorm:"column:status;comment:0:未发货,1:已发货,2:已收货,3:配货中,4：拒收;"`                                   //云仓子订单状态
	PayStatus             int    `json:"pay_status" form:"pay_status" gorm:"column:pay_status;comment:0:未付款,1:已付款,2申请退款,3,退款中,4已退款5退款申请失败"`                 //云仓子订单售后状态
	CloudGoodsId          uint   `json:"cloud_goods_id" form:"cloud_goods_id" gorm:"column:cloud_goods_id;comment:云仓商品id;"`                                 //商品id
	Total                 uint   `json:"total" form:"total" gorm:"column:total;comment:云仓商品数量;"`                                                            //云仓商品数量
	CloudGoodsOptionId    uint   `json:"cloud_goods_option_id" form:"cloud_goods_option_id" gorm:"column:cloud_goods_option_id;comment:云仓商品规格id;"`          //云仓商品规格id
	CloudGoodsTitle       string `json:"cloud_goods_title" form:"cloud_goods_title" gorm:"column:cloud_goods_title;comment:云仓商品名称;"`                        //云仓商品名称
	CloudGoodsOptionTitle string `json:"cloud_goods_option_title" form:"cloud_goods_option_title" gorm:"column:cloud_goods_option_title;comment:云仓商品规格名称;"` //云仓商品规格名称
	ProductId             uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:中台商品id;"`                                             //中台商品id
	SkuId                 uint   `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:中台商品规格id;"`                                                       //中台商品id
	ErrorMsg              string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"`                                                  //错误内容
	SupplyPayStatus       int    `json:"supply_pay_status" form:"supply_pay_status" gorm:"column:supply_pay_status;comment:0:中台支付状态 -1未付款"`                 //云仓子订单售后状态

}

type CloudFreight struct {
	Id             int            `json:"id" gorm:"-"`              //排序
	Sort           int            `json:"sort" gorm:"-"`            //排序
	Name           string         `json:"name" gorm:"-"`            //运费模板名称
	IsDefault      int            `json:"is_default" gorm:"-"`      //是否默认 1是0否
	ChargeType     int            `json:"charge_type" `             //计费方式 1按重量2按件;
	Dispatching    []Dispatchings `json:"dispatching" gorm:"-"`     //地区
	DisDispatching string         `json:"dis_dispatching" gorm:"-"` //未知默认空字符串
	Publish        int            `json:"publish" gorm:"-"`         //是否启用 1是0否;
	Created        int            `json:"created" gorm:"-"`         //创建时间
	Modified       int            `json:"modified" gorm:"-"`        //修改时间？

}
type Dispatchings struct {
	A  string `json:"a" gorm:"-"`  //地区名称
	F  int    `json:"f" gorm:"-"`  //首重
	Fp int    `json:"fp" gorm:"-"` //首费
	N  int    `json:"n" gorm:"-"`  //续重
	Np int    `json:"np" gorm:"-"` //续费

}

type CloudRefundAddress struct {
	Id         int    `json:"id" form:"id" gorm:"-"` //id
	RealName   string `json:"real_name" gorm:"-"`    //姓名
	Mobile     string `json:"mobile" gorm:"-"`       //联系方式
	Address    string `json:"address" gorm:"-"`      //详细地址
	ProvinceId int    `json:"province_id" gorm:"-"`  //省id
	CityId     int    `json:"city_id" `              //市id
	DistrictId int    `json:"district_id" gorm:"-"`  //县id
	IsDefault  int    `json:"is_default" gorm:"-"`   //是否默认 1是0否;
}

// 推送商品结构体
type CloudPushGoods struct {
	GoodsId         uint         `json:"goods_id" gorm:"-"`         //id
	GoodsName       string       `json:"goods_name" gorm:"-"`       //商品名称
	GoodsDes        string       `json:"goods_des" gorm:"-"`        //商品关键字
	CategoryId      uint         `json:"category_id" gorm:"-"`      //商品分类id，第三级
	Price           uint         `json:"price" gorm:"-"`            //指导价
	JsPrice         uint         `json:"js_price" gorm:"-"`         //结算价
	ScPrice         uint         `json:"sc_price" gorm:"-"`         //市场价
	ProducingArea   string       `json:"producing_area" gorm:"-"`   //产地
	DeliverArea     string       `json:"deliver_area" gorm:"-"`     //发货地
	Weight          uint         `json:"weight" gorm:"-"`           //重量
	FreightId       uint         `json:"freight_id" gorm:"-"`       //运费模板id
	AftersaleTime   uint         `json:"aftersale_time" gorm:"-"`   //售后时长 可选 7，15，30 天
	DelayCompensate uint         `json:"delay_compensate" gorm:"-"` //发货延期 时长 可选24，48，72，0 小时
	GoodsBrand      string       `json:"goods_brand" gorm:"-"`      //品牌 没有品牌时 可以传暂无品牌
	BrandId         uint         `json:"brand_id" gorm:"-"`         //品牌id 没有品牌时 可以传1
	Unit            string       `json:"unit" gorm:"-"`             //单位
	Stags           string       `json:"stags" gorm:"-"`            //服务标签 从服务标签列表获取 id 英文逗号连接
	OutGoodsId      uint         `json:"out_goods_id" gorm:"-"`     //合作方的商品id
	Description     string       `json:"description" gorm:"-"`      //详情
	WebUrl          string       `json:"web_url" gorm:"-"`          //商品市场价 参考连接
	Imgs            []string     `json:"imgs" gorm:"-"`             //列表图 默认第一张为列表图
	Specs           []Specs      `json:"specs" gorm:"-"`            //规格
	SpecsGroup      []SpecsGroup `json:"specs_group" gorm:"-"`      //列表图 默认第一张为列表图
	IsOn            int          `json:"is_on" gorm:"-"`            //是否删除 0删除 1正常
	IsOnsale        int          `json:"is_onsale" gorm:"-"`        //上架下架 1上0下

}
type Specs struct {
	Id        uint        `json:"id" gorm:"-"`                         //id
	SpecName  string      `json:"spec_name" form:"spec_name" gorm:"-"` //规格名 例如 颜色
	SpecValue []SpecValue `json:"spec_value" gorm:"-"`                 //spec_value
}
type SpecValue struct {
	Value string `json:"value" form:"value"  gorm:"-"` //规格值

}
type SpecsGroup struct {
	Id          uint     `json:"id" gorm:"-"`            //id
	SpecValues  []string `json:"spec_values" gorm:"-"`   //规格值组合
	Stock       int      `json:"stock" gorm:"-"`         //库存
	Price       uint     `json:"price" gorm:"-"`         //指导价
	JsPrice     uint     `json:"js_price" gorm:"-"`      //结算价
	ScPrice     uint     `json:"sc_price" gorm:"-"`      //市场价
	Weight      int      `json:"weight" gorm:"-"`        //重量
	OutOptionId uint     `json:"out_option_id" gorm:"-"` //	外部skuid
	Thumb       string   `json:"thumb" gorm:"-"`         //规格图片
}
