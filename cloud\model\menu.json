[{"ID": 2018, "menuId": "2018", "path": "cloudIndexManage", "name": "cloudIndexManage", "hidden": false, "parentId": "0", "component": "view/supply/cloud/cloudIndexManage.vue", "meta": {"title": "云仓管理", "icon": "cloudy", "defaultMenu": false, "keepAlive": false}, "sort": 12, "parameters": []}, {"ID": 2019, "menuId": "2019", "path": "cloudSupplyManage", "name": "cloudSupplyManage", "hidden": false, "parentId": "2018", "component": "view/supply/cloud/cloudSupplyManage.vue", "meta": {"title": "供应链管理", "icon": "cloudIndex", "defaultMenu": false, "keepAlive": false}, "sort": 50, "parameters": []}, {"ID": 2980, "menuId": "2980", "CreatedAt": "2021-02-18T09:24:51Z", "UpdatedAt": "2021-02-18T09:24:51Z", "path": "cloudSupplySetting", "name": "cloudSupplySetting", "hidden": true, "parentId": "2018", "component": "view/supply/cloud/cloudSupplySetting.vue", "meta": {"title": "配置信息", "icon": "goods", "defaultMenu": false, "keepAlive": false}, "sort": 10, "parameters": []}, {"ID": 2007, "menuId": "2007", "path": "cloudIndex", "name": "cloudIndex", "hidden": true, "parentId": "0", "component": "view/supply/cloud/index.vue", "meta": {"title": "云仓", "icon": "el-icon-orange", "defaultMenu": false, "keepAlive": false}, "sort": 50, "parameters": []}, {"ID": 2008, "menuId": "2008", "path": "cloudManageIndex", "name": "cloudManageIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/manage/index.vue", "meta": {"title": "云仓管理", "icon": "cloudManageIndex", "defaultMenu": false, "keepAlive": false}, "sort": 51, "parameters": []}, {"ID": 2009, "menuId": "2009", "path": "cloudProductIndex", "name": "cloudProductIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/product/index.vue", "meta": {"title": "云仓商品", "icon": "cloudProductIndex", "defaultMenu": false, "keepAlive": false}, "sort": 52, "parameters": []}, {"ID": 2010, "menuId": "2010", "path": "cloudFreightTempIndex", "name": "cloudFreightTempIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/freightTemp/index.vue", "meta": {"title": "云仓运费模板", "icon": "cloudFreightTempIndex", "defaultMenu": false, "keepAlive": false}, "sort": 53, "parameters": []}, {"ID": 2011, "menuId": "2011", "path": "cloudOrderIndex", "name": "cloudOrderIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/order/index.vue", "meta": {"title": "云仓订单", "icon": "cloudOrderIndex", "defaultMenu": false, "keepAlive": false}, "sort": 54, "parameters": []}, {"ID": 2012, "menuId": "2012", "path": "cloudAfterSaleIndex", "name": "cloudAfterSaleIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/afterSale/index.vue", "meta": {"title": "云仓售后", "icon": "cloudAfterSaleIndex", "defaultMenu": false, "keepAlive": false}, "sort": 55, "parameters": []}, {"ID": 2013, "menuId": "2013", "path": "cloudAfterSaleAddressIndex", "name": "cloudAfterSaleAddressIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/afterSaleAddress/index.vue", "meta": {"title": "云仓售后地址", "icon": "cloudAfterSaleAddressIndex", "defaultMenu": false, "keepAlive": false}, "sort": 56, "parameters": []}, {"ID": 2014, "menuId": "2014", "path": "orderRecordIndex", "name": "orderRecordIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/orderRecord/index.vue", "meta": {"title": "同步订单记录", "icon": "orderRecordIndex", "defaultMenu": false, "keepAlive": false}, "sort": 57, "parameters": []}, {"ID": 2015, "menuId": "2015", "path": "cloudRecordIndex", "name": "cloudRecordIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/cloudRecord/index.vue", "meta": {"title": "推送商品记录", "icon": "cloudRecordIndex", "defaultMenu": false, "keepAlive": false}, "sort": 58, "parameters": []}, {"ID": 2016, "menuId": "2016", "path": "expressMatchIndex", "name": "expressMatchIndex", "hidden": false, "parentId": "2007", "component": "view/supply/cloud/expressMatch/index.vue", "meta": {"title": "快递匹配", "icon": "expressMatchIndex", "defaultMenu": false, "keepAlive": false}, "sort": 59, "parameters": []}]