package model

import (
	"encoding/json"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"yz-go/source"
	"yz-go/utils"

	_ "embed"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(

		CloudGoods{},
		CloudPushGoodsMessage{},
		CloudOrder{},
		CloudOrderItem{},
		MiddlegroundCloudExpressMatching{},
		CloudAreaName{},
		CloudAreaMatching{},
	)

	menus := []model.SysMenu{}
	menuJson := menu
	err = json.Unmarshal([]byte(menuJson), &menus)
	if err != nil {
		return
	}
	//if gva.DomainName != "zhongtai.xls91666.com" {
	//if gva.GlobalAuth.Supply
	if collection.Collect(gva.GlobalAuth.Supply).Contains(1) == true || utils.LocalEnv() != true {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}

	//}

	if err != nil {
		return
	}
	return
}
