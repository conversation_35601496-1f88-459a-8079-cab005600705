package request

import (
	"cloud/model"
	yzRequest "yz-go/request"
)

// 云仓公共参数
type CloudCommon struct {
	//Key            string `json:"key" form:"key" query:"gather_supply_id" binding:"required"`
	GatherSuppliesId uint `json:"gather_supply_id" form:"gather_supply_id" query:"gather_supply_id"  binding:"required" err:"供应链id不可为空"`
}

// 云仓获取自营商品条件
type ProductSearch struct {
	Id                uint   `json:"id" form:"id"`
	IsNew             int    `json:"is_new" form:"is_new" gorm:"column:is_new;comment:新品（1是0否）;type:smallint;size:1;index;"`                   // 新品（1是0否）
	IsRecommend       int    `json:"is_recommend" form:"is_recommend" gorm:"column:is_recommend;comment:推荐（1是0否）;type:smallint;size:1;index;"` // 推荐（1是0否）
	IsHot             int    `json:"is_hot" form:"is_hot" gorm:"column:is_hot;comment:热销（1是0否）;type:smallint;size:1;index;"`                   // 热销（1是0否）
	IsPromotion       int    `json:"is_promotion" form:"is_promotion" gorm:"column:is_promotion;comment:促销（1是0否）;type:smallint;size:1;index;"` // 促销（1是0否）
	Title             string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"`                      // 标题
	MaxPrice          uint   `json:"maxPrice" form:"maxPrice" gorm:"column:max_price;comment:最高价(单位:分);"`                                      // 最高价(单位:分)
	MinPrice          uint   `json:"minPrice" form:"minPrice" gorm:"column:min_price;comment:最低价(单位:分);"`                                      // 最低价(单位:分)
	Category1ID       uint   `json:"category1_id" form:"category1_id" gorm:"index"`                                                            // 一级分类
	Category2ID       uint   `json:"category2_id" form:"category2_id" gorm:"index"`                                                            // 二级分类
	Category3ID       uint   `json:"category3_id" form:"category3_id" gorm:"index"`
	Barcode           string `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"`                     // 条形码
	SupplierID        *uint  `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                            // 供应商id
	ResGatherSupplyID *uint  `json:"res_gather_supply_id" form:"res_gather_supply_id" gorm:"column:res_gather_supply_id;comment:供应链id;index;"` // 供应链id
	yzRequest.PageInfo
	Filter                           int    `json:"filter" form:"filter" query:"filter"`
	CloudStatus                      *int   `json:"cloud_status" form:"cloud_status" query:"cloud_status"`
	JushuitanDistributorSupplierName string `json:"jushuitan_distributor_supplier_name" form:"jushuitan_distributor_supplier_name" gorm:"column:jushuitan_distributor_supplier_name;comment:聚水潭店铺名称;type:varchar(255);size:255;index;"` // 聚水潭店铺名称

	CloudCommon
}

// 获取运费模板条件
type FreightSearch struct {
	CloudCommon
	Page          int `json:"page" form:"page"`                       //分页
	PageSize      int `json:"pageSize" form:"pageSize"`               //每页多久条
	IsHaveDefault int `json:"is_have_default" form:"is_have_default"` //是否默认
}

// 获取云仓商品的条件
type CloudGoodsSearch struct {
	CloudCommon
	Page         int    `json:"page" form:"page"`                     //分页
	PageSize     int    `json:"pageSize" form:"pageSize"`             //每页多久条
	ThirdGoodsId int    `json:"third_goods_id" form:"third_goods_id"` //第三方商品id
	GoodsName    string `json:"goods_name" form:"goods_name"`         //第三方商品名称
	PriceMin     int    `json:"price_min" form:"price_min"`           //最小金额
	PriceMax     int    `json:"price_max" form:"price_max"`           //最大金额
	IsOnsale     *int   `json:"is_onsale" form:"is_onsale"`           //上架下架 1上0下
	LocGoodsId   int    `json:"loc_goods_id" form:"loc_goods_id"`     //本地商品id

}

// 获取云仓商品上下架的条件
type GoodsOnsale struct {
	CloudCommon
	ThirdGoodsId []int `json:"third_goods_id" form:"third_goods_id"` //第三方商品id
	IsOnsale     int   `json:"is_onsale" form:"is_onsale"`           //上架下架 1上0下
}

// 删除商品
type DeleteGoods struct {
	CloudCommon
	ThirdGoodsId []int `json:"third_goods_id" form:"third_goods_id"` //第三方商品id
}

// 获取运费模板详情
type FreightDetail struct {
	CloudCommon
	FreightId int `json:"freight_id" form:"freight_id" binding:"required" err:"运费模板id不可为空"` //模板id
}

// 删除运费模板
type DeleteFreight struct {
	CloudCommon
	FreightId int `json:"freight_id" form:"freight_id" binding:"required" err:"运费模板id不可为空"` //模板id
}

// 添加修改运费模板
type SaveFreight struct {
	CloudCommon
	model.CloudFreight
}
type CloudUpdateGoodsBySource struct {
	CloudCommon
	ProductSource *int   `json:"product_source" form:"product_source"` //商品来源
	Page          int    `json:"page" form:"page"`                     //分页
	ProductIds    []uint `json:"product_ids" form:"product_ids"`       //商品id

}
type CloudProductOnStep1 struct {
	ProductSource *int `json:"product_source" form:"product_source"` //商品来源
	Page          int  `json:"page" form:"page"`                     //分页

}

// 获取云仓订单
type CloudOrderSearch struct {
	CloudCommon
	Page             int    `json:"page" form:"page"`                             //分页
	PageSize         int    `json:"pageSize" form:"pageSize"`                     //每页多久条
	OrderSn          string `json:"order_sn" form:"order_sn"`                     //订单号
	Status           *int   `json:"status" form:"status"`                         //订单状态 1待发货 2待收货 3已完成
	RealName         string `json:"real_name" form:"real_name"`                   //收货人
	Mobile           int    `json:"mobile" form:"mobile"`                         //联系方式
	CreatedStartTime int    `json:"created_start_time" form:"created_start_time"` //开始时间 时间戳
	CreatedEndTime   int    `json:"created_end_time" form:"created_end_time"`     //结束时间 时间戳

}

// 云仓订单详情
type CloudOrderDetatil struct {
	CloudCommon
	OrderId int `json:"order_id" form:"order_id"` //订单id
}

// 云仓订单发货
type CloudOrderSend struct {
	CloudCommon
	OrderId     int    `json:"order_id" form:"order_id"  binding:"required" err:"订单id不可为空"`           //订单id
	ExpressName string `json:"express_name" form:"express_name"  binding:"required" err:"快递公司名称不可为空"` //快递公司
	ExpressSn   string `json:"express_sn" form:"express_sn"  binding:"required" err:"快递单号不可为空"`       //快递单号

}

// 获取售后列表
type CloudRefundSearch struct {
	CloudCommon
	Page         int    `json:"page" form:"page"`                     //分页
	PageSize     int    `json:"pageSize" form:"pageSize"`             //每页多久条
	OrderSn      string `json:"order_sn" form:"order_sn"`             //订单号
	RefundType   *int   `json:"refund_type" form:"refund_type"`       //订单状态 1仅退款 2退货退款
	GoodsOrderSn string `json:"goods_order_sn" form:"goods_order_sn"` //子订单号

}

// 获取售后详情
type CloudRefund struct {
	CloudCommon
	RefundSn string `json:"refund_sn" form:"refund_sn"  binding:"required" err:"售后单号不可为空"` //售后单号
}

// 售后同意
type CloudRefundAgree struct {
	CloudCommon
	OrderRefundSn      string `json:"order_refund_sn" form:"order_refund_sn"  binding:"required" err:"售后单号不可为空"`          //售后单号
	OrderRefundAddress int    `json:"order_refund_address" form:"order_refund_address"  binding:"required" err:"请选择售后地址"` //售后地址id
}

// 售后拒绝
type CloudRefundReject struct {
	CloudCommon
	OrderRefundSn string `json:"order_refund_sn" form:"order_refund_sn"  binding:"required" err:"售后单号不可为空"` //售后单号
	Reason        string `json:"reason" form:"reason"  binding:"required" err:"请填写驳回原因"`                    //驳回原因
	Content       string `json:"content" form:"content"  binding:"required" err:"请填写驳回内容"`                  //请填写驳回内容

}

// 获取售后地址列表
type CloudRefundAddressList struct {
	CloudCommon
	Page     int `json:"page" form:"page"`         //分页
	PageSize int `json:"pageSize" form:"pageSize"` //每页多久条
}

// 获取售后地址列表
type CloudRegion struct {
	CloudCommon
	Pid int `json:"pid" form:"pid"` //上级id 没有传0
}

// 推送商品（勾选推送版）
type CloudPushProductIds struct {
	CloudCommon
	ProductIds       []uint `json:"product_ids" form:"product_ids"` //商品id
	GoodsDes         string `json:"goods_des" gorm:"-"`             //商品关键字
	CloudCategory1Id uint   `json:"cloud_category1_id" gorm:"-"`    //云仓商品分类id，第1级
	CloudCategory2Id uint   `json:"cloud_category2_id" gorm:"-"`    //云仓商品分类id，第2级
	CloudCategory3Id uint   `json:"cloud_category3_id" gorm:"-"`    //云仓商品分类id，第3级
	ProducingArea    string `json:"producing_area" gorm:"-"`        //产地
	DeliverArea      string `json:"deliver_area" gorm:"-"`          //发货地
	FreightId        uint   `json:"freight_id" gorm:"-"`            //运费模板id
	AftersaleTime    uint   `json:"aftersale_time" gorm:"-"`        //售后时长 可选 7，15，30 天
	DelayCompensate  uint   `json:"delay_compensate" gorm:"-"`      //发货延期 时长 可选24，48，72，0 小时
	Stags            string `json:"stags" gorm:"-"`                 //服务标签 从服务标签列表获取 id 英文逗号连接
}
type CloudAllPush struct {
	Id                uint   `json:"id" form:"id"`
	IsNew             int    `json:"is_new" form:"is_new" gorm:"column:is_new;comment:新品（1是0否）;type:smallint;size:1;index;"`                   // 新品（1是0否）
	IsRecommend       int    `json:"is_recommend" form:"is_recommend" gorm:"column:is_recommend;comment:推荐（1是0否）;type:smallint;size:1;index;"` // 推荐（1是0否）
	IsHot             int    `json:"is_hot" form:"is_hot" gorm:"column:is_hot;comment:热销（1是0否）;type:smallint;size:1;index;"`                   // 热销（1是0否）
	IsPromotion       int    `json:"is_promotion" form:"is_promotion" gorm:"column:is_promotion;comment:促销（1是0否）;type:smallint;size:1;index;"`
	Title             string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"` // 标题
	MaxPrice          uint   `json:"maxPrice" form:"maxPrice" gorm:"column:max_price;comment:最高价(单位:分);"`                 // 最高价(单位:分)
	MinPrice          uint   `json:"minPrice" form:"minPrice" gorm:"column:min_price;comment:最低价(单位:分);"`                 // 最低价(单位:分)
	Category1ID       uint   `json:"category1_id" form:"category1_id" gorm:"index"`                                       // 一级分类
	Category2ID       uint   `json:"category2_id" form:"category2_id" gorm:"index"`                                       // 二级分类
	Category3ID       uint   `json:"category3_id" form:"category3_id" gorm:"index"`
	Barcode           string `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"`                     // 条形码
	SupplierID        *uint  `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                            // 供应商id
	ResGatherSupplyID *uint  `json:"res_gather_supply_id" form:"res_gather_supply_id" gorm:"column:res_gather_supply_id;comment:供应链id;index;"` // 供应链id
	CloudStatus       *int   `json:"cloud_status" form:"cloud_status" query:"cloud_status"`

	Filter                           int    `json:"filter" form:"filter" query:"filter"`
	GoodsDes                         string `json:"goods_des" gorm:"-"`                                                                                                                                                                 //商品关键字
	CloudCategory1Id                 uint   `json:"cloud_category1_id" gorm:"-"`                                                                                                                                                        //云仓商品分类id，第1级
	CloudCategory2Id                 uint   `json:"cloud_category2_id" gorm:"-"`                                                                                                                                                        //云仓商品分类id，第2级
	CloudCategory3Id                 uint   `json:"cloud_category3_id" gorm:"-"`                                                                                                                                                        //云仓商品分类id，第3级
	ProducingArea                    string `json:"producing_area" gorm:"-"`                                                                                                                                                            //产地
	DeliverArea                      string `json:"deliver_area" gorm:"-"`                                                                                                                                                              //发货地
	FreightId                        uint   `json:"freight_id" gorm:"-"`                                                                                                                                                                //运费模板id
	AftersaleTime                    uint   `json:"aftersale_time" gorm:"-"`                                                                                                                                                            //售后时长 可选 7，15，30 天
	DelayCompensate                  uint   `json:"delay_compensate" gorm:"-"`                                                                                                                                                          //发货延期 时长 可选24，48，72，0 小时
	Stags                            string `json:"stags" gorm:"-"`                                                                                                                                                                     //服务标签 从服务标签列表获取 id 英文逗号连接
	JushuitanDistributorSupplierName string `json:"jushuitan_distributor_supplier_name" form:"jushuitan_distributor_supplier_name" gorm:"column:jushuitan_distributor_supplier_name;comment:聚水潭店铺名称;type:varchar(255);size:255;index;"` // 聚水潭店铺名称

	CloudCommon
}

// 更新云仓商品
type CloudUpdateCloudGoods struct {
	CloudCommon
	ProductIds []uint `json:"product_ids" form:"product_ids"` //商品id
}

// 添加修改运费模板
type SaveRefundAddress struct {
	CloudCommon
	model.CloudRefundAddress
}

// 获取售后详情
type GetCloudGoodsDetail struct {
	CloudCommon
	GoodsId int `json:"goods_id" form:"goods_id"` //云仓商品id
}

// 获取物流详情
type CloudGetDeliver struct {
	CloudCommon
	GoodsOrderSn string `json:"goods_order_sn" form:"goods_order_sn"` //商品订单号
}

type CloudPushGoodsMessage struct {
	CloudCommon
	Page       int    `json:"page" form:"page"`                                                            //分页
	PageSize   int    `json:"pageSize" form:"pageSize"`                                                    //每页多久条
	Batch      string `json:"batch" form:"batch" gorm:"column:batch;comment:批次（同样的属于 同一批次）;"`              //批次（同样的属于 同一批次）
	ProductIds string `json:"product_ids" form:"product_ids" gorm:"column:product_ids;comment:本次导入的商品id;"` //商城商品id
	Status     *int   `json:"status"  form:"status" gorm:"column:status;comment:0执行中1成功2错误"`               // 0开始1成功2错误
}

// 选择在中台下单的订单
type CloudOrderCreate struct {
	CloudCommon
	OrderId []int `json:"order_id" form:"order_id"` //分页
}

// 手动填写信息在中台下单的订单（云仓）
type ManualCloudOrderCreate struct {
	CloudCommon
	CloudOrderId uint    `json:"cloud_order_id"`           //云仓记录表ID
	OrderId      uint    `json:"order_id" form:"order_id"` //云仓订单ID
	Address      Address `json:"address" form:"order_id"`  //收货地址信息
	Goods        []Goods `json:"goods" form:"goods"`
}
type Goods struct {
	GoodsOrderSn string `json:"goods_order_sn" form:"goods_order_sn"` //子订单号
	SkuId        uint   `json:"sku_id" form:"sku_id"`                 //中台规格ID
}
type Address struct {
	Province    string `json:"province" form:"province"`
	City        string `json:"city"`
	Area        string `json:"area"`
	Street      string `json:"street"`
	Description string `json:"description"`
}

// 选择在中台下单的订单
type CeshiLisOrderSend struct {
	OrderId int `json:"order_id" form:"order_id"` //分页
}

// 获取云仓记录表数据
type CloudOrderRecordSearch struct {
	yzRequest.PageInfo
	model.CloudOrder
	Status           *int   `json:"status" form:"status" gorm:"column:status;comment:-1订单错误1待发货2已发货3完成;"`                                        //-1订单错误0待支付1待发货2完成//云仓订单发货之后就算完成
	CreatedStartTime string `json:"created_start_time" form:"created_start_time"`                                                                //开始时间 时间戳
	CreatedEndTime   string `json:"created_end_time" form:"created_end_time"`                                                                    //结束时间 时间戳
	IsOffline        *int   `json:"is_offline" form:"is_offline" gorm:"column:is_offline;comment:是否线下操作下单 1是0否;default:0;type:smallint;size:3;"` //是否线下操作下单 1是0否

}

// 新版云仓发货
type CloudOrderSendNew struct {
	CloudCommon
	CloudOrderId      int                 `json:"cloud_order_id" form:"cloud_order_id"` //云仓订单id
	CloudOrderSendApi []CloudOrderSendApi `json:"cloud_order_send_api" form:"cloud_order_send_api"`
}

// 请求云仓API发货的结构体
type CloudOrderSendApi struct {
	GoodsOrderSn string `json:"goods_order_sn" form:"goods_order_sn" ` //子订单号
	ExpressName  string `json:"express_name" form:"express_name" `     //快递公司
	ExpressSn    string `json:"express_sn" form:"express_sn"`          //快递单号
}

// 选择在中台下单的订单
type ProductRelevanceCloudGoods struct {
	CloudCommon
	ProductId uint `json:"product_id" form:"product_id"` //中台商品ID
	GoodsId   uint `json:"goods_id" form:"goods_id"`     //云仓商品ID
}

// 重复的商品id删除
type DeleteProductRelevanceCloudGoods struct {
	CloudCommon
	ProductId uint `json:"product_id" form:"product_id"` //中台商品ID
}

// 标记订单线下处理了不显示手动下单按钮
type UpdateCloudOrderIsOffline struct {
	CloudCommon
	CloudOrderId int `json:"cloud_order_id" form:"cloud_order_id"` //云仓订单id
}

// 云仓获取自营商品条件
type MiddlegroundCloudExpressMatchinSearch struct {
	model.MiddlegroundCloudExpressMatching
	yzRequest.PageInfo
}

// 知道code 重新发货
type UpdateSendErrorOrder struct {
	CompanyCode string `json:"company_code" form:"company_code"` //云仓订单id
}

// 标记订单线下处理了不显示手动下单按钮
type UpdateCloudPushGoodsMessageType struct {
	CloudPushGoodsMessageId int `json:"cloud_push_goods_message_id" form:"cloud_push_goods_message_id"` //推送/更新商品记录id
}

// 标记订单线下处理了不显示手动下单按钮
type CloudOrderCreateNewStep1APi struct {
	CreatedStartTime int64 `json:"created_start_time" form:"created_start_time"`
	CreatedEndTime   int64 `json:"created_end_time" form:"created_end_time"`
}
