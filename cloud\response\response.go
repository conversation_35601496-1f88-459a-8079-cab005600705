package response

//运费模板详情返回数据 （因需要处理区域所以定义一下）
type CloudFreight struct {
	Id  int `json:"id" gorm:"-"`//排序
	Sort  int `json:"sort" gorm:"-"`//排序
	Name  string `json:"name" gorm:"-"`//运费模板名称
	IsDefault int `json:"is_default" gorm:"-"`//是否默认 1是0否
	ChargeType int `json:"charge_type" `//计费方式 1按重量2按件;
	Dispatching []Dispatchings `json:"dispatching" gorm:"-"`//地区
	DisDispatching  string `json:"dis_dispatching" gorm:"-"`//未知默认空字符串
	Publish  int `json:"publish" gorm:"-"`//是否启用 1是0否;
	Created int `json:"created" gorm:"-"`//创建时间
	Modified int `json:"modified" gorm:"-"`//修改时间？

}
type Dispatchings struct {
	A  string `json:"a" gorm:"-"`//地区名称
	F  int `json:"f" gorm:"-"`//首重
	Fp int `json:"fp" gorm:"-"`//首费
	N int `json:"n" gorm:"-"`//续重
	Np int `json:"np" gorm:"-"`//续费
	Area []Area `json:"area" gorm:"-"`//续费
}
type Area struct {
	Name  string `json:"name" gorm:"-"`//地区名称
	ID  int `json:"id" gorm:"-"`//id
	Children []Area `json:"children" gorm:"-"`//子集
}
