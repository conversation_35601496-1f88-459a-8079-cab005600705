package service

import (
	cloudModel "cloud/model"
	"cloud/request"
	"errors"
	"yz-go/source"
)

/**
  添加
*/
func CreateMiddlegroundCloudExpressMatching(matching cloudModel.MiddlegroundCloudExpressMatching) (err error) {
	var middlegroundCloudExpressMatching cloudModel.MiddlegroundCloudExpressMatching
	//中台快递名称是唯一的即可
	source.DB().Where("name = ?", matching.Name).First(&middlegroundCloudExpressMatching)
	if middlegroundCloudExpressMatching.ID != 0 {
		return errors.New("已存在对应关系")
	}
	err = source.DB().Create(&matching).Error
	return
}

/**
  修改
*/
func UpdateMiddlegroundCloudExpressMatching(matching cloudModel.MiddlegroundCloudExpressMatching) (err error) {
	var middlegroundCloudExpressMatching cloudModel.MiddlegroundCloudExpressMatching
	if matching.ID == 0 {
		return errors.New("请提交id参数")
	}
	//中台快递名称是唯一的即可
	source.DB().Where("id != ?", matching.ID).Where("name = ?", matching.Name).First(&middlegroundCloudExpressMatching)
	if middlegroundCloudExpressMatching.ID != 0 {
		return errors.New("已存在对应关系")
	}
	err = source.DB().Where("id = ?", matching.ID).Save(&matching).Error
	return
}

/**
  删除
*/
func DeleteMiddlegroundCloudExpressMatching(matching cloudModel.MiddlegroundCloudExpressMatching) (err error) {
	if matching.ID == 0 {
		return errors.New("请提交id参数")
	}
	err = source.DB().Where("id = ?", matching.ID).Delete(&matching).Error
	return
}

/**
  获取可推送至云仓的商品，与推送状态
*/
//GetAfterSalesList
//
//@function: GetAfterSalesList
//@description: 分页获取Product记录
//@param: info request.ProductSearch
//@return: err error, list interface{}, total int64
func GetMiddlegroundCloudExpressMatchingList(info request.MiddlegroundCloudExpressMatchinSearch) (err error, list []cloudModel.MiddlegroundCloudExpressMatching, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&cloudModel.MiddlegroundCloudExpressMatching{})

	if info.Name != "" {
		db.Where("name = ?", info.Name)
	}

	if info.CloudName != "" {
		db.Where("cloud_name = ?", info.CloudName)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&list).Error

	return err, list, total
}
