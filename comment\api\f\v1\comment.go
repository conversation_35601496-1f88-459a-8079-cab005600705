package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"comment/model"
	"comment/request"
	"comment/service"
	u1f1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)
// CreateComment
// @Tags 评论
// @Summary 创建Comment
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Comment true "创建Comment"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /comment/create [post]
func CreateComment(c *gin.Context) {
	var comment model.Comment
	err := c.ShouldBindJSON(&comment)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := u1f1.GetUserID(c)
	comment.UserId = int(userId)
	if err := service.CreateComment(comment); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// GetCommentList
// @Tags 评论
// @Summary 分页获取评论列表
// @accept application/json
// @Produce application/json
// @Param data body request.CommentSearch true "分页获取Comment列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /comment/list [post]
func GetCommentList(c *gin.Context) {
	var pageInfo request.CommentSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCommentList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
// CreateComment
// @Tags 评论
// @Summary 创建Comment
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Comment true "创建Comment"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /comment/create [post]
func CreateCommentComment(c *gin.Context) {
	var comment model.Comment
	err := c.ShouldBindJSON(&comment)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if comment.CommentId == 0 {
		yzResponse.FailWithMessage("请提交回复的id", c)
		return
	}
	userId := u1f1.GetUserID(c)
	comment.UserId = int(userId)
	if err := service.CreateCommentComment(comment); err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// GetCommentList
// @Tags 评论
// @Summary 分页获取评论列表
// @accept application/json
// @Produce application/json
// @Param data body request.CommentSearch true "分页获取Comment列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /comment/list [post]
func GetCommentByCommentId(c *gin.Context) {
	var pageInfo request.CommentSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCommentByCommentId(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}