package v1

import (
    "comment/model"
	"yz-go/component/log"
	yzRequest "yz-go/request"
    "comment/request"
	yzResponse "yz-go/response"
    "comment/service"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

// @Tags comment表
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Comment true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/comment/createComment [post]
func CreateComment(c *gin.Context) {
	var comment model.Comment
	var err error
	err = c.ShouldBindJSON(&comment)
	if err != nil{
	    yzResponse.FailWithMessage(err.Error(), c)
	    return
	}
	if comment.OrderId == 0{
		yzResponse.FailWithMessage("请提交订单id", c)
		return
	}
	if comment.OrderItemId == 0{
		yzResponse.FailWithMessage("请提交子订单id", c)
		return
	}
	if comment.ProductId == 0{
		yzResponse.FailWithMessage("请提交商品id", c)
		return
	}
	err = service.CreateComment(comment)
	if err != nil {
        log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
    yzResponse.OkWithMessage("创建成功", c)
}

// @Tags comment表
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Comment true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /api/comment/deleteComment [post]
func DeleteComment(c *gin.Context) {
	var comment model.Comment
	var err error
    err = c.ShouldBindJSON(&comment)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err = service.DeleteComment(comment)
	if err != nil {
        log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
    yzResponse.OkWithMessage("删除成功", c)

}

// @Tags comment表
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /api/comment/deleteCommentByIds [post]
func DeleteCommentByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
    var err error
	err = service.DeleteCommentByIds(IDS)
	if err != nil {
        log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
    yzResponse.OkWithMessage("批量删除成功", c)
}

// @Tags comment表
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Comment true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /api/comment/updateComment [post]
func UpdateComment(c *gin.Context) {
	var comment model.Comment
	var err error
    err = c.ShouldBindJSON(&comment)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err = service.UpdateComment(comment)
	if err != nil {
        log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	}
    yzResponse.OkWithMessage("更新成功", c)
}

// @Tags comment表
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询"
// @Success 200 {object} model.Comment
// @Router /api/comment/findComment [post]
func FindComment(c *gin.Context) {
	var reqId yzRequest.GetById
    err := c.ShouldBindQuery(&reqId)
    if err != nil {
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err, recomment := service.GetComment(reqId.Id)
	if err != nil {
        log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"recomment": recomment}, c)
}

// @Tags comment表
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CommentSearch true "分页获取列表"
// @Success 200 {string} string []model.Comment
// @Router /api/comment/getCommentList [post]
func GetCommentList(c *gin.Context) {
	var pageInfo request.CommentSearch
	var err error
    err = c.ShouldBindQuery(&pageInfo)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err, list, total := service.GetCommentInfoList(pageInfo)
	if err != nil {
        log.Log().Error("获取失败", zap.Any("err", err))
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
    yzResponse.OkWithDetailed(yzResponse.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}
