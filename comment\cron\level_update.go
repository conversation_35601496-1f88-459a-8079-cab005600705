package cron

import (
	cmodel "comment/model"
	"fmt"
	"math"
	"product/model"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/setting"
	"yz-go/source"
)

func PushCommentLevelUpdateHandle() {
	task := cron.Task{
		Key:  "commentLevelUpdate",
		Name: "自动升级评分等级",
		Spec: "0 */27 * * * *",
		Handle: func(task cron.Task) {
			LevelUpdate()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

type Product struct {
	ID             uint `json:"id"`
	SupplierID     uint `json:"supplier_id"`
	GatherSupplyId uint `json:"gather_supply_id"`

	Level        float64
	DesLevel     float64
	ShopLevel    float64
	ExpressLevel float64
}

type Comment struct {
	ID           uint `json:"id"`
	ProductID    uint `json:"product_id"`
	Level        float64
	DesLevel     float64
	ShopLevel    float64
	ExpressLevel float64
}

func LevelUpdate() {
	var products []Product
	var comments []Comment
	err := source.DB().Model(&cmodel.Comment{}).Select("product_id, AVG(level) as level, AVG(des_level) as des_level, AVG(express_level) as express_level, AVG(shop_level) as shop_level").Group("product_id").Find(&comments).Error
	if err != nil || len(comments) <= 0 {
		log.Log().Info("自动更新商品评论平均分数出错：缺少需要更新的数据")
		return
	}
	var productComments []Comment
	err = source.DB().Model(&cmodel.Comment{}).Find(&productComments).Error
	if err != nil || len(comments) <= 0 {
		return
	}
	var productCommentsMap = make(map[uint][]Comment)
	for _, pc := range productComments {
		productCommentsMap[pc.ProductID] = append(productCommentsMap[pc.ProductID], pc)
	}
	var productIds []uint
	var productUpdate []map[string]interface{}
	for _, v := range comments {
		var i int
		var feedBackRate int
		for _, pcm := range productCommentsMap[v.ProductID] {
			if pcm.Level > 3 {
				i++
			}
		}
		feedBackRate = int(math.Ceil(float64(i) / float64(len(productCommentsMap[v.ProductID])) * 100))
		productUpdateRow := make(map[string]interface{})
		productUpdateRow["level"] = v.Level
		productUpdateRow["des_level"] = v.DesLevel
		productUpdateRow["shop_level"] = v.ShopLevel
		productUpdateRow["express_level"] = v.ExpressLevel
		productUpdateRow["feedback_rate"] = feedBackRate
		productUpdateRow["id"] = v.ProductID
		productUpdateRow["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
		productIds = append(productIds, v.ProductID)
		productUpdate = append(productUpdate, productUpdateRow)
	}

	err = source.BatchUpdate(productUpdate, "products", "")
	if err != nil {
		log.Log().Info(err.Error())
		return
	}
	var product Product
	//自动更新自营商品评分 --  自营来源0  供应商0  供应链 0
	err = source.DB().Model(&model.Product{}).Where("source = 0 and supplier_id = 0 and gather_supply_id = 0").Select("supplier_id, AVG(level) as level, AVG(des_level) as des_level, AVG(express_level) as express_level, AVG(shop_level) as shop_level").Where("id in ?", productIds).First(&product).Error
	//可能没有自营商品，错误信息不为nil也继续往下执行
	if err == nil {
		err = source.DB().Model(&model.Supplier{}).Where("is_self_support = 1").Update("describe_score", product.DesLevel).Update("service_score", product.ShopLevel).Update("shopping_score", product.ExpressLevel).Error
		if err != nil {
			log.Log().Info("自动更新自营商品平均分数出错：" + err.Error())
			return
		}
	}
	//自动更新  供应链商品评分
	err = source.DB().Model(&model.Product{}).Where("gather_supply_id > 0").Select("gather_supply_id, AVG(level) as level, AVG(des_level) as des_level, AVG(express_level) as express_level, AVG(shop_level) as shop_level").Where("id in ?", productIds).Group("gather_supply_id").Find(&products).Error
	//可能没有供应链商品，错误信息不为nil也继续往下执行
	var gatherSupplyAverageScoreSetting setting.GatherSupplyAverageScoreSetting
	if err == nil {
		for _, v := range products {
			_, gatherSupplyAverageScoreSetting = setting.GetGatherSupplyAverageScoreSetting("GatherSupplyAverageScore" + strconv.Itoa(int(v.GatherSupplyId)))
			gatherSupplyAverageScoreSetting.Value.DescribeScore = v.DesLevel
			gatherSupplyAverageScoreSetting.Value.ServiceScore = v.ShopLevel
			gatherSupplyAverageScoreSetting.Value.ShoppingScore = v.ExpressLevel
			gatherSupplyAverageScoreSetting.Key = "GatherSupplyAverageScore" + strconv.Itoa(int(v.GatherSupplyId))
			_ = setting.SetGatherSupplyAverageScoreSetting(gatherSupplyAverageScoreSetting)
		}
	}

	err = source.DB().Model(&model.Product{}).Where("supplier_id > 0").Select("supplier_id, AVG(level) as level, AVG(des_level) as des_level, AVG(express_level) as express_level, AVG(shop_level) as shop_level").Where("id in ?", productIds).Group("supplier_id").Find(&products).Error
	if err != nil {
		log.Log().Info("自动更新商品评论平均分数出错：" + err.Error())
		return
	}

	var supplierUpdate []map[string]interface{}
	for _, v := range products {
		//if v.SupplierID == 0 {
		//	err = source.DB().Model(&model.Supplier{}).Where("is_self_support = 1").Update("describe_score", v.DesLevel).Update("service_score", v.ShopLevel).Update("shopping_score", v.ExpressLevel).Error
		//	if err != nil {
		//		return
		//	}
		//} else {
		supplierUpdateRow := make(map[string]interface{})
		supplierUpdateRow["describe_score"] = v.DesLevel
		supplierUpdateRow["service_score"] = v.ShopLevel
		supplierUpdateRow["shopping_score"] = v.ExpressLevel
		supplierUpdateRow["id"] = v.SupplierID
		supplierUpdate = append(supplierUpdate, supplierUpdateRow)
		//}
	}

	err = source.BatchUpdate(supplierUpdate, "suppliers", "")
	if err != nil {
		log.Log().Info("自动更新商品评论平均分数出错：" + err.Error())
		return
	}
	fmt.Println("自动更新商品及供应商评价平均分")
}
