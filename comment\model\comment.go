// 自动生成模板Comment
package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type CommentModel struct {
	ID          uint              `json:"id" form:"id" gorm:"primarykey"`
	DeletedAt   gorm.DeletedAt    `json:"-"`
	CreatedAt   *source.LocalTime `json:"created_at" gorm:"index;"`
	UpdatedAt   *source.LocalTime `json:"updated_at" gorm:"index;"`
	ProductId   int               `json:"productId" form:"productId" gorm:"column:product_id;comment:;type:int(11);size:10;"`
	OrderId     int               `json:"orderId" form:"orderId" gorm:"column:order_id;comment:;type:int(11);size:10;"`
	OrderItemId int               `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:;type:int(11);size:10;"`

	UserId         int    `json:"userId" form:"userId" gorm:"column:user_id;comment:;type:int(11);size:10;"`
	SupplierId     int    `json:"supplierId" form:"supplierId" gorm:"column:supplier_id;comment:;type:int(11);size:10;"`
	ProductAttr    string `json:"product_attr" form:"product_attr" gorm:"column:product_attr;comment:;type:varchar(255);size:255;"`
	Nickname       string `json:"nickname" form:"nickname" gorm:"column:nickname;comment:;type:varchar(255);size:255;"`
	Avatar         string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:;type:varchar(255);size:255;"`
	Content        string `json:"content" form:"content" gorm:"column:content;comment:;type:text;"`
	ImageUrls      string `json:"imageUrls" form:"imageUrls" gorm:"column:image_urls;comment:;type:text;"`
	Level          int    `json:"level" form:"level" gorm:"column:level;comment:;type:tinyint;"`                                      //商品总评价
	DesLevel       int    `json:"des_level" form:"des_level" gorm:"column:des_level;comment:;type:tinyint;"`                          //描述相符
	ShopLevel      int    `json:"shop_level" form:"shop_level" gorm:"column:shop_level;comment:;type:tinyint;"`                       //卖家服务
	ExpressLevel   int    `json:"express_level" form:"express_level" gorm:"column:express_level;comment:;type:tinyint;"`              //物流服务
	CommentId      int    `json:"commentId" form:"commentId" gorm:"column:comment_id;comment:;type:int;size:10;"`                     //上级ID
	FirstCommentId int    `json:"first_comment_id" form:"first_comment_id" gorm:"column:first_comment_id;comment:;type:int;size:10;"` //评论ID 永远是第一级别评论的ID 回复时使用

	Type int `json:"type" form:"type" gorm:"column:type;comment:1评论2回复;type:tinyint;"`
}

func (CommentModel) TableName() string {
	return "comments"
}

type Comment struct {
	CommentModel
	Reply []Comment `json:"reply" gorm:"foreignKey:CommentId"`
}
