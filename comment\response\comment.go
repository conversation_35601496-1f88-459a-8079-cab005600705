// 自动生成模板Comment
package response

import (
	"user/model"
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type CommentCards struct {
	source.SoftDel
	CreatedAt   *source.LocalTime `json:"created_at" gorm:"index;"`
	ID          string            `json:"id"`
	Nickname    string            `json:"nickname"`
	Avatar      string            `json:"avatar"`
	Content     string            `json:"content"`
	ImageUrls   string            `json:"imageUrls"`
	ProductAttr string            `json:"product_attr" form:"product_attr" gorm:"column:product_attr;comment:;type:varchar(255);size:255;"`
	Level       int               `json:"level"`
	UserId      int               `json:"user_id" form:"user_id" gorm:"column:user_id;comment:;type:int(11);size:10;"`
	User        User              `json:"user" gorm:"foreignkey:user_id"`
	ProductId   int               `json:"productId" form:"productId" gorm:"column:product_id;comment:;type:int(11);size:10;"`
	Count       int64             `json:"count" gorm:"-"` //回复数量
	OrderItemId int               `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:;type:int(11);size:10;"`
	OrderItem   OrderItem         `json:"order_item" gorm:"foreignkey:order_item_id"`

	Reply []Reply `gorm:"-"`
}

func (receiver CommentCards) TableName() string {
	return "comments"
}

type Product struct {
	ID       uint   `json:"id"`
	Title    string `json:"title"`
	Price    int    `json:"price"`
	ImageUrl string `json:"image_url"`
}

type User struct {
	ID        string                  `json:"id"`
	LevelId   int                     `json:"level_id"`
	NickName  string                  `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Avatar    string                  `json:"avatar"`
	UserLevel model.UserLevelDiscount `json:"user_level" form:"user_level" gorm:"foreignKey:level_id"`
}
type UserLevel struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
type Reply struct {
	CreatedAt *source.LocalTime `json:"created_at" gorm:"index;"`
	ID        string            `json:"id"`
	Nickname  string            `json:"nickname"`
	Avatar    string            `json:"avatar"`
	Content   string            `json:"content"`
	Count     int64             `json:"count" gorm:"-"` //回复数量
}

// 回复返回数据
type CommentReplys struct {
	CreatedAt     *source.LocalTime `json:"created_at" gorm:"index;"`
	ID            string            `json:"id"`
	Nickname      string            `json:"nickname"`
	Avatar        string            `json:"avatar"`
	Content       string            `json:"content"`
	CommentId     int               `json:"commentId" form:"commentId" gorm:"column:comment_id;comment:;type:int;size:10;"` //上级ID
	ParentComment ParentComment     `json:"parent_comment" gorm:"foreignkey:comment_id"`                                    //被回复的数据
	UserId        int               `json:"user_id" form:"user_id" gorm:"column:user_id;comment:;type:int(11);size:10;"`
	User          User              `json:"user" gorm:"foreignkey:user_id"`
}

type ParentComment struct {
	CreatedAt *source.LocalTime `json:"created_at" gorm:"index;"`
	ID        string            `json:"id"`
	Nickname  string            `json:"nickname"`
	Avatar    string            `json:"avatar"`
	Content   string            `json:"content"`
	UserId    int               `json:"user_id" form:"user_id" gorm:"column:user_id;comment:;type:int(11);size:10;"`
	User      User              `json:"user" gorm:"foreignkey:user_id"`
}

func (receiver CommentReplys) TableName() string {
	return "comments"
}
func (receiver ParentComment) TableName() string {
	return "comments"
}

type OrderItem struct {
	source.Model
	Title    string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`                // 名
	SkuTitle string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(255);size:255;"` // 规格名
	Unit     string `json:"unit" form:"unit" gorm:"column:unit;comment:单位;type:varchar(20);size:20;"`                     // 单位
	Qty      uint   `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                                               // 商品数量
	Amount   uint   `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`                                        // 总价(分)
	ImageUrl string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`  // 图片地址
}
