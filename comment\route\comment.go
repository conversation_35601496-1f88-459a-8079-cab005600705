package route

import (
	"github.com/gin-gonic/gin"
	fv1 "comment/api/f/v1"
	"comment/api/v1"
)

func InitAdminCommentPrivateRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("comment")
	{
		ProductRouter.POST("createComment", v1.CreateComment)   // 新建Comment
		ProductRouter.DELETE("deleteComment", v1.DeleteComment) // 删除Comment
		ProductRouter.DELETE("deleteCommentByIds", v1.DeleteCommentByIds) // 批量删除Comment
		ProductRouter.PUT("updateComment", v1.UpdateComment)    // 更新Comment
		ProductRouter.GET("findComment", v1.FindComment)        // 根据ID获取Comment
		ProductRouter.GET("getCommentList", v1.GetCommentList)  // 获取Comment列表
	}
}
func InitUserPublicCommentRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("comment")
	{
		ProductRouter.GET("list", fv1.GetCommentList)  // 获取Comment列表
		ProductRouter.GET("getCommentByCommentId", fv1.GetCommentByCommentId)  // 获取Comment指定评论/回复的 回复
	}
}


func InitUserPrivateCommentRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("comment")
	{
		ProductRouter.POST("create", fv1.CreateComment)  // 新建Comment列表
		ProductRouter.POST("createComment", fv1.CreateCommentComment)  // 新建Comment列表
	}
}
