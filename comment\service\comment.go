package service

import (
	"comment/model"
	"comment/request"
	"comment/response"
	"errors"
	"fmt"
	"strings"
	"unicode/utf8"
	yzRequest "yz-go/request"
	"yz-go/source"
)

type Order struct {
	ID            uint `json:"id"`
	CommentStatus int  `json:"comment_status"`
}
type OrderItem struct {
	ID            uint `json:"id"`
	OrderID       uint `json:"order_id"`
	CommentStatus int  `json:"comment_status"`
}
type Product struct {
	ID         uint `json:"id"`
	SupplierID uint `json:"supplier_id"`
}

//
//@function: CreateComment
//@description: 创建Comment记录
//@param: comment model.Comment
//@return: err error

func CreateComment(comment model.Comment) (err error) {
	var commentCheck model.Comment
	err = source.DB().Model(model.Comment{}).Where("order_item_id = ?", comment.OrderItemId).Where("product_id = ?", comment.ProductId).Where("order_id = ?", comment.OrderId).Where("user_id = ?", comment.UserId).First(&commentCheck).Error
	if err == nil {
		return errors.New("此商品您已经评价过，无需再次评价")
	}
	var product Product
	err = source.DB().Model(&Product{}).First(&product, comment.ProductId).Error
	comment.SupplierId = int(product.SupplierID)
	err = source.DB().Create(&comment).Error
	if err != nil {
		return
	}
	err = source.DB().Model(OrderItem{}).Where("id = ?", comment.OrderItemId).Update("comment_status", 1).Error
	if err != nil {
		return
	}
	var orderItem []OrderItem
	source.DB().Model(OrderItem{}).Where("order_id = ?", comment.OrderId).Where("comment_status", 0).Find(&orderItem)
	if len(orderItem) == 0 {
		err = source.DB().Model(Order{}).Where("id = ?", comment.OrderId).Update("comment_status", 1).Error
	}
	return
}

//
//@function: CreateComment
//@description: 评价之上评价
//@param: comment model.Comment
//@return: err error

func CreateCommentComment(comment model.Comment) (err error) {
	var commentCheck model.Comment
	err = source.DB().Model(model.Comment{}).Where("id = ?", comment.CommentId).First(&commentCheck).Error
	if err != nil {
		return errors.New("评价不存在")
	}
	//不验证是否回复过
	//var CommentUid model.Comment
	//err = source.DB().Model(model.Comment{}).Where("comment_id = ?", comment.CommentId).Where("user_id = ?", comment.UserId).First(&CommentUid).Error
	//if err == nil {
	//	return errors.New("你已回复过这个评价")
	//}
	comment.SupplierId = commentCheck.SupplierId
	err = source.DB().Create(&comment).Error
	if err != nil {
		return
	}

	return
}

//
//@function: DeleteComment
//@description: 删除Comment记录
//@param: comment model.Comment
//@return: err error

func DeleteComment(comment model.Comment) (err error) {
	err = source.DB().Delete(&comment).Error
	return err
}

//
//@function: DeleteCommentByIds
//@description: 批量删除Comment记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteCommentByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.Comment{}, "id in ?", ids.Ids).Error
	return err
}

//
//@function: UpdateComment
//@description: 更新Comment记录
//@param: comment *model.Comment
//@return: err error

func UpdateComment(comment model.Comment) (err error) {
	err = source.DB().Save(&comment).Error
	return err
}

//
//@function: GetComment
//@description: 根据id获取Comment记录
//@param: id uint
//@return: err error, comment model.Comment

func GetComment(id uint) (err error, comment model.Comment) {
	err = source.DB().Where("id = ?", id).First(&comment).Error
	return
}

//
//@function: GetCommentInfoList
//@description: 分页获取Comment记录
//@param: info request.CommentSearch
//@return: err error, list interface{}, total int64

func GetCommentInfoList(info request.CommentSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.Comment{})
	var comments []model.Comment
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Where("`type` = 1").Limit(limit).Offset(offset).Find(&comments).Error
	for k, v := range comments {
		var reply []model.Comment
		err = source.DB().Model(&model.Comment{}).Where("`type` = 2").Where("`comment_id` = ?", v.ID).Find(&reply).Error
		if err == nil {
			comments[k].Reply = reply
		}
	}
	return err, comments, total
}

// @function: GetCommentList
// @description: 分页获取Comment记录
// @param: info request.CommentSearch
// @return: err error, list interface{}, total int64
func GetCommentList(info request.CommentSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.CommentCards{})
	var comments []response.CommentCards
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.ProductId != 0 {
		db.Where("`product_id` = ?", info.ProductId)
	}
	db.Where("`type` = 1")
	err = db.Count(&total).Error

	err = db.Limit(limit).Offset(offset).Preload("OrderItem").Preload("User").Preload("User.UserLevel").Order("created_at desc").Find(&comments).Error

	//err, userSetting := userService.GetSysSetting("user_setting")
	//if err != nil {
	//	userSetting.Value.DefaultLevelName = ""
	//}

	for k, v := range comments {
		//赋值默认会员名称
		if v.User.UserLevel.ID == 0 {
			//comments[k].User.UserLevel.Name = userSetting.Value.DefaultLevelName
			// 默认等级填写已经弃用了
			comments[k].User.UserLevel.Name = "默认等级"
		}
		//var reply []response.Reply
		source.DB().Model(&model.Comment{}).Where("`type` = 2").Where("`first_comment_id` = ?", v.ID).Count(&comments[k].Count)
		//处理昵称
		comments[k].User.NickName = ProcessString(comments[k].User.NickName)
		comments[k].Nickname = ProcessString(comments[k].Nickname)

		//if err==nil {
		//	for k1,v1 := range reply{
		//		reply[k1].Nickname1 = v.Nickname
		//		source.DB().Model(&model.Comment{}).Where("`type` = 2").Where("`comment_id` = ?", v1.ID).Count(&reply[k1].Count)
		//
		//	}
		//	comments[k].Reply = reply
		//}
	}

	return err, comments, total
}

// 字符串处理  两个保留首位  两个以上保留首尾位
func ProcessString(inputStr string) string {
	if len(inputStr) == 0 {
		return inputStr
	}
	// 判断字符串是否包含中文字符
	containsChinese := func(s string) bool {
		for _, char := range s {
			if utf8.RuneLen(char) > 1 {
				return true
			}
		}
		return false
	}

	// 如果字符串包含中文字符
	if containsChinese(inputStr) {
		// 长度为 2，则保留首位字符，其余替换为星号
		if utf8.RuneCountInString(inputStr) == 2 {
			firstChar, _ := utf8.DecodeRuneInString(inputStr)
			return fmt.Sprintf("%c*", firstChar)
		} else {
			// 长度为 3 或者更长，则保留首尾字符，其余替换为星号
			firstChar, _ := utf8.DecodeRuneInString(inputStr)
			lastChar, _ := utf8.DecodeLastRuneInString(inputStr)
			return fmt.Sprintf("%c%s%c", firstChar, strings.Repeat("*", utf8.RuneCountInString(inputStr)-2), lastChar)
		}
	} else {
		// 如果字符串不包含中文字符
		// 长度为 2，则保留首位字符，其余替换为星号
		if len(inputStr) == 2 {
			return inputStr[:1] + "*"
		} else {
			// 长度为 3 或者更长，则保留首尾字符，其余替换为星号
			return inputStr[:1] + strings.Repeat("*", len(inputStr)-2) + inputStr[len(inputStr)-1:]
		}
	}
}

// @function: GetCommentList
// @description: 分页获取Comment记录
// @param: info request.CommentSearch
// @return: err error, list interface{}, total int64
func GetCommentByCommentId(info request.CommentSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&response.CommentReplys{})
	var comments []response.CommentReplys
	if info.CommentId != 0 {
		db.Where("`first_comment_id` = ?", info.CommentId)
	}
	db.Where("`type` = 2")
	err = db.Count(&total).Error

	err = db.Limit(limit).Preload("User").Preload("ParentComment").Preload("ParentComment.User").Offset(offset).Find(&comments).Error

	return err, comments, total
}
