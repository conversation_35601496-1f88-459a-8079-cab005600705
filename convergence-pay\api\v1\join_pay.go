package v1

import (
	//usertopup "finance/model"
	//fservice "finance/service"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"joinpay/model"
	"joinpay/service"
	usertopuputils "joinpay/utils"
	"yz-go/component/log"
	"yz-go/response"
	"yz-go/utils"
)

func GetPayQrCode(c *gin.Context) {
	var Pay model.UniPay
	err := c.ShouldBindJSON(&Pay)

	if err!=nil{
		response.FailWithMessage(err.Error(),c)
		return
	}

	if err,Account := service.GetPayQrCode(Pay); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}


func SeparateAccount(c *gin.Context) {
	var param model.AllocateData
	err:= c.ShouldBindJSON(&param)

	if err!=nil{
		response.FailWithMessage(err.Error(),c)
		return
	}
	fmt.Println("绑定参数：",param)
	if err,Account := service.SeparateAccount(param); err != nil {
		log.Log().Error("分账失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}


func MoreSeparateAccount(c *gin.Context) {
	var param model.MoreSeparateData
	err:= c.ShouldBindJSON(&param)

	if err!=nil{
		response.FailWithMessage(err.Error(),c)
		return
	}
	fmt.Println("绑定参数：",param)
	if err,Account := service.MoreSeparateAccount(param); err != nil {
		log.Log().Error("多次分账失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}



func EndSeparateAccount(c *gin.Context) {
	var param model.MoreSeparateData
	err:= c.ShouldBindJSON(&param)

	if err!=nil{
		response.FailWithMessage(err.Error(),c)
		return
	}
	fmt.Println("绑定参数：",param)
	if err,Account := service.EndSeparateAccount(param); err != nil {
		log.Log().Error("多次分账失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}


func ManualClearing(c *gin.Context) {
	var param model.ManualClearing
	err:= c.ShouldBindJSON(&param)

	if err!=nil{
		response.FailWithMessage(err.Error(),c)
		return
	}
	fmt.Println("绑定参数：",param)
	if err,Account := service.ManualClearing(param); err != nil {
		log.Log().Error("结算分账失败!", zap.Any("err", err))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&Account, c)
	}
}





func UserTopUp(c *gin.Context) {
	//var param usertopup.UserTopUp
	//err:= c.ShouldBindJSON(&param)
	//if err!=nil{
	//	response.FailWithMessage(err.Error(),c)
	//}
	//if err := utils.GVerify(param, usertopuputils.UserTopUp); err != nil {
	//	response.FailWithMessage(err.Error(), c)
	//	return
	//}
	//if err := fservice.UserTopUp(param); err != nil {
	//	log.Log().Error("充值插入失败", zap.Any("err", err))
	//	err := fmt.Sprintf("%s", err)
	//	response.FailWithMessage(err, c)
	//	return
	//} else {
	//	response.OkWithData(&param, c)
	//}
}





func RefundAction(c *gin.Context) {
	var err error
	var param model.Refund
	err= c.ShouldBindJSON(&param)
	if err!=nil{
		response.FailWithMessage(err.Error(),c)
		return
	}
	if err = utils.GVerify(param, usertopuputils.Refund); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err,res := service.RefundAction(param); err != nil {
		log.Log().Error("充值插入失败", zap.Any("err", res))
		err := fmt.Sprintf("%s", err)
		response.FailWithMessage(err, c)
		return
	} else {
		response.OkWithData(&res, c)
	}

}