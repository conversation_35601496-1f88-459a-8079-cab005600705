package common

import (
	"bytes"
	joinpay_res "convergence/response"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	nurl "net/url"
)

var BaseUrl string

const (
	SEPARATEACCOUNTCALLBACK_URL  = "/SeparateAccountCallBack"
	SINGLEPAYCALLBACK_URL        = "/singlePayCallBack"
	REFUND_CALL_BACK_URL         = "/refundNotify"
	PAY_CALL_BACK_URL            = "/joinPayNotify"
	SMALL_SHOP_PAY_CALL_BACK_URL = "/joinPayNotifyBySmallShop"
	RECHARGE_PAY_CALL_BACK_URL   = "/rechargeJoinPayNotify"
	CALLBACK_URL                 = "/CallBack"
	SEPARATEACCOUNT_URL          = "https://www.joinpay.com/allocFunds"
	PAYQRCODE_URL                = "https://www.joinpay.com/trade/uniPayApi.action"
	PAYREFUND_URL                = "https://www.joinpay.com/trade/refund.action"
)

func SetUrl(url string, TLS *tls.ConnectionState) {

	scheme := "http://"
	if TLS != nil {
		scheme = "https://"
	}
	BaseUrl = scheme + url

	fmt.Println("设置url", BaseUrl)
}

func HttpPostForm(data nurl.Values, url string) (res map[string]interface{}) {
	resp, err := http.PostForm(url, data)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		// handle error
	}
	fmt.Println("返回jsongg:", string(body))
	resErr := json.Unmarshal(body, &res) //第二个参数要地址传递
	if resErr != nil {
		fmt.Println("err = ", resErr)
		return
	}
	return res

}

func HttpPost(data []byte, url string) (res joinpay_res.Response) {
	resp, err := http.Post(url,
		"application/json",
		bytes.NewBuffer(data),
	)
	if err != nil {
		fmt.Println(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		// handle error
	}
	fmt.Println("返回json:", string(body))

	res_err := json.Unmarshal([]byte(body), &res) //第二个参数要地址传递
	if res_err != nil {
		fmt.Println("err = ", res_err)
		return
	}

	return res

}
