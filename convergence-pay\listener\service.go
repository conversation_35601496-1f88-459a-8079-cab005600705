package listener

import (
	"convergence/model"
	"encoding/json"
	"fmt"
	"github.com/streadway/amqp"

	"yz-go/source"
)

var publishRabbitMQ *source.SafeCh

const (
	ExchangedName = "orderE"
	PayRouTing    = "order.payment.pay"
	RefundRouTing = "order.payment.refund"
	ExchangeType  = "topic"
)

func getPublishRabbitMQ() *amqp.Channel {
	if publishRabbitMQ == nil {
		var err error
		publishRabbitMQ = source.SafeMQ(func(ch *amqp.Channel) {
			err = ch.ExchangeDeclare(
				ExchangedName, // name
				ExchangeType,  // type
				true,          // durable
				false,         // auto-deleted
				false,         // internal
				false,         // no-wait
				nil,           // arguments
			)
		})
		if err != nil {
			fmt.Println(err)
		}
	}
	return publishRabbitMQ.GetCh()

}
func PublishExchange(routing string, data model.OrderMessage) (err error) {

	fmt.Println("准备发消息")
	message, err := json.Marshal(data)

	if err != nil {
		fmt.Println("数据解析错误", err)
		return
	}

	err = getPublishRabbitMQ().Publish(
		ExchangedName,
		routing,
		//如果为true，根据exchange类型和route规则，如果无法找到符合条件的队列，那么会把发送的消息返回给发送者
		false,
		//如果为true，当exchange发送消息到队列侯发现队列上没有绑定消费者，则会把消息发还给发送者
		false,
		amqp.Publishing{
			ContentType:  "text/plain",
			Body:         message,
			DeliveryMode: 2,
		})

	if err != nil {
		fmt.Println("join-pay  发送消息失败")
	}
	return
}
