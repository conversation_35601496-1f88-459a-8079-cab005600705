package join_pay

func RunServer() {
	//router := gin.Default()

	//router.POST("getPayQrCode",v1.GetPayQrCode)
	//router.POST("separateAccount",v1.SeparateAccount)       //延迟分账
	//router.POST("moreSeparateAccount",v1.MoreSeparateAccount)   //多次分账
	//router.POST("endSeparateAccount",v1.EndSeparateAccount) 	   //完结分账
	//router.POST("manualClearing",v1.ManualClearing) 	   //手动完结
	////router.POST("editAccount",v1.ModifyAccount) 	   //完结分账
	//router.POST("userTopUp",v1.UserTopUp)
	//router.POST("refund",v1.RefundAction)
	////router.GET("joinPayNotify",v1.JoinPayNotify)   //支付回调地址
	////router.GET("refundNotify",v1.RefundNotify)   //退款回调地址
	//log.Fatalln(router.Run(":8002"))
}
