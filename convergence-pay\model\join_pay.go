package model

import (
	"yz-go/source"
)

type MerchantsNet struct {
	Method   string   `json:"method" form:"method" `
	Version  string   `json:"version" form:"version" `
	Data     InfoData `json:"data" `
	RandStr  string   `json:"rand_str" `
	SignType string   `json:"sign_type" form:"sign_type" `
	MchNo    string   `json:"mch_no" form:"mch_no" `
	Sign     string   `json:"sign" form:"sign" `
}

type SeparateAccountingRecords struct {
	source.Model
	Amount     uint   `json:"amount"`
	OrderSN    uint   `json:"order_sn"`
	SupplierID uint   `json:"supplier_id"`
	Info       string `json:"info" gorm:"column:info;comment:分账消息;"`
	Status     uint   `json:"status" gorm:"column:status;comment:分账状态;"`
	ReqData    string `json:"req_data" gorm:"column:req_data;comment:请求数据;type:text;"`
	ResData    string `json:"res_data" gorm:"column:res_data;comment:返回数据;type:text;"`
}

type InfoData struct {
	LoginName           string `json:"login_name" form:"login_name" `
	AltMchName          string `json:"alt_mch_name" form:"alt_mch_name" `
	AltMchShortName     string `json:"alt_mch_short_name" form:"alt_mch_short_name" `
	AltMerchantType     int8   `json:"alt_merchant_type" form:"alt_merchant_type" `
	BusiContactName     string `json:"busi_contact_name" form:"busi_contact_name" `
	BusiContactMobileNo string `json:"busi_contact_mobile_no" form:"busi_contact_mobile_no" `
	PhoneNo             string `json:"phone_no" form:"phone_no" `
	ManageScope         string `json:"manage_scope" form:"manage_scope" `
	ManageAddr          string `json:"manage_addr" form:"manage_addr" `
	LegalPerson         string `json:"legal_person" form:"legal_person" `
	IdCardNo            string `json:"id_card_no" form:"id_card_no" `
	IdCardExpiry        string `json:"id_card_expiry" form:"id_card_expiry" `
	LicenseNo           string `json:"license_no" form:"license_no" `
	LicenseExpiry       string `json:"license_expiry" form:"license_expiry" `
	SettMode            int8   `json:"sett_mode" form:"sett_mode" `
	SettDateType        int8   `json:"sett_date_type" form:"sett_date_type" `
	RiskDay             int8   `json:"risk_day" form:"risk_day" `
	BankAccountType     int8   `json:"bank_account_type" form:"bank_account_type" `
	BankAccountName     string `json:"bank_account_name" form:"bank_account_name" `
	BankAccountNo       string `json:"bank_account_no" form:"bank_account_no" `
	BankChannelMo       string `json:"bank_channel_no" form:"bank_channel_no" `
	NotifyUrl           string `json:"notify_url" form:"notify_url" `
}

// 如果含有time.Time 请自行import time包
type User struct {
	source.Model
	Mobile            string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar            string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username          string `json:"username" form:"username" gorm:"comment:用户登录名"`
	Password          string `json:"-"  gorm:"comment:用户登录密码"`
	NickName          string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status            int    `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	LevelID           uint   `json:"level_id" form:"level_id"`
	ParentId          uint   `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
	TemporaryParentId uint   `json:"temporary_parent_id" form:"temporary_parent_id" gorm:"comment:临时推荐会员id;"`
	QrCode            string `json:"qr_code" form:"qr_code" gorm:"column:qr_code;comment:个人二维码;type:varchar(255);"`
	InviteCode        string `json:"invite_code" gorm:"unique;comment:邀请码;type:varchar(50);"` // 允许读和创建
	WxUsername        string `json:"wx_username" form:"wx_username" gorm:"column:wx_username;comment:微信号"`
	WxOpenid          string `json:"wx_openid" form:"wx_openid" gorm:"column:wx_openid;comment:微信openid;type:varchar(255);size:255;"`
	WxMiniOpenid      string `json:"wx_mini_openid" form:"wx_mini_openid" gorm:"column:wx_mini_openid;comment:微信小程序openid;type:varchar(255);size:255;"`
	WxUnionid         string `json:"wx_unionid" form:"wx_unionid" gorm:"column:wx_unionid;comment:微信unionid;type:varchar(255);size:255;"`
}
type UniPay struct {
	P0_Version     string `json:"p0_Version" form:"p0_Version" `
	P1_MerchantNo  string `json:"p1_MerchantNo" form:"p1_MerchantNo" `
	P2_OrderNo     string `json:"p2_OrderNo" form:"p2_OrderNo" binding:"required"`
	P3_Amount      string `json:"p3_Amount" form:"p3_Amount"  binding:"required"`
	P4_Cur         string `json:"p4_Cur" form:"p4_Cur" `
	P5_ProductName string `json:"p5_ProductName" form:"p5_ProductName" `
	P7_Mp          int    `json:"p7_Mp" form:"p7_Mp" `
	MiniAppWechat  int    `json:"mini_app_wechat" form:"mini_app_wechat" `
	P9_NotifyUrl   string `json:"p9_NotifyUrl" form:"p9_NotifyUrl" `
	Q1_FrpCode     string `json:"q1_FrpCode" form:"q1_FrpCode" `
	Q4_IsShowPic   string `json:"q4_IsShowPic" form:"q4_IsShowPic" `
	Q5_OpenId      string `json:"q5_OpenId" form:"q5_OpenId" `
	Q7_AppId       string `json:"q7_AppId" form:"q7_AppId" `
	Hmac           string `json:"hmac" form:"hmac" `
	Uid            uint   `json:"uid" form:"uid" `
}

type Refund struct {
	P1_MerchantNo    string `json:"p1_MerchantNo" form:"p1_MerchantNo" `
	P2_OrderNo       string `json:"p2_OrderNo" form:"p2_OrderNo" binding:"required"`
	P3_RefundOrderNo string `json:"p3_RefundOrderNo" form:"p3_RefundOrderNo" `
	P4_RefundAmount  string `json:"p4_RefundAmount" form:"p4_RefundAmount" `
	P5_RefundReason  string `json:"p5_RefundReason" form:"p5_RefundReason" `
	P6_NotifyUrl     string `json:"p6_NotifyUrl" form:"p6_NotifyUrl" `
	P9_AltOrderNo    string `json:"p9_AltOrderNo" form:"p9_AltOrderNo" `
	Hmac             string `json:"hmac" form:"hmac" `
}

type SingleLaterAllocate struct {
	Method   string       `json:"method" form:"method" `
	Version  string       `json:"version" form:"version" `
	Data     AllocateData `json:"data" `
	RandStr  string       `json:"rand_str" `
	SignType string       `json:"sign_type" form:"sign_type" `
	MchNo    string       `json:"mch_no" form:"mch_no" `
	Sign     string       `json:"sign" form:"sign" `
}

type MoreSeparateAllocate struct {
	Method   string           `json:"method" form:"method" `
	Version  string           `json:"version" form:"version" `
	Data     MoreSeparateData `json:"data" `
	RandStr  string           `json:"rand_str" `
	SignType string           `json:"sign_type" form:"sign_type" `
	MchNo    string           `json:"mch_no" form:"mch_no" `
	Sign     string           `json:"sign" form:"sign" `
}

type ManualClearingAllocate struct {
	Method   string         `json:"method" form:"method" `
	Version  string         `json:"version" form:"version" `
	Data     ManualClearing `json:"data" `
	RandStr  string         `json:"rand_str" `
	SignType string         `json:"sign_type" form:"sign_type" `
	MchNo    string         `json:"mch_no" form:"mch_no" `
	Sign     string         `json:"sign" form:"sign" `
}

type AllocateData struct {
	AltOrderNo string    `json:"alt_order_no" form:"alt_order_no" `
	MchOrderNo string    `json:"mch_order_no" form:"mch_order_no" `
	AltInfo    []AltInfo `json:"alt_info" form:"alt_info" `
	AltUrl     string    `json:"alt_url" form:"alt_url" `
}

type MoreSeparateData struct {
	AltOrderNo    string    `json:"alt_order_no" form:"alt_order_no" `
	MchOrderNo    string    `json:"mch_order_no" form:"mch_order_no" `
	AltInfo       []AltInfo `json:"alt_info" form:"alt_info" `
	AltThisAmount string    `json:"alt_this_amount" form:"alt_this_amount" `
	CallBackUrl   string    `json:"callback_url" form:"callback_url" `
}

type ManualClearing struct {
	AltMchNo     string  `json:"alt_mch_no" form:"alt_mch_no" `
	SettleAmount float64 `json:"settle_amount" form:"settle_amount" `
	SettleFee    string  `json:"settle_fee" form:"settle_fee" `
	MchOrderNo   string  `json:"mch_order_no" form:"mch_order_no" `
	CallBackUrl  string  `json:"callback_url" form:"callback_url" `
}

type AltInfo struct {
	AltMchNo  string `json:"alt_mch_no" form:"alt_mch_no" `
	AltAmount string `json:"alt_amount" form:"alt_amount" `
}

type MiniPlusErrData struct {
	R3RefundOrderNo   string `json:"r3_RefundOrderNo"`
	RbCode            string `json:"rb_Code"`
	RcCodeMsg         string `json:"rc_CodeMsg"`
	R4RefundAmount    string `json:"r4_RefundAmount"`
	RdMarketRefAmount string `json:"rd_MarketRefAmount"`
	R2OrderNo         string `json:"r2_OrderNo"`
	R5RefundTrxNo     string `json:"r5_RefundTrxNo"`
	Hmac              string `json:"hmac"`
	R1MerchantNo      string `json:"r1_MerchantNo"`
	RaStatus          string `json:"ra_Status"`
}

type MiniPlusResData struct {
	R5Mp         string `json:"r5_Mp"`
	R1MerchantNo string `json:"r1_MerchantNo"`
	RdPic        string `json:"rd_Pic"`
	R6FrpCode    string `json:"r6_FrpCode"`
	R7TrxNo      string `json:"r7_TrxNo"`
	R0Version    string `json:"r0_Version"`
	R3Amount     string `json:"r3_Amount"`
	R4Cur        string `json:"r4_Cur"`
	R2OrderNo    string `json:"r2_OrderNo"`
	RbCodeMsg    string `json:"rb_CodeMsg"`
	Hmac         string `json:"hmac"`
	RaCode       int    `json:"ra_Code"`
	RcResult     string `json:"rc_Result"`
}
type MiniPlusRefundResData struct {
	R3RefundOrderNo   string `json:"r3_RefundOrderNo"`
	RbCode            string `json:"rb_Code"`
	RcCodeMsg         string `json:"rc_CodeMsg"`
	R4RefundAmount    string `json:"r4_RefundAmount"`
	RdMarketRefAmount string `json:"rd_MarketRefAmount"`
	R2OrderNo         string `json:"r2_OrderNo"`
	R5RefundTrxNo     string `json:"r5_RefundTrxNo"`
	Hmac              string `json:"hmac"`
	R1MerchantNo      string `json:"r1_MerchantNo"`
	RaStatus          string `json:"ra_Status"`
}
