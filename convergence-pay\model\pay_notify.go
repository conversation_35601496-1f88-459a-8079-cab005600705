package model

type PayNotify struct {
	MerchantNo string `json:"r1_MerchantNo" form:"r1_MerchantNo" query:"r1_MerchantNo"`
	OrderNo    string `json:"r2_OrderNo" form:"r2_OrderNo"  query:"r2_OrderNo"`
	Mp         string `json:"r5_Mp" form:"r5_Mp" query:"R5_Mp"`
	Status     string `json:"r6_Status" form:"r6_Status" `
	TrxNo      string `json:"r7_TrxNo" form:"r7_TrxNo" `
}

type SingPayCallBack struct {
	Fee                  float64 `json:"fee" form:"fee"`
	MerchantOrderNo      string  `json:"merchantOrderNo" form:"merchantOrderNo"`
	PaidAmount           float64 `json:"paidAmount" form:"paidAmount"`
	PlatformSerialNo     string  `json:"platformSerialNo" form:"platformSerialNo"`
	ReceiverAccountNoEnc string  `json:"receiverAccountNoEnc" form:"receiverAccountNoEnc"`
	ReceiverNameEnc      string  `json:"receiverNameEnc" form:"receiverNameEnc"`
	Status               int     `json:"status" form:"status"`
	ErrorCode            string  `json:"errorCode" form:"errorCode"`
	ErrorCodeDesc        string  `json:"errorCodeDesc" form:"errorCodeDesc"`
}

type RefundNotify struct {
	MerchantNo   string `json:"r1_MerchantNo" form:"r1_MerchantNo" query:"r1_MerchantNo"`
	OrderNo      string `json:"r2_OrderNo" form:"r2_OrderNo"  query:"r2_OrderNo"`
	Status       string `json:"ra_Status" form:"ra_Status" `
	Code         string `json:"rb_Code" form:"rb_Code" `
	CodeMsg      string `json:"rc_CodeMsg" form:"rc_CodeMsg" `
	RefundAmount string `json:"r4_RefundAmount_str" form:"r4_RefundAmount_str" `
}

type OrderMessage struct {
	MessageType int `json:"message_type"`

	PaySN string `json:"pay_sn"`
}
