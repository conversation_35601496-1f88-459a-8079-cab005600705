package response

//
//type Response struct {
//	RespCode          string    `json:"resp_code"`
//	RespMsg       string `json:"resp_msg"`
//	RandStr    string `json:"rand_str"`
//	SignType string    `json:"sign_type"`
//	MchNo       string   `json:"mch_no"`
//	Sign  string   `json:"sign"`
//	AesKey  string   `json:"aes_key"`
//	Data ResData  `json:"data"`
//}
//
//type ResData struct {
//	AltMchNo  string   `json:"alt_mch_no"`
//	AltMchName  string   `json:"alt_mch_name"`
//	OrderStatus  string   `json:"order_status"`
//	BizCode  string   `json:"biz_code"`
//	BizMsg  string   `json:"biz_msg"`
//
//
//
//}



type PageResult struct {
	Data     interface{} `json:"data"`
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
	NextUrl  string      `json:"next_url"`
}

type Response struct {
	RespCode          string    `json:"resp_code"`
	RespMsg       string `json:"resp_msg"`
	RandStr    string `json:"rand_str"`
	SignType string    `json:"sign_type"`
	MchNo       string   `json:"mch_no"`
	Sign  string   `json:"sign"`
	AesKey  string   `json:"aes_key"`
	Data ResData  `json:"data"`
}

type ResData struct {
	AltMchNo  string   `json:"alt_mch_no"`
	SignTrxNo string `json:"sign_trx_no"`
	AltMchName  string   `json:"alt_mch_name"`
	OrderStatus  string   `json:"order_status"`
	BizCode  string   `json:"biz_code"`
	BizMsg  string   `json:"biz_msg"`
	SignContent  string   `json:"sign_content"`






}
