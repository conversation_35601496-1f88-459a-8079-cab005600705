package service

import (
	"convergence/common"
	"convergence/model"
	"encoding/json"
	"errors"
	"fmt"
	reqv3 "github.com/imroc/req/v3"
	"github.com/xingliuhua/leaf"
	"go.uber.org/zap"
	"net/url"
	model3 "payment/model"
	"payment/request"
	model4 "small-shop/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/config"
	model2 "yz-go/model"
	setting2 "yz-go/setting"
	"yz-go/utils"
)

func GetHost() string {
	Host := config.Config().Local.Host
	lastChar := string(Host[len(Host)-1])
	if lastChar == "/" {
		Host = Host[:len(Host)-1]
	}
	return Host
}

func GetMiniPlusPayInfo(param model.UniPay) (err error, miniPlusResData model.MiniPlusResData) {
	log.Log().Info("GetMiniPlusPayInfo param", zap.Any("info", param))
	var setting model2.SysSetting
	_, shopJson := setting.GetSetting("shop_setting")
	var shop Value
	err = json.Unmarshal([]byte(shopJson), &shop)
	if err != nil {
		return
	}
	//config.Config().Join.MerchantNo = "888121700005599"
	//config.Config().Join.TradeMerchantNo = "777156000686981"
	//config.Config().Join.HmacVal = "7bb62e69c2f248e58db336220dd8aa8f"
	postData := url.Values{}
	postData.Add("p0_Version", "1.0")
	postData.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	postData.Add("p2_OrderNo", param.P2_OrderNo)
	log.Log().Info("GetMiniPlusPayInfo P3_Amount", zap.Any("info", param.P3_Amount))

	postData.Add("p3_Amount", param.P3_Amount)
	postData.Add("p4_Cur", "1")
	postData.Add("p5_ProductName", shop.ShopName)
	postData.Add("p7_Mp", strconv.Itoa(model3.CONVERGENCEMINIPLUS))
	log.Log().Info("plus  notify url ", zap.Any("info", GetHost()+"/supplyapi/api/payment/convergenceMicroPlus"))
	postData.Add("p9_NotifyUrl", GetHost()+"/supplyapi/api/payment/convergenceMicroPlus")
	postData.Add("q1_FrpCode", "WEIXIN_CJXCX")
	postData.Add("qa_TradeMerchantNo", config.Config().Join.TradeMerchantNo)
	var str string
	str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("qa_TradeMerchantNo") + "" + config.Config().Join.HmacVal
	signStr := utils.MD5V([]byte(str))
	postData.Add("hmac", signStr)
	log.Log().Info("GetMiniPlusPayInfo", zap.Any("info", postData))
	client := reqv3.C()
	clientRes, clientErr := client.R().SetSuccessResult(&miniPlusResData).SetFormDataFromValues(postData).Post(common.PAYQRCODE_URL)
	log.Log().Info("clientRes SetSuccessResult", zap.Any("info", clientRes.String()))
	if clientErr != nil {
		err = errors.New(clientErr.Error())
		log.Log().Info("获取支付信息失败!", zap.Any("info", clientErr.Error()))
		return
	}
	if clientRes.IsSuccessState() {
		if 100 != miniPlusResData.RaCode {
			err = errors.New(miniPlusResData.RbCodeMsg)
			log.Log().Error("获取支付信息失败!", zap.Any("info", miniPlusResData))
			return
		}
	}
	if clientRes.IsErrorState() {
		err = errors.New("获取支付信息请求发生错误:" + clientRes.Status)
		log.Log().Info("获取支付信息请求发生错误!", zap.Any("info", clientRes.ResultState()))
		return
	}
	return
}

func GetMiniPlusPayInfoBySmallShop(param model.UniPay) (err error, miniPlusResData model.MiniPlusResData) {
	log.Log().Info("GetMiniPlusPayInfo小商店", zap.Any("info", param))
	//config.Config().Join.MerchantNo = "888121700005599"
	//config.Config().Join.TradeMerchantNo = "777156000686981"
	//config.Config().Join.HmacVal = "7bb62e69c2f248e58db336220dd8aa8f"

	_, data := setting2.GetSetting("smallShopConvergenceSetting")

	byteData := []byte(data)
	var setting model4.ConvergenceSetting
	err = json.Unmarshal(byteData, &setting)
	if err != nil {
		return
	}
	if setting.Merchantno == "" || setting.Trademerchantno == "" || setting.Hmacval == "" {
		err = errors.New("请先完善小商店汇聚支付设置")
		return
	}

	//setting.Hmacval = "7bb62e69c2f248e58db336220dd8aa8f"
	//setting.Merchantno = "888121700005599"
	//setting.Trademerchantno = "777128000701254"

	//param.P3_Amount = "0.01"
	//param.P2_OrderNo = strconv.Itoa(int(time.Now().Unix()))
	postData := url.Values{}
	postData.Add("p0_Version", "1.0")
	postData.Add("p1_MerchantNo", setting.Merchantno)
	postData.Add("p2_OrderNo", param.P2_OrderNo)
	postData.Add("p3_Amount", param.P3_Amount)
	postData.Add("p4_Cur", "1")
	postData.Add("p5_ProductName", "支付")
	postData.Add("p7_Mp", strconv.Itoa(model3.CONVERGENCEMINIPLUS))
	log.Log().Info("plus  notify url ", zap.Any("info", GetHost()+"/supplyapi/api/smallShop/finance/joinPlusPayNotify"))
	postData.Add("p9_NotifyUrl", GetHost()+"/supplyapi/api/smallShop/finance/joinPlusPayNotify")
	postData.Add("q1_FrpCode", "WEIXIN_CJXCX")
	postData.Add("qa_TradeMerchantNo", setting.Trademerchantno)
	var str string
	str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("qa_TradeMerchantNo") + "" + setting.Hmacval
	signStr := utils.MD5V([]byte(str))
	postData.Add("hmac", signStr)
	log.Log().Info("GetMiniPlusPayInfo小商店 reqdata ", zap.Any("info", postData))
	client := reqv3.C()
	clientRes, clientErr := client.R().SetSuccessResult(&miniPlusResData).SetFormDataFromValues(postData).Post(common.PAYQRCODE_URL)
	log.Log().Info("clientRes SetSuccessResult GetMiniPlusPayInfoBySmallShop", zap.Any("info", clientRes.String()))

	if clientErr != nil {
		err = errors.New(clientErr.Error())
		log.Log().Info("获取支付信息失败!", zap.Any("info", clientErr.Error()))
		return
	}
	if clientRes.IsSuccessState() {
		if 100 != miniPlusResData.RaCode {
			err = errors.New(miniPlusResData.RbCodeMsg)
			log.Log().Error("获取支付信息失败!", zap.Any("info", miniPlusResData))
			return
		}
	}
	if clientRes.IsErrorState() {
		err = errors.New("获取支付信息请求发生错误:" + clientRes.Status)
		log.Log().Info("获取支付信息请求发生错误!", zap.Any("info", clientRes.ResultState()))
		return
	}
	return
}
func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}
func MiniPlusRefund(param request.Refund) (err error, miniPlusRefundResData model.MiniPlusRefundResData) {
	log.Log().Info("MiniPlusRefund  ", zap.Any("info", param))
	//
	//config.Config().Join.MerchantNo = "888121700005599"
	//config.Config().Join.TradeMerchantNo = "777156000686981"
	//config.Config().Join.HmacVal = "7bb62e69c2f248e58db336220dd8aa8f"
	//param.Amount = 1
	//param.PaySN = "188809541851"
	postData := url.Values{}
	postData.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	postData.Add("p2_OrderNo", param.PaySN)
	postData.Add("p3_RefundOrderNo", GetOrderNo())
	amount := strconv.FormatFloat(Decimal(float64(param.Amount)/float64(100)), 'f', -1, 64)
	//strAmount := strconv.Itoa(int(amount))
	postData.Add("p4_RefundAmount", amount)
	postData.Add("p5_RefundReason", "申请退款")
	log.Log().Info("plus  refund notify url ", zap.Any("info", GetHost()+"/supplyapi/api/payment/convergenceMicroPlusRefund"))
	postData.Add("p6_NotifyUrl", GetHost()+"/supplyapi/api/payment/convergenceMicroPlusRefund")
	postData.Add("q1_version", "2.2")

	var str string
	str = postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_RefundOrderNo") + postData.Get("p4_RefundAmount") + postData.Get("p5_RefundReason") + postData.Get("p6_NotifyUrl") + postData.Get("q1_version") + "" + config.Config().Join.HmacVal
	signStr := utils.MD5V([]byte(str))
	postData.Add("hmac", signStr)
	log.Log().Info("MiniPlusRefundInfo", zap.Any("info", postData))
	client := reqv3.C()
	var miniPlusErrData model.MiniPlusErrData
	clientRes, clientErr := client.R().SetErrorResult(&miniPlusErrData).SetSuccessResult(&miniPlusRefundResData).SetFormDataFromValues(postData).Post(common.PAYREFUND_URL)
	log.Log().Info("汇聚 小程序plus退款申请返回信息", zap.Any("info", clientRes.String()))

	if clientErr != nil {
		err = errors.New(clientErr.Error())
		log.Log().Info("获取支付信息失败!", zap.Any("info", clientErr.Error()))
		return
	}
	if clientRes.IsSuccessState() {

		if "100" != miniPlusRefundResData.RaStatus {
			err = errors.New(miniPlusErrData.RcCodeMsg)
			log.Log().Error("获取plus退款信息失败!", zap.Any("info", miniPlusErrData))
			return
		}
	}
	if clientRes.IsErrorState() {
		err = errors.New("获取支付信息请求发生错误:" + clientRes.Status)
		log.Log().Info("获取plus支付信息请求发生错误!", zap.Any("info", clientRes.ResultState()))
		return
	}
	return
}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}
