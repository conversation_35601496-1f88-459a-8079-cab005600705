package service

import (
	joinpay_res "convergence/response"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gin-vue-admin/cmd/gva"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"

	"convergence/common"
	"convergence/model"
	//usertopup "finance/model"
	//joinpay_res "finance/response"
	"fmt"
	"net/url"
	//shop "shop/setting"
	"yz-go/config"

	"yz-go/utils"
)

const MiniApp = 4
const OfficialAccount = 3
const PC = 9999
const H5 = 11

func SeparateAccount(param model.AllocateData) (err error, res joinpay_res.Response) {
	fmt.Println("请求参数order_id:", param.AltOrderNo)
	var data model.SingleLaterAllocate
	param.AltUrl = config.Config().Join.PayNotifyUrl + common.SEPARATEACCOUNTCALLBACK_URL

	data = model.SingleLaterAllocate{
		Method:   "altHandle.singleLaterAllocate",
		Version:  "1.0",
		RandStr:  utils.MD5V([]byte("str")),
		SignType: "1",
		MchNo:    config.Config().Join.MerchantNo,
		Data:     param,
	}

	//data.Data.AltInfo = append(data.Data.AltInfo, model.AltInfo{AltMchNo:"***************",AltAmount:"0.1"})
	//data.Data.AltInfo = append(data.Data.AltInfo, model.AltInfo{AltMchNo:"111",AltAmount:"0.1"})
	sign, _ := json.Marshal(data.Data)
	str := "data=" + string(sign) + "&mch_no=" + data.MchNo + "&method=" + data.Method + "&rand_str=" + data.RandStr + "&sign_type=" + data.SignType + "&version=" + data.Version + "&key=" + config.Config().Join.HmacVal
	sign_str := utils.MD5V([]byte(str))
	data.Sign = sign_str
	b, _ := json.Marshal(data)
	fmt.Println("分账post数据：", string(b))

	fmt.Println("签名数据：", string(str))
	res = common.HttpPost(b, common.SEPARATEACCOUNT_URL)
	if "B100000" == res.Data.BizCode {
		fmt.Println("分账成功")
	} else {
		fmt.Println("分账失败")
		fmt.Println(res.Data.BizMsg)
		err = errors.New(res.Data.BizMsg)
	}

	return
}

func MoreSeparateAccount(param model.MoreSeparateData) (err error, res joinpay_res.Response) {
	fmt.Println("分账请求参数order_id:", param.AltOrderNo)
	var data model.MoreSeparateAllocate
	param.CallBackUrl = config.Config().Join.PayNotifyUrl + common.SEPARATEACCOUNTCALLBACK_URL

	data = model.MoreSeparateAllocate{
		Method:   "altHandle.manyLaterAllocate",
		Version:  "1.0",
		RandStr:  utils.MD5V([]byte("str")),
		SignType: "1",
		MchNo:    config.Config().Join.MerchantNo,
		Data:     param,
	}

	sign, _ := json.Marshal(data.Data)
	str := "data=" + string(sign) + "&mch_no=" + data.MchNo + "&method=" + data.Method + "&rand_str=" + data.RandStr + "&sign_type=" + data.SignType + "&version=" + data.Version + "&key=" + config.Config().Join.HmacVal
	signStr := utils.MD5V([]byte(str))
	data.Sign = signStr
	b, _ := json.Marshal(data)
	log.Log().Info("分账post数据!", zap.Any("err", string(b)))

	res = common.HttpPost(b, common.SEPARATEACCOUNT_URL)

	//resJsonData, _ := json.Marshal(res)
	//
	//source.DB().Create(&SeparateAccountingRecords)
	if "B100000" == res.Data.BizCode {

		log.Log().Info("分账成功!", zap.Any("err", res))

	} else {

		log.Log().Error("分账失败!", zap.Any("err", res))
		//err = errors.New(res.Data.BizMsg)
	}

	return
}

func EndSeparateAccount(param model.MoreSeparateData) (err error, res joinpay_res.Response) {
	fmt.Println("请求参数order_id:", param.AltOrderNo)
	var data model.MoreSeparateAllocate
	param.CallBackUrl = config.Config().Join.PayNotifyUrl + common.SEPARATEACCOUNTCALLBACK_URL

	data = model.MoreSeparateAllocate{
		Method:   "altHandle.finishAllocate",
		Version:  "1.0",
		RandStr:  utils.MD5V([]byte("str")),
		SignType: "1",
		MchNo:    config.Config().Join.MerchantNo,
		Data:     param,
	}

	sign, _ := json.Marshal(data.Data)
	str := "data=" + string(sign) + "&mch_no=" + string(data.MchNo) + "&method=" + string(data.Method) + "&rand_str=" + string(data.RandStr) + "&sign_type=" + string(data.SignType) + "&version=" + string(data.Version) + "&key=" + config.Config().Join.HmacVal
	sign_str := utils.MD5V([]byte(str))
	data.Sign = sign_str
	b, _ := json.Marshal(data)
	fmt.Println("完结post数据：", string(b))
	res = common.HttpPost(b, common.SEPARATEACCOUNT_URL)
	if "B100000" == res.Data.BizCode {
		fmt.Println("完结分账成功")
	} else {
		fmt.Println("完结分账失败")
		fmt.Println(res.Data.BizMsg)
		err = errors.New(res.Data.BizMsg)
	}

	return
}

func ManualClearing(param model.ManualClearing) (err error, res joinpay_res.Response) {
	fmt.Println("请求参数order_id:", param.AltMchNo)
	var data model.ManualClearingAllocate
	param.CallBackUrl = common.SEPARATEACCOUNTCALLBACK_URL

	data = model.ManualClearingAllocate{
		Method:   "altSettle.launch",
		Version:  "1.1",
		RandStr:  utils.MD5V([]byte("str")),
		SignType: "1",
		MchNo:    config.Config().Join.MerchantNo,
		Data:     param,
	}

	//data.Data.AltInfo = append(data.Data.AltInfo, model.AltInfo{AltMchNo:"***************",AltAmount:"0.1"})
	//data.Data.AltInfo = append(data.Data.AltInfo, model.AltInfo{AltMchNo:"111",AltAmount:"0.1"})
	sign, _ := json.Marshal(data.Data)
	str := "data=" + string(sign) + "&mch_no=" + string(data.MchNo) + "&method=" + string(data.Method) + "&rand_str=" + string(data.RandStr) + "&sign_type=" + string(data.SignType) + "&version=" + string(data.Version) + "&key=" + config.Config().Join.HmacVal
	sign_str := utils.MD5V([]byte(str))
	data.Sign = sign_str
	b, _ := json.Marshal(data)
	fmt.Println("post数据：", string(b))
	res = common.HttpPost(b, common.SEPARATEACCOUNT_URL)
	if "B100000" == res.Data.BizCode {
		fmt.Println("结算分账成功")
	} else {
		fmt.Println("结算分账失败")
		fmt.Println(res.Data.BizMsg)
		err = errors.New(res.Data.BizMsg)
	}

	return
}

type Value struct {
	ShopName string `json:"shop_name"`
}

//var WechatMiniSetting *Value

// 微信小程序配置
type WxMiniSysSetting struct {
	model2.SysSetting
	Values Values `json:"value"`
}

func (WxMiniSysSetting) TableName() string {
	return "sys_settings"
}

func (value Values) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Values) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type Values struct {
	IsOpen                     int    `mapstructure:"isopen" json:"isopen" yaml:"isopen"`                                                                      //状态1开始2关闭
	AppId                      string `mapstructure:"appid" json:"appid" yaml:"appid"`                                                                         //AppID
	AppSecret                  string `mapstructure:"appsecret" json:"appsecret" yaml:"appsecret"`                                                             //AppSecret
	Key                        string `mapstructure:"key" json:"key" yaml:"key"`                                                                               //腾讯位置服务KEY
	Sign                       string `mapstructure:"sign" json:"sign" yaml:"sign"`                                                                            //腾讯位置服务签名
	Mchid                      string `mapstructure:"mch_id" json:"mch_id" yaml:"mch_id"`                                                                      //支付商户号
	PayKey                     string `mapstructure:"pay_key" json:"pay_key" yaml:"pay_key"`                                                                   //支付密钥
	CertPath                   string `mapstructure:"cert_path" json:"cert_path" yaml:"cert_path"`                                                             //cert证书文件路径
	KeyPath                    string `mapstructure:"key_path" json:"key_path" yaml:"key_path"`                                                                //cert证书文件路径
	MchCertificateSerialNumber string `mapstructure:"mch_certificate_serial_number" json:"mch_certificate_serial_number" yaml:"mch_certificate_serial_number"` //证书编号
	Cert                       string `mapstructure:"Cert" json:"Cert" yaml:"Cert"`                                                                            //微信平台证书文件路径 （不需要前端上传）
}

func GetSysMiniSetting(key string) (err error, sysSettings string) {
	var set model2.SysSetting
	err = source.DB().Where("`key` = ?", key).First(&set).Error

	sysSettings = set.Value
	return
}

type WxSetting struct {
	model2.SysSetting
	Value WxValue `json:"value"`
}
type WxValue struct {
	IsOpen                     int    `mapstructure:"isopen" json:"isopen" yaml:"isopen"`                                                                      //状态1开始2关闭
	AppId                      string `mapstructure:"appid" json:"appid" yaml:"appid"`                                                                         //AppID
	AppSecret                  string `mapstructure:"appsecret" json:"appsecret" yaml:"appsecret"`                                                             //AppSecret
	Mchid                      string `mapstructure:"mch_id" json:"mch_id" yaml:"mch_id"`                                                                      //支付商户号
	PayKey                     string `mapstructure:"pay_key" json:"pay_key" yaml:"pay_key"`                                                                   //支付密钥
	CertPath                   string `mapstructure:"cert_path" json:"cert_path" yaml:"cert_path"`                                                             //cert证书文件路径
	KeyPath                    string `mapstructure:"key_path" json:"key_path" yaml:"key_path"`                                                                //cert证书文件路径
	MchCertificateSerialNumber string `mapstructure:"mch_certificate_serial_number" json:"mch_certificate_serial_number" yaml:"mch_certificate_serial_number"` //证书编号
	Cert                       string `mapstructure:"cert" json:"cert" yaml:"cert"`                                                                            //微信平台证书 后端下载的不是前端上传的 （暂时微信除了API下载没有任何地方获取这个证书）
	Serial                     string `mapstructure:"serial" json:"serial" yaml:"serial"`                                                                      //微信平台证书序列号
}

func (value WxValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *WxValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (WxSetting) TableName() string {
	return "sys_settings"
}

type SmallShopUser struct {
	ID           uint   `json:"id" form:"id" gorm:"primarykey"`
	WxMiniOpenid string `json:"wx_mini_openid" form:"wx_mini_openid" gorm:"column:wx_mini_openid;comment:微信小程序openid;type:varchar(255);size:255;"`
}

func GetPayQrCodeBySmallShopMini(param model.UniPay) (err error, res map[string]interface{}) {
	var setting model2.SysSetting
	var user SmallShopUser
	err = source.DB().Where("id = ?", param.Uid).First(&user).Error
	if err != nil {
		return
	}
	if param.P7_Mp == MiniApp {
		var miniSetting WxSetting
		err = source.DB().Where("`key` = ?", "small_shop_wx_setting").First(&miniSetting).Error
		if err != nil {
			return
		}
		var wechatmini_setting WxValue
		wechatmini_setting = miniSetting.Value
		param.Q7_AppId = wechatmini_setting.AppId
		param.Q5_OpenId = user.WxMiniOpenid
	}
	_, shopJson := setting.GetSetting("shop_setting")
	var shop Value
	err = json.Unmarshal([]byte(shopJson), &shop)
	if err != nil {
		return
	}
	postData := url.Values{}
	postData.Add("p0_Version", "1.0")
	postData.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	postData.Add("p2_OrderNo", param.P2_OrderNo)
	postData.Add("p3_Amount", param.P3_Amount)
	postData.Add("p4_Cur", "1")
	postData.Add("p5_ProductName", shop.ShopName)
	postData.Add("p6_ProductDesc", shop.ShopName)
	postData.Add("p7_Mp", strconv.Itoa(param.P7_Mp))
	postData.Add("p9_NotifyUrl", config.Config().Join.PayNotifyUrl+common.SMALL_SHOP_PAY_CALL_BACK_URL)
	postData.Add("p9_NotifyUrl", gva.DomainName+"/supplyapi/api/smallShop/finance/joinPayNotify")
	if param.P7_Mp == OfficialAccount {
		postData.Add("q1_FrpCode", "WEIXIN_GZH")
	} else if param.P7_Mp == MiniApp {
		postData.Add("q1_FrpCode", "WEIXIN_XCX")
	} else if param.P7_Mp == PC || param.P7_Mp == 1 {
		postData.Add("q1_FrpCode", "WEIXIN_NATIVE")
	} else if param.P7_Mp == H5 {
		postData.Add("q1_FrpCode", "ALIPAY_NATIVE")
	}

	postData.Add("q4_IsShowPic", "1")
	if param.P7_Mp == OfficialAccount || param.P7_Mp == MiniApp {
		postData.Add("q5_OpenId", param.Q5_OpenId)
		postData.Add("q7_AppId", param.Q7_AppId)
	}
	postData.Add("qa_TradeMerchantNo", config.Config().Join.TradeMerchantNo)
	if config.Config().Join.DivideAccountsSettings.Enable == "1" {
		postData.Add("qc_IsAlt", "1")
		postData.Add("qd_AltType", "13")
	} else {
		postData.Add("qc_IsAlt", "0")
		postData.Add("qd_AltType", "")
	}

	var str string
	if param.P7_Mp == OfficialAccount || param.P7_Mp == MiniApp {
		param.P7_Mp = PC
		str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("q5_OpenId") + postData.Get("q7_AppId") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal
	} else {
		str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal
	}
	signStr := utils.MD5V([]byte(str))

	postData.Add("hmac", signStr)
	log.Log().Info("post数据", zap.Any("info", postData))
	res = common.HttpPostForm(postData, common.PAYQRCODE_URL)
	code := int64(res["ra_Code"].(float64))
	if 100 != code {
		err = errors.New("获取二维码失败")
		log.Log().Info("获取二维码失败!", zap.Any("info", res))
		return
	}
	return
}

func GetPayQrCode(param model.UniPay) (err error, res map[string]interface{}) {
	log.Log().Info("GetPayQrCode", zap.Any("info", param))
	var setting model2.SysSetting
	//param.P7_Mp = 4

	var user model.User

	source.DB().Where("id=?", param.Uid).First(&user)

	if param.P7_Mp == OfficialAccount {
		_, settings := GetSysMiniSetting("wechatmini_setting")
		var wechatmini_setting Values

		json.Unmarshal([]byte(settings), &wechatmini_setting)

		param.Q7_AppId = config.Config().WechatPayment.AppId
		param.Q5_OpenId = config.Config().WechatPayment.AppSecret

	}

	if param.P7_Mp == MiniApp {
		_, settings := GetSysMiniSetting("wechatmini_setting")
		var wechatmini_setting Values

		json.Unmarshal([]byte(settings), &wechatmini_setting)

		param.Q7_AppId = wechatmini_setting.AppId
		param.Q5_OpenId = user.WxMiniOpenid
	}

	_, shopJson := setting.GetSetting("shop_setting")
	var shop Value
	err = json.Unmarshal([]byte(shopJson), &shop)
	if err != nil {
		return
	}

	postData := url.Values{}
	postData.Add("p0_Version", "1.0")
	postData.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	postData.Add("p2_OrderNo", param.P2_OrderNo)
	postData.Add("p3_Amount", param.P3_Amount)
	postData.Add("p4_Cur", "1")
	postData.Add("p5_ProductName", shop.ShopName)
	postData.Add("p6_ProductDesc", shop.ShopName)
	if param.MiniAppWechat == 1 {
		postData.Add("p7_Mp", "3001")
	} else {
		postData.Add("p7_Mp", strconv.Itoa(param.P7_Mp))
	}
	postData.Add("p9_NotifyUrl", config.Config().Join.PayNotifyUrl+common.PAY_CALL_BACK_URL)
	if param.P7_Mp == OfficialAccount {
		postData.Add("q1_FrpCode", "WEIXIN_GZH")
	} else if param.P7_Mp == MiniApp {
		postData.Add("q1_FrpCode", "WEIXIN_XCX")
	} else if param.P7_Mp == PC || param.P7_Mp == 1 {
		postData.Add("q1_FrpCode", "WEIXIN_NATIVE")
	} else if param.P7_Mp == H5 {
		postData.Add("q1_FrpCode", "ALIPAY_NATIVE")

	}

	postData.Add("q4_IsShowPic", "1")
	if param.P7_Mp == OfficialAccount || param.P7_Mp == MiniApp {
		postData.Add("q5_OpenId", param.Q5_OpenId)
		postData.Add("q7_AppId", param.Q7_AppId)
	}
	postData.Add("qa_TradeMerchantNo", config.Config().Join.TradeMerchantNo)

	if config.Config().Join.DivideAccountsSettings.Enable == "1" {
		postData.Add("qc_IsAlt", "1")
		postData.Add("qd_AltType", "13")
	} else {
		postData.Add("qc_IsAlt", "0")
		postData.Add("qd_AltType", "")
	}

	var str string
	if param.P7_Mp == OfficialAccount || param.P7_Mp == MiniApp {
		param.P7_Mp = PC
		str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("q5_OpenId") + postData.Get("q7_AppId") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal

	} else {
		str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal

	}
	//str := postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal
	signStr := utils.MD5V([]byte(str))

	postData.Add("hmac", signStr)
	log.Log().Info("post数据", zap.Any("info", postData))
	res = common.HttpPostForm(postData, common.PAYQRCODE_URL)
	code := int64(res["ra_Code"].(float64))
	if 100 != code {
		err = errors.New("获取二维码失败")
		log.Log().Info("获取二维码失败!", zap.Any("info", res))
		return
	}

	return
}

func GetRechargePayQrCode(param model.UniPay) (err error, res map[string]interface{}) {
	var setting model2.SysSetting
	//param.P7_Mp = 4

	var user model.User

	source.DB().Where("id=?", param.Uid).First(&user)

	if param.P7_Mp == OfficialAccount {
		_, settings := GetSysMiniSetting("wechatmini_setting")
		var wechatmini_setting Values

		json.Unmarshal([]byte(settings), &wechatmini_setting)

		param.Q7_AppId = config.Config().WechatPayment.AppId
		param.Q5_OpenId = config.Config().WechatPayment.AppSecret

	}

	if param.P7_Mp == MiniApp {
		_, settings := GetSysMiniSetting("wechatmini_setting")
		var wechatmini_setting Values

		json.Unmarshal([]byte(settings), &wechatmini_setting)

		param.Q7_AppId = wechatmini_setting.AppId
		param.Q5_OpenId = user.WxMiniOpenid
	}

	_, shopJson := setting.GetSetting("shop_setting")
	var shop Value
	err = json.Unmarshal([]byte(shopJson), &shop)
	if err != nil {
		return
	}

	postData := url.Values{}
	postData.Add("p0_Version", "1.0")
	postData.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	postData.Add("p2_OrderNo", param.P2_OrderNo)
	postData.Add("p3_Amount", param.P3_Amount)
	postData.Add("p4_Cur", "1")
	postData.Add("p5_ProductName", shop.ShopName)
	postData.Add("p6_ProductDesc", shop.ShopName)
	postData.Add("p7_Mp", strconv.Itoa(param.P7_Mp))
	postData.Add("p9_NotifyUrl", config.Config().Join.PayNotifyUrl+common.RECHARGE_PAY_CALL_BACK_URL)
	if param.P7_Mp == OfficialAccount {
		postData.Add("q1_FrpCode", "WEIXIN_GZH")
	} else if param.P7_Mp == MiniApp {
		postData.Add("q1_FrpCode", "WEIXIN_XCX")
	} else if param.P7_Mp == PC || param.P7_Mp == 1 {
		postData.Add("q1_FrpCode", "WEIXIN_NATIVE")
	} else if param.P7_Mp == H5 {
		postData.Add("q1_FrpCode", "ALIPAY_NATIVE")

	}

	postData.Add("q4_IsShowPic", "1")
	if param.P7_Mp == OfficialAccount || param.P7_Mp == MiniApp {
		postData.Add("q5_OpenId", param.Q5_OpenId)
		postData.Add("q7_AppId", param.Q7_AppId)
	}
	postData.Add("qa_TradeMerchantNo", config.Config().Join.TradeMerchantNo)
	//postData.Add("qc_IsAlt", "1")
	//postData.Add("qd_AltType", "13")

	var str string
	if param.P7_Mp == OfficialAccount || param.P7_Mp == MiniApp {
		param.P7_Mp = PC
		str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("q5_OpenId") + postData.Get("q7_AppId") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal

	} else {
		str = postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal

	}
	//str := postData.Get("p0_Version") + postData.Get("p1_MerchantNo") + postData.Get("p2_OrderNo") + postData.Get("p3_Amount") + postData.Get("p4_Cur") + postData.Get("p5_ProductName") + postData.Get("p6_ProductDesc") + postData.Get("p7_Mp") + postData.Get("p9_NotifyUrl") + postData.Get("q1_FrpCode") + postData.Get("q4_IsShowPic") + postData.Get("qa_TradeMerchantNo") + postData.Get("qc_IsAlt") + postData.Get("qd_AltType") + "" + config.Config().Join.HmacVal
	signStr := utils.MD5V([]byte(str))

	postData.Add("hmac", signStr)
	log.Log().Info("post数据", zap.Any("info", postData))
	res = common.HttpPostForm(postData, common.PAYQRCODE_URL)
	code := int64(res["ra_Code"].(float64))
	if 100 != code {
		err = errors.New("获取二维码失败")
		log.Log().Info("获取二维码失败!", zap.Any("info", res))
		return
	}

	return
}

//
//func PurchasingBalanceChange(param usertopup.PurchasingBalance) (err error) {
//	err = source.DB().Create(&param).Error
//	return
//}
//
//func SettlementBalanceChange(param usertopup.SettlementBalance) (err error) {
//	err = source.DB().Create(&param).Error
//	return
//}
//
//func UpdateUserBalance(order_id string) (err error) {
//
//	var topup usertopup.UserTopUp
//	res := source.DB().Model(topup).Where("order_id = ? ", order_id).First(&topup)
//	if res.Error != nil {
//		if res.Error == gorm.ErrRecordNotFound {
//			if res.Error != nil {
//				err = errors.New("未查到到该笔订单")
//				return
//			}
//		}
//	}
//
//	var param = usertopup.UpdateBalance{
//		Uid:    topup.Uid,
//		Amount: topup.Amount,
//		Type:   topup.PayType,
//		Action: 2,
//	}
//
//	err = UpdateJoinBalance(param)
//
//	return
//
//}

//func UpdateJoinBalance(param usertopup.UpdateBalance) (err error) {
//
//	var balance = usertopup.AccountBalance{}
//
//	balance.Uid = param.Uid
//	balance.PurchasingBalance = param.Amount
//	results := source.DB().Where("uid = ? and type= ?", param.Uid, param.Type).First(&balance)
//	if results.Error != nil {
//		if results.Error == gorm.ErrRecordNotFound {
//			results := source.DB().Create(&balance)
//			if results.Error != nil {
//				err = results.Error
//			}
//		}
//	} else {
//
//		var update_balance usertopup.AccountBalance
//		var action string
//
//		if param.Action == 1 {
//			action = "purchasing_balance+ ?"
//
//		}
//		if param.Action == 2 {
//			action = "purchasing_balance- ?"
//		}
//
//		if erra := source.DB().Model(update_balance).Where("uid = ? and type=?", param.Uid, param.Type).Update("purchasing_balance", gorm.Expr(action, param.Amount)).Error; err != nil {
//			err = erra
//		}
//
//	}
//
//	return
//
//}

type ReInfo struct {
	AltMchNo     string `json:"altMchNo"`
	AltRefAmount string `json:"altRefAmount"`
}

func RefundAction(parm model.Refund) (err error, res map[string]interface{}) {

	var reInfo ReInfo
	var reInfoList []ReInfo

	reInfo.AltMchNo = "***************" //分账方 开户号
	reInfo.AltRefAmount = parm.P4_RefundAmount

	reInfoList = append(reInfoList, reInfo)

	jsonData, _ := json.Marshal(reInfoList)

	post_data := url.Values{}

	post_data.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	post_data.Add("p2_OrderNo", parm.P2_OrderNo)
	post_data.Add("p3_RefundOrderNo", parm.P3_RefundOrderNo)
	post_data.Add("p4_RefundAmount", parm.P4_RefundAmount)
	post_data.Add("p5_RefundReason", "申请退款")
	post_data.Add("p6_NotifyUrl", config.Config().Join.PayNotifyUrl+common.REFUND_CALL_BACK_URL)
	post_data.Add("p7_AltRefInfo", string(jsonData))
	//post_data.Add("p9_AltOrderNo", parm.P9_AltOrderNo)

	str := post_data.Get("p1_MerchantNo") + post_data.Get("p2_OrderNo") + post_data.Get("p3_RefundOrderNo") + post_data.Get("p4_RefundAmount") + post_data.Get("p5_RefundReason") + post_data.Get("p5_ProductName") + post_data.Get("p6_NotifyUrl") + post_data.Get("p7_AltRefInfo") + "" + config.Config().Join.HmacVal
	sign_str := utils.MD5V([]byte(str))

	post_data.Add("hmac", sign_str)
	log.Log().Info("订单退款信息", zap.Any("info", post_data))

	res = common.HttpPostForm(post_data, common.PAYREFUND_URL)
	//code :=  int64(res["rb_Code"].(string))

	if "100" == res["rb_Code"] {
		log.Log().Info("退款成功", zap.Any("info", res))

	} else {
		err = errors.New(res["rc_CodeMsg"].(string))
		log.Log().Info("退款失败", zap.Any("info", res))
		return

	}

	return

}

func NoSepareteRefundAction(parm model.Refund) (err error, res map[string]interface{}) {

	post_data := url.Values{}

	post_data.Add("p1_MerchantNo", config.Config().Join.MerchantNo)
	post_data.Add("p2_OrderNo", parm.P2_OrderNo)
	post_data.Add("p3_RefundOrderNo", parm.P3_RefundOrderNo)
	post_data.Add("p4_RefundAmount", parm.P4_RefundAmount)
	post_data.Add("p5_RefundReason", "申请退款")
	post_data.Add("p6_NotifyUrl", config.Config().Join.PayNotifyUrl+common.REFUND_CALL_BACK_URL)

	str := post_data.Get("p1_MerchantNo") + post_data.Get("p2_OrderNo") + post_data.Get("p3_RefundOrderNo") + post_data.Get("p4_RefundAmount") + post_data.Get("p5_RefundReason") + post_data.Get("p5_ProductName") + post_data.Get("p6_NotifyUrl") + "" + config.Config().Join.HmacVal
	sign_str := utils.MD5V([]byte(str))

	post_data.Add("hmac", sign_str)
	log.Log().Info("订单退款信息", zap.Any("info", post_data))

	res = common.HttpPostForm(post_data, common.PAYREFUND_URL)
	code := res["rb_Code"]

	if 100 == code || code == "100" {
		log.Log().Info("退款成功", zap.Any("info", res))

	} else {
		err = errors.New(res["rc_CodeMsg"].(string))
		log.Log().Info("退款失败", zap.Any("info", res))

	}

	return

}
