package service

import (
	"convergence/listener"
	"convergence/model"
)

func JoinPaypayNotify(parm model.PayNotify) (err error) {

	if parm.Status == "100" {
		data := model.OrderMessage{
			PaySN:       parm.OrderNo,
			MessageType: 1,
		}
		err = listener.PublishExchange(listener.PayRouTing, data)
	}

	return

}

func RefundNotify(parm model.RefundNotify) (err error) {

	if parm.Status == "100" {
		data := model.OrderMessage{
			PaySN:       parm.OrderNo,
			MessageType: 1,
		}
		err = listener.PublishExchange(listener.RefundRouTing, data)
	}

	return

}

func SeparateNotify(parm model.RefundNotify) (err error) {

	//if parm.Status=="100" {
	//	data:=model.OrderMessage{
	//		PaySN: parm.OrderNo,
	//		MessageType:1,
	//	}
	//	err = listener.PublishExchange(listener.RefundRouTing, data)
	//}

	return

}
