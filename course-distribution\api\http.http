
POST  https://yx.gz.cn//supplyapi/app/lianlianapp/lianLianGetProductList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiNTVlYTUwOWEtNGExMi00ODFmLWJhN2UtNzQyYzUzYjlhMmE4IiwiSUQiOjI1LCJBcHBJRCI6MTMsIlVzZXJuYW1lIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2Nzc4MTYxMzgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NzIxMDMzOH0.65w-vjRJAXeDfJRAIDc0VFJTO8DKFVYq9cmjQZAtgz4
x-user-id: 1

{

  "page": 1,
  "pageSize": 10
}

### aaa
POST  https://yx.gz.cn/supplyapi/app/publicCurriculum/selectImportCurriculum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZGNhOTBiNzktMzE5Ny00MzFiLWFiYTYtMGU3NzE5Y2NkYzU5IiwiSUQiOjIyOSwiQXBwSUQiOjk4LCJVc2VybmFtZSI6IiIsIk5pY2tOYW1lIjoiIiwiQXV0aG9yaXR5SWQiOiIiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjgzOTQ0MzkxLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2ODMzMzg1OTF9.-b2_smDjJ4UhpslI1QQh04luRFq2zDcAwQEsg3ZpMRg
x-user-id: 1

{"page":1,"pageSize":10,"status":1,"is_import":"1"}




### findLecturerAndCurriculum
POST  {{api}}/api/Curriculum/findLecturerAndCurriculum
Content-Type: application/json
x-token:
x-user-id: 1

{"id": 1}


### findLecturerAndCurriculum
POST  {{api}}/api/Curriculum/getVideoUrl
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzg3NGMxYWEtMDQ0My00ZWExLWE2NmMtNzhlMDRhYjljNzllIiwiVXNlcm5hbWUiOiIiLCJJRCI6MiwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MTE2ODMzMjksImlzcyI6InFtUGx1cyIsIm5iZiI6MTcxMTA3NzUyOX0.l5Jjx-O0fznuC-DagoaTZaKClcXaPUUWTZJpkcgCOp4
x-user-id: 1

{"id": 1}




### 创建课程供应链
POST {{api}}/gatherSupply/createSupply
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzY0NTA2MDQsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTg0NDgwNH0.hCpeNYynOp24lISGhiJgPJhMKnSzAfbMWtP8IoKsEKw
x-user-id: 1

{"name":"中台课程供应链1","category_id":2,"is_plugin": 1}

### 查询是否购买
POST {{api}}/api/Curriculum/selectCurriculumProductDetail
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzg3NGMxYWEtMDQ0My00ZWExLWE2NmMtNzhlMDRhYjljNzllIiwiVXNlcm5hbWUiOiIiLCJJRCI6MiwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MTAyMjY3ODQsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcwOTYyMDk4NH0.ziLleGioqcetDLq46ff7DnGi-EUCkw4mUOGJDfqo-zA
x-user-id: 1

{"id":1228111}


### 保存课程的基础配置设置
POST {{api}}/curriculum/savaBaseSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc5NTUzODAzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2Nzg5NDgwMDN9.d-EDl_224gakMlX0MZDx50w1c9vGIf9-8kqIkut0_ts
x-user-id: 1

{"settlement_period":73,"key":"HNAHdpyTuijQQrq6pjKZ"}

### 获取课程的基础配置设置

POST {{api}}/curriculum/getBaseSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc5NTUzODAzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2Nzg5NDgwMDN9.d-EDl_224gakMlX0MZDx50w1c9vGIf9-8kqIkut0_ts
x-user-id: 1




###更新讲师
POST {{api}}/lecturer/updateLecturer
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzU4MzUzMjYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTIyOTUyNn0.fe4n-iq9XfDOUBt9h5yhQouiyOmdxiR8pxzp-PULHsk
x-user-id: 1

{
  "id": 1,
  "uid": 1,
  "name": "333",
  "introduce": "444",
  "label": "6666uuuu",
  "other": 1

}


###  获取token
POST https://supply.yunzmall.com/supplyapi/app/application/getToken
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc3NzIwMDAxLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2NzcxMTQyMDF9.rIqWdtyEh7G_wYrRvuUa51coMHHizo4ww_k3wgodhpM
x-user-id: 1

{"app_key":"application4","app_secret":"525b25beacb4402cdc11801587bf2211"}


### 外部查询课程列表导入用
POST {{api}}/app/publicCurriculum/getApplicationInfo
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzg3NGMxYWEtMDQ0My00ZWExLWE2NmMtNzhlMDRhYjljNzllIiwiSUQiOjIsIkFwcElEIjoxLCJVc2VybmFtZSI6IiIsIk5pY2tOYW1lIjoiIiwiQXV0aG9yaXR5SWQiOiIiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjgwMTY5MTUzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2Nzk1NjMzNTN9.k_4f_pqHxoOttU3OavQjFB46wzBRwJBvp8rwpGfkHPU
x-user-id: 1



### 查询单条课程
POST {{api}}/app/publicCurriculum/selectCurriculumDetail
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzg3NGMxYWEtMDQ0My00ZWExLWE2NmMtNzhlMDRhYjljNzllIiwiSUQiOjIsIkFwcElEIjoxLCJVc2VybmFtZSI6IiIsIk5pY2tOYW1lIjoiIiwiQXV0aG9yaXR5SWQiOiIiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc4OTQ5MTc4LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2NzgzNDMzNzh9.VXorF5scHyG0cALHTWET_WvI7GWCrgh3VlQAMiVpX60
x-user-id: 1

{
  "id": 11
}


### 外部查询课程列表导入用
POST https://supply.yunzmall.com/supplyapi/app/publicCurriculum/selectImportCurriculum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiNjlmMWZiNmEtYzdkNS00YWI2LWJjN2MtNDg4NDQ0ODdjNjhmIiwiSUQiOjQsIkFwcElEIjo0LCJVc2VybmFtZSI6IiIsIk5pY2tOYW1lIjoiIiwiQXV0aG9yaXR5SWQiOiIiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjgzMDIwODEzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2ODI0MTUwMTN9.ASK6rwUO7NkGuvPZ8AVco_1OT68m1jPq-gWGjor5Wn4
x-user-id: 1

{
  "page": 1,
  "pageSize": 10
}

### 查询选择的供应链 已导入 未导入  课程数量
POST {{api}}/curriculum/selectCurriculumCount
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc5NTUzODAzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2Nzg5NDgwMDN9.d-EDl_224gakMlX0MZDx50w1c9vGIf9-8kqIkut0_ts
x-user-id: 1

{
  "id": 37
}



### 开始导入选择的供应链课程数据
POST {{api}}/curriculum/importCurriculum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNTY0NjEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ1MDY2MX0.g9IC8KLefUsGb4oXXcNcd2sf2Af08QTUATnKJsPa4Xo
x-user-id: 1

{
  "gather_supply_id": 31
}

### 更新课程状态
POST {{api}}/curriculum/updateChapterStatus
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjgwNzcwNTgzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2ODAxNjQ3ODN9.oMfP7gUURbA5el5hqZYBSg4DNHeGm6zw4jdNEOG9Rng
x-user-id: 1

{
  "id": 21,
  "status": 0
}

### 查询本地课程数量
POST {{api}}/app/publicCurriculum/selectCurriculumCount
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNTY0NjEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ1MDY2MX0.g9IC8KLefUsGb4oXXcNcd2sf2Af08QTUATnKJsPa4Xo
x-user-id: 1

### 查询课程是否支持试看
POST {{api}}/api/publicCurriculum/tryCurriculum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNTY0NjEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ1MDY2MX0.g9IC8KLefUsGb4oXXcNcd2sf2Af08QTUATnKJsPa4Xo
x-user-id: 1

{
  "product_id": 150998,
  "chapter_id": "123485345345123123",
  "subsection_id": "3333333"
}

###  生成课程视频 防盗链 试看url
POST {{api}}/app/publicCurriculum/getVideoUrl
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzg3NGMxYWEtMDQ0My00ZWExLWE2NmMtNzhlMDRhYjljNzllIiwiSUQiOjIsIkFwcElEIjoxLCJVc2VybmFtZSI6IiIsIk5pY2tOYW1lIjoiIiwiQXV0aG9yaXR5SWQiOiIiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjgwNjY2NDM3LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2ODAwNjA2Mzd9.N61PHiG6xBnJEzBd7hWvBGumkEN6W1tetWaP4zEcvyU
x-user-id: 1

{
  "product_id": 1093876,
  "chapter_id": "eff2066d-66b5-48e2-8e7f-702e2af8eb3f",
  "subsection_id": "a43e8289-ad04-4b7f-8cb9-a09f886abf72"
}

###  生成课程视频 防盗链 试看url
POST https://supply.yunzmall.com/supplyapi/app/publicCurriculum/getVideoUrl
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzg3NGMxYWEtMDQ0My00ZWExLWE2NmMtNzhlMDRhYjljNzllIiwiSUQiOjIsIkFwcElEIjoxLCJVc2VybmFtZSI6IiIsIk5pY2tOYW1lIjoiIiwiQXV0aG9yaXR5SWQiOiIiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjgwNzczNTY5LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2ODAxNjc3Njl9.ApsDWUE5BKc2dpAXRW-2_qlv7wNg9tDgcIQOZbJcNPo
x-user-id: 1

{
  "product_id":1093909,
  "chapter_id":"10c2e315-9204-42cc-83e4-157de310dd4a",
  "subsection_id":"7ff3301b-9221-4ae0-bb59-61ea7a70bdff",
  "order_sn":"SN2304031419720F9C"
}




###更新课程
POST {{api}}/curriculum/updateChapter
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc4OTQ0ODU4LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2NzgzMzkwNTh9.aLLjUQo6i-V-30toit2-UHnCi7ivYcSFtMlzNiNUbSY
x-user-id: 1

{
  "id": 11,
  "product_id": 151010,
  "category1_id": 1085,
  "category2_id": 1087,
  "category3_id": 1193,
  "lecturer_id": 1,
  "curriculum_name": "rrrrr",
  "detailed": "<p>sssssss</p>",
  "chapter": "[{\"id\":\"123485345345123123\",\"sort\":1,\"chapter_name\":\"测试章节1\",\"subsection\":[{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节111\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":50,\"video_minute\":5,\"video_second\":60},{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节222\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":32423432235,\"video_minute\":1,\"video_second\":30}]},{\"id\":\"eb56e362-f7fa-46a1-a0cd-0efcbbc2c697\",\"sort\":1,\"chapter_name\":\"eee\",\"subsection\":[]},{\"id\":\"505baf69-4af4-419d-9640-a2627a487f94\",\"sort\":1,\"chapter_name\":\"rrrrrr\",\"subsection\":[]}]"
}

//{
//  "id": 1,
//  "sku_id": 2424167,
//  "product_id": 150994,
//  "lecturer_id": 1,
//  "course_name": "课程名称4444999",
//  "detailed": "详情oooo",
//  "curriculum_code": "课程码",
//  "sales_model": 1,
//  "price": 100,
//  "cost_price": 90,
//  "retail_price": 120,
//  "reward": 10,
//  "chapter": "{\"aa\":11,\"bb\":22}"
//
//
//}

###创建课程
POST {{api}}/curriculum/createChapter
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc4OTQ0ODU4LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2NzgzMzkwNTh9.aLLjUQo6i-V-30toit2-UHnCi7ivYcSFtMlzNiNUbSY
x-user-id: 1

{
  "category1_id": 1137,
  "category2_id": 1196,
  "category3_id": 1203,
  "lecturer_id": 2,
  "curriculum_name": "水水水水",
  "detailed": "<p>2222222</p>",
  "chapter": "[{\"id\":\"123485345345123123\",\"sort\":1,\"chapter_name\":\"测试章节1\",\"subsection\":[{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节111\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":50,\"video_minute\":5,\"video_second\":60},{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节222\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":32423432235,\"video_minute\":1,\"video_second\":30}]},{\"id\":\"85d498b4-7910-4abe-8a56-493ba799adde\",\"sort\":1,\"chapter_name\":\"无名\",\"subsection\":[]},{\"id\":\"68700ebd-d905-47bf-86aa-70317d73b311\",\"sort\":1,\"chapter_name\":\"绿夜\",\"subsection\":[]}]"
}

//{
//  "lecturer_id": 1,
//  "curriculum_name": "测试课程33525235",
//  "curriculum_img": "sadfasf",
//  "detailed": "详情",
//  "curriculum_code": "课程码",
//  "sales_model": 1,
//  "price": 100,
//  "status": 0,
//  "cost_price": 90,
//  "retail_price": 120,
//  "reward": 10,
//  "chapter": "[{\"id\":\"123485345345123123\",\"sort\":1,\"chapter_name\":\"测试章节1\",\"subsection\":[{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节111\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":50,\"video_minute\":5,\"video_second\":60},{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节222\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":32423432235,\"video_minute\":1,\"video_second\":30}]},{\"id\":\"12348534534512312323\",\"sort\":2,\"chapter_name\":\"测试章节2\",\"subsection\":[{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节333\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":32423432235,\"video_minute\":1,\"video_second\":3},{\"chapter_id\":\"123485345345123123\",\"subsection_name\":\"测试小节444\",\"sort\":1,\"url\":\"http://sdfsf324234.com\",\"img\":\"http://img.com\",\"try\":1,\"try_time\":32423432235,\"video_minute\":1,\"video_second\":3}]}]"
//
//
//}



###查询课程
POST {{api}}/curriculum/findChapter
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzY0NTA2MDQsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTg0NDgwNH0.hCpeNYynOp24lISGhiJgPJhMKnSzAfbMWtP8IoKsEKw
x-user-id: 1

{

  "id": 3


}

###  查询讲师

POST {{api}}/lecturer/findLecturer
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc3NzIwMDAxLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2NzcxMTQyMDF9.rIqWdtyEh7G_wYrRvuUa51coMHHizo4ww_k3wgodhpM
x-user-id: 1

{
  "page": 1,
  "pageSize": 10

}

###  查询讲师分成列表

POST {{api}}/lecturer/findLecturerAward
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNTY0NjEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ1MDY2MX0.g9IC8KLefUsGb4oXXcNcd2sf2Af08QTUATnKJsPa4Xo
x-user-id: 1

{
  "page": 1,
  "pageSize": 10

}

###  查询三方讲师

POST {{api}}/lecturer/findThirdLecturer
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzU4MzUzMjYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTIyOTUyNn0.fe4n-iq9XfDOUBt9h5yhQouiyOmdxiR8pxzp-PULHsk
x-user-id: 1

{
  "page": 1,
  "pageSize": 10

}


### aaa
POST {{api}}/curriculum/supplyList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjc5NTUzODAzLCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2Nzg5NDgwMDN9.d-EDl_224gakMlX0MZDx50w1c9vGIf9-8kqIkut0_ts
x-user-id: 1

{
  "page": 1,
  "pageSize": 10

}




### 删除
POST {{api}}/lecturer/deleteLecturer
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzU4MzUzMjYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTIyOTUyNn0.fe4n-iq9XfDOUBt9h5yhQouiyOmdxiR8pxzp-PULHsk
x-user-id: 1

{
  "id": 1

}



###

## 添加讲师

POST {{api}}/lecturer/createLecturer
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzU4MzUzMjYsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTIyOTUyNn0.fe4n-iq9XfDOUBt9h5yhQouiyOmdxiR8pxzp-PULHsk
x-user-id: 1


{
  "uid": 1,
  "name": "333",
  "introduce": "444",
  "label": "555",
  "other": 1


}


