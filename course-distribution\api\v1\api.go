package v1

import (
	"course-distribution/model"
	"course-distribution/request"
	"course-distribution/service"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	v12 "user/api/f/v1"

	model2 "user/model"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func CreateLecturer(c *gin.Context) {
	var requestData model.Lecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateLecturer(requestData); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func DeleteLecturer(c *gin.Context) {
	var requestData model.RequestLecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteLecturer(requestData); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func UpdateLecturer(c *gin.Context) {
	var requestData model.Lecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateLecturer(requestData); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func GetApplicationInfo(c *gin.Context) {

	appID := utils.GetAppID(c)

	var appliction Application
	source.DB().Where("id=?", appID).First(&appliction)

	var user model2.User
	source.DB().Where("id=?", appliction.MemberID).First(&user)

	var infoMap = make(map[string]interface{})
	infoMap["name"] = appliction.AppName
	infoMap["phone"] = user.Username
	yzResponse.OkWithData(infoMap, c)

	//requestData.AppId = appID

}

func FindLecturer(c *gin.Context) {
	var requestData model.RequestLecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.FindLecturer(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}

func FindThirdLecturer(c *gin.Context) {
	var requestData model.RequestLecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.FindThirdLecturer(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}
func FindLecturerAward(c *gin.Context) {
	var requestData model.RequestLecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total, TotalOrderAmount, TotalAmount, Settled, Unsettled := service.FindLecturerAward(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(PageResultAward{
			List:             data,
			Total:            total,
			Page:             requestData.Page,
			PageSize:         requestData.PageSize,
			TotalOrderAmount: TotalOrderAmount,
			TotalAmount:      TotalAmount,
			Settled:          Settled,
			Unsettled:        Unsettled,
		}, "获取成功", c)

	}
}

type PageResultAward struct {
	List             interface{} `json:"list"`
	Total            int64       `json:"total"`
	Page             int         `json:"page"`
	PageSize         int         `json:"pageSize"`
	NextUrl          string      `json:"next_url"`
	TotalOrderAmount uint        `json:"total_order_amount" gorm:"-"` //累计订单金额
	TotalAmount      uint        `json:"total_amount" gorm:"-"`       //累计分成金额
	Settled          uint        `json:"settled" gorm:"-"`            //已经结算金额
	Unsettled        uint        `json:"unsettled" gorm:"-"`          //未结算金额
}

func SupplyList(c *gin.Context) {
	var requestData model.RequestLecturer
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.SupplyList(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}

func CreateChapter(c *gin.Context) {
	var requestData model.Curriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateChapter(requestData); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func UpdateChapter(c *gin.Context) {
	var requestData model.CurriculumList
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateChapter(requestData); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func UpdateChapterStatus(c *gin.Context) {
	var requestData model.CurriculumList
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateChapterStatus(requestData); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func DeleteChapter(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteChapter(requestData); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func SavaBaseSetting(c *gin.Context) {
	var requestData model.BaseSetting
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.SavaBaseSetting(requestData); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}
func GetBaseSetting(c *gin.Context) {

	err, data := service.GetBaseSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func FindChapter(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.FindChapter(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     requestData.Page,
			PageSize: requestData.PageSize,
		}, "获取成功", c)

	}
}

//
//func SelectImportCurriculum(c *gin.Context) {
//	var requestData model.RequestCurriculum
//	err := c.ShouldBindJSON(&requestData)
//	if err != nil {
//		log.Log().Error("获取失败", zap.Any("err", err))
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	if err, data, total := service.SelectImportCurriculum(requestData); err != nil {
//		log.Log().Error("获取失败!", zap.Any("err", err))
//		yzResponse.FailWithMessage("获取失败", c)
//		return
//	} else {
//		yzResponse.OkWithDetailed(yzResponse.PageResult{
//			List:     data,
//			Total:    total,
//			Page:     requestData.Page,
//			PageSize: requestData.PageSize,
//		}, "获取成功", c)
//
//	}
//}

func SelectSupplyCount(c *gin.Context) {
	var requestData model.RequestCount
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, IsImport, NotImport := service.SelectSupplyCount(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		var resMap = make(map[string]interface{})
		resMap["IsImport"] = IsImport
		resMap["NotImport"] = NotImport
		yzResponse.OkWithData(resMap, c)

	}
}

func UpdateCurriculum(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	go service.UpdateCurriculum(requestData)

	yzResponse.Ok(c)

}

func ImportGatherSupplyCurriculum(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	go service.ImportGatherSupplyCurriculum(requestData)
	yzResponse.Ok(c)

	//service.ImportGatherSupplyCurriculum(requestData)

}

//试看课程权限

func TryCurriculum(c *gin.Context) {
	var requestData model.RequestTryCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data := service.TryCurriculum(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}

}
func TryCurriculumAuth(c *gin.Context) {
	var requestData model.RequestTryCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data := service.TryCurriculum(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}

}
func GetVideoUrl(c *gin.Context) {
	var requestData model.RequestTryCurriculum
	err := c.ShouldBindJSON(&requestData)
	log.Log().Info("GetVideoUrl 解析参数", zap.Any("info", requestData))

	uid := utils.GetAppUserID(c)

	log.Log().Info("获取url 解析  gin uid", zap.Any("info", uid))
	requestData.Uid = uid
	fmt.Println(uid)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data := service.GetVideoUrl(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}

}

func VideoUrl(c *gin.Context) {
	var requestData model.RequestTryCurriculum
	err := c.ShouldBindJSON(&requestData)

	uid := v12.GetUserID(c)
	log.Log().Info("获取url 解析  gin uid", zap.Any("info", uid))
	requestData.Uid = uid
	fmt.Println(uid)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data := service.VideoUrl(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}

}

//func HigherLevelKey(c *gin.Context) {
//	var requestData model.Curriculum
//	err := c.ShouldBindJSON(&requestData)
//	if err != nil {
//		log.Log().Error("获取失败", zap.Any("err", err))
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	if err, data := service.HigherLevelKey(requestData); err != nil {
//		log.Log().Error("获取失败!", zap.Any("err", err))
//		yzResponse.FailWithMessage("获取失败", c)
//		return
//	} else {
//		yzResponse.OkWithData(data, c)
//	}
//
//}

type Application struct {
	ID               uint             `json:"id"`
	SupplierID       uint             `json:"supplier_id"`
	AppLevelID       uint             `json:"app_level_id"`
	MemberID         uint             `json:"member_id"`
	AppName          string           `json:"app_name"`
	PetSupplierID    uint             `json:"pet_supplier_id"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}

func (Application) TableName() string {
	return "application"
}

type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}

type AppProductPageResult struct {
	yzResponse.PageResult
	SeverRatio int `json:"sever_ratio"`
}

//查询单条课程并关联讲师与商品

func SelectCurriculumProductDetail(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	uid := v12.GetUserID(c)

	if err, data := service.SelectCurriculumProductDetail(uid, requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func FindLecturerAndCurriculum(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.FindLecturerAndCurriculum(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func SelectCurriculumDetail(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.SelectCurriculumDetail(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func AddStorage(c *gin.Context) {
	var requestData model.CurriculumStorage
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	if err = service.AddStorage(requestData); err != nil {
		yzResponse.FailWithMessage("添加失败", c)
		return
	} else {
		yzResponse.Ok(c)

	}
}

func DeleteStorage(c *gin.Context) {
	var requestData model.DeleteStorageData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppID = appID
	if err = service.DeleteStorage(requestData); err != nil {
		yzResponse.FailWithMessage("添加失败", c)
		return
	} else {
		yzResponse.Ok(c)

	}
}

func SelectImportCurriculum(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	requestData.AppId = appID
	//userID := utils.GetAppUserID(c)
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.SelectImportCurriculum(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {

		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     data,
				Total:    total,
				Page:     requestData.Page,
				PageSize: requestData.PageSize,
			},
			SeverRatio: app.ApplicationLevel.ServerRadio,
		}, "获取成功", c)

	}
}

func LocalSelectImportCurriculum(c *gin.Context) {
	var requestData model.RequestCurriculum
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	//userID := utils.GetAppUserID(c)
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data, total := service.SelectImportCurriculum(requestData); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {

		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     data,
				Total:    total,
				Page:     requestData.Page,
				PageSize: requestData.PageSize,
			},
			SeverRatio: app.ApplicationLevel.ServerRadio,
		}, "获取成功", c)

	}
}

//查询本地课程的总数

func SelectCurriculumCount(c *gin.Context) {
	if err, count := service.SelectCurriculumCount(); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		var resData = make(map[string]interface{})
		resData["count"] = count
		c.JSON(200, resData)
		return

	}
}

func GetOrderList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum := service.GetOrderInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		type PageResultWithNum struct {
			yzResponse.PageResult
			WaitPayNum     int64
			WaitSendNum    int64
			WaitReceiveNum int64
			CompletedNum   int64
			ClosedNum      int64
			BackNum        int64
			RefundNum      int64
		}
		var pageResultWithNum PageResultWithNum
		pageResultWithNum.WaitPayNum = WaitPayNum
		pageResultWithNum.WaitSendNum = WaitSendNum
		pageResultWithNum.WaitReceiveNum = WaitReceiveNum
		pageResultWithNum.CompletedNum = CompletedNum
		pageResultWithNum.ClosedNum = ClosedNum
		pageResultWithNum.BackNum = BackNum
		pageResultWithNum.RefundNum = RefundNum
		pageResultWithNum.Page = pageInfo.Page
		pageResultWithNum.PageSize = pageInfo.PageSize
		pageResultWithNum.Total = total
		pageResultWithNum.List = list
		yzResponse.OkWithDetailed(pageResultWithNum, "获取成功", c)
	}
}
