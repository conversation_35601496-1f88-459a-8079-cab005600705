package cron

import (
	"course-distribution/model"
	"testing"
)

func TestOrderSettlement(t *testing.T) {

	OrderSettlement()

	return
}

func TestSettlement(t *testing.T) {
	type args struct {
		order model.LecturerDivided
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := Settlement(tt.args.order); (err != nil) != tt.wantErr {
				t.Errorf("Settlement() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
