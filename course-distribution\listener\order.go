package listener

import (
	"course-distribution/model"
	"go.uber.org/zap"
	model2 "order/model"
	"order/mq"
	order2 "order/order"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

func PushCourseOrderCreatedHandles() {
	log.Log().Info("课程监听订单创建!")
	mq.PushHandles("createCourseOrderAward", func(orderMsg mq.OrderMessage) (err error) {
		log.Log().Info("createCourseOrderAward[" + strconv.Itoa(int(orderMsg.OrderID)) + "]")
		if orderMsg.MessageType != mq.Paid {
			return
		}
		//if orderMsg.MessageType == mq.Created {
		//	CreateOrder(orderMsg.OrderID)
		//}

		err = CreateCourseAward(orderMsg.OrderID)
		if err != nil {
			log.Log().Error("createCourseOrderAwardERR", zap.Any("err", err))
			return nil
		}

		return nil
	})
}

func CreateOrder(orderID uint) (err error) {

	log.Log().Error("info", zap.Any("", err))

	err = source.DB().Model(model2.Order{}).Where("id=?", orderID).UpdateColumn("plugin_id", "18").Error

	if err != nil {
		log.Log().Error("err", zap.Any("课程订单plugin_id更新订单插件类型错误", err))
		return
	}

	return
}

func CreateCourseAward(orderID uint) (err error) {

	var order model2.Order

	err = source.DB().Preload("OrderItems").First(&order, "id=?", orderID).Error
	if err != nil {
		log.Log().Error("err", zap.Any("课程创建奖励查询订单失败", err))
		return nil
	}
	//if order.GatherSupplyID==0 && order.is

	for _, item := range order.OrderItems {

		var Curriculum model.Curriculum
		err = source.DB().Preload("Lecturer").First(&Curriculum, "product_id=?", item.ProductID).Error
		if err != nil {
			log.Log().Error("err", zap.Any("课程创建奖励查询OrderItems失败", err))
			continue
		}

		if Curriculum.ID == 0 {
			continue
		}
		CreateOrder(orderID)
		err = order2.Send(orderID)
		if err != nil {
			log.Log().Info("课程发货1", zap.Any("课程订单 发货", err))

		}
		err = order2.Received(orderID)
		if err != nil {
			log.Log().Info("课程收货2", zap.Any("课程订单 收货", err))

		}

		if Curriculum.Lecturer.Uid == 0 {
			continue
		}

		var lecturerAward model.LecturerDivided
		lecturerAward.LecturerID = Curriculum.LecturerID
		lecturerAward.OrderSN = order.OrderSN
		lecturerAward.CurriculumID = Curriculum.ID
		lecturerAward.Amount = Curriculum.Reward
		lecturerAward.Status = 0
		err = source.DB().Create(&lecturerAward).Error
		if err != nil {
			log.Log().Error("err", zap.Any("创建lecturerAward课程奖励订单失败", err))
			return nil
		}

	}

	return
}
