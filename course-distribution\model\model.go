package model

import (
	"gorm.io/gorm"
	model2 "order/model"
	"product/model"
	model3 "public-supply/model"
	setting2 "public-supply/setting"
	userModel "user/model"
	"yz-go/request"
	"yz-go/source"
)

type DeleteStorageData struct {
	Ids   []int64 `json:"ids"`
	AppID uint    `json:"appID"`
}

type CurriculumStorage struct {
	source.Model
	AppID     uint `json:"app_id" form:"app_id" gorm:"column:app_id;comment:用户id;index;"`             // 用户id
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;index;"` // 产品id
}

type HttpCurriculumList struct {
	Code int `json:"code"`
	Data struct {
		List     []CurriculumList `json:"list"`
		Total    int              `json:"total"`
		Page     int              `json:"page"`
		PageSize int              `json:"pageSize"`
		NextUrl  string           `json:"next_url"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type CurriculumCount struct {
	Count int64 `json:"count"`
}

type SupplySetting struct {
	BaseInfo   BaseInfoData            `json:"baseInfo"`
	UpdateInfo setting2.UpdateInfoData `json:"update"`
	Pricing    setting2.PricingData    `json:"pricing"`
	Management setting2.Management     `json:"management"`
}
type SelfSupplySetting struct {
	SupplySetting
	BaseInfo SelfBaseInfoData `json:"baseInfo"`
}

type SelfBaseInfoData struct {
	BaseInfoData
	Host string `json:"host"`
}
type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

//课程

type RequestCount struct {
	ID uint `json:"id"`
}

type RequestTryCurriculum struct {
	ProductID    uint   `json:"product_id"`
	CurriculumID string `json:"curriculum_id"`
	ChapterID    string `json:"chapter_id"`
	SubsectionID string `json:"subsection_id"`
	Uid          uint   `json:"uid"`
	OrderSN      string `json:"order_sn"`
}

type RequestCurriculum struct {
	source.Model
	request.PageInfo
	Curriculum
	SkuId            uint   `json:"sku_id"`
	PriceStart       *uint  `json:"price_start" ` //供货价搜索区间
	PriceEnd         *uint  `json:"price_end" `
	RetailPriceStart *uint  `json:"retail_price_start" ` //零售价搜索区间
	RetailPriceEnd   *uint  `json:"retail_price_end" `
	ProfitStart      string `json:"profit_start" ` //利润率区间
	ProfitEnd        string `json:"profit_end" `   //利润率区间
	IsImport         string `json:"is_import" `    // 是否导入
	AppId            uint   `json:"app_id"`
	SortType         string `json:"sort_type"`
	Sort             string `json:"sort"`
	SID              uint   `json:"sid" form:"sid"` // 小商店id

	//Category1Name    string `json:"category_1_name" gorm:"-"`
	//Category2Name    string `json:"category_2_name" gorm:"-"`
	//Category3Name    string `json:"category_3_name" gorm:"-"`
}

type CurriculumForUpdate struct {
	Curriculum
	//SkuId uint `json:"sku_id" gorm:"-"`
}

//讲师

type RequestLecturer struct {
	source.Model
	request.PageInfo
	Uid            uint   `json:"uid"`
	Mobile         string `json:"mobile"`
	Status         string `json:"status"`
	Name           string `json:"name"`
	LecturerName   string `json:"lecturer_name"`
	CurriculumName string `json:"curriculum_name"`
	CurriculumID   uint   `json:"curriculum_id"`
	OrderSN        string `json:"order_sn"`
	GatherSupplyId uint   `json:"gather_supply_id"`
}

// 讲师
type Lecturer struct {
	source.Model
	Uid              uint           `json:"uid"`
	Mobile           uint           `json:"mobile"`
	LecturerName     string         `json:"lecturer_name"`
	Img              string         `json:"img"`
	Introduce        string         `json:"introduce"`
	Label            string         `json:"label"`
	Detailed         string         `json:"detailed" gorm:"type:text"`
	Other            uint           `json:"other"`
	LecturerStatus   uint           `json:"lecturer_status"`
	GatherSupplyID   uint           `json:"gather_supply_id"`
	SourceLecturerId uint           `json:"source_lecturer_id"`
	User             userModel.User `json:"user_info" gorm:"foreignKey:uid;references:ID"`
}
type LecturerModel struct {
	source.Model
	Uid              uint   `json:"uid"`
	Mobile           uint   `json:"mobile"`
	LecturerName     string `json:"lecturer_name"`
	Img              string `json:"img"`
	Introduce        string `json:"introduce"`
	Label            string `json:"label"`
	Detailed         string `json:"detailed" gorm:"type:text"`
	Other            uint   `json:"other"`
	LecturerStatus   uint   `json:"lecturer_status"`
	GatherSupplyID   uint   `json:"gather_supply_id"`
	SourceLecturerId uint   `json:"source_lecturer_id"`
}

func (LecturerModel) TableName() string {
	return "lecturers"
}

func (l *LecturerSearch) AfterFind(tx *gorm.DB) (err error) {

	//l.TotalShare = 33
	//l.TotalSales = 44
	//l.CurriculumNumber = 55

	return

}

type LecturerSearch struct {
	Lecturer
	TotalSales       uint         `json:"total_sales" gorm:"-"`
	TotalShare       uint         `json:"total_share" gorm:"-"`
	CurriculumNumber uint         `json:"curriculum_number" gorm:"-"`
	ChapterCount     int          `json:"chapter_count" gorm:"-"`
	SubsectionCount  int          `json:"subsection_count" gorm:"-"`
	Curriculum       []Curriculum `json:"curriculum" gorm:"foreignKey:lecturer_id"`
}

func (LecturerSearch) TableName() string {
	return "lecturers"
}

type LecturerDivided struct {
	source.Model
	OrderSN      uint         `json:"order_sn"`
	LecturerID   uint         `json:"lecturer_id"`
	CurriculumID uint         `json:"curriculum_id"`
	Amount       uint         `json:"amount"`
	Status       uint         `json:"status"`
	Lecturer     Lecturer     `json:"lecturer" gorm:"foreignKey:LecturerID"`
	Order        model2.Order `json:"order" gorm:"foreignKey:order_sn;references:order_sn"`
	Curriculum   Curriculum   `json:"curriculum" gorm:"foreignKey:CurriculumID"`
}
type LecturerDividedModel struct {
	source.Model
	OrderSN      uint `json:"order_sn"`
	LecturerID   uint `json:"lecturer_id"`
	CurriculumID uint `json:"curriculum_id"`
	Amount       uint `json:"amount"`
	Status       uint `json:"status"`
}

func (LecturerDividedModel) TableName() string {
	return "lecturer_divideds"
}

type HttpKey struct {
	Code int    `json:"code"`
	Data string `json:"data"`
	Msg  string `json:"msg"`
}

type BaseSetting struct {
	SettlementPeriod uint   `json:"settlement_period"`
	Key              string `json:"key"`
}

// 如果含有time.Time 请自行import time包
type Category struct {
	source.Model
	Name         string     `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"`         //分类名称
	Sort         int        `json:"sort" form:"sort" gorm:"column:sort;default:0;comment:排序;"`                                                   //排序
	Level        int        `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`                 //等级
	Desc         string     `json:"desc" form:"desc" gorm:"column:desc;comment:简介;type:varchar(255);size:255;"`                                  //描述
	IsDisplay    *int       `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;default:0;type:tinyint;size:1;index;"` //是否显示
	Image        string     `json:"image" form:"image" gorm:"column:image;comment:图片;"`                                                          //分类图片
	Icon         string     `json:"icon" form:"icon" gorm:"column:icon;comment:icon;"`                                                           //分类图片
	HasChildrens bool       `json:"hasChildrens"`
	Url          string     `json:"url"`
	Source       int        `json:"source"`
	ParentID     uint       `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"` //父级id
	IsNew        int        `json:"is_new" gorm:"column:is_new;default:0;comment:;"`
	Childrens    []Category `json:"childrens" gorm:"foreignKey:ParentID;references:ID"` //子分类
}

// 课程
type Curriculum struct {
	source.Model
	ProductID        uint    `json:"product_id" gorm:"comment:关联的product_id"`
	Category1ID      uint    `json:"category1_id" gorm:"comment:一级分类"`
	Category2ID      uint    `json:"category2_id" gorm:"comment:二级分类"`
	Category3ID      uint    `json:"category3_id" gorm:"comment:三级分类"`
	LecturerID       uint    `json:"lecturer_id" gorm:"comment:讲师Id"`
	CurriculumName   string  `json:"curriculum_name" gorm:"comment:课程名称"`
	CurriculumImg    string  `json:"curriculum_img" gorm:"comment:课程图片"`
	Status           *uint   `json:"status" gorm:"default:1;comment:上下架状态（0下架1上架）"`
	Detailed         string  `json:"detailed" gorm:"type:longtext"`
	CurriculumCode   string  `json:"curriculum_code" gorm:"comment:课程编码"`
	SalesModel       uint    `json:"sales_model" gorm:"comment:销售模式（1无限制2指定期限）"`
	TimeLimit        uint    `json:"time_limit" gorm:"comment:限制天数"`
	Price            uint    `json:"price" gorm:"comment:供货价"`
	CostPrice        uint    `json:"cost_price" gorm:"comment:成本价"`
	RetailPrice      uint    `json:"retail_price" gorm:"comment:零售价"`
	Reward           uint    `json:"reward" gorm:"comment:分成金额"`
	GatherSupplyID   uint    `json:"gather_supply_id" gorm:"comment:所属供应链"`
	GatherSupplyName string  `json:"gather_supply_name" gorm:"-"`
	SourceGoodsID    uint    `json:"source_goods_id" gorm:"comment:上级供应链的课程id"`
	Chapter          string  `json:"chapter" gorm:"type:longtext"`
	Profit           float64 `json:"profit" gorm:"comment:利润率"`
	SubsectionCount  int     `json:"subsection_count" gorm:"-"`
	ChapterCount     int     `json:"chapter_count" gorm:"-"`

	Product  ResponseProduct `json:"product" gorm:"foreignKey:ProductID"`
	Lecturer Lecturer        `json:"lecturer" gorm:"foreignKey:LecturerID"`
}

type ResponseProduct struct {
	model.Product
	NormalPrice uint `json:"normal_price" form:"normal_price" gorm:"-"` // 原始售价

}

func (ResponseProduct) TableName() string {
	return "products"
}

type CurriculumModel struct {
	source.Model
	ProductID        uint    `json:"product_id" gorm:"comment:关联的product_id"`
	Category1ID      uint    `json:"category1_id" gorm:"comment:一级分类"`
	Category2ID      uint    `json:"category2_id" gorm:"comment:二级分类"`
	Category3ID      uint    `json:"category3_id" gorm:"comment:三级分类"`
	LecturerID       uint    `json:"lecturer_id" gorm:"comment:讲师Id"`
	CurriculumName   string  `json:"curriculum_name" gorm:"comment:课程名称"`
	CurriculumImg    string  `json:"curriculum_img" gorm:"comment:课程图片"`
	Status           *uint   `json:"status" gorm:"default:1;comment:上下架状态（0下架1上架）"`
	Detailed         string  `json:"detailed" gorm:"type:longtext"`
	CurriculumCode   string  `json:"curriculum_code" gorm:"comment:课程编码"`
	SalesModel       uint    `json:"sales_model" gorm:"comment:销售模式（1无限制2指定期限）"`
	TimeLimit        uint    `json:"time_limit" gorm:"comment:限制天数"`
	Price            uint    `json:"price" gorm:"comment:供货价"`
	CostPrice        uint    `json:"cost_price" gorm:"comment:成本价"`
	RetailPrice      uint    `json:"retail_price" gorm:"comment:零售价"`
	Reward           uint    `json:"reward" gorm:"comment:分成金额"`
	GatherSupplyID   uint    `json:"gather_supply_id" gorm:"comment:所属供应链"`
	GatherSupplyName string  `json:"gather_supply_name" gorm:"-"`
	SourceGoodsID    uint    `json:"source_goods_id" gorm:"comment:上级供应链的课程id"`
	Chapter          string  `json:"chapter" gorm:"type:longtext"`
	Profit           float64 `json:"profit" gorm:"comment:利润率"`
}

func (CurriculumModel) TableName() string {
	return "curriculums"
}

func (l *Curriculum) AfterFind(tx *gorm.DB) (err error) {

	if l.GatherSupplyID == 0 {
		l.GatherSupplyName = "平台自营"
		return
	}
	var gatherSupply model3.GatherSupply
	err = source.DB().Where("id=?", l.GatherSupplyID).First(&gatherSupply).Error

	l.GatherSupplyName = gatherSupply.Name

	return

}

func (CurriculumList) TableName() string {
	return "curriculums"
}

func (l *CurriculumList) AfterFind(tx *gorm.DB) (err error) {

	var category1, category2, category3 model.Category
	source.DB().Where("id=?", l.Category1ID).First(&category1)
	source.DB().Where("id=?", l.Category2ID).First(&category2)
	source.DB().Where("id=?", l.Category3ID).First(&category3)

	l.Category1Name = category1.Name
	l.Category2Name = category2.Name
	l.Category3Name = category3.Name
	l.Category1Img = category1.Image
	l.Category2Img = category2.Image
	l.Category3Img = category3.Image

	return

}

type CurriculumDetail struct {
	Code int            `json:"code"`
	Data CurriculumList `json:"data"`
	Msg  string         `json:"msg"`
}

type CurriculumList struct {
	Curriculum
	Category1Name string `json:"category_1_name" gorm:"-"`
	Category2Name string `json:"category_2_name" gorm:"-"`
	Category3Name string `json:"category_3_name" gorm:"-"`
	Category1Img  string `json:"category_1_img" gorm:"-"`
	Category2Img  string `json:"category_2_img" gorm:"-"`
	Category3Img  string `json:"category_3_img" gorm:"-"`
	IsPurchase    uint   `json:"is_purchase" gorm:"-"`
	EndTime       string `json:"end_time" gorm:"-"`
	EndTimeString string `json:"end_time_string" gorm:"-"` //年月是时分秒

}

type Chapter struct {
	//source.Model
	ID          string       `json:"id" form:"id" gorm:"primarykey"`
	ChapterName string       `json:"chapter_name" gorm:"comment:章节名"`
	Sort        uint         `json:"sort" gorm:"comment:排序"`
	Subsection  []Subsection `json:"subsection" gorm:"comment:小节列表"`
}

type Subsection struct {
	//source.Model
	ID             string `json:"id" form:"id" gorm:"primarykey"`
	ChapterID      string `json:"chapter_id"`
	SubsectionName string `json:"subsection_name" gorm:"comment:小节名称"`
	Sort           uint   `json:"sort"`
	Url            string `json:"url"`
	Img            string `json:"img"`
	Try            uint   `json:"try" gorm:"comment:是否支持试看 1支持"`
	TryTime        uint   `json:"try_time" gorm:"comment:试看时间/秒"`
	VideoMinute    uint   `json:"video_minute" gorm:"comment:视频长度/分"`
	VideoSecond    uint   `json:"video_second" gorm:"comment:视频长度/秒"`
}
