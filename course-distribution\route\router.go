package route

import (
	v1 "course-distribution/api/v1"
	"github.com/gin-gonic/gin"
)

// 后台私有
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	LecturerRouter := Router.Group("lecturer") //讲师
	{
		LecturerRouter.POST("createLecturer", v1.CreateLecturer)
		LecturerRouter.POST("deleteLecturer", v1.DeleteLecturer)
		LecturerRouter.POST("updateLecturer", v1.UpdateLecturer)
		LecturerRouter.POST("findLecturer", v1.FindLecturer)
		LecturerRouter.POST("findThirdLecturer", v1.FindThirdLecturer)
		LecturerRouter.POST("findLecturerAward", v1.FindLecturerAward)

	}
	CategoryRouter := Router.Group("curriculum") //课程
	{
		CategoryRouter.POST("createChapter", v1.CreateChapter)
		CategoryRouter.POST("updateChapter", v1.UpdateChapter)
		CategoryRouter.POST("updateChapterStatus", v1.UpdateChapterStatus)
		CategoryRouter.POST("deleteChapter", v1.DeleteChapter)
		CategoryRouter.POST("findChapter", v1.FindChapter)
		CategoryRouter.POST("savaBaseSetting", v1.SavaBaseSetting)
		CategoryRouter.POST("getBaseSetting", v1.GetBaseSetting)
		CategoryRouter.POST("supplyList", v1.SupplyList)

		CategoryRouter.POST("selectCurriculumCount", v1.SelectSupplyCount)       //查询供应链导入数量情况
		CategoryRouter.POST("importCurriculum", v1.ImportGatherSupplyCurriculum) //导入选择的供应链未导入课程
		CategoryRouter.POST("orderList", v1.GetOrderList)                        //订单列表
		CategoryRouter.POST("updateCurriculum", v1.UpdateCurriculum)             //更新已导入课程

	}

	//Appcurriculum := Router.Group("appCurriculum") //课程
	//{
	//	Appcurriculum.POST("selectImportCurriculum", v1.LocalSelectImportCurriculum) // 查询导入课程列表
	//	Appcurriculum.POST("selectCurriculumCount", v1.SelectSupplyCount)            //查询供应链导入数量情况
	//
	//}
}

////中台导入中台课程查询
//func InitPublicCurriculum(Router *gin.RouterGroup) {
//
//	Appcurriculum := Router.Group("appCurriculum") //课程
//	{
//		Appcurriculum.POST("selectImportCurriculum", v1.LocalSelectImportCurriculum) // 查询导入课程列表
//		Appcurriculum.POST("selectCurriculumCount", v1.SelectSupplyCount)            //查询供应链导入数量情况
//
//	}
//
//}

func InitPublicCurriculumRouter(Router *gin.RouterGroup) {

	public := Router.Group("publicCurriculum") //课程  采购端 商城端用接口
	{
		public.POST("selectCurriculumCount", v1.SelectCurriculumCount)   // 查询导入课程数量
		public.POST("selectImportCurriculum", v1.SelectImportCurriculum) // 查询导入课程列表
		public.POST("addStorage", v1.AddStorage)                         // 添加选品库
		public.POST("deleteStorage", v1.DeleteStorage)                   // 删除选品库
		public.POST("selectCurriculumDetail", v1.SelectCurriculumDetail) // 查询单条课程
		public.POST("tryCurriculum", v1.TryCurriculum)                   // 查询课程是否有试看权限
		public.POST("getVideoUrl", v1.GetVideoUrl)                       // 生成课程视频 防盗链url
		//public.POST("higherLevelKey", v1.HigherLevelKey)                 // 获取setting中防盗链key
		public.POST("findLecturer", v1.FindLecturer)
		public.POST("getApplicationInfo", v1.GetApplicationInfo) //获取采购端会员的 信息

		//Appcurriculum.POST("selectCurriculumCount", v1.SelectSupplyCount)       //查询供应链导入数量情况

	}

}

func InitFrontPrivateCurriculumRouter(Router *gin.RouterGroup) {

	public := Router.Group("Curriculum") //课程中台前端用接口
	{

		//--------------------------------中台前端  新增接口
		public.POST("tryCurriculumAuth", v1.TryCurriculumAuth)                         // 中台查询课程是否有试看权限
		public.POST("selectCurriculumProductDetail", v1.SelectCurriculumProductDetail) // 查询单条课程

		//查询单条讲师信息并且关联课程列表
		public.POST("findLecturerAndCurriculum", v1.FindLecturerAndCurriculum)
		public.POST("getVideoUrl", v1.VideoUrl) // 生成课程视频 防盗链url

	}

}
