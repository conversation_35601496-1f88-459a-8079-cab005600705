package service

import (
	"course-distribution/model"
	"reflect"
	"testing"
)

func TestCreateChapter(t *testing.T) {

	var reqData model.RequestTryCurriculum
	var RequestCurriculum model.RequestCurriculum
	RequestCurriculum.ID = 3
	DeleteChapter(RequestCurriculum)

	return
	GetVideoUrl(reqData)
	return

	var request model.RequestLecturer

	request.PageSize = 10
	request.Page = 1
	FindLecturerAward(request)

	return

	var sysSetting model.BaseSetting

	sysSetting.SettlementPeriod = 7

	SavaBaseSetting(sysSetting)
}

func TestSelectCurriculumProductDetail(t *testing.T) {
	var requestData model.RequestCurriculum
	requestData.ID = 1228111
	SelectCurriculumProductDetail(2, requestData)

}

func TestFindLecturerAndCurriculum(t *testing.T) {
	var requestData model.RequestCurriculum
	requestData.ID = 1
	FindLecturerAndCurriculum(requestData)

}

func TestVideoUrl(t *testing.T) {
	type args struct {
		reqData model.RequestTryCurriculum
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		wantData model.Subsection
	}{{name: "333", args: args{model.RequestTryCurriculum{ProductID: 455107, Uid: 1, CurriculumID: "7", ChapterID: "8abc0625-a3c3-4f6e-a546-bf822b0265a5", SubsectionID: "edc968e2-be77-4cc5-8873-687ad9b9117f"}}}} // TODO: Add test cases.

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotData := VideoUrl(tt.args.reqData)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("VideoUrl() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotData, tt.wantData) {
				t.Errorf("VideoUrl() gotData = %v, want %v", gotData, tt.wantData)
			}
		})
	}
}
