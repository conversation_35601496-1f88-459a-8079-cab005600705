package service

import (
	model2 "course-distribution/model"
	"public-supply/model"
	"strconv"
)

func GetPricingPrice(elem model.Goods, dat *model2.SelfSupplySetting) (err error, costPrice, salePrice, activityPrice, guidePrice, originPrice uint) {

	var intX uint64
	var intXa uint64
	var intXb uint64
	var intXc uint64
	var intXd uint64
	var intXe uint64
	var intXf uint64

	if dat.Pricing.Strategy == 2 { //本地定价策略关闭

		if elem.Source == 101 { //中台本地

			//销售价计算
			if dat.Pricing.SupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 4 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesExec, 10, 32)
				salePrice = elem.AgreementPrice + ((elem.GuidePrice - elem.AgreementPrice) * uint(intX) / 100)
			}
			if dat.Pricing.SupplySales == 5 {
				intXa, err = strconv.ParseUint(dat.Pricing.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.SupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostGuide, 10, 32)
				costPrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束
			//
			//指导价
			if dat.Pricing.SupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideGuide, 10, 32)
				guidePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

			//指导价计算结束
			//
			//营销价
			if dat.Pricing.SupplyActivity == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityGuide, 10, 32)
				activityPrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyActivity == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyActivityMarketing, 10, 32)
				activityPrice = elem.ActivityPrice * uint(intX) / 100
			}
			//营销价计算结束
			//
			//建议零售价
			if dat.Pricing.SupplyAdvice == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceGuide, 10, 32)
				originPrice = elem.GuidePrice * uint(intX) / 100

				intXa, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecD, 10, 32)
				intXe, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecE, 10, 32)
				intXf, err = strconv.ParseUint(dat.Pricing.SupplyAdviceExecF, 10, 32)
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXa)/100 && intXa > 0 && intXb > 0 {
					originPrice = elem.AgreementPrice * uint(intXb) / 100
				}
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 && intXc > 0 && intXd > 0 {
					originPrice = elem.AgreementPrice * uint(intXd) / 100
				}
				if float64(elem.GuidePrice)/float64(elem.AgreementPrice) > float64(intXe)/100 && intXe > 0 && intXf > 0 {
					originPrice = elem.AgreementPrice * uint(intXf) / 100
				}
			}
			if dat.Pricing.SupplyAdvice == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyAdvice == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyAdviceMarketing, 10, 32)
				originPrice = elem.ActivityPrice * uint(intX) / 100
			}
			//建议零售价计算结束

		}

	} else {
		salePrice = elem.AgreementPrice
		costPrice = elem.CostPrice
		originPrice = elem.MarketPrice
		activityPrice = elem.ActivityPrice
		guidePrice = elem.GuidePrice
	}

	return

}
