package app

import (
	"cps/cps"
	"cps/request"
	"cps/response"
	"cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GenerateLink(c *gin.Context) {

	var info request.GenerateLinkRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var customize []string
	customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	if info.ThirdUserID > 0 {
		customize = append(customize, strconv.Itoa(info.ThirdUserID))
	} else {
		yzResponse.FailWithMessage("third_user_id不能为空", c)
		return
	}
	if info.IsDistributor > 0 {
		customize = append(customize, strconv.Itoa(info.IsDistributor))
	}
	info.Customize = strings.Join(customize, "a")
	var handle = cps.NewCps(info.LinkType)
	var generateLinkResponse response.GenerateLinkResponse
	if err, generateLinkResponse = handle.GenerateLink(info); err != nil {
		//log.Log().Error("取链失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		if generateLinkResponse.Link == "" {
			yzResponse.FailWithMessage("不支持当前取链类型", c)
			return
		}
		yzResponse.OkWithData(generateLinkResponse, c)
		return
	}
}

func MockOrderCallback(c *gin.Context) {

	var info request.MockOrderCallBackRequest
	err := c.ShouldBindQuery(&info)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var link string
	if err, link = service.MockOrderCallback(info); err != nil {
		//log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": link}, c)
	}
}
func GetMeituanCouponList(c *gin.Context) {
	var info = make(map[string]interface{})
	err := c.ShouldBindJSON(&info)
	if err != nil {
		//log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//var customize []string
	//customize = append(customize, strconv.Itoa(int(utils.GetAppID(c))))
	//if _, ok := info["third_user_id"]; ok {
	//	customize = append(customize, strconv.Itoa(info["third_user_id"]))
	//} else {
	//	yzResponse.FailWithMessage("third_user_id不能为空", c)
	//	return
	//}
	//info["RequestId"] = strings.Join(customize, "_")
	if err, data := service.GetMeituanCoupon(info, utils.GetAppUserID(c)); err != nil {
		//log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.JSON(http.StatusOK, data)
		return

	}
}
