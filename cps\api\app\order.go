package app

import (
	"cps/request"
	"cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GetJhCpsOrderList(c *gin.Context) {
	var pageInfo request.JhCpsOrderSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.ApplicationID = utils.GetAppID(c)
	if err, list, total := service.GetOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.<PERSON><PERSON><PERSON>(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			list, total, pageInfo.Page, pageInfo.PageSize,"",
		}, "获取成功", c)
	}
}
