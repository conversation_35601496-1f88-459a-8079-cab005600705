package v1

import (
	"cps/model"
	"cps/request"
	"cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	request2 "product/request"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func CreateActivity(c *gin.Context) {
	var activity model.Activity
	err := c.ShouldBindJSON(&activity)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	
	if err := service.CreateActivity(activity); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 应用
// @Summary 删除Activity
// @Security ApiKeyAuth
// @accept activity/json
// @Produce activity/json
// @Param data body model.Activity true "删除Activity"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /activity/deleteActivity [delete]
func DeleteActivity(c *gin.Context) {
	var activity model.Activity
	err := c.ShouldBindJSON(&activity)
	err = source.DB().First(&activity, activity.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteActivity(activity); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 应用
// @Summary 批量删除Activity
// @Security ApiKeyAuth
// @accept activity/json
// @Produce activity/json
// @Param data body yzRequest.IdsReq true "批量删除Activity"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /activity/deleteActivityByIds [delete]
func DeleteActivityByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteActivityByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
	
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags 应用
// @Summary 更新Activity
// @Security ApiKeyAuth
// @accept activity/json
// @Produce activity/json
// @Param data body model.Activity true "更新Activity"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /activity/updateActivity [put]
func UpdateActivity(c *gin.Context) {
	var activity model.Activity
	err := c.ShouldBindJSON(&activity)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateActivity(activity); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}



// @Tags 应用
// @Summary 用id查询Activity
// @Security ApiKeyAuth
// @accept activity/json
// @Produce activity/json
// @Param data body model.Activity true "用id查询Activity"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /activity/findActivity [get]
func FindActivity(c *gin.Context) {
	var activity model.Activity
	_ = c.ShouldBindQuery(&activity)
	if err, reactivity := service.GetActivity(activity.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reactivity": reactivity}, c)
	}
}


func GetActivityList(c *gin.Context) {
	var pageInfo request.ActivitySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetActivityInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			list, total, pageInfo.Page, pageInfo.PageSize,"",
		}, "获取成功", c)
	}
}

func GetAppActivityList(c *gin.Context) {
	var pageInfo request.ActivitySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	one := 1
	pageInfo.Status = &one
	if err, list, total := service.GetActivityInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			list, total, pageInfo.Page, pageInfo.PageSize,"",
		}, "获取成功", c)
	}
}

func ExportActivity(c *gin.Context) {
	var orderSearch request.ActivitySearch
	err := c.ShouldBindQuery(&orderSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, link := service.ExportActivityInfoList(orderSearch); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
func SetAttributeStatus(c *gin.Context) {
	var columnStatus request2.ColumnStatus
	err := c.ShouldBindJSON(&columnStatus)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(columnStatus); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, enable := service.SetAttributeStatus(columnStatus); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"enable": enable}, c)
	}
}
