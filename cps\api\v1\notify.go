package v1

import (
	"cps/model"
	service2 "cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"yz-go/component/log"
)
type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
}
func DidiNotify(c  *gin.Context){
	var DidiOrder model.DidiOrder
	err := c.ShouldBindJSON(&DidiOrder)
	if err != nil {
		log.Log().Error("didi回调失败", zap.Any("err", err))
		c.JSO<PERSON>(http.StatusOK, Response{
			1, "err",
		})
		return
	}
	if err = service2.DidiNotify(DidiOrder); err != nil {
		c.JSON(http.StatusOK, Response{
			0, "ok",
		})
		return
	} else {
		c.JSON(http.StatusOK, Response{
			0, "ok",
		})
	}
}


func DidiTestNotify(c  *gin.Context){

		c.<PERSON>(http.StatusOK, Response{
			0, "ok",
		})

}

type MeituanResponse struct {
	Errcode string `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}
func MeituanNotify(c  *gin.Context){
	data, err := c.GetRawData()
	if err != nil {
		log.Log().Error("meituan回调失败", zap.Any("err", err))
		c.JSON(http.StatusOK, MeituanResponse{
			"1", "err",
		})
		return
	}
	log.Log().Error("meituan回调数据", zap.Any("data", string(data)))
	
	var MeituanOrder model.MeituanNotifyOrder
	err = c.ShouldBindJSON(&MeituanOrder)
	if err != nil {
		log.Log().Error("meituan回调失败", zap.Any("err", err))
		c.JSON(http.StatusOK, MeituanResponse{
			"1", "err",
		})
		return
	}
	if err = service2.MeituanNotify(MeituanOrder); err != nil {
		c.JSON(http.StatusOK, MeituanResponse{
			"1", "err",
		})
		return
	} else {
		c.JSON(http.StatusOK, MeituanResponse{
			"0", "ok",
		})
	}
}