package v1

import (
	"cps/model"
	"cps/request"
	"cps/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

func GetJhCpsOrderList(c *gin.Context) {
	var pageInfo request.JhCpsOrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			list, total, pageInfo.Page, pageInfo.PageSize, "",
		}, "获取成功", c)
	}
}

func NotifyOrder(c *gin.Context) {
	var MeituanOrder request.JhCpsOrderSearch
	err := c.ShouldBindQuery(&MeituanOrder)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var cpsOrder model.JhCpsOrder
	err = source.DB().First(&cpsOrder, MeituanOrder.ID).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var operationType = "update"
	if cpsOrder.IsConnected != 1 {
		operationType = "create"
	}

	if err = service.NotifyJhCpsOrders(cpsOrder, operationType); err != nil {
		yzResponse.FailWithMessage("同步失败", c)

	} else {
		err = source.DB().Model(model.JhCpsOrder{}).Where("id = ?", cpsOrder.ID).Update("is_connected", 1).Error
		yzResponse.OkWithMessage("同步成功", c)

	}
	return
}

func ExportOrderList(c *gin.Context) {
	var orderSearch request.JhCpsOrderSearch
	err := c.ShouldBindQuery(&orderSearch)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//
	if err, link := service.ExportOrderList(orderSearch); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
