package v1

import (
	"cps/model"
	"cps/service"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	yzResponse "yz-go/response"
)

func UpdateCpsSetting(c *gin.Context) {
	var sysSetting model.CpsSetting
	err := c.ShouldBindJSON(&sysSetting)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	fmt.Println(sysSetting.Value)
	sysSetting.Key = "jhcps_setting"
	err = service.SaveCpsSetting(sysSetting)

	if err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	model.ResetCps()
	yzResponse.OkWithMessage("修改成功", c)

}

func FindCpsSetting(c *gin.Context) {
	err, tradeSetting := service.ShowCpsSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": tradeSetting}, c)

}

func UpdateCpsSettingMask(c *gin.Context) {
	var params service.UpdateMask
	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateCpsSettingMask(params); err != nil {
		yzResponse.FailWithMessage("设置失败，"+err.Error(), c)
	}
	yzResponse.OkWithData("设置成功", c)
}
