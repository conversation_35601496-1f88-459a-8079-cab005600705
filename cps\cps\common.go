package cps

import (
	"cps/model"
	"cps/request"
	"cps/response"
	"cps/service"
)

type Cps interface {
	GenerateLink(info request.GenerateLinkRequest) (err error, generateLinkResponse response.GenerateLinkResponse)
}

func NewCps(key model.CpsOrderType) Cps {
	switch key {
	case model.Didi:
		return &service.DidiApi{}
	case model.Meituan:
		return &service.MeituanApi{}
	case model.Eleme:
		return &service.ElemeApi{}
	//case model.MeituanDistributor:
	//	return &service.MeituanDistributorApi{}
	default:
		return nil
	}
}
