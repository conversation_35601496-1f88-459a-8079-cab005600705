package cron

import (
	"cps/model"
	"cps/service"
	"errors"
	"fmt"
	"time"
	"topsdk"
	"topsdk/defaultability"
	"topsdk/defaultability/domain"
	toprequest "topsdk/defaultability/request"
	"yz-go/cron"
)

func PushElemeSyncCpsOrderHandle() {
	task := cron.Task{
		Key:  "syncElemeCpsOrder",
		Name: "定时同步Elemecps订单",
		//Spec: "23 */30 * * * *",
		Spec: "23 */1 * * * *",
		Handle: func(task cron.Task) {
			ElemeSyncCpsOrder()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func ElemeSyncCpsOrder() {
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	client := topsdk.NewDefaultTopClient(setting.ElemeAppKey, setting.ElemeAppSecret, "https://eco.taobao.com/router/rest", 20000, 20000)
	ability := defaultability.NewDefaultability(&client)
	var timenow = time.Now().Unix()
	req := toprequest.AlibabaAlscUnionKbcpxPositiveOrderGetRequest{}
	req.SetDateType(4)
	req.SetEndDate(time.Unix(int64(timenow), 0).Format("2006-01-02 15:04:05"))
	req.SetBizUnit(2)
	req.SetPageSize(50)
	req.SetPageNumber(1)
	req.SetStartDate(time.Unix(int64(timenow-86400), 0).Format("2006-01-02 15:04:05"))
	req.SetPid(setting.ElemePid)
	req.SetIncludeUsedStoreId(false)

	resp, err := ability.AlibabaAlscUnionKbcpxPositiveOrderGet(&req)
	if err != nil {
		return
	} else {
		if resp.ResultSuccess == true {
			if len(resp.Result) > 0 {
				var newElemeOrders []domain.AlibabaAlscUnionKbcpxPositiveOrderGetOrderDetailReportDTO
				for _, elemeResultOrder := range resp.Result {
					newElemeOrders = append(newElemeOrders, elemeResultOrder)
				}
				var totalPage = resp.TotalCount/50 + 1
				if totalPage > 1 {
					for i := 2; i <= int(totalPage); i++ {
						req = toprequest.AlibabaAlscUnionKbcpxPositiveOrderGetRequest{}
						req.SetDateType(4)
						req.SetEndDate(time.Unix(int64(timenow), 0).Format("2006-01-02 15:04:05"))
						req.SetBizUnit(2)
						req.SetPageSize(50)
						req.SetPageNumber(int64(i))
						req.SetStartDate(time.Unix(int64(timenow-86400), 0).Format("2006-01-02 15:04:05"))
						req.SetPid(setting.ElemePid)
						req.SetIncludeUsedStoreId(false)

						resp, err = ability.AlibabaAlscUnionKbcpxPositiveOrderGet(&req)
						if err != nil {
							return
						}
						if resp.ResultSuccess == true {
							for _, elemeResultOrder := range resp.Result {
								newElemeOrders = append(newElemeOrders, elemeResultOrder)
							}
						} else {
							err = errors.New(resp.BizErrorDesc)
							return
						}

					}
				}
				for _, norder := range newElemeOrders {

					err = service.ElemeNotify(norder)
					if err != nil {
						return
					}
				}

			}
		} else {
			err = errors.New(resp.BizErrorDesc)
			return
		}

		fmt.Println(resp.Body)
	}

}
