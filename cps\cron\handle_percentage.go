package cron

import (
	"cps/model"
	"cps/mq"
	"cps/service"
	incomeModel "finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
	pluginModel "plugin/model"
	"strconv"
	"time"
	level2 "user/level"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushCpsDidiPercentageHandle() {
	task := cron.Task{
		Key:  "jhCpsPercentage",
		Name: "定时执行聚合cps分成操作",
		Spec: "37 42 */1 * * *",
		Handle: func(task cron.Task) {
			PercentageHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func PercentageHandle() {
	var waitPercentOrders []model.JhCpsOrder
	err := source.DB().Preload("User.Level").Where("status = ?", model.Completed).Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		return
	}

	for _, order := range waitPercentOrders {
		if order.User.ID == 0 || order.User.Level.ID == 0 {
			continue
		}
		var activity model.Activity
		err = source.DB().Where("activity_id = ?", order.ActivityID).First(&activity).Error
		if err != nil {
			continue
		}
		if activity.SettleType == 1 {
			_, month, day := time.Now().Date()
			var monthCheck = make(map[time.Month]int)
			monthCheck[1] = 31
			monthCheck[2] = 28
			monthCheck[3] = 31
			monthCheck[4] = 30
			monthCheck[5] = 31
			monthCheck[6] = 30
			monthCheck[7] = 31
			monthCheck[8] = 31
			monthCheck[9] = 30
			monthCheck[10] = 31
			monthCheck[11] = 30
			monthCheck[12] = 31
			if activity.SettleTime > monthCheck[month] {
				activity.SettleTime = monthCheck[month]
			}
			if day != activity.SettleTime {
				continue
			}
		}
		log.Log().Info("cps分成结算,奖励id[" + strconv.Itoa(int(order.ID)) + "]")
		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 奖励结算
			err = service.SettleAwardByTx(tx, order)
			if err != nil {
				log.Log().Info("结算失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}

			// JhCpsRatio 增加默认等级比例
			err, level := level2.GetLevel(order.User.LevelID)
			if err != nil {
				log.Log().Info("获取会员等级失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}

			// 增加收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.UserID)
			income.OrderSn = int(order.OrderSN)
			income.IncomeType = incomeModel.JhCps
			income.Amount = uint(order.CommissionPrice * level.JhCpsRatio / 10000)
			// 如果是小店订单，收入类型为小店
			if order.PluginID == pluginModel.SmallShopPluginID {
				income.IncomeType = incomeModel.SmallShop
			}
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Info("增加收入失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}
			//执行分销
			err = mq.PublishMessage(order.ID, mq.Settle, 0)
			if err != nil {
				log.Log().Info("cps分销失败,返回")
				return err
			}
			return nil
		})
		if err != nil {
			log.Log().Info("结算失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}

		log.Log().Info("cps分成结算,成功")
	}
	return
}
