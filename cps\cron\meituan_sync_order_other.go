package cron

import (
	"cps/meituan"
	"cps/model"
	"cps/service"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"yz-go/cron"
	"yz-go/utils"
)

func PushSyncCpsOrderOtherHandle() {
	task := cron.Task{
		Key:  "syncJhCpsOrderOther",
		Name: "定时同步jhcps订单Other",
		Spec: "23 40 */1 * * *",
		Handle: func(task cron.Task) {
			SyncCpsOrderOther()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func SyncCpsOrderOther() {
	//var activities []model.Activity
	//err := source.DB().Where("type = ?", model.Meituan).Find(&activities).Error
	//if err != nil {
	//	return
	//}
	var timeStr = time.Now().Unix()
	//for _, activity := range activities {
	var requestParams = make(map[string]interface{})
	//requestParams["actId"] = "null"
	//requestParams["tradeType"] = 2
	//requestParams["ts"] = strconv.Itoa(int(timeStr))
	requestParams["startTime"] = strconv.Itoa(int(timeStr - 86400))
	requestParams["endTime"] = strconv.Itoa(int(timeStr))
	requestParams["queryTimeType"] = 2
	requestParams["page"] = 1
	requestParams["limit"] = 100
	requestParams["platform"] = 2
	requestParams["businessLine"] = []int{1, 2, 3, 4, 5, 6, 15}

	config := map[string]interface{}{
		"method": "post",
		"url":    "https://media.meituan.com/cps_open/common/api/v1/query_order",
		"data":   requestParams,
	}
	var setting model.CpsValue
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	signUtil := meituan.NewSignUtil(setting.MeituanAppKey, setting.MeituanSecret)
	signHeaders := signUtil.GetSignHeaders(config)
	fmt.Println("签名头部字段:", signHeaders)
	//将signHeaders由struct转换成map结构
	var signHeadersMap = make(map[string]string)
	signHeadersJson, _ := json.Marshal(signHeaders)
	json.Unmarshal(signHeadersJson, &signHeadersMap)
	signHeadersMap["Content-Type"] = "application/json; charset=utf-8"
	var result []byte
	err, result = utils.Post("https://media.meituan.com/cps_open/common/api/v1/query_order", requestParams, signHeadersMap)
	if err != nil {
		return
	}
	var meituanResultOrders MeituanOrdersV2
	err = json.Unmarshal(result, &meituanResultOrders)
	if err != nil {
		return
	}
	if meituanResultOrders.Code == 0 {
		var newMeituanOrders []model.MeituanOrderV2
		for _, meituanResultOrder := range meituanResultOrders.Data.DataList {
			newMeituanOrders = append(newMeituanOrders, meituanResultOrder)
		}
		if meituanResultOrders.Data.SkuCount > 1 {
			for i := 2; i <= meituanResultOrders.Data.SkuCount; i++ {
				requestParams["page"] = i
				config = map[string]interface{}{
					"method": "post",
					"url":    "https://media.meituan.com/cps_open/common/api/v1/query_order",
					"data":   requestParams,
				}
				signHeaders = signUtil.GetSignHeaders(config)
				signHeadersMap = make(map[string]string)
				signHeadersJson, _ = json.Marshal(signHeaders)
				json.Unmarshal(signHeadersJson, &signHeadersMap)
				signHeadersMap["Content-Type"] = "application/json; charset=utf-8"
				err, result = utils.Post("https://media.meituan.com/cps_open/common/api/v1/query_order", requestParams, signHeadersMap)
				if err != nil {
					return
				}
				var formeituanResultOrders MeituanOrdersV2
				err = json.Unmarshal(result, &formeituanResultOrders)
				if err != nil {
					return
				}
				if formeituanResultOrders.Code == 0 {
					if len(formeituanResultOrders.Data.DataList) > 0 {
						for _, formeituanResultOrder := range formeituanResultOrders.Data.DataList {
							newMeituanOrders = append(newMeituanOrders, formeituanResultOrder)
						}
					}
				}

			}
		}
		for _, norder := range newMeituanOrders {

			err = service.MeituanNotifyV2(norder)
			if err != nil {
				return
			}
		}

	}
	//}
}
