module cps

go 1.21

require (
	douyin-cps v1.0.0
	dunion-go-sdk v1.0.0
	finance v1.0.0
	gin-vue-admin v1.0.0
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/gin-gonic/gin v1.6.3
	github.com/streadway/amqp v1.1.0
	go.uber.org/zap v1.21.0
	gorm.io/gorm v1.25.5
	product v1.0.0
	topsdk v1.0.0
	user v1.0.0
	yz-go v1.0.0
)

replace (
	convergence => ../convergence-pay
	douyin-cps => ../douyin-cps
	dunion-go-sdk => ../dunion-go-sdk
	finance => ../finance
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product => ../product
	region => ../region
	shipping => ../shipping
	topsdk => ../topsdk
	user => ../user
	yz-go => ../yz-go
)
