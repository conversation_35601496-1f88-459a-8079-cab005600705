package meituan

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"github.com/guonaihong/gout"
	"io"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

type MediaApi struct {
	appKey    string
	appSecret string
}

func NewMediaApi(appKey, appSecret string) *MediaApi {
	return &MediaApi{
		appKey:    appKey,
		appSecret: appSecret,
	}
}

type MeituanGenerateLinkRequestV2 struct {
	ActId    string `json:"actId"`
	Sid      string `json:"sid"`
	LinkType int    `json:"linkType"`
}
type MeituanGenerateLinkResponseV2 struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      string      `json:"data"`
	SkuViewId interface{} `json:"skuViewId"`
}

func (a *MediaApi) GetReferralLink(req interface{}) (rsp *MeituanGenerateLinkResponseV2, err error) {
	if req == nil {
		return
	}
	rsp = &MeituanGenerateLinkResponseV2{}
	if err = gout.POST("https://media.meituan.com/cps_open/common/api/v1/get_referral_link").
		SetHeader(a.getSignHeaders("POST", "https://media.meituan.com/cps_open/common/api/v1/get_referral_link", req)).
		SetJSON(a.jsonMarshal(req)).
		BindJSON(rsp).Do(); err != nil {
		return
	}
	if rsp.Code != 0 {
		return nil, errors.New(rsp.Message)
	}
	return
}

//
//func (a *MediaApi) QueryOrder(ctx context.Context, req *QueryOrderReq) (rsp *QueryOrderRsp, err error) {
//	if req == nil {
//		return
//	}
//	rsp = &QueryOrderRsp{}
//	if err = gout.POST(queryOrderUrl).
//		SetHeader(a.getSignHeaders("POST", queryOrderUrl, req)).
//		SetJSON(a.jsonMarshal(req)).
//		BindJSON(rsp).Do(); err != nil {
//		return
//	}
//	if rsp.Code != 0 {
//		return nil, errors.New(rsp.Message)
//	}
//	return
//}
//
//func (a *MediaApi) QueryCoupon(ctx context.Context, req *QueryCouponReq) (rsp *QueryCouponRsp, err error) {
//	if req == nil {
//		return
//	}
//	rsp = &QueryCouponRsp{}
//	if err = gout.POST(queryCouponUrl).
//		SetHeader(a.getSignHeaders("POST", queryCouponUrl, req)).
//		SetJSON(a.jsonMarshal(req)).
//		BindJSON(rsp).Do(); err != nil {
//		return
//	}
//	if rsp.Code != 0 {
//		return nil, errors.New(rsp.Message)
//	}
//	return
//}

func (a *MediaApi) getSignHeaders(method, url string, body interface{}) gout.H {
	headers := a.getHeaders()
	cm := a.contentMd5(body)
	headers["Content-MD5"] = cm
	headers["S-Ca-Signature"] = a.sign(method, cm, url)
	return headers
}

func (a *MediaApi) sign(method, contentMd5, reqUrl string) string {
	stringStr := strings.ToUpper(method) + "\n" + contentMd5 + "\n"
	headers := a.getHeaders()
	keys := a.sortHeaders(headers)
	for _, key := range keys {
		if key == "S-Ca-Signature-Headers" {
			continue
		}
		v := headers[key]
		vv, ok := v.(string)
		if ok {
			stringStr += key + ":" + vv + "\n"
		}
	}
	u, _ := url.Parse(reqUrl)
	stringStr += u.Path

	h := hmac.New(sha256.New, []byte(a.appSecret))
	h.Write([]byte(stringStr))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func (a *MediaApi) getHeaders() gout.H {
	return gout.H{
		"S-Ca-App":               a.appKey,
		"S-Ca-Signature-Headers": "S-Ca-App,S-Ca-Timestamp",
		"S-Ca-Timestamp":         strconv.FormatInt(time.Now().UnixMilli(), 10),
	}
}

func (a *MediaApi) sortHeaders(header gout.H) []string {
	keys := make([]string, 0, len(header))
	for k := range header {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	return keys
}

func (a *MediaApi) contentMd5(body interface{}) string {
	jsonData, _ := json.Marshal(body)
	md5Hash := md5.New()
	_, err := io.Copy(md5Hash, strings.NewReader(string(jsonData)))
	if err != nil {
		panic(err)
	}
	return base64.StdEncoding.EncodeToString(md5Hash.Sum(nil))
}
func (a *MediaApi) computeContentMD5(bodyStream string) string {
	// 将字符串转换为UTF-8字节数组
	bodyBytes := []byte(bodyStream)

	// 计算MD5哈希值
	hash := md5.New()
	_, err := io.Copy(hash, strings.NewReader(string(bodyBytes)))
	if err != nil {
		panic(err)
	}
	md5Hash := hash.Sum(nil)

	// 将MD5哈希值进行Base64编码
	contentMD5 := base64.StdEncoding.EncodeToString(md5Hash)

	return contentMD5
}
func (a *MediaApi) jsonMarshal(body interface{}) string {
	dataBytes, _ := json.Marshal(body)
	return string(dataBytes)
}
