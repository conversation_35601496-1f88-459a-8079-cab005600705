package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type Activity struct {
	source.Model
	Title          string       `json:"title" form:"title"`             //活动标题
	CategoryID     uint         `json:"category_id" form:"category_id"` //分类id(暂不使用)
	Type           CpsOrderType `json:"type" form:"type"`               //活动类型 美团meituan 滴滴didi
	ActivityID     string       `json:"activity_id" form:"activity_id"` //cps活动id
	ActivityType   ActivityType `json:"activity_type" form:"activity_type" gorm:"type:text;"`
	Image          string       `json:"image" form:"image"`                               //活动图片
	SettleType     int          `json:"settle_type" form:"settle_type"`                   // 1月结  2订单完成时
	SettleTime     int          `json:"settle_time" form:"settle_time"`                   //月结时间 (1-31)
	CommissionRate string       `json:"commission_rate" form:"commission_rate"`           //佣金比例 例如:98% 则传98即可
	Description    string       `json:"description" form:"description" gorm:"type:text;"` //活动描述
	Status         int          `json:"status" form:"status" gorm:"default:0;"`           //状态 0关闭 1启用
	ExpireTime     int64        `json:"expire_time" form:"expire_time" gorm:"default:0;"`
}
type ActivityType []string

func (value ActivityType) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *ActivityType) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
