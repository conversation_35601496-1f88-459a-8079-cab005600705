package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		JhCpsOrderModel{},
		Activity{},
	)

	// 菜单,权限
	menus := []model.SysMenu{}
	//sysBaseMenu := []model.SysBaseMenu{}

	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(20) == true || utils.LocalEnv() == false {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	} //json.Unmarshal([]byte(menuJson), &sysBaseMenu)
	//if err != nil {
	//	return
	//}
	//err = source.DB().Transaction(func(tx *gorm.DB) (err error) {
	//	// 删掉旧菜单
	//	for _, v := range sysBaseMenu {
	//		menu := &v
	//		// 增加菜单
	//		err = service.AddMenuAuthority(menu, "888")
	//		if err != nil {
	//			return
	//		}
	//	}
	//	return
	//})
	return
}
