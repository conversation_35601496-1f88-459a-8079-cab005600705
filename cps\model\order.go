package model

import (
	"gorm.io/gorm"
	model2 "user/model"
	"yz-go/source"
)

type CpsOrderType string //售后状态

const (
	Meituan            CpsOrderType = "meituan" // 美团
	Didi               CpsOrderType = "didi"
	MeituanDistributor CpsOrderType = "meituanDistributor"
	Eleme              CpsOrderType = "eleme"
)

type CpsOrderStatus string //售后状态

const (
	Payed     CpsOrderStatus = "payed"
	Completed CpsOrderStatus = "completed"
	Settled   CpsOrderStatus = "settled"
	Refund    CpsOrderStatus = "refunded"
)

type JhCpsOrderModel struct {
	source.Model
	OrderSN         uint              `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"` //订单编号
	CpsOrderId      string            `json:"order_id" form:"order_id" gorm:"column:order_id;index;"`        //cps订单号
	DataString      string            `json:"data_string" gorm:"type:text;"`                                 //元数据
	ApplicationID   uint              `json:"application_id"`                                                //采购端id
	UserID          uint              `json:"user_id"`                                                       //用户id
	ThirdUserID     uint              `json:"third_user_id"`                                                 //采购端用户id
	ActivityID      string            `json:"activity_id"`                                                   //活动id
	Price           uint              `json:"price"`                                                         //订单价格
	RefundPrice     uint              `json:"refund_price"`                                                  //退款金额
	Ratio           float64           `json:"ratio"`                                                         //佣金比例
	CommissionPrice uint              `json:"commission_price"`                                              //cps佣金 分成基数
	Type            CpsOrderType      `json:"type"`                                                          //订单类型 meituan美团 didi滴滴
	Title           string            `json:"title"`                                                         //订单标题
	Status          CpsOrderStatus    `json:"status" form:"status" gorm:"column:status;index;"`              //订单状态
	LocalSettleAt   *source.LocalTime `json:"local_settle_at" form:"local_settle_at"`                        //结算时间
	PayAt           *source.LocalTime `json:"pay_at" form:"pay_at"`                                          //付款时间
	CompleteAt      *source.LocalTime `json:"complete_at" form:"complete_at"`                                //完成时间
	RefundAt        *source.LocalTime `json:"refund_at" form:"refund_at"`                                    //退款时间
	IsConnected     int               `json:"is_connected" form:"is_connected" gorm:"default:0;"`            //1同步 0未同步
	ProductId       string            `json:"product_id" form:"product_id"`                                  //cps商品id
	ProductName     string            `json:"product_name" form:"product_name"`                              //cps商品名称
	IsDistributor   int               `json:"is_distributor" gorm:"default:1;"`
	// 插件id
	PluginID uint `json:"plugin_id" form:"plugin_id" gorm:"column:plugin_id;index;"`
	// 小商店id
	ShopID uint `json:"shop_id" form:"shop_id" gorm:"column:shop_id;index;"`
}

func (JhCpsOrderModel) TableName() string {
	return "jh_cps_orders"
}

type JhCpsOrder struct {
	JhCpsOrderModel
	User model2.ChildUser `json:"user" gorm:"foreignKey:UserID"`
}
type JhCpsOrderCron struct {
	JhCpsOrder
	User model2.ChildUser
}

func (JhCpsOrderCron) TableName() string {
	return "jh_cps_orders"
}

func (o *JhCpsOrder) AfterCreate(tx *gorm.DB) (err error) {
	timestamp := uint(o.CreatedAt.Unix())
	orderSn := o.ID + timestamp*110
	err = tx.Model(&o).Update("order_sn", orderSn).Error
	return
}

type MeituanOrder struct {
	Orderid                     string      `json:"orderid"`
	Paytime                     string      `json:"paytime"`
	Payprice                    string      `json:"payprice"`
	Sid                         string      `json:"sid"`
	Smstitle                    string      `json:"smstitle"`
	Appkey                      string      `json:"appkey"`
	Status                      int         `json:"status"`
	Profit                      string      `json:"profit"`
	CpaProfit                   string      `json:"cpaProfit"`
	Refundtime                  interface{} `json:"refundtime"`
	Refundprice                 interface{} `json:"refundprice"`
	Refundprofit                interface{} `json:"refundprofit"`
	CpaRefundProfit             interface{} `json:"cpaRefundProfit"`
	Extra                       interface{} `json:"extra"`
	TradeTypeList               []int       `json:"tradeTypeList"`
	TradeTypeBusinessTypeMapStr string      `json:"tradeTypeBusinessTypeMapStr"`
	RiskOrder                   interface{} `json:"riskOrder"`
	BusinessLine                int         `json:"businessLine"`
	SubBusinessLine             interface{} `json:"subBusinessLine"`
	ActId                       int         `json:"actId"`
	ProductId                   string      `json:"productId"`
	ProductName                 string      `json:"productName"`
}
type MeituanOrderV2 struct {
	BusinessLine   int    `json:"businessLine"`
	OrderId        string `json:"orderId"`
	PayTime        int    `json:"payTime"`
	PayPrice       string `json:"payPrice"`
	UpdateTime     int    `json:"updateTime"`
	CommissionRate string `json:"commissionRate"`
	Profit         string `json:"profit"`
	CpaProfit      string `json:"cpaProfit"`
	Sid            string `json:"sid"`
	ProductId      string `json:"productId"`
	ProductName    string `json:"productName"`
	OrderDetail    []struct {
		FinishTime   string      `json:"finishTime"`
		BasicAmount  string      `json:"basicAmount"`
		CouponFee    string      `json:"couponFee"`
		OrderViewId  interface{} `json:"orderViewId"`
		RefundAmount string      `json:"refundAmount"`
		RefundFee    string      `json:"refundFee"`
		RefundTime   string      `json:"refundTime"`
		SettleTime   string      `json:"settleTime"`
		UpdateTime   string      `json:"updateTime"`
		CouponStatus string      `json:"couponStatus"`
		ItemOrderId  string      `json:"itemOrderId"`
	} `json:"orderDetail"`
	RefundPrice     string      `json:"refundPrice"`
	RefundTime      string      `json:"refundTime"`
	RefundProfit    string      `json:"refundProfit"`
	CpaRefundProfit string      `json:"cpaRefundProfit"`
	Status          string      `json:"status"`
	TradeType       int         `json:"tradeType"`
	ActId           int         `json:"actId"`
	Appkey          string      `json:"appkey"`
	SkuCount        int         `json:"skuCount"`
	Platform        int         `json:"platform"`
	CityName        interface{} `json:"cityName"`
	CategoryId      interface{} `json:"categoryId"`
	CategoryName    interface{} `json:"categoryName"`
}
type ElemeOrder struct {
	Title                  string `json:"title"`
	PicUrl                 string `json:"pic_url"`
	ShopName               string `json:"shop_name"`
	PayAmount              string `json:"pay_amount"`
	SettleAmount           string `json:"settle_amount"`
	TraceTime              string `json:"trace_time"`
	TkCreateTime           string `json:"tk_create_time"`
	PayTime                string `json:"pay_time"`
	ReceiveTime            string `json:"receive_time"`
	SettleTime             string `json:"settle_time"`
	Income                 string `json:"income"`
	Settle                 string `json:"settle"`
	ItemId                 string `json:"item_id"`
	ProductNum             int    `json:"product_num"`
	UnitPrice              string `json:"unit_price"`
	CategoryName           string `json:"category_name"`
	BizOrderId             int    `json:"biz_order_id"`
	ParentOrderId          int    `json:"parent_order_id"`
	MainItemId             string `json:"main_item_id"`
	MainItemTitle          string `json:"main_item_title"`
	OrderState             int    `json:"order_state"`
	OrderItemStatusName    string `json:"order_item_status_name"`
	SettleState            int    `json:"settle_state"`
	FullSettleAmount       string `json:"full_settle_amount"`
	CommissionRate         string `json:"commission_rate"`
	CommissionFee          string `json:"commission_fee"`
	SubsidyRate            string `json:"subsidy_rate"`
	SubsidyFee             string `json:"subsidy_fee"`
	IncomeRate             string `json:"income_rate"`
	StratifyRate           string `json:"stratify_rate"`
	DeductRate             string `json:"deduct_rate"`
	PlatformCommissionRate string `json:"platform_commission_rate"`
	PlatformCommissionFee  string `json:"platform_commission_fee"`
	ChannelRate            string `json:"channel_rate"`
	ChannelFee             string `json:"channel_fee"`
	MediaId                string `json:"media_id"`
	MediaName              string `json:"media_name"`
	AdZoneId               string `json:"ad_zone_id"`
	AdZoneName             string `json:"ad_zone_name"`
	ActivityFee            string `json:"activity_fee"`
	ActivityServiceFee     string `json:"activity_service_fee"`
	ActivityServiceRate    string `json:"activity_service_rate"`
	GmtModified            string `json:"gmt_modified"`
	Tag                    string `json:"tag"`
	Sid                    string `json:"sid"`
	PlatformType           int    `json:"platform_type"`
	ActivityId             int    `json:"activity_id"`
	UsedStoreId            string `json:"used_store_id"`
	Pid                    string `json:"pid"`
	RelationOrderId        int    `json:"relation_order_id"`
	FlowType               int    `json:"flow_type"`
	OrderItemStatus        int    `json:"order_item_status"`
	ActivityInfoRemarkList string `json:"activity_info_remark_list"`
	ChannelRightId         string `json:"channel_right_id"`
	ExtInfo                string `json:"ext_info"`
}
type DidiOrder struct {
	CpaProfit   int    `json:"cpa_profit"`
	CpaType     string `json:"cpa_type"`
	CpsProfit   int    `json:"cps_profit"`
	IsRisk      int    `json:"is_risk"`
	OpenUid     string `json:"open_uid"`
	OrderId     string `json:"order_id"`
	OrderStatus int    `json:"order_status"`
	PayPrice    int    `json:"pay_price"`
	PayTime     int    `json:"pay_time"`
	ProductId   string `json:"product_id"`
	PromotionId string `json:"promotion_id"`
	RefundPrice int    `json:"refund_price"`
	RefundTime  int    `json:"refund_time"`
	RetryTimes  int    `json:"retry_times"`
	SourceId    string `json:"source_id"`
	Status      int    `json:"status"`
	Title       string `json:"title"`
	FailReason  string `json:"fail_reason"`
	ActivityId  int    `json:"activity_id"`
}

type MeituanNotifyOrder struct {
	Smstitle            string      `json:"smstitle" form:"smstitle"`
	Quantity            string      `json:"quantity" form:"quantity"`
	Orderid             string      `json:"orderid" form:"orderid" `
	Dealid              string      `json:"dealid" form:"dealid"`
	Paytime             string      `json:"paytime" form:"paytime"`
	Type                string      `json:"type" form:"type"`
	Ordertime           string      `json:"ordertime" form:"ordertime"`
	Sid                 string      `json:"sid" form:"sid"`
	Uid                 string      `json:"uid" form:"uid"`
	ProductId           string      `json:"productId" form:"productId"`
	ProductName         string      `json:"productName" form:"productName"`
	Status              interface{} `json:"status" form:"status"`
	Total               string      `json:"total" form:"total"`
	Direct              string      `json:"direct" form:"direct"`
	Ratio               string      `json:"ratio" form:"ratio"`
	Sign                string      `json:"sign" form:"sign"`
	TradeTypeList       []int       `json:"tradeTypeList" form:"tradeTypeList"`
	ActId               interface{} `json:"actId" form:"actId"`
	BusinessLine        interface{} `json:"businessLine" form:"businessLine"`
	SubBusinessLine     interface{} `json:"subBusinessLine" form:"subBusinessLine"`
	Appkey              string      `json:"appkey" form:"appkey"`
	PayPrice            string      `json:"payPrice" form:"payPrice"`
	ModTime             string      `json:"modTime" form:"modTime"`
	ConsumeType         string      `json:"consumeType" form:"consumeType"`
	RefundType          string      `json:"refundType" form:"refundType"`
	EncryptionVoucherId string      `json:"encryptionVoucherId" form:"encryptionVoucherId"`
	Refundtime          string      `json:"refundtime"`
	Profit              string      `json:"profit"`
}

type MeituanNotifyOrderV2 struct {
	Smstitle            string      `json:"smstitle" form:"smstitle"`
	Quantity            string      `json:"quantity" form:"quantity"`
	Dealid              string      `json:"dealid" form:"dealid"`
	Paytime             string      `json:"paytime" form:"paytime"`
	Type                string      `json:"type" form:"type"`
	Ordertime           string      `json:"ordertime" form:"ordertime"`
	Uid                 string      `json:"uid" form:"uid"`
	Total               string      `json:"total" form:"total"`
	Direct              string      `json:"direct" form:"direct"`
	Ratio               string      `json:"ratio" form:"ratio"`
	Sign                string      `json:"sign" form:"sign"`
	TradeTypeList       []int       `json:"tradeTypeList" form:"tradeTypeList"`
	ActId               interface{} `json:"actId" form:"actId"`
	SubBusinessLine     interface{} `json:"subBusinessLine" form:"subBusinessLine"`
	ModTime             string      `json:"modTime" form:"modTime"`
	ConsumeType         string      `json:"consumeType" form:"consumeType"`
	RefundType          string      `json:"refundType" form:"refundType"`
	EncryptionVoucherId string      `json:"encryptionVoucherId" form:"encryptionVoucherId"`
	Refundtime          string      `json:"refundtime"`

	BusinessLine    int         `json:"businessLine"`
	OrderId         string      `json:"orderId"`
	PayTime         int         `json:"payTime"`
	PayPrice        string      `json:"payPrice"`
	UpdateTime      int         `json:"updateTime"`
	CommissionRate  string      `json:"commissionRate"`
	Profit          string      `json:"profit"`
	CpaProfit       string      `json:"cpaProfit"`
	Sid             string      `json:"sid"`
	ProductId       string      `json:"productId"`
	ProductName     string      `json:"productName"`
	OrderDetail     interface{} `json:"orderDetail"`
	RefundPrice     interface{} `json:"refundPrice"`
	RefundTime      string      `json:"refundTime"`
	RefundProfit    string      `json:"refundProfit"`
	CpaRefundProfit string      `json:"cpaRefundProfit"`
	Status          string      `json:"status"`
	TradeType       int         `json:"tradeType"`
	Appkey          string      `json:"appkey"`
	SkuCount        int         `json:"skuCount"`
}

type Application struct {
	source.Model
	CompanyName          string `json:"companyName" form:"companyName" gorm:"column:company_name;comment:;type:varchar(255);size:255;"`
	CompanyIntro         string `json:"companyIntro" form:"companyIntro" gorm:"column:company_intro;comment:;type:text;"`
	ProvinceId           int    `json:"provinceId" form:"provinceId" gorm:"column:province_id;comment:;type:int(10);size:10;"`
	CityId               int    `json:"cityId" form:"cityId" gorm:"column:city_id;comment:;type:int(10);size:10;"`
	DistrictId           int    `json:"districtId" form:"districtId" gorm:"column:district_id;comment:;type:int(10);size:10;"`
	Address              string `json:"address" form:"address" gorm:"column:address;comment:;type:varchar(255);size:255;"`
	CreditCode           string `json:"creditCode" form:"creditCode" gorm:"column:credit_code;comment:;type:varchar(255);size:255;"`
	BusinessLicense      string `json:"businessLicense" form:"businessLicense" gorm:"column:business_license;comment:;type:text;"`
	LegalPersonName      string `json:"legalPersonName" form:"legalPersonName" gorm:"column:legal_person_name;comment:;type:varchar(255);size:255;"`
	IdCardNumber         string `json:"idCardNumber" form:"idCardNumber" gorm:"column:id_card_number;comment:;type:varchar(255);size:255;"`
	IdCardFront          string `json:"idCardFront" form:"idCardFront" gorm:"column:id_card_front;comment:;type:text;"`
	IdCardBackend        string `json:"idCardBackend" form:"idCardBackend" gorm:"column:id_card_backend;comment:;type:text;"`
	ContactsName         string `json:"contactsName" form:"contactsName" gorm:"column:contacts_name;comment:;type:varchar(255);size:255;"`
	ContactsPhontnumber  string `json:"contactsPhontnumber" form:"contactsPhontnumber" gorm:"column:contacts_phontnumber;comment:;type:varchar(255);size:255;"`
	ContactsEmail        string `json:"contactsEmail" form:"contactsEmail" gorm:"column:contacts_email;comment:;type:varchar(255);size:255;"`
	AppName              string `json:"appName" form:"appName" gorm:"column:app_name;comment:;type:varchar(255);size:255;"`
	AppLevelID           uint   `json:"appLevelId" form:"appLevelId" gorm:"column:app_level_id;type:int;"`
	CallBackLink         string `json:"callBackLink" form:"callBackLink" gorm:"column:call_back_link;comment:;type:varchar(255);size:255;"`
	CallBackLinkValidity int    `json:"call_back_link_validity" form:"call_back_link_validity" gorm:"column:call_back_link_validity;default:1;type:int;"`
	CallBackLinkCps      string `json:"callBackLinkCps" form:"callBackLinkCps" gorm:"column:call_back_link_cps;comment:;type:varchar(255);size:255;"`
	CallBackLinkJhCps    string `json:"callBackLinkJhCps" form:"callBackLinkJhCps" gorm:"column:call_back_link_jh_cps;comment:;type:varchar(255);size:255;"`

	IpList        string `json:"ipList" form:"ipList" gorm:"column:ip_list;comment:;type:text;"`
	MemberId      int    `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	SupplierID    uint   `json:"supplier_id"`
	PetSupplierID uint   `json:"pet_supplier_id"`
}

func (Application) TableName() string {
	return "application"
}
