package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type CpsSetting struct {
	model.SysSetting
	Value CpsValue `json:"value"`
}

func (i CpsSetting) TableName() string {
	return "sys_settings"
}

type CpsValue struct {
	MeituanSyncOrder              int    `json:"meituan_sync_order"` //同步订单 1允许 2不允许
	MeituanAppKey                 string `json:"meituan_app_key"`
	MeituanSecret                 string `json:"meituan_secret"`
	DidiAppKey                    string `json:"didi_app_key"`
	DidiAccessKey                 string `json:"didi_access_key"`
	DidiPromotionID               string `json:"didi_promotion_id"`
	MeituanDistributorUtmSource   string `json:"meituan_distributor_utm_source"`
	MeituanDistributorAppKey      string `json:"meituan_distributor_app_key"`
	MeituanDistributorPromotionId string `json:"meituan_distributor_promotion_id"`
	ElemeAppKey                   string `json:"eleme_app_key"`
	ElemeAppSecret                string `json:"eleme_app_secret"`
	ElemePid                      string `json:"eleme_pid"`
}

func (value CpsValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *CpsValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var cpsSetting *CpsValue

func getCpsSetting(key string) (err error, sysSetting CpsSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}
func GetCpsSetting() (err error, setting CpsValue) {
	if cpsSetting == nil {
		var sysSetting CpsSetting
		err, sysSetting = getCpsSetting("jhcps_setting")
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		cpsSetting = &sysSetting.Value
	}
	return err, *cpsSetting
}

func ResetCps() {
	//重置全局变量 start
	cpsSetting = nil
}
