package response

type GenerateLinkResponse struct {
	Link      string `json:"link"`
	Dsi       string `json:"dsi"`
	Barcode   string `json:"minicode"`
	Appid     string `json:"appid"`
	AppSource string `json:"app_source"`
}

type JumpType struct {
	Name        string `json:"name"`
	Enumeration int    `json:"value"`
	Type        string `json:"type"`
}

const (
	MEITUAN_H5                   = 1
	MEITUAN_DEEPLINK_APP         = 2
	MEITUAN_MIDDLE_APP           = 3
	MEITUAN_WX_MINI_MEITUAN      = 4
	MEITUAN_TUANKOULING          = 5
	MEITUAN_DEEPLINK__YOUXUANAPP = 6
	MEITUAN_MIDDLE__YOUXUANAPP   = 7
	MEITUAN_WX_MINI_YOUXUAN      = 8
	MEITUAN_WX_MINI_PINHAOFAN    = 9
)

const (
	DIDI_H5      = 1
	DIDI_WX_MINI = 2
)

const (
	ELEME_H5       = 1
	ELEME_WX_MINI  = 2
	ELEME_DEEPLINK = 3
)
