package route

import (
	"cps/api/app"
	v1 "cps/api/v1"
	v12 "cps/api/v1"
	"github.com/gin-gonic/gin"
)

// 后台公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("jhcps")
	{
		CpsRouter.POST("didiNotify", v12.DidiNotify)
		CpsRouter.POST("meituanNotify", v12.MeituanNotify)
	}
}

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("jhcps")
	{
		CpsRouter.POST("createActivity", v1.CreateActivity)             // 新建Activity
		CpsRouter.DELETE("deleteActivity", v1.DeleteActivity)           // 删除Activity
		CpsRouter.DELETE("deleteActivityByIds", v1.DeleteActivityByIds) // 批量删除Activity
		CpsRouter.PUT("updateActivity", v1.UpdateActivity)              // 更新Activity
		CpsRouter.GET("findActivity", v1.FindActivity)                  // 根据ID获取Activity
		CpsRouter.GET("getActivityList", v1.GetActivityList)            // 获取Activity列表
		CpsRouter.GET("exportActivity", v1.ExportActivity)              // 获取Activity列表
		CpsRouter.POST("setAttributeStatus", v1.SetAttributeStatus)     // 获取Activity列表
		CpsRouter.GET("getOrderList", v1.GetJhCpsOrderList)             // 获取Activity列表
		CpsRouter.GET("exportOrderList", v1.ExportOrderList)            // 获取Activity列表
		CpsRouter.GET("notifyOrder", v1.NotifyOrder)                    // 获取Activity列表
		CpsRouter.POST("saveSetting", v1.UpdateCpsSetting)              //
		CpsRouter.POST("getSetting", v1.FindCpsSetting)                 //
		CpsRouter.POST("UpdateCpsSettingMask", v1.UpdateCpsSettingMask) // 更新基础设置敏感内容
	}

}
func InitAppPrivateRouter(Router *gin.RouterGroup) {
	CpsRouter := Router.Group("jhcps")
	{
		CpsRouter.GET("getActivityList", v1.GetAppActivityList)          // 获取Activity列表
		CpsRouter.GET("generateLink", app.GenerateLink)                  // 获取Activity列表
		CpsRouter.GET("mockOrderCallback", app.MockOrderCallback)        // 获取Activity列表
		CpsRouter.POST("getOrderList", app.GetJhCpsOrderList)            // 获取Activity列表
		CpsRouter.POST("GetMeituanCouponList", app.GetMeituanCouponList) //
		//CpsRouter.GET("getMeituanProvince", app.GetMeituanProvince)                    // 获取Activity列表
		//CpsRouter.GET("getMeituanCityCategories", app.GetMeituanCityCategories)        // 获取Activity列表
		//CpsRouter.GET("getMeituanCity", app.GetMeituanCity)                            // 获取Activity列表
		//CpsRouter.GET("getMeituanRegions", app.GetMeituanRegions)                      // 获取Activity列表
		//CpsRouter.GET("getMeituanSeckill", app.GetMeituanSeckill)                      // 获取Activity列表
		//CpsRouter.GET("getMeituanSeckillList", app.GetMeituanSeckillList)              // 获取Activity列表
		//CpsRouter.POST("getMeituanSearchDeals", app.GetMeituanSearchDeals)             // 获取Activity列表
		//CpsRouter.POST("GetMeituanSearchDealsDetail", app.GetMeituanSearchDealsDetail) // 获取Activity列表
	}
}
