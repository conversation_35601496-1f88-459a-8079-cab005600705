package service

import (
	"cps/model"
	"cps/request"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"os"
	request2 "product/request"
	"strconv"
	"time"
	YzGoConfig "yz-go/config"
	yzRequest "yz-go/request"
	"yz-go/source"
	"yz-go/utils"
)

func CreateActivity(activity model.Activity) (err error) {
	var check model.Activity
	err = source.DB().Where("activity_id = ?", activity.ActivityID).First(&check).Error
	if err == nil {
		err = errors.New("相同activity_id的活动已经存在")
		return
	}
	err = source.DB().Create(&activity).Error
	if err != nil {
		return
	}
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteActivity
//@description: 删除Activity记录
//@param: activity model.Activity
//@return: err error

func DeleteActivity(activity model.Activity) (err error) {
	err = source.DB().Delete(&activity).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteActivityByIds
//@description: 批量删除Activity记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteActivityByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.Activity{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateActivity
//@description: 更新Activity记录
//@param: activity *model.Activity
//@return: err error

func UpdateActivity(activity model.Activity) (err error) {
	err = source.DB().Updates(&activity).Error
	return err
}

func GetActivity(id uint) (err error, activity model.Activity) {
	err = source.DB().Where("id = ?", id).First(&activity).Error
	return
}

func GetActivityInfoList(info request.ActivitySearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.Activity{})
	if info.Title != "" {
		db = db.Where("title like ?", "%"+info.Title+"%")
	}
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	if info.Status != nil {
		db = db.Where("status = ?", info.Status)
	}
	if info.ActivityID != "" {
		db = db.Where("activity_id = ?", info.ActivityID)
	}
	var activities []model.Activity
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&activities).Error

	return err, activities, total
}

func ExportActivityInfoList(info request.ActivitySearch) (err error, link string) {

	db := source.DB().Model(&model.Activity{})
	if info.Title != "" {
		db = db.Where("title like ?", "%"+info.Title+"%")
	}
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	var activities []model.Activity
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Order("created_at desc").Find(&activities).Error
	if err != nil {
		return
	}
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "活动id")
	f.SetCellValue("Sheet1", "B1", "活动标题")
	//f.SetCellValue("Sheet1", "C1", "分类id")
	f.SetCellValue("Sheet1", "C1", "活动类型")
	f.SetCellValue("Sheet1", "D1", "cps活动id")
	f.SetCellValue("Sheet1", "E1", "结算时间点(1月结 2订单完成时)")
	f.SetCellValue("Sheet1", "F1", "结算日期")
	f.SetCellValue("Sheet1", "G1", "佣金比例")
	f.SetCellValue("Sheet1", "H1", "活动创建时间")
	i := 2
	//var getCloudGoodsDetail request.GetCloudGoodsDetail
	//getCloudGoodsDetail.GatherSuppliesId = searchData.GatherSuppliesId
	for _, v := range activities {

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Title)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.SettleType)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.ActivityID)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.SettleType)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.SettleTime)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.CommissionRate)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.CreatedAt.Format("2006-01-02 15:04:05"))

		i++

	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	times := time.Now().Format("20060102150405")

	path := YzGoConfig.Config().Local.Path + "/export_cps_activity"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + times + "cps活动导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return err, link
}

func SetAttributeStatus(columnStatus request2.ColumnStatus) (err error, enable int) {
	var product model.Activity
	enable = 1
	err = source.DB().Model(&product).Where("`id` = ?", columnStatus.ID).First(&product).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return err, enable
	} else {

		if product.Status == 1 {
			enable = 0
		}

		err = source.DB().Model(&product).Where("id = ?", columnStatus.ID).Update("status", enable).Error

	}
	return err, enable
}

//func GetJumpTypeList(platform string) (err error, list []response.JumpType) {
//	if platform == "meituan" {
//		return GetMeituanJumpTypeList()
//	}
//	if platform == "didi" {
//		return GetDidiJumpTypeList()
//	}
//	if platform == "eleme" {
//		return GetElemeJumpTypeList()
//	}
//	return
//}
//
//func GetMeituanJumpTypeList() (err error, list []response.JumpType) {
//	list = append(list, response.JumpType{
//		Name:        "H5链接",
//		Enumeration: response.MEITUAN_H5,
//		Type:        "h5",
//	})
//	list = append(list, response.JumpType{
//		Name:        "Deeplink-美团APP",
//		Enumeration: response.MEITUAN_DEEPLINK_APP,
//		Type:        "other",
//	})
//	list = append(list, response.JumpType{
//		Name:        "中间页唤起-美团APP",
//		Enumeration: response.MEITUAN_MIDDLE_APP,
//		Type:        "app",
//	})
//	list = append(list, response.JumpType{
//		Name:        "微信小程序-美团小程序",
//		Enumeration: response.MEITUAN_WX_MINI_MEITUAN,
//		Type:        "mini",
//	})
//	list = append(list, response.JumpType{
//		Name:        "团口令",
//		Enumeration: response.MEITUAN_TUANKOULING,
//		Type:        "other",
//	})
//	list = append(list, response.JumpType{
//		Name:        "Deeplink-优选APP",
//		Enumeration: response.MEITUAN_DEEPLINK__YOUXUANAPP,
//		Type:        "other",
//	})
//	list = append(list, response.JumpType{
//		Name:        "中间页唤起-优选APP",
//		Enumeration: response.MEITUAN_MIDDLE__YOUXUANAPP,
//		Type:        "app",
//	})
//	list = append(list, response.JumpType{
//		Name:        "微信小程序-优选小程序",
//		Enumeration: response.MEITUAN_WX_MINI_YOUXUAN,
//		Type:        "mini",
//	})
//	list = append(list, response.JumpType{
//		Name:        "微信小程序-拼好饭小程序",
//		Enumeration: response.MEITUAN_WX_MINI_PINHAOFAN,
//		Type:        "mini",
//	})
//	return
//}
//
//func GetDidiJumpTypeList() (err error, list []response.JumpType) {
//	list = append(list, response.JumpType{
//		Name:        "H5链接",
//		Enumeration: response.DIDI_H5,
//		Type:        "h5",
//	})
//	list = append(list, response.JumpType{
//		Name:        "小程序",
//		Enumeration: response.DIDI_WX_MINI,
//		Type:        "mini",
//	})
//
//	return
//}
//
//func GetElemeJumpTypeList() (err error, list []response.JumpType) {
//	list = append(list, response.JumpType{
//		Name:        "H5链接",
//		Enumeration: response.ELEME_H5,
//		Type:        "h5",
//	})
//	list = append(list, response.JumpType{
//		Name:        "小程序",
//		Enumeration: response.ELEME_WX_MINI,
//		Type:        "mini",
//	})
//	list = append(list, response.JumpType{
//		Name:        "app",
//		Enumeration: response.ELEME_DEEPLINK,
//		Type:        "app",
//	})
//
//	return
//}
