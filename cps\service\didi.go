package service

import (
	"context"
	"cps/model"
	"cps/request"
	"cps/response"
	"dunion-go-sdk/client"
	consts "dunion-go-sdk/const"
	model2 "dunion-go-sdk/model"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	pluginModel "plugin/model"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

type Didi<PERSON><PERSON> struct{}

func (*DidiApi) GenerateLink(info request.GenerateLinkRequest) (err error, generateLinkResponse response.GenerateLinkResponse) {
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	c := client.NewUnionClient(setting.DidiAppKey, setting.DidiAccessKey)
	//日志可选，将在指定目录生成日志

	//或者使用日志注入的方式，需实现两个接口函数：
	//Infof(template string, args ...interface{})
	//Errorf(template string, args ...interface{})
	//然后调用
	//util.SetLogger(yourLogger)

	//设置全局超时时间
	//util.SetTimeoutDuration(2*time.Second)

	//或者设置单个接口的超时时间
	//link, err := c.GenerateH5Link(context.Background(), 6133, 6834408369283047676, "d", model.Option{Timeout: time.Millisecond})
	var activityID int
	activityID, err = strconv.Atoi(info.ActivityID)
	if err != nil {
		return
	}
	var promotionId int
	promotionId, err = strconv.Atoi(setting.DidiPromotionID)
	if err != nil {
		return
	}
	var data *model2.LinkResponse
	if info.ActivityID == "215488701344" {
		//套餐活动只能获取小程序链接
		info.JumpType = response.DIDI_WX_MINI
	}
	if info.JumpType == response.DIDI_H5 {
		data, err = c.GenerateH5Link(context.Background(), int64(activityID), int64(promotionId), info.Customize)
	} else if info.JumpType == response.DIDI_WX_MINI {
		data, err = c.GenerateMiniLink(context.Background(), int64(activityID), int64(promotionId), info.Customize)
	}

	//log.Log().Info("didiGenerateSourceId", zap.Any("data", info.Customize))
	if err != nil {
		fmt.Println(err)
		return
	}
	if data.Errno == 0 {
		generateLinkResponse.Link = data.Data.Link
		generateLinkResponse.Dsi = data.Data.DSI
		generateLinkResponse.Appid = data.Data.AppId
		generateLinkResponse.AppSource = data.Data.AppSource
		var code *model2.QrcodeResponse
		if info.JumpType == response.DIDI_H5 {
			code, err = c.GenerateH5Code(context.Background(), generateLinkResponse.Dsi, info.Customize)
		} else if info.JumpType == response.DIDI_WX_MINI {
			code, err = c.GenerateMiniCode(context.Background(), generateLinkResponse.Dsi, info.Customize)
		}
		if err != nil {
			fmt.Println(err)
			return
		}
		if code.Errno == 0 {
			generateLinkResponse.Barcode = code.Data.CodeLink
		}
	} else {
		err = errors.New(data.ErrMsg)
	}
	return
}

func DidiNotify(info model.DidiOrder) (err error) {

	log.Log().Info("didi回调数据", zap.Any("data", info))
	var cpsOrder model.JhCpsOrder
	err = source.DB().Where("order_id = ?", info.OrderId).Where("type = ?", model.Didi).First(&cpsOrder).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	cpsOrder.CpsOrderId = info.OrderId
	cpsOrder.Type = model.Didi
	cpsOrder.Price = uint(info.PayPrice)
	cpsOrder.RefundPrice = uint(info.RefundPrice)
	cpsOrder.PayAt = &source.LocalTime{Time: time.Unix(int64(info.PayTime), 0)}
	cpsOrder.RefundAt = &source.LocalTime{Time: time.Unix(int64(info.RefundTime), 0)}

	var customize []string
	// 先用 _ 分割customize，再用 a 分割customize
	customize = strings.Split(info.SourceId, "_")
	// 如果第一个切片 等于 S，则说明是小商店；否则是第三方
	if customize[0] == "S" {
		var userID, shopID int
		userID, err = strconv.Atoi(customize[2])
		if err != nil {
			return
		}
		shopID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(userID)
		cpsOrder.ShopID = uint(shopID)
		cpsOrder.ThirdUserID = uint(userID)
		cpsOrder.PluginID = pluginModel.SmallShopPluginID
	} else {
		customize = strings.Split(info.SourceId, "a")
		if len(customize) != 2 {
			err = errors.New("customize不正确")
			return
		}
		var applicationID, thirdUserID int
		applicationID, err = strconv.Atoi(customize[0])
		thirdUserID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		var application model.Application
		err = source.DB().First(&application, applicationID).Error
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(application.MemberId)
		cpsOrder.ApplicationID = uint(applicationID)
		cpsOrder.ThirdUserID = uint(thirdUserID)
	}
	cpsOrder.CommissionPrice = uint(info.CpsProfit)
	cpsOrder.ActivityID = strconv.Itoa(info.ActivityId)
	var dataString []byte
	dataString, err = json.Marshal(info)
	if err != nil {
		return
	}
	cpsOrder.DataString = string(dataString)
	cpsOrder.Title = info.Title
	if info.OrderStatus == 2 {
		cpsOrder.Status = model.Payed
	} else if info.OrderStatus == 8 {
		cpsOrder.Status = model.Refund
	}
	if info.Status == 7 || info.OrderStatus == 9 {
		cpsOrder.Status = model.Completed
	}
	err = source.DB().Save(&cpsOrder).Error
	return
}

func MockOrderCallback(info request.MockOrderCallBackRequest) (err error, data string) {
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	c := client.NewUnionClient(setting.DidiAppKey, setting.DidiAccessKey)
	//日志可选，将在指定目录生成日志

	//或者使用日志注入的方式，需实现两个接口函数：
	//Infof(template string, args ...interface{})
	//Errorf(template string, args ...interface{})
	//然后调用
	//util.SetLogger(yourLogger)

	//设置全局超时时间
	//util.SetTimeoutDuration(2*time.Second)

	//或者设置单个接口的超时时间
	//link, err := c.GenerateH5Link(context.Background(), 6133, 6834408369283047676, "d", model.Option{Timeout: time.Millisecond})
	result, err := c.MockOrderCallback(context.Background(), info.Dsi, info.SourceID, consts.MockPay)
	if err != nil {
		fmt.Println(err)
		return
	} else {
		err = errors.New(result.ErrMsg)
	}
	if result.Errno == 0 {
		var jsonData []byte
		jsonData, err = json.Marshal(result.Data)
		if err != nil {
			return
		}
		data = string(jsonData)
	}
	return
}
