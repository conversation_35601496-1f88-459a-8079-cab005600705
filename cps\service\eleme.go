package service

import (
	"cps/model"
	"cps/request"
	"cps/response"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	pluginModel "plugin/model"
	"strconv"
	"strings"
	"time"
	"topsdk"
	"topsdk/defaultability"
	"topsdk/defaultability/domain"
	toprequest "topsdk/defaultability/request"
	"yz-go/component/log"
	"yz-go/source"
)

type ElemeApi struct{}

func (*ElemeApi) GenerateLink(info request.GenerateLinkRequest) (err error, generateLinkResponse response.GenerateLinkResponse) {
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	client := topsdk.NewDefaultTopClient(setting.ElemeAppKey, setting.ElemeAppSecret, "https://eco.taobao.com/router/rest", 20000, 20000)
	ability := defaultability.NewDefaultability(&client)

	alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest := domain.AlibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest{}
	alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest.SetPid(setting.ElemePid)
	alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest.SetActivityId(info.ActivityID)
	alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest.SetSid(info.Customize)
	alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest.SetIncludeWxImg(true)
	alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest.SetIncludeQrCode(true)

	req := toprequest.AlibabaAlscUnionElemePromotionOfficialactivityGetRequest{}
	req.SetQueryRequest(alibabaAlscUnionElemePromotionOfficialactivityGetActivityRequest)

	resp, err := ability.AlibabaAlscUnionElemePromotionOfficialactivityGet(&req)
	if err != nil {
		return
	} else {
		if resp.ResultCode == 0 {
			switch info.JumpType {
			case response.ELEME_H5:

				if resp.Data.Link.H5Url != nil {
					generateLinkResponse.Link = *resp.Data.Link.H5Url
				}
				if resp.Data.Link.H5MiniQrcode != nil {
					generateLinkResponse.Barcode = *resp.Data.Link.H5MiniQrcode
				}
				break
			case response.ELEME_WX_MINI:
				if resp.Data.Link.WxAppid != nil {
					generateLinkResponse.Appid = *resp.Data.Link.WxAppid
				}
				if resp.Data.Link.WxPath != nil {
					generateLinkResponse.Link = *resp.Data.Link.WxPath
				}
				if resp.Data.Link.MiniQrcode != nil {
					generateLinkResponse.Barcode = *resp.Data.Link.MiniQrcode
				}
				break
			case response.ELEME_DEEPLINK:

				if resp.Data.Link.EleSchemeUrl != nil {
					generateLinkResponse.AppSource = *resp.Data.Link.EleSchemeUrl
					generateLinkResponse.Link = *resp.Data.Link.EleSchemeUrl
				}

				break
			}

		} else {
			err = errors.New(resp.Message)
		}

	}
	return
}

func ElemeNotify(info domain.AlibabaAlscUnionKbcpxPositiveOrderGetOrderDetailReportDTO) (err error) {

	log.Log().Info("eleme回调数据", zap.Any("data", info))
	if *info.PlatformType == 1 {
		return
	}
	var cpsOrder model.JhCpsOrder
	err = source.DB().Where("order_id = ?", info.BizOrderId).Where("type = ?", model.Eleme).First(&cpsOrder).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	cpsOrder.CpsOrderId = strconv.Itoa(int(*info.BizOrderId))
	cpsOrder.Type = model.Eleme
	var payPrice float64
	payPrice, err = strconv.ParseFloat(*info.PayAmount, 64)
	if err != nil {
		log.Log().Info("meituan价格转换错误", zap.Any("data", info))
		return
	}
	cpsOrder.Price = uint(payPrice * 100)
	//cpsOrder.RefundPrice = uint(info.RefundPrice)
	payTime, _ := time.ParseInLocation("2006-01-02 15:04:05", *info.PayTime, time.Local)
	cpsOrder.PayAt = &source.LocalTime{Time: payTime}

	if info.OrderState != nil {
		if *info.OrderState == 2 {
			cpsOrder.Status = model.Payed
		}
	}

	if info.OrderItemStatus != nil {
		if *info.OrderItemStatus == 3 || *info.OrderItemStatus == 5 {
			cpsOrder.Status = model.Refund
		}
	}
	if *info.SettleState == 1 {
		cpsOrder.Status = model.Completed
		var commissionPrice float64
		commissionPrice, err = strconv.ParseFloat(*info.Settle, 64)
		if err != nil {
			log.Log().Info("meituan价格转换错误", zap.Any("data", info))
			return
		}
		cpsOrder.CommissionPrice = uint(commissionPrice * 100)

	} else {
		var commissionPrice float64
		commissionPrice, err = strconv.ParseFloat(*info.Income, 64)
		if err != nil {
			log.Log().Info("meituan价格转换错误", zap.Any("data", info))
			return
		}
		cpsOrder.CommissionPrice = uint(commissionPrice * 100)

	}
	if info.Sid == nil {
		return
	}
	var customize []string
	// 先用 _ 分割customize，再用 a 分割customize
	customize = strings.Split(*info.Sid, "_")
	// 如果第一个切片 等于 S，则说明是小商店；否则是第三方
	if customize[0] == "S" {
		var userID, shopID int
		userID, err = strconv.Atoi(customize[2])
		if err != nil {
			return
		}
		shopID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(userID)
		cpsOrder.ShopID = uint(shopID)
		cpsOrder.ThirdUserID = uint(userID)
		cpsOrder.PluginID = pluginModel.SmallShopPluginID
	} else {
		customize = strings.Split(*info.Sid, "a")
		if len(customize) != 2 {
			err = errors.New("customize不正确")
			return
		}
		var applicationID, thirdUserID int
		applicationID, err = strconv.Atoi(customize[0])
		thirdUserID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		var application model.Application
		err = source.DB().First(&application, applicationID).Error
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(application.MemberId)
		cpsOrder.ApplicationID = uint(applicationID)
		cpsOrder.ThirdUserID = uint(thirdUserID)
	}
	if info.ActivityId != nil {
		cpsOrder.ActivityID = strconv.Itoa(int(*info.ActivityId))
	}
	var dataString []byte
	dataString, err = json.Marshal(info)
	if err != nil {
		return
	}
	cpsOrder.DataString = string(dataString)
	cpsOrder.Title = *info.Title

	err = source.DB().Save(&cpsOrder).Error
	return
}
