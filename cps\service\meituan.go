package service

import (
	"cps/meituan"
	"cps/model"
	"cps/request"
	"cps/response"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	pluginModel "plugin/model"
	"sort"
	"strconv"
	"strings"
	"time"
	userModel "user/model"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type MeituanApi struct{}
type MeituanGenerateLinkResponse struct {
	Status int    `json:"status"`
	Des    string `json:"des"`
	Data   string `json:"data"`
}

func (*MeituanApi) GenerateLinkV1(info request.GenerateLinkRequest) (err error, generateLinkResponse response.GenerateLinkResponse) {
	var requestParams = make(map[string]interface{})
	requestParams["actId"] = info.ActivityID
	requestParams["sid"] = info.Customize
	requestParams["linkType"] = info.JumpType
	err, requestParams = GetMeituanRequestParams(requestParams)
	if err != nil {
		return
	}
	var queryString []string
	for k, v := range requestParams {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")
	var result []byte
	err, result = utils.Get("https://openapi.meituan.com/api/generateLink?"+query, nil)
	if err != nil {
		return
	}
	var resData MeituanGenerateLinkResponse
	err = json.Unmarshal(result, &resData)
	if err != nil {
		return
	}
	if resData.Status != 0 {
		err = errors.New(resData.Des)
		return
	}
	generateLinkResponse.Link = resData.Data

	//获取小程序二维码
	var requestCodeParams = make(map[string]interface{})
	requestCodeParams["actId"] = info.ActivityID
	requestCodeParams["sid"] = info.Customize
	requestCodeParams["linkType"] = info.JumpType
	err, requestCodeParams = GetMeituanRequestParams(requestCodeParams)
	if err != nil {
		return
	}
	var codeQueryString []string
	for k, v := range requestParams {
		codeQueryString = append(codeQueryString, k+"="+source.Strval(v))
	}
	codeQuery := strings.Join(codeQueryString, "&")
	err, result = utils.Get("https://openapi.meituan.com/api/miniCode?"+codeQuery, nil)
	if err != nil {
		return
	}
	var resCodeData MeituanGenerateLinkResponse
	err = json.Unmarshal(result, &resCodeData)
	if err != nil {
		return
	}
	if resCodeData.Status == 0 {
		generateLinkResponse.Barcode = resCodeData.Data
	}
	return
}

func (*MeituanApi) GenerateLink(info request.GenerateLinkRequest) (err error, generateLinkResponse response.GenerateLinkResponse) {
	var requestParams = make(map[string]interface{})
	if info.ActivityID != "" {
		requestParams["actId"] = info.ActivityID
	} else if info.SkuViewId != "" {
		requestParams["skuViewId"] = info.SkuViewId
	}
	requestParams["sid"] = info.Customize
	requestParams["linkType"] = info.JumpType
	if info.PlatForm > 0 {
		requestParams["platform"] = info.PlatForm
	}
	if info.BizLine > 0 {
		requestParams["bizLine"] = info.BizLine
	}
	config := map[string]interface{}{
		"method": "post",
		"url":    "https://media.meituan.com/cps_open/common/api/v1/get_referral_link",
		"data":   requestParams,
	}
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	signUtil := meituan.NewSignUtil(setting.MeituanAppKey, setting.MeituanSecret)
	signHeaders := signUtil.GetSignHeaders(config)
	fmt.Println("签名头部字段:", signHeaders)
	//将signHeaders由struct转换成map结构
	var signHeadersMap = make(map[string]string)
	signHeadersJson, _ := json.Marshal(signHeaders)
	json.Unmarshal(signHeadersJson, &signHeadersMap)
	signHeadersMap["Content-Type"] = "application/json; charset=utf-8"
	var result []byte
	err, result = utils.Post("https://media.meituan.com/cps_open/common/api/v1/get_referral_link", requestParams, signHeadersMap)
	if err != nil {
		return
	}
	var resData meituan.MeituanGenerateLinkResponseV2
	err = json.Unmarshal(result, &resData)
	if err != nil {
		return
	}
	if resData.Code != 0 {
		err = errors.New(resData.Message)
		return
	}
	generateLinkResponse.Link = resData.Data

	return
}

func GetMeituanRequestParams(inputParam map[string]interface{}) (err error, params map[string]interface{}) {

	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	inputParam["appkey"] = setting.MeituanAppKey
	sign := setting.MeituanSecret
	TraverseMapInStringOrder(inputParam, func(key string, value interface{}) {
		sign += key + source.Strval(value)
	})
	sign += setting.MeituanSecret
	inputParam["sign"] = utils.MD5V([]byte(sign))
	return err, inputParam
}

type MapEntryHandler func(string, interface{})

func TraverseMapInStringOrder(params map[string]interface{}, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}

func MeituanNotify(info model.MeituanNotifyOrder) (err error) {
	log.Log().Info("meituan回调数据", zap.Any("data", info))
	var cpsOrder model.JhCpsOrder
	var notifyType = "update"
	err = source.DB().Where("order_id = ?", info.Orderid).Where("type = ?", model.Meituan).First(&cpsOrder).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			notifyType = "create"
		} else {
			return
		}
	}
	var customize []string
	// 先用 _ 分割customize，再用 a 分割customize
	customize = strings.Split(info.Sid, "_")
	// 如果第一个切片 等于 S，则说明是小商店；否则是第三方
	if customize[0] == "S" {
		var userID, shopID int
		userID, err = strconv.Atoi(customize[2])
		if err != nil {
			return
		}
		shopID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(userID)
		cpsOrder.ShopID = uint(shopID)
		cpsOrder.ThirdUserID = uint(userID)
		cpsOrder.PluginID = pluginModel.SmallShopPluginID
	} else {
		customize = strings.Split(info.Sid, "a")
		if len(customize) != 2 {
			err = errors.New("customize不正确")
			return
		}
		var applicationID, thirdUserID int
		applicationID, err = strconv.Atoi(customize[0])
		thirdUserID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		cpsOrder.ApplicationID = uint(applicationID)
		var application model.Application
		err = source.DB().First(&application, applicationID).Error
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(application.MemberId)
		cpsOrder.ThirdUserID = uint(thirdUserID)
	}
	cpsOrder.ActivityID = source.Strval(info.ActId)
	cpsOrder.CpsOrderId = info.Orderid
	cpsOrder.Type = model.Meituan
	var payPrice float64
	payPrice, err = strconv.ParseFloat(info.PayPrice, 64)
	if err != nil {
		log.Log().Info("meituan价格转换错误", zap.Any("data", info))
		return
	}
	cpsOrder.Price = uint(payPrice * 100)
	ratio, _ := strconv.ParseFloat(info.Ratio, 64)
	if ratio == 0 {
		var profit float64
		if info.Profit != "" {
			profit, _ = strconv.ParseFloat(info.Profit, 64)
			cpsOrder.CommissionPrice = uint(math.Round(profit * 100))
			cpsOrder.Ratio = math.Round(float64(cpsOrder.CommissionPrice) / float64(cpsOrder.Price) * 100)

		} else {
			cpsOrder.Ratio = 0
			cpsOrder.CommissionPrice = 0
		}

	} else {
		cpsOrder.Ratio = ratio / 100
		var profit float64
		if info.Profit != "" {
			profit, _ = strconv.ParseFloat(info.Profit, 64)
			cpsOrder.CommissionPrice = uint(math.Round(profit * 100))
		} else {
			cpsOrder.CommissionPrice = uint(math.Round(payPrice * (cpsOrder.Ratio / 100)))
		}

	}

	var dataString []byte
	dataString, err = json.Marshal(info)
	if err != nil {
		return
	}
	cpsOrder.DataString = string(dataString)
	cpsOrder.Title = info.Smstitle
	var paytime int
	paytime, err = strconv.Atoi(info.Paytime)
	cpsOrder.PayAt = &source.LocalTime{Time: time.Unix(int64(paytime), 0)}
	var refundtime int
	refundtime, err = strconv.Atoi(info.Refundtime)
	if refundtime > 0 {
		cpsOrder.RefundAt = &source.LocalTime{Time: time.Unix(int64(refundtime), 0)}
	}
	if info.Status == float64(1) {
		cpsOrder.Status = model.Payed

	} else if info.Status == float64(9) {
		cpsOrder.Status = model.Refund

	} else if info.Status == float64(8) {
		cpsOrder.Status = model.Completed
		cpsOrder.CompleteAt = &source.LocalTime{Time: time.Now()}
	}
	err = source.DB().Save(&cpsOrder).Error

	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	if setting.MeituanSyncOrder == 1 {
		err = NotifyJhCpsOrders(cpsOrder, notifyType)
	}
	return
}

func MeituanNotifyV2(info model.MeituanOrderV2) (err error) {

	log.Log().Info("meituan回调数据", zap.Any("data", info))
	var cpsOrder model.JhCpsOrder
	var notifyType = "update"
	err = source.DB().Where("order_id = ?", info.OrderId).Where("type = ?", model.Meituan).First(&cpsOrder).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			notifyType = "create"
		} else {
			return
		}
	}
	var applicationID, thirdUserID, isDistributor int
	var customize []string
	// 先用 _ 分割customize，再用 a 分割customize
	customize = strings.Split(info.Sid, "_")
	// 如果第一个切片 等于 S，则说明是小商店；否则是第三方
	if customize[0] == "S" {
		var userID, shopID int
		userID, err = strconv.Atoi(customize[2])
		if err != nil {
			return
		}
		shopID, err = strconv.Atoi(customize[1])
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(userID)
		cpsOrder.ShopID = uint(shopID)
		cpsOrder.ThirdUserID = uint(userID)
		cpsOrder.PluginID = pluginModel.SmallShopPluginID
	} else {
		customize = strings.Split(info.Sid, "a")
		if len(customize) < 2 {
			err = errors.New("customize不正确")
			return
		} else {
			applicationID, err = strconv.Atoi(customize[0])
			thirdUserID, err = strconv.Atoi(customize[1])
			if len(customize) == 3 {
				isDistributor, err = strconv.Atoi(customize[2])
			}
			if err != nil {
				return
			}
		}
		cpsOrder.IsDistributor = isDistributor
		cpsOrder.ApplicationID = uint(applicationID)
		var application model.Application
		err = source.DB().First(&application, applicationID).Error
		if err != nil {
			return
		}
		cpsOrder.UserID = uint(application.MemberId)
		cpsOrder.ThirdUserID = uint(thirdUserID)
	}
	cpsOrder.ActivityID = source.Strval(info.ActId)
	cpsOrder.CpsOrderId = info.OrderId
	cpsOrder.Type = model.Meituan
	var payPrice float64
	payPrice, err = strconv.ParseFloat(info.PayPrice, 64)
	if err != nil {
		log.Log().Info("meituan价格转换错误", zap.Any("data", info))
		return
	}
	cpsOrder.Price = uint(math.Round(payPrice * 100))
	ratio, _ := strconv.ParseFloat(info.CommissionRate, 64)
	if ratio == 0 {
		var profit float64
		if info.Profit != "" {
			profit, _ = strconv.ParseFloat(info.Profit, 64)
			cpsOrder.CommissionPrice = uint(math.Round(profit * 100))
			cpsOrder.Ratio = math.Round(float64(cpsOrder.CommissionPrice) / float64(cpsOrder.Price) * 100)

		} else {
			cpsOrder.Ratio = 0
			cpsOrder.CommissionPrice = 0
		}

	} else {
		cpsOrder.Ratio = ratio / 100
		var profit float64
		if info.Profit != "" {
			profit, _ = strconv.ParseFloat(info.Profit, 64)
			cpsOrder.CommissionPrice = uint(math.Round(profit * 100))
		} else {
			cpsOrder.CommissionPrice = uint(math.Round(payPrice * (cpsOrder.Ratio / 100)))
		}

	}

	var dataString []byte
	dataString, err = json.Marshal(info)
	if err != nil {
		return
	}
	cpsOrder.DataString = string(dataString)
	cpsOrder.Title = info.ProductName
	var paytime int
	paytime = info.PayTime
	cpsOrder.PayAt = &source.LocalTime{Time: time.Unix(int64(paytime), 0)}
	var refundtime int
	refundtime, err = strconv.Atoi(info.RefundTime)
	if refundtime > 0 {
		cpsOrder.RefundAt = &source.LocalTime{Time: time.Unix(int64(refundtime), 0)}
	}
	if info.Status == "2" {
		cpsOrder.Status = model.Payed

	} else if info.Status == "4" {
		cpsOrder.Status = model.Refund

	} else if info.Status == "6" {
		cpsOrder.Status = model.Completed
		cpsOrder.CompleteAt = &source.LocalTime{Time: time.Now()}
	}
	err = source.DB().Save(&cpsOrder).Error
	if err != nil {
		return
	}
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	if setting.MeituanSyncOrder == 1 && cpsOrder.PluginID == 0 {
		if cpsOrder.IsDistributor == 2 {
			err = NotifyJhCpsMeituanDisOrders(cpsOrder, notifyType)

		} else {
			err = NotifyJhCpsOrders(cpsOrder, notifyType)
		}
	}
	return
}

type CpsOrderMessage struct {
	OperationType string   `json:"type"`
	Ids           []string `json:"ids"`
}

func NotifyJhCpsOrders(order model.JhCpsOrder, operationType string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}

	var message CpsOrderMessage
	message.OperationType = operationType
	message.Ids = []string{order.CpsOrderId}
	var application model.Application
	err = source.DB().First(&application, order.ApplicationID).Error
	if err != nil {
		return
	}
	if application.CallBackLinkJhCps != "" {
		log.Log().Info("开始通知下游", zap.Any("application", message))
		err, _ = utils.Post(application.CallBackLinkJhCps, message, header)
		if err != nil {
			log.Log().Info("通知下游失败", zap.Any("application", application))
			return
		}
	}

	return
}

func NotifyJhCpsMeituanDisOrders(order model.JhCpsOrder, operationType string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}

	var message CpsOrderMessage
	message.OperationType = "meituanDistributor." + operationType
	message.Ids = []string{order.CpsOrderId}
	var application model.Application
	err = source.DB().First(&application, order.ApplicationID).Error
	if err != nil {
		return
	}
	if application.CallBackLink != "" {
		log.Log().Info("开始通知下游", zap.Any("application", message))
		err, _ = utils.Post(application.CallBackLink, message, header)
		if err != nil {
			log.Log().Info("通知下游失败", zap.Any("application", application))
			return
		}
	}

	return
}

func GetMeituanCoupon(info map[string]interface{}, userID uint) (err error, data interface{}) {
	var user userModel.ChildUser
	if userID != 0 {
		err = source.DB().Preload("Level").First(&user, userID).Error
		if err != nil {
			return
		}
	}
	config := map[string]interface{}{
		"method": "post",
		"url":    "https://media.meituan.com/cps_open/common/api/v1/query_coupon",
		"data":   info,
	}
	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	signUtil := meituan.NewSignUtil(setting.MeituanAppKey, setting.MeituanSecret)
	signHeaders := signUtil.GetSignHeaders(config)
	fmt.Println("签名头部字段:", signHeaders)
	//将signHeaders由struct转换成map结构
	var signHeadersMap = make(map[string]string)
	signHeadersJson, _ := json.Marshal(signHeaders)
	json.Unmarshal(signHeadersJson, &signHeadersMap)
	signHeadersMap["Content-Type"] = "application/json; charset=utf-8"
	var result []byte
	err, result = utils.Post("https://media.meituan.com/cps_open/common/api/v1/query_coupon", info, signHeadersMap)
	if err != nil {
		return
	}
	var resData MeituanCouponListResponse
	err = json.Unmarshal(result, &resData)
	if err != nil {
		return
	}
	if resData.Code != 0 {
		return nil, resData
	}
	var ratio uint
	ratio = 100
	if user.ID != 0 {
		ratio = user.Level.MeituanDistributorRatio
	}
	if len(resData.Data) > 0 {
		for key, item := range resData.Data {
			var commissionPrice float64
			commissionPrice, err = strconv.ParseFloat(item.CommissionInfo.Commission, 64)
			if err != nil {
				continue
			}

			resData.Data[key].CommissionInfo.CommissionPrice = int(commissionPrice * float64(ratio) / 100)
			resData.Data[key].CommissionInfo.Commission = ""
			resData.Data[key].CommissionInfo.CommissionPercent = ""
		}
	}

	return nil, resData
}

type MeituanCouponListResponse struct {
	Code    int         `json:"code"`
	Message interface{} `json:"message"`
	Data    []struct {
		AvailablePoiInfo struct {
			AvailablePoiNum int `json:"availablePoiNum"`
		} `json:"availablePoiInfo"`
		BrandInfo struct {
			BrandName    string `json:"brandName"`
			BrandLogoUrl string `json:"brandLogoUrl"`
		} `json:"brandInfo"`
		CommissionInfo struct {
			CommissionPercent string `json:"commissionPercent"`
			Commission        string `json:"commission"`
			CommissionPrice   int    `json:"commission_price"`
		} `json:"commissionInfo"`
		CouponPackDetail struct {
			Name          string `json:"name"`
			SkuViewId     string `json:"skuViewId"`
			CouponNum     int    `json:"couponNum"`
			ValidTime     int    `json:"validTime"`
			HeadUrl       string `json:"headUrl"`
			SaleVolume    string `json:"saleVolume"`
			StartTime     int    `json:"startTime"`
			EndTime       int    `json:"endTime"`
			SaleStatus    bool   `json:"saleStatus"`
			OriginalPrice string `json:"originalPrice"`
			SellPrice     string `json:"sellPrice"`
			Platform      int    `json:"platform"`
			BizLine       int    `json:"bizLine"`
		} `json:"couponPackDetail"`
		DeliverablePoiInfo struct {
			PoiName          interface{} `json:"poiName"`
			PoiLogoUrl       interface{} `json:"poiLogoUrl"`
			DeliveryDistance interface{} `json:"deliveryDistance"`
			DistributionCost interface{} `json:"distributionCost"`
			DeliveryDuration interface{} `json:"deliveryDuration"`
			LastDeliveryFee  interface{} `json:"lastDeliveryFee"`
		} `json:"deliverablePoiInfo"`
		PurchaseLimitInfo struct {
			SingleDayPurchaseLimit int `json:"singleDayPurchaseLimit"`
		} `json:"purchaseLimitInfo"`
		CouponValidTimeInfo struct {
			CouponValidTimeType int `json:"couponValidTimeType"`
			CouponValidDay      int `json:"couponValidDay"`
			CouponValidSTime    int `json:"couponValidSTime"`
			CouponValidETime    int `json:"couponValidETime"`
		} `json:"couponValidTimeInfo,omitempty"`
	} `json:"data"`
	HasNext  bool        `json:"hasNext"`
	SearchId interface{} `json:"searchId"`
}
