package service

import (
	"cps/model"
	"cps/request"
	"crypto/aes"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"log"
	"strconv"
	"strings"
	"testing"
	"time"
	"yz-go/source"
	"yz-go/utils"
)

var CpsSetting model.CpsValue

func GetMeituanProvince(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var params map[string]interface{}
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	url := "https://union.dianping.com/api/province/all?"
	var queryString []string
	for k, v := range params {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanCity(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var params map[string]interface{}
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	url := "https://union.dianping.com/api/province/" + strconv.Itoa(info.ProvinceId) + "/cities?"
	var queryString []string
	for k, v := range params {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanCityCategories(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var params map[string]interface{}
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	if info.Cat0Id > 0 {
		params["cat0Id"] = info.Cat0Id
	}
	url := "https://union.dianping.com/api/city/" + strconv.Itoa(info.CityId) + "/categories?"
	var queryString []string
	for k, v := range params {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanRegions(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var params map[string]interface{}
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	params["includeHot"] = true
	url := "https://union.dianping.com/api/mt/city/" + strconv.Itoa(info.CityId) + "/regions?"
	var queryString []string
	for k, v := range params {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanSearchDeals(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var queryParams map[string]interface{}
	err, queryParams = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	var queryString []string
	for k, v := range queryParams {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")
	var params = make(map[string]interface{})
	params["platformId"] = 2
	params["utmMedium"] = hex.EncodeToString(AesEncryptECB([]byte("1000"), []byte(CpsSetting.MeituanDistributorAppKey)))
	params["utmSource"] = CpsSetting.MeituanDistributorUtmSource
	params["cat0Id"] = info.Cat0Id
	params["page"] = info.Page
	params["size"] = info.Size
	params["geo"] = info.Geo

	if info.Cat1Id > 0 {
		params["cat1Id"] = info.Cat1Id
	}
	if len(info.Cat1Ids) > 0 {
		params["cat1Ids"] = info.Cat1Ids

	}
	if len(info.ShopIds) > 0 {
		params["shopIds"] = info.ShopIds

	}
	if len(info.DealIds) > 0 {
		params["dealIds"] = info.DealIds

	}
	if info.DealType > 0 {
		params["dealType"] = info.DealType

	}
	if info.SortType > 0 {
		params["sortType"] = info.SortType

	}
	if info.Filters != nil {
		params["filters"] = info.Filters
	}
	if info.KeyWords != "" {
		params["keyWords"] = info.KeyWords
	}

	url := "https://union.dianping.com/api/search/deals?"

	var result []byte
	err, result = utils.Post(url+query, params, map[string]string{"Content-Type": "application/json"})
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanSearchDealsDetail(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var queryParams map[string]interface{}
	err, queryParams = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	var queryString []string
	for k, v := range queryParams {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")
	var params = make(map[string]interface{})
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	params["utmSource"] = CpsSetting.MeituanDistributorUtmSource
	params["cat0Id"] = info.Cat0Id
	params["shopId"] = info.ShopId

	url := "https://union.dianping.com/api/shop/detail?"

	var result []byte
	err, result = utils.Post(url+query, params, map[string]string{"Content-Type": "application/json"})
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanSeckill(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var params map[string]interface{}
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	params["cityId"] = info.CityId
	url := "https://union.dianping.com/api/seckill/showInfo?"
	var queryString []string
	for k, v := range params {
		queryString = append(queryString, k+"="+source.Strval(v))
	}

	query := strings.Join(queryString, "&")

	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func GetMeituanSeckillList(info request.MeituanDistirbutorRequest) (err error, data interface{}) {
	var params map[string]interface{}
	err, params = getRequestParams(info.RequestId)
	if err != nil {
		return
	}
	params["platformId"] = 2
	params["cityId"] = info.CityId
	params["showId"] = info.ShowId
	params["phone"] = hex.EncodeToString(AesEncryptECB([]byte(info.Phone), []byte(CpsSetting.MeituanDistributorAppKey)))
	params["os"] = info.Os
	params["lat"] = info.Lat
	params["lng"] = info.Lng
	params["page"] = info.Page
	params["promotionId"] = info.PromotionId
	params["utmMedium"] = hex.EncodeToString(AesEncryptECB([]byte("1000"), []byte(CpsSetting.MeituanDistributorAppKey)))

	url := "https://union.dianping.com/api/seckill/list?"
	var queryString []string
	for k, v := range params {
		queryString = append(queryString, k+"="+source.Strval(v))
	}

	query := strings.Join(queryString, "&")

	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {
		return
	}
	var response map[string]interface{}
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	return nil, response
}

func getRequestParams(requestId string) (err error, params map[string]interface{}) {
	var param = make(map[string]interface{})
	param["requestId"] = requestId
	err, CpsSetting = model.GetCpsSetting()
	if err != nil {
		return
	}
	param["utmSource"] = CpsSetting.MeituanDistributorUtmSource
	param["version"] = "1.0"
	var timeNow = time.Now().Unix()
	param["timestamp"] = timeNow
	param["accessToken"] = hex.EncodeToString(AesEncryptECB([]byte(source.Strval(param["utmSource"])+strconv.Itoa(int(timeNow))), []byte(CpsSetting.MeituanDistributorAppKey)))

	return nil, param
}

func Test_B_2(t *testing.T) {
	origData := []byte("460154561234") // 待加密的数据
	key := []byte("9876787656785679")  // 加密的密钥
	log.Println("原文：", string(origData))

	log.Println("------------------ ECB模式 --------------------")
	encrypted := AesEncryptECB(origData, key)
	log.Println("密文(hex)：", hex.EncodeToString(encrypted))
	log.Println("密文(base64)：", base64.StdEncoding.EncodeToString(encrypted))
	decrypted := AesDecryptECB(encrypted, key)
	log.Println("解密结果：", string(decrypted))
}
func AesEncryptECB(origData []byte, key []byte) (encrypted []byte) {
	cipher, _ := aes.NewCipher(generateKey(key))
	length := (len(origData) + aes.BlockSize) / aes.BlockSize
	plain := make([]byte, length*aes.BlockSize)
	copy(plain, origData)
	pad := byte(len(plain) - len(origData))
	for i := len(origData); i < len(plain); i++ {
		plain[i] = pad
	}
	encrypted = make([]byte, len(plain))
	// 分组分块加密
	for bs, be := 0, cipher.BlockSize(); bs <= len(origData); bs, be = bs+cipher.BlockSize(), be+cipher.BlockSize() {
		cipher.Encrypt(encrypted[bs:be], plain[bs:be])
	}

	return encrypted
}
func AesDecryptECB(encrypted []byte, key []byte) (decrypted []byte) {
	cipher, _ := aes.NewCipher(generateKey(key))
	decrypted = make([]byte, len(encrypted))
	//
	for bs, be := 0, cipher.BlockSize(); bs < len(encrypted); bs, be = bs+cipher.BlockSize(), be+cipher.BlockSize() {
		cipher.Decrypt(decrypted[bs:be], encrypted[bs:be])
	}

	trim := 0
	if len(decrypted) > 0 {
		trim = len(decrypted) - int(decrypted[len(decrypted)-1])
	}

	return decrypted[:trim]
}
func generateKey(key []byte) (genKey []byte) {
	genKey = make([]byte, 16)
	copy(genKey, key)
	for i := 16; i < len(key); {
		for j := 0; j < 16 && i < len(key); j, i = j+1, i+1 {
			genKey[j] ^= key[i]
		}
	}
	return genKey
}

type MeituanDistributorApi struct{}
type MeituanDistributorLinkResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Activity                 string `json:"activity"`
		CommonLink               string `json:"commonLink"`
		ShortLink                string `json:"shortLink"`
		CommonQrCode             string `json:"commonQrCode"`
		MiniProgramPath          string `json:"miniProgramPath"`
		MiniProgramPathForCommon string `json:"miniProgramPathForCommon"`
		MiniProgramQrCode        string `json:"miniProgramQrCode"`
		MaterialDownloadLink     string `json:"materialDownloadLink"`
		DeepLink                 string `json:"deepLink"`
		BeginTime                int    `json:"beginTime"`
		EndTime                  int    `json:"endTime"`
		RuleInfo                 string `json:"ruleInfo"`
	} `json:"data"`
	ServerTrackId string `json:"serverTrackId"`
}

func (*MeituanDistributorApi) GenerateLink(info request.GenerateLinkRequest) (err error, link string, dsi string) {
	var queryParams map[string]interface{}
	err, queryParams = getRequestParams(info.Customize)
	if err != nil {
		return
	}
	var queryString []string
	for k, v := range queryParams {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")
	var params = make(map[string]interface{})
	params["pageLevel"] = 1
	params["utmSource"] = CpsSetting.MeituanDistributorUtmSource
	params["utmMedium"] = hex.EncodeToString(AesEncryptECB([]byte("1000"), []byte(CpsSetting.MeituanDistributorAppKey)))
	params["activity"] = info.ActivityID
	params["promotionId"] = CpsSetting.MeituanDistributorPromotionId

	url := "https://union.dianping.com/api/promotion/link?"

	var result []byte
	err, result = utils.Post(url+query, params, map[string]string{"Content-Type": "application/json"})
	if err != nil {
		return
	}
	var response MeituanDistributorLinkResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code == 200 {
		link = response.Data.ShortLink
	} else {
		err = errors.New(response.Msg)
		return
	}
	return nil, link, dsi
}
