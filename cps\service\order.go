package service

import (
	"cps/model"
	"cps/request"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"os"
	"strconv"
	"time"
	level2 "user/level"
	model2 "user/model"
	YzGoConfig "yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func GetOrderList(info request.JhCpsOrderSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Preload("User.Level").Model(&model.JhCpsOrder{}).Where("plugin_id = ?", 0)
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	if info.Alliance != "" {
		db = db.Where("type = ?", info.Alliance)
	}
	if info.OrderSN > 0 {
		db = db.Where("order_sn = ?", info.OrderSN)
	}
	if info.CpsOrderId != "" {
		db = db.Where("order_id = ?", info.CpsOrderId)

	}
	if len(info.IDs) > 0 {
		db = db.Where("order_id in ?", info.IDs)

	}
	if info.Username != "" {
		var userIds []uint
		err = source.DB().Model(&model2.User{}).Where("username like ?", "%"+info.Username+"%").Or("nick_name like ?", "%"+info.Username+"%").Pluck("id", &userIds).Error
		if err != nil {
			return
		}
		db = db.Where("user_id in ?", userIds)
	}
	if info.ApplicationID > 0 {
		db = db.Where("application_id = ?", info.ApplicationID)

	}
	if info.IsDistributor > 0 {
		db = db.Where("is_distributor = ?", info.IsDistributor)

	}
	if info.Title != "" {
		db = db.Where("title like ?", "%"+info.Title+"%")

	}
	if info.Status != "" {
		db = db.Where("status = ?", info.Status)

	}
	var timeType string
	if info.TimeType == "" {
		timeType = "pay_at"
	} else {
		timeType = info.TimeType
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	var activities []model.JhCpsOrder
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Omit("data_string").Order("created_at desc").Limit(limit).Offset(offset).Find(&activities).Error

	return err, activities, total
}

func ExportOrderList(info request.JhCpsOrderSearch) (err error, link string) {

	// 创建db
	db := source.DB().Preload("User.Level").Model(&model.JhCpsOrder{})
	if info.Type != "" {
		db = db.Where("type = ?", info.Type)
	}
	if info.OrderSN > 0 {
		db = db.Where("order_sn = ?", info.OrderSN)
	}
	if info.CpsOrderId != "" {
		db = db.Where("order_id = ?", info.CpsOrderId)

	}
	if len(info.IDs) > 0 {
		db = db.Where("order_id in ?", info.IDs)

	}
	if info.Username != "" {
		var userIds []uint
		err = source.DB().Where("username like ?", "%"+info.Username+"%").Or("nick_name like ?", "%"+info.Username+"%").Pluck("id", &userIds).Error
		if err != nil {
			return
		}
		db = db.Where("user_id in ?", userIds)
	}
	if info.ApplicationID > 0 {
		db = db.Where("application_id = ?", info.ApplicationID)

	}
	if info.Title != "" {
		db = db.Where("title like ?", "%"+info.Title+"%")

	}
	if info.Status != "" {
		db = db.Where("status = ?", info.Status)

	}
	var timeType string
	if info.TimeType == "" {
		timeType = "pay_at"
	} else {
		timeType = info.TimeType
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	var activities []model.JhCpsOrder
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Omit("data_string").Order("created_at desc").Find(&activities).Error
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "订单ID")
	f.SetCellValue("Sheet1", "B1", "订单编号")
	//f.SetCellValue("Sheet1", "C1", "分类id")
	f.SetCellValue("Sheet1", "C1", "订单标题")
	f.SetCellValue("Sheet1", "D1", "订单类型")
	f.SetCellValue("Sheet1", "E1", "支付金额")
	f.SetCellValue("Sheet1", "F1", "会员id")
	f.SetCellValue("Sheet1", "G1", "会员名称")
	f.SetCellValue("Sheet1", "H1", "预估佣金")
	f.SetCellValue("Sheet1", "I1", "佣金比率")
	f.SetCellValue("Sheet1", "J1", "分成基数")
	f.SetCellValue("Sheet1", "K1", "分成比例")
	f.SetCellValue("Sheet1", "L1", "分成金额")
	f.SetCellValue("Sheet1", "M1", "状态")
	i := 2
	//var getCloudGoodsDetail request.GetCloudGoodsDetail
	//getCloudGoodsDetail.GatherSuppliesId = searchData.GatherSuppliesId
	for _, v := range activities {

		err, level := level2.GetLevel(v.User.LevelID)
		if err != nil {
			return err, link
		}

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.OrderSN)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.Title)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Type)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), float64(v.Price)/100)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.User.ID)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.User.Username)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), float64(v.CommissionPrice)/100)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), strconv.Itoa(int(v.Ratio))+"%")
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), float64(v.CommissionPrice)/100)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), fmt.Sprintf("%0.2f", float64(level.JhCpsRatio)/100)+"%")
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), float64(v.CommissionPrice)/100*float64(level.JhCpsRatio)/10000)
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.Status)

		i++

	}

	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	times := time.Now().Format("20060102150405")

	path := YzGoConfig.Config().Local.Path + "/export_cps_order"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + times + "jhcps订单导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}
	return err, link
}

func SettleAwardByTx(tx *gorm.DB, award model.JhCpsOrder) (err error) {
	award.Status = model.Settled
	award.LocalSettleAt = &source.LocalTime{Time: time.Now()}
	err = tx.Updates(&award).Error
	return
}
