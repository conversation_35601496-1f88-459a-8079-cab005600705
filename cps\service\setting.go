package service

import (
	"cps/model"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"yz-go/source"
	"yz-go/utils"
)

// 基础设置敏感信息掩码字段

func CpsSettingMaskFields() []string {
	return []string{"meituan_secret", "eleme_app_secret", "didi_access_key"}
}

func ShowCpsSetting() (err error, setting model.CpsSetting) {
	if err, setting = GetCpsSetting(); err != nil {
		return
	}

	var valueByte []byte
	if valueByte, err = json.Marshal(setting.Value); err != nil {
		return
	}

	// 将 setting.Value 解析为 map
	var valueMap = make(map[string]interface{})
	if err = json.Unmarshal(valueByte, &valueMap); err != nil {
		return
	}

	for _, m := range CpsSettingMaskFields() {
		for k, v := range valueMap {
			if k == m {
				valueMap[k] = utils.MaskMiddle(fmt.Sprintf("%v", v), 2, 2)
			}
		}
	}

	// 将处理后的 valueMap 重新序列化为 JSON
	if valueByte, err = json.Marshal(valueMap); err != nil {
		return
	}

	var settingValue model.CpsValue
	if err = json.Unmarshal(valueByte, &settingValue); err != nil {
		return
	}

	setting.Value = settingValue

	return
}

type UpdateMask struct {
	Field string `json:"field"`
	Value string `json:"value"`
}

func UpdateCpsSettingMask(p UpdateMask) (err error) {
	// 验证提交字段是否在掩码字段中
	if !utils.IsStringInSlice(p.Field, CpsSettingMaskFields()) {
		err = errors.New("字段验证错误，请重试")
		return
	}

	// 查询设置
	var sysSetting model.CpsSetting
	if err, sysSetting = GetCpsSetting(); err != nil {
		return
	}

	var valueByte []byte
	if valueByte, err = json.Marshal(sysSetting.Value); err != nil {
		return
	}

	// 解析新 byteValue 为 map
	var valueMap map[string]interface{}
	if err = json.Unmarshal(valueByte, &valueMap); err != nil {
		return
	}

	// 根据字段名设置字段的值
	for k, _ := range valueMap {
		if k == p.Field {
			valueMap[k] = p.Value
		}
	}

	// 将处理后的 valueMap 重新序列化为 JSON
	if valueByte, err = json.Marshal(valueMap); err != nil {
		return
	}

	var settingValue model.CpsValue
	if err = json.Unmarshal(valueByte, &settingValue); err != nil {
		return
	}

	sysSetting.Value = settingValue

	if err = source.DB().Omit("created_at").Save(sysSetting).Error; err != nil {
		return
	}

	return
}

func GetCpsSetting() (err error, sysSetting model.CpsSetting) {

	err = source.DB().Where("`key` = ?", "jhcps_setting").First(&sysSetting).Error

	return
}

func SaveCpsSetting(data model.CpsSetting) (err error) {
	var sysSetting model.CpsSetting
	if err, sysSetting = GetCpsSetting(); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	err = nil

	// 更新时，保留敏感字段原值
	if sysSetting.ID > 0 {
		// 解析原设置的 Value 为 map
		var oldValueByte []byte
		if oldValueByte, err = json.Marshal(sysSetting.Value); err != nil {
			return
		}

		var oldValue map[string]interface{}
		if err = json.Unmarshal(oldValueByte, &oldValue); err != nil {
			return
		}

		// 解析新设置的 Value 为 map
		var newValueByte []byte
		if newValueByte, err = json.Marshal(data.Value); err != nil {
			return
		}

		var newValue map[string]interface{}
		if err = json.Unmarshal(newValueByte, &newValue); err != nil {
			return
		}

		// 将脱敏字段保持原值
		for _, k := range CpsSettingMaskFields() {
			_, nExist := newValue[k]
			if !nExist {
				continue
			}

			oValue, oExist := oldValue[k]
			if !oExist {
				continue
			}

			newValue[k] = oValue
		}

		// 将处理后的 newValue 重新序列化为 JSON
		if newValueByte, err = json.Marshal(newValue); err != nil {
			return
		}

		var settingValue model.CpsValue
		if err = json.Unmarshal(newValueByte, &settingValue); err != nil {
			return
		}

		data.Value = settingValue

		if err = source.DB().Omit("created_at").Save(&data).Error; err != nil {
			return
		}
	} else {
		if err = source.DB().Create(&data).Error; err != nil {
			return
		}
	}

	return
}
