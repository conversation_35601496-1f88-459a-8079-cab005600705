package goods

import (
	catemodel "category/model"
	"cross-border-supply/common"
	cpub "cross-border-supply/component/public"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"github.com/xingliuhua/leaf"
	"go.uber.org/zap"
	log2 "log"
	"math"
	url2 "net/url"
	pmodel "product/model"
	"public-supply/service"

	callback2 "public-supply/callback"
	commno2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type CrossSupply struct {
	dat      *model.SupplySetting
	SupplyID uint
	reqData  url2.Values
	shopId   int
}

func (self *CrossSupply) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *CrossSupply) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

func GetIdArr(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}
func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}
func splitArray(arr []model.Goods, num int64) [][]model.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]model.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]model.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func (self *CrossSupply) ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	commno2.GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []model.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *CrossSupply) RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []model.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *CrossSupply) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	// TODO 这里用的获取shop_id方法，可以单独提出来用来获取shop_id
	reqJson, err := json.Marshal(make(map[string]interface{}))
	if err != nil {
		return
	}

	reqData := url2.Values{
		"timestamp":   {time.Now().Format("2006-01-02 15:04:05")},
		"version":     {"1.0"},
		"charset":     {"UTF-8"},
		"format":      {"json"},
		"sign_type":   {"RSA2"},
		"method":      {"shop.query"},
		"app_id":      {params.AppKey},
		"biz_content": {string(reqJson)},
	}

	// 签名
	sign := cpub.GetSign(reqData, params.AppSecret)
	// 添加签名
	reqData.Set("sign", sign)

	var resData []byte
	err, resData = utils.PostForm(params.ApiUrl, reqData, nil)
	if err != nil {
		return
	}

	var ShopInfo ResShopInfo

	if err = json.Unmarshal(resData, &ShopInfo); err != nil {
		return
	}

	if ShopInfo.Code != "0" {
		err = errors.New(ShopInfo.Msg)
		return
	}
	return
}

func (y *CrossSupply) DeleteGoods(id uint) (err error) {

	var cross model.CrossTempProduct
	if id > 0 {
		err = source.DB().Unscoped().Where("id=?", id).Delete(&cross).Error
	}
	err = source.DB().Unscoped().Where("id > 0").Delete(&cross).Error
	//TODO implement me
	return
}

func (y *CrossSupply) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

func (y *CrossSupply) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	return
}

func (y *CrossSupply) InitGoods() (err error) {
	return
}

func (y *CrossSupply) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product, recordErrors []model.SupplyGoodsImportRecordErrors) {
	if len(list) == 0 {
		log.Log().Error("选择可导商品数量为空", zap.Any("err", err))
		return
	}
	for _, item := range list {
		var yzhGoods model.CrossTempProduct

		err = source.DB().Where("id=?", item.ID).First(&yzhGoods).Error
		if err != nil {
			log.Log().Error("查询yzh商品数据错误", zap.Any("err", err))
			return
		}
		//	var detail model.CorssSupply
		//detail. = yzhGoods
		//detail.CategoryNames = yzhGoods.CateNames
		var goodsDetail []*pmodel.Product
		err, goodsDetail = y.CommodityAssemblyLocal(yzhGoods, cateId1, cateId2, cateId3)
		if err != nil {
			log.Log().Error("解析yzh选择商品数据错误", zap.Any("err", err))
			return
		}
		listGoods = append(listGoods, goodsDetail...)
	}
	return
}

func (y *CrossSupply) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	//panic("implement me")

	return
}

func (y *CrossSupply) InitSetting(gatherSupplyID uint) (err error) {

	y.SupplyID = gatherSupplyID
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	common.YZH_HTTP_URL = y.dat.BaseInfo.ApiUrl

	reqData := url2.Values{}

	reqData.Add("app_id", y.dat.BaseInfo.AppKey)
	reqData.Add("version", "1.0")
	reqData.Add("charset", "UTF-8")
	reqData.Add("timestamp", time.Now().Format("2006-01-02 15:04:05"))
	reqData.Add("sign_type", "RSA2")
	reqData.Add("format", "json")

	y.reqData = reqData

	if y.shopId == 0 {
		err, y.shopId = y.GetShopId()
	}
	return
}

type ResShopInfo struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	Content []struct {
		ChannelNo   int    `json:"channelNo"`
		ChannelName string `json:"channelName"`
	} `json:"content"`
}

func (y *CrossSupply) GetShopId() (err error, shopId int) {

	y.reqData.Del("sign")

	var reqMaps = make(map[string]interface{})
	y.reqData.Set("method", "shop.query")

	reqJson, _ := json.Marshal(reqMaps)
	y.reqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.reqData, y.dat.BaseInfo.AppSecret)
	y.reqData.Set("sign", sign)
	url := string(common.YZH_HTTP_URL)
	var resData []byte
	fmt.Println("请求数据：", y.reqData)
	log.Log().Error("请求数据2", zap.Any("err", y.reqData))
	err, resData = utils.PostForm(url, y.reqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}

	var ShopInfo ResShopInfo
	err = json.Unmarshal(resData, &ShopInfo)
	if err != nil {
		return
	}
	if ShopInfo.Code != "40002" {
		shopId = ShopInfo.Content[0].ChannelNo
		fmt.Println("", string(resData))

	}

	return
}

func (y *CrossSupply) GetStock(pid int) (goodsStock model.GoodsStock, err error) {

	goodsID := strconv.Itoa(pid)

	url := string(common.YZH_HTTP_URL + common.YZH_GET_GOODS_STOCK)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("pid", goodsID)
	reqData.Add("num", "1")
	//       999999_999998_999997
	reqData.Add("address", "1_2800_55830")
	err, resData = utils.PostForm(url, reqData, headerData)
	if err != nil {
		return
	}

	fmt.Println(string(resData))

	err = json.Unmarshal(resData, &goodsStock)

	if err != nil {
		log.Log().Error("GetStock解析失败", zap.Any("err", err))
		return
	}

	return
}

func (*CrossSupply) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {

	db := source.DB().Model(model.CrossTempProduct{})

	if info.SearchWords != "" {
		db = db.Where("`goods_name` LIKE ?", "%"+info.SearchWords+"%")
	}

	if info.CategoryLevel == "1" {
		db = db.Where("`cate1` = ?", info.CategoryStrID)
	}
	if info.CategoryLevel == "2" {
		db = db.Where("`cate2` = ?", info.CategoryStrID)
	}
	if info.CategoryLevel == "3" {
		db = db.Where("`cate3` = ?", info.CategoryStrID)
	}
	if info.CategoryLevel == "4" {
		db = db.Where("`cate4` = ?", info.CategoryStrID)
	}

	//if info.CategoryID > 0 {
	//	db = db.Where("cate1=? or cate2=? or cate3=?", info.CategoryID, info.CategoryID, info.CategoryID)
	//}
	//
	//if info.Categorys != "" {
	//	db = db.Where("category_name LIKE ?", "%"+info.Categorys+"%")
	//}
	//
	if info.Sort != "" {

		if info.Type == "created_time" {
			db = db.Order("created_at" + " " + info.Sort)
		}
		if info.Type == "agreement_price" {
			db = db.Order("cost_price" + " " + info.Sort)
		}
		if info.Type == "guide_price" || info.Type == "activity_price" {
			db = db.Order("market_price" + " " + info.Sort)
		}
		if info.Type == "promotion_rate" || info.Type == "activity_rate" {
			db = db.Select("yzh_products.*", "((market_price-retail_price)/retail_price) as activity_rate").Order("(market_price-retail_price)/retail_price" + " " + info.Sort)
		}

	}
	//
	if info.RangeType == "agreement_price" {
		db = db.Where("cost_price between ? and ?", info.RangeForm, info.RangeTo)
	}
	if info.RangeType == "activity_price" || info.RangeType == "guide_price" {
		db = db.Where("market_price between ? and ?", info.RangeForm, info.RangeTo)
	}

	var productList []pmodel.Product
	source.DB().Where("source=? and gather_supply_id=?", 102, info.GatherSupplyID).Find(&productList)

	var list []model.Goods
	var SupplyList []model.CrossTempProduct
	var count int64
	limit := info.Limit
	offset := info.Limit * (info.Page - 1)
	err = db.Count(&count).Error
	db.Limit(limit).Offset(offset).Find(&SupplyList)

	for _, item := range SupplyList {

		MarketPrice, _ := strconv.ParseFloat(item.MarketPrice, 64)
		CostCPrice, _ := strconv.ParseFloat(item.CostPrice, 64)
		Mprice := uint(MarketPrice)
		Cprice := uint(CostCPrice)
		isImport := 0
		cost := int64(Mprice - Cprice)
		rates := float32(cost) / float32(Cprice) * 100 //(item.MarketPrice - item.CostPrice) / item.CostPrice

		ratesn := int64(rates)
		if ratesn <= 0 || cost < 0 {
			ratesn = 0
		}

		GoodsNo, _ := strconv.Atoi(item.SkuNo)
		for _, pitem := range productList {
			if pitem.SourceGoodsID == uint(GoodsNo) {
				isImport = 1
			}
		}

		list = append(list, model.Goods{
			Title:          item.GoodsName,
			MarketPrice:    Mprice,
			Rate:           float64(ratesn),
			ThirdBrandName: item.BrandNameCn,
			Cover:          item.GoodsMainImage,
			AgreementPrice: Cprice * 100,
			GuidePrice:     Mprice * 100,
			ActivityPrice:  Mprice * 100,
			SalePrice:      Mprice * 100,
			ID:             int(item.ID),
			IsImport:       uint(isImport),
		})

	}
	fmt.Println("查询商品返回count:", count, info.Page, info.Limit)
	data = list
	total = count
	return
}

var cross_startTime, cross_endTime string

func (y *CrossSupply) ForDateUpdate(startTime string) (err error) {
	//日期转化为时间戳
	timeLayout := "2006-01-02 15:04:05"  //转化所需模板
	loc, _ := time.LoadLocation("Local") //获取时区
	tmp, _ := time.ParseInLocation(timeLayout, startTime, loc)

	t := tmp.Unix()

	endTime := time.Unix(t, 0).AddDate(0, 0, 1).Format("2006-01-02 15:04:05")

	y.reqData.Del("sign")

	var reqMaps = make(map[string]interface{})
	y.reqData.Set("method", "goods.query")

	reqMaps["channelNo"] = y.shopId

	cross_startTime = startTime
	cross_endTime = endTime
	reqMaps["startTime"] = startTime
	reqMaps["endTime"] = endTime
	reqJson, _ := json.Marshal(reqMaps)
	y.reqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.reqData, y.dat.BaseInfo.AppSecret)
	y.reqData.Set("sign", sign)
	log.Log().Info("请求参数", zap.Any("info", y.reqData))

	url := string(common.YZH_HTTP_URL)
	var resData []byte
	err, resData = utils.PostForm(url, y.reqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}
	fmt.Println("", string(resData))
	var goodsDetail model.CorssSupply
	err = json.Unmarshal(resData, &goodsDetail)

	count := float64(goodsDetail.Content.Total)
	//if count == 0 && len(goodsDetail.Content.List) == 0 {
	//	err = errors.New("无数据，停止")
	//	return
	//}

	var limit = float64(100)
	var forCount = count / limit
	var counts = int64(math.Ceil(forCount))
	if counts <= 0 {
		return
	}
	fmt.Println(counts)
	orderPN := utils.GetOrderNo()
	err, _ = y.GetCategoryListAll()
	if err != nil {
		return err
	}
	y.RunGoods(counts, orderPN)

	return
}

func (y *CrossSupply) UpdateGoodsRun() (err error) {

	var CrossTempProduct model.CrossTempProduct
	source.DB().Order("update_time desc").First(&CrossTempProduct)

	var startTime string
	if CrossTempProduct.UpdateTime != "" {
		startTime = CrossTempProduct.UpdateTime
	} else {
		startTime = y.dat.Pricing.OpenTime + " 00:00:00"

	}
	timeLayout := "2006-01-02 15:04:05" //转化所需模板

	loc, _ := time.LoadLocation("Local") //获取时区
	var endTime string

	nowTime := time.Now().Format(timeLayout)

	for i := 0; i <= 999999; i++ {

		var tmp time.Time

		tmp, _ = time.ParseInLocation(timeLayout, startTime, loc)

		t := tmp.Unix()

		endTime = time.Unix(t, 0).AddDate(0, 0, i).Format("2006-01-02 15:04:05")

		if endTime > nowTime {
			fmt.Println("超出当前时间")
			return

		}
		err = y.ForDateUpdate(endTime)
		if err != nil {
			return err
		}

	}

	return

}

func (y *CrossSupply) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	db := source.DB().Model(model.CrossTempProduct{})

	if info.SearchWords != "" {
		db = db.Where("`goods_name` LIKE ?", "%"+info.SearchWords+"%")
	}

	if info.CategoryLevel == "1" {
		db = db.Where("`cate1` = ?", info.CategoryStrID)
	}
	if info.CategoryLevel == "2" {
		db = db.Where("`cate2` = ?", info.CategoryStrID)
	}
	if info.CategoryLevel == "3" {
		db = db.Where("`cate3` = ?", info.CategoryStrID)
	}
	if info.CategoryLevel == "4" {
		db = db.Where("`cate4` = ?", info.CategoryStrID)
	}

	if info.RangeType == "agreement_price" {
		db = db.Where("cost_price between ? and ?", info.RangeForm, info.RangeTo)
	}
	if info.RangeType == "activity_price" || info.RangeType == "guide_price" {
		db = db.Where("market_price between ? and ?", info.RangeForm, info.RangeTo)
	}

	var ProductList []model.CrossTempProduct
	err = db.Find(&ProductList).Error
	if err != nil {
		log.Log().Error("cross查询导入商品错误", zap.Any("err", err))
		return
	}
	var count = len(ProductList)
	orderPN := utils.GetOrderNo()
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: count,
		Status:            1,
		SearchCriteria:    "all",
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	var wg sync.WaitGroup
	for _, item := range ProductList {
		wg.Add(1)
		y.ImportLocalGoods(&wg, item, orderPN, info.Categorys)
	}

	wg.Wait()
	err = cpub.SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

func (y *CrossSupply) ImportLocalGoods(wg *sync.WaitGroup, detail model.CrossTempProduct, orderPN string, caters string) {
	defer wg.Done()
	ids := strings.Split(caters, ",")
	var cate1, cate2, cate3 int
	if len(ids) >= 3 {
		cate1, _ = strconv.Atoi(ids[0])
		cate2, _ = strconv.Atoi(ids[1])
		cate3, _ = strconv.Atoi(ids[2])
	}
	//var detail model.CrossTempProduct
	//
	//
	//detail=yzhList

	err, listGoods := y.CommodityAssemblyLocal(detail, cate1, cate2, cate3)
	if err != nil {
		log.Log().Error("CommodityAssemblyLocal错误", zap.Any("err", err))
	}

	if len(listGoods) > 0 {
		service.FinalProcessing(listGoods, orderPN)
	}

	return
}

func ForGetYzhListCode(code uint) (pid uint, name string) {
	for _, item := range AllCateData {
		if item.Code == code {
			return item.ParentId, item.Name

		}
	}

	return 0, ""
}

func (y *CrossSupply) GetCateLoop() {

}

func (y *CrossSupply) SynergeticProcess(wg *sync.WaitGroup, item int64) {
	defer wg.Done()

	var err error

	y.reqData.Del("sign")
	var reqMaps = make(map[string]interface{})
	y.reqData.Set("method", "goods.query")

	/*业务公共参数*/
	//startTime:=time.Now().Format("2006-01-02 15:04:05")
	//t := time.Now().Unix()
	//t += 86400 //增加一天
	//endTime:=time.Unix(t, 0).Format("2006-01-02 15:04:05")

	reqMaps["channelNo"] = y.shopId
	reqMaps["startTime"] = cross_startTime
	reqMaps["endTime"] = cross_endTime
	reqMaps["pageNo"] = item
	reqJson, _ := json.Marshal(reqMaps)
	y.reqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.reqData, y.dat.BaseInfo.AppSecret)
	y.reqData.Set("sign", sign)

	url := string(common.YZH_HTTP_URL)
	var resData []byte
	err, resData = utils.PostForm(url, y.reqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}
	fmt.Println("", string(resData))
	var goodsDetail model.CorssSupply
	err = json.Unmarshal(resData, &goodsDetail)
	if err != nil {
		log.Log().Error("解析错误", zap.Any("err", err))
	}
	fmt.Println(string(resData))

	//if "selling" != goodsDetail.RESULTDATA.PRODUCTDATA.Status {
	//	return
	//}

	//stock, stockErr := y.GetStock(goodsDetail.RESULTDATA.PRODUCTDATA.ProductId)
	//
	//if stockErr != nil {
	//	log.Log().Error("GetStockerr", zap.Any("stock", stockErr))
	//	return
	//}
	//if stock.RESPONSESTATUS != "true" || stock.RESULTDATA.StockStatus != true {
	//	log.Log().Error("stock.RESPONSESTATUS", zap.Any("stock错误", stock))
	//	err = errors.New("库存无效")
	//	return
	//}

	var CorssProduct []model.CrossTempProduct

	CorssProduct = goodsDetail.Content.List

	for _, Ditem := range CorssProduct {
		var SaveData model.CrossTempProduct
		db := source.DB()
		db.Where("sku_no=? and goods_no=?", Ditem.SkuNo, Ditem.GoodsNo).First(&SaveData)

		var imgstr string

		for _, dimg := range Ditem.GoodsDetailImageList {
			imgstr = imgstr + dimg + ","
		}

		//CorssProduct[index].GoodsDetailImageMore = imgstr
		//CorssProduct[index].Cate4 = Ditem.CategoryNo
		//CorssProduct[index].Cate3 = AllCateDataMap[CorssProduct[index].Cate4].ParentCategoryNo
		//CorssProduct[index].Cate2 = AllCateDataMap[CorssProduct[index].Cate3].ParentCategoryNo
		//CorssProduct[index].Cate1 = AllCateDataMap[CorssProduct[index].Cate2].ParentCategoryNo

		Ditem.GoodsDetailImageMore = imgstr
		Ditem.Cate4 = Ditem.CategoryNo
		Ditem.Cate3 = AllCateDataMap[Ditem.Cate4].ParentCategoryNo
		Ditem.Cate2 = AllCateDataMap[Ditem.Cate3].ParentCategoryNo
		Ditem.Cate1 = AllCateDataMap[Ditem.Cate2].ParentCategoryNo

		if SaveData.ID == 0 {
			source.DB().Create(&Ditem)
		} else {
			source.DB().Where("sku_no=? and goods_no=?", Ditem.SkuNo, Ditem.GoodsNo).Updates(&Ditem)
		}

	}

	//source.DB().Where("product_id=?", goodsDetail.RESULTDATA.PRODUCTDATA.ProductId).First(&yzhProduct)

	//if yzhProduct.ID == 0 {
	//log.Log().Info("开始查询分类", zap.Any("err", goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate))

	//var cateName3, cateName2, cateName1 string
	//var pid uint
	//yzhProduct.Cate3 = uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate)
	//pid, cateName3 = ForGetYzhListCode(uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate))
	//
	//pid3, cateName4 := ForGetYzhListCode(uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate))
	//pid2, cateName3 := ForGetYzhListCode(uint(pid3))
	//pid1, cateName2 := ForGetYzhListCode(uint(pid2))
	//_, cateName1 := ForGetYzhListCode(uint(pid1))

	//if pid1 > 0 {
	//	yzhProduct.Cate1 = pid1
	//	yzhProduct.Cate2 = pid2
	//	yzhProduct.Cate3 = pid3
	//
	//	yzhProduct.CateNames = cateName1 + "," + cateName2 + "," + cateName3
	//} else if pid2 > 0 && pid1 == 0 {
	//	yzhProduct.Cate1 = pid2
	//	yzhProduct.Cate2 = pid3
	//	yzhProduct.Cate3 = uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate)
	//	yzhProduct.CateNames = cateName2 + "," + cateName3 + "," + cateName4
	//} else if pid3 > 0 && pid2 == 0 {
	//	yzhProduct.Cate1 = pid3
	//	yzhProduct.Cate2 = uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate)
	//	yzhProduct.CateNames = cateName3 + "," + cateName4
	//
	//} else if uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate) > 0 && pid3 == 0 {
	//	yzhProduct.Cate1 = uint(goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate)
	//
	//	yzhProduct.CateNames = cateName4
	//
	//}
	//for index,item:=range  goodsDetail.Content.List{
	//
	//
	//}
	//
	//CorssProduct.GoodsName = goodsDetail.Content.List[1].GoodsName
	//yzhProduct.ProductCate = goodsDetail.RESULTDATA.PRODUCTDATA.ProductCate
	//yzhProduct.Stock = stock.RESULTDATA.StockNum
	////yzhProduct.Features = goodsDetail.RESULTDATA.PRODUCTDATA.Features
	//yzhProduct.ProductPlace = goodsDetail.RESULTDATA.PRODUCTDATA.ProductPlace
	//yzhProduct.Status = goodsDetail.RESULTDATA.PRODUCTDATA.Status
	//yzhProduct.Name = goodsDetail.RESULTDATA.PRODUCTDATA.Name
	//yzhProduct.ProductDescription = goodsDetail.RESULTDATA.PRODUCTDESCRIPTION
	//yzhProduct.ThumbnailImage = goodsDetail.RESULTDATA.PRODUCTDATA.ThumbnailImage
	//yzhProduct.Brand = goodsDetail.RESULTDATA.PRODUCTDATA.Brand
	//yzhProduct.Type = goodsDetail.RESULTDATA.PRODUCTDATA.Type
	//yzhProduct.MarketPrice = goodsDetail.RESULTDATA.PRODUCTDATA.MarketPrice * 100
	//yzhProduct.RetailPrice = goodsDetail.RESULTDATA.PRODUCTDATA.RetailPrice * 100
	//yzhProduct.Hot = goodsDetail.RESULTDATA.PRODUCTDATA.Hot
	//yzhProduct.ProductCode = goodsDetail.RESULTDATA.PRODUCTDATA.ProductCode
	//
	////}
	//var galleryList pmodel.Gallery
	//var galleryItem pmodel.GalleryItem
	//for _, gItem := range goodsDetail.RESULTDATA.PRODUCTIMAGE {
	//	galleryItem.Type = 1
	//	galleryItem.Src = gItem.ImageUrl
	//	galleryList = append(galleryList, galleryItem)
	//}
	//yzhProduct.ProductImage = galleryList
	//yzhProduct.ProductDescription = goodsDetail.RESULTDATA.PRODUCTDESCRIPTION
	//
	////if yzhProduct.Name != "" && yzhProduct.MarketPrice != 0 {
	//err = source.DB().Create(&yzhProduct).Error
	//	if err != nil {
	//		log.Log().Error("创建商品错误", zap.Any("err", err))
	//	}
	//}

}

var wg sync.WaitGroup

func (y *CrossSupply) RunGoods(maps int64, orderPN string) {
	var item int64

	for item = 1; item <= maps; item++ {

		wg.Add(1)
		if item%10 == 0 {
			time.Sleep(time.Second * 1)
		}
		y.SynergeticProcess(&wg, item)
		//if index >1000 {
		//	return
		//}
		//yzhProduct.ProductImage=goodsDetail.RESULTDATA.PRODUCTIMAGE

		//var resultArr int
		//err = source.DB().Select("id").Model(model.SupplyGoods{}).Where("gather_supply_id=? and supply_goods_id=?", y.SupplyID,item).Pluck("supply_goods_id",&resultArr).Error
		//if err != nil {
		//	log.Log().Error("查询错误",zap.Any("err",err))
		//	continue
		//}

		//var listGoods []*pmodel.Product
		//
		//err, listGoods = y.CommodityAssemblyA(goodsDetail)
		//
		//if len(listGoods) > 0 {
		//	FinalProcessing(listGoods, orderPN)
		//}

		//
		//difference := collection.Collect(resultArr).WhereIn(goodsDetail.RESULTDATA.PRODUCTDATA)

	}
	wg.Wait()

}

// 获取分类数据
func (y *CrossSupply) GetCategory(infof request.GetCategorySearch) (err error, data interface{}) {

	var info request.GetCategoryChild
	err, Listdata := y.GetCategoryChildSub(0, info)

	fmt.Println(Listdata)
	if err != nil {
		fmt.Println("获取以及分类错误")
	}
	AllCateData = Listdata.([]model.RESULTDATA)
	for _, item := range AllCateData {

		IsDisplay := 1

		var cate1 catemodel.Category
		cate1.Name = item.Name
		cate1.Source = 100
		cate1.Level = 1
		cate1.ParentID = 0
		cate1.IsDisplay = &IsDisplay
		source.DB().Where("level=1 and name =?", item.Name).FirstOrCreate(&cate1)
		err, cdata := y.GetCategoryChildSub(int(item.Code), info)
		if err != nil {
			fmt.Println("获取二级分类错误")
		}
		clist := cdata.([]model.RESULTDATA)
		//AllCateData = append(AllCateData, clist...)
		for _, citem := range clist {
			var cate2 catemodel.Category
			cate2.Name = citem.Name
			cate2.Source = 100
			cate2.Level = 2
			cate2.ParentID = cate1.ID
			cate2.IsDisplay = &IsDisplay
			source.DB().Where("level=2 and name =?", citem.Name).FirstOrCreate(&cate2)

			time.Sleep(time.Microsecond * 100000)
			err, ccdata := y.GetCategoryChildSub(int(citem.Code), info)
			if err != nil {
				fmt.Println("获取二级分类错误")
			}
			cclist := ccdata.([]model.RESULTDATA)

			for _, csitem := range cclist {
				var cate3 catemodel.Category
				cate3.Name = csitem.Name
				cate3.Source = 100
				cate3.Level = 3
				cate3.ParentID = cate2.ID
				cate3.IsDisplay = &IsDisplay
				source.DB().Where("level=3 and name =?", csitem.Name).FirstOrCreate(&cate3)

			}

		}

	}

	return
}

func (y *CrossSupply) GetGroup() (err error, data interface{}) {

	return

}

var AllCateData []model.RESULTDATA

var AllCateDataMap = make(map[string]model.CrossCateData)

func GetCateGoryMap(list []model.CrossCateData) {
	for _, item := range list {
		var citem model.CrossCateData
		citem.CategoryNo = item.CategoryNo
		citem.CategoryName = item.CategoryName
		citem.ParentCategoryNo = item.ParentCategoryNo
		AllCateDataMap[item.CategoryNo] = citem
		if len(item.Children) > 0 {
			GetCateGoryMap(item.Children)
		}
		//AllCateData=append(AllCateData,)

		//fmt.Println(item)
	}

	//aaan, _ :=json.Marshal(AllCateDataMap)
	//
	//fmt.Println("分类：",string(aaan))
	//return
}

func (y *CrossSupply) GetCategoryListAll() (err error, data interface{}) {
	var reqMaps = make(map[string]interface{})

	y.reqData.Set("method", "category.query")
	y.reqData.Del("sign")
	var cateGory model.CrossCategory

	reqJson, _ := json.Marshal(reqMaps)
	y.reqData.Set("biz_content", string(reqJson))
	aaa, _ := json.Marshal(y.reqData)
	fmt.Println("请求参数：", string(aaa))
	sign := cpub.GetSign(y.reqData, y.dat.BaseInfo.AppSecret)
	y.reqData.Set("sign", sign)
	url := common.YZH_HTTP_URL
	var resData []byte
	err, resData = utils.PostForm(url, y.reqData, nil)
	err = json.Unmarshal(resData, &cateGory)
	if err != nil {
		return err, nil
	}

	fmt.Println("分类返回", string(resData))
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}
	GetCateGoryMap(cateGory.Content.Children)

	//	data = cateGory
	return

	//	fmt.Println("333")
}

func (y *CrossSupply) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	y.reqData.Del("sign")
	var reqMaps = make(map[string]interface{})

	y.reqData.Set("method", "category.query")
	var cateGory model.CrossCategory

	reqJson, _ := json.Marshal(reqMaps)
	y.reqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.reqData, y.dat.BaseInfo.AppSecret)
	y.reqData.Set("sign", sign)
	url := string(common.YZH_HTTP_URL)
	var resData []byte
	err, resData = utils.PostForm(url, y.reqData, nil)
	err = json.Unmarshal(resData, &cateGory)
	if err != nil {
		return err, nil
	}

	fmt.Println("分类返回", string(resData))
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}

	data = cateGory
	return

	fmt.Println("333")
	//timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//reqData := url2.Values{}
	//reqData.Add("wid", y.dat.BaseInfo.AppKey)
	//reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	//reqData.Add("timestamp", timeUnix)
	//var url string
	//url = string(common.YZH_HTTP_URL + common.YZH_GETCATE_GORY)
	//
	//parentCate := strconv.Itoa(pid)
	//if pid > 0 {
	//	reqData.Add("parentCate", parentCate)
	//	url = string(common.YZH_HTTP_URL + common.YZH_GETCATE_CHILD)
	//}
	//var resData []byte
	//err, resData = utils.PostForm(url, reqData, nil)
	//if err != nil {
	//	log.Log().Error("请求错误", zap.Any("err", err))
	//	return
	//}
	//fmt.Println(string(resData))
	//var yzhCateGory model.YzhCateGory
	//err = json.Unmarshal(resData, &yzhCateGory)
	//if err != nil {
	//	return
	//}
	var yzhCategory []model.YzhCategory
	for _, cateItem := range cateGory.Content.Children {
		yzhCategory = append(yzhCategory, model.YzhCategory{
			Id:     1,
			Title:  cateItem.CategoryName,
			Level:  1,
			Pid:    0,
			Status: true,
			Code:   1,
		})
	}
	data = yzhCategory
	return

}

func (y *CrossSupply) GetCategoryChildSub(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	var url string
	url = string(common.YZH_HTTP_URL + common.YZH_GETCATE_GORY)

	parentCate := strconv.Itoa(pid)
	if pid > 0 {
		reqData.Add("parentCate", parentCate)
		url = string(common.YZH_HTTP_URL + common.YZH_GETCATE_CHILD)
	}
	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
		return
	}
	fmt.Println(string(resData))
	var yzhCateGory model.YzhCateGory
	err = json.Unmarshal(resData, &yzhCateGory)
	if err != nil {
		return
	}
	//var yzhCategory []model.YzhCategory
	//for _, cateItem := range yzhCateGory.RESULTDATA {
	//	yzhCategory = append(yzhCategory, model.YzhCategory{
	//		Id:     cateItem.Code,
	//		Title:  cateItem.Name,
	//		Level:  cateItem.Level,
	//		Pid:    cateItem.ParentId,
	//		Status: cateItem.Status,
	//		Code:   cateItem.Code,
	//	})
	//}
	data = yzhCateGory.RESULTDATA
	return

}

func (y *CrossSupply) GetCategoryDetail(id uint) (err error, cate uint) {

	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	url := string(common.YZH_HTTP_URL + common.YZH_GETCATE_DETAIL)
	cid := strconv.Itoa(int(id))
	reqData.Add("cid", cid)
	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
		return
	}
	var yzhCateGory model.YzhCateGoryDetail
	err = json.Unmarshal(resData, &yzhCateGory)
	if err != nil {
		return
	}
	fmt.Println(id, "返回分类数据：", yzhCateGory)
	cate = yzhCateGory.RESULTDATA.ParentId
	//categoryList = append(categoryList, yzhCateGory.RESULTDATA.ID)
	//if yzhCateGory.RESULTDATA.ParentId > 0 {
	//	return y.GetCategoryDetail(yzhCateGory.RESULTDATA.ParentId, categoryList)
	//}
	//	cates = categoryList
	return

}

// 商品组装
func (y *CrossSupply) CommodityAssemblyLocal(detail model.CrossTempProduct, cateId1, cateId2, cateId3 int) (err error, listGoods []*pmodel.Product) {

	sourceCode := 102
	goods := new(pmodel.Product)

	product := pmodel.Product{}
	err = source.DB().Where("source_goods_id =  ? and source=?", detail.SkuNo, sourceCode).Preload("Skus").First(&product).Error
	if product.ID > 0 {
		y.CommodityAssemblyLocalUpdate(detail, cateId1, cateId2, cateId3)
		return
	}

	goodsNo, _ := strconv.Atoi(detail.SkuNo)
	goods.SourceGoodsID = uint(goodsNo)
	var brand = new(catemodel.Brand)
	if detail.BrandNameCn != "" {
		brand.Name = detail.BrandNameCn
		brand.Source = sourceCode
		err = source.DB().Where(brand).FirstOrCreate(&brand).Error
		goods.BrandID = brand.ID
	}
	var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	var elem model.Goods
	CostPrice, _ := strconv.ParseFloat(detail.CostPrice, 64)
	MarketPrice, _ := strconv.ParseFloat(detail.MarketPrice, 64)
	elem.AgreementPrice = uint(CostPrice * 100)
	elem.GuidePrice = uint(MarketPrice * 100)
	elem.ActivityPrice = uint(MarketPrice * 100)
	elem.Source = sourceCode

	err, costPrice, salePrice, originPrice, activityPrice, guidePrice = cpub.GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))

	//stock, stockErr := y.GetStock(detail.RESULTDATA.PRODUCTDATA.ProductId)
	//if stockErr != nil {
	//	return
	//}
	//if stock.RESPONSESTATUS != "true" || stock.RESULTDATA.StockStatus != true {
	//	err = errors.New("库存无效")
	//	return
	//}

	goods.Title = detail.GoodsName
	goods.OriginPrice = originPrice
	goods.Price = salePrice
	goods.CostPrice = costPrice
	goods.ActivityPrice = activityPrice
	goods.GuidePrice = guidePrice

	goods.Stock = 999
	goods.SingleOption = 1
	var Gallery pmodel.Gallery

	GoodsDetailImageMore := strings.Split(detail.GoodsDetailImageMore, ",")
	for _, item := range GoodsDetailImageMore {
		if item == "" {
			continue
		}
		Gallery = append(Gallery, pmodel.GalleryItem{
			Type: 1,
			Src:  item,
		})
	}

	goods.Gallery = Gallery

	var Attrs pmodel.Attrs

	GoodsPropertiesName := strings.Split(detail.GoodsPropertiesName, ";")

	for _, item := range GoodsPropertiesName {
		if item == "item" {
			continue
		}
		attr := strings.Split(item, ":")
		if len(attr) < 2 {
			continue
		}
		Attrs = append(Attrs, pmodel.Attr{
			Name:  attr[0],
			Value: attr[1],
		})
	}

	goods.Attrs = Attrs
	//goods.Sales = elem.Sale

	goods.IsDisplay = 1
	goods.ImageUrl = detail.GoodsMainImage
	goods.Unit = "个"
	//goods.SourceGoodsID = uint(detail.RESULTDATA.PRODUCTDATA.ProductId)
	goods.Source = sourceCode
	goods.DetailImages = detail.GoodsPcDesc

	//cateList := strings.Split(detail.CategoryNames, ",")
	var cate1, cate2, cate3 catemodel.Category
	display := 1

	var dat model.SupplySetting
	err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}

	if len(AllCateDataMap) <= 0 {
		y.GetCategoryListAll()
	}

	if dat.UpdateInfo.CateGory == 1 {

		cateName1 := AllCateDataMap[detail.Cate1].CategoryName

		cate1.IsDisplay = &display
		cate1.ParentID = 0
		cate1.Level = 1
		cate1.Name = cateName1
		source.DB().Where("name=? and level=? and parent_id=?", cateName1, 1, 0).FirstOrCreate(&cate1)

		cateName2 := AllCateDataMap[detail.Cate2].CategoryName
		cate2.IsDisplay = &display
		cate2.ParentID = cate1.ID
		cate2.Level = 2
		cate2.Name = cateName2
		source.DB().Where("name=? and level=? and parent_id=?", cateName2, 2, cate1.ID).FirstOrCreate(&cate2)

		cateName3 := AllCateDataMap[detail.Cate4].CategoryName
		cate3.IsDisplay = &display
		cate3.ParentID = cate2.ID
		cate3.Level = 3
		cate3.Name = cateName3
		source.DB().Where("name=? and level=? and parent_id=?", cateName3, 3, cate2.ID).FirstOrCreate(&cate3)

		//

		goods.Category1ID = cate1.ID
		goods.Category2ID = cate2.ID
		goods.Category3ID = cate3.ID
	}
	if cateId1 > 0 {
		goods.Category1ID = uint(cateId1)
	}
	if cateId2 > 0 {
		goods.Category2ID = uint(cateId2)
	}
	if cateId3 > 0 {
		goods.Category3ID = uint(cateId3)
	}

	//}

	goods.FreightType = 2
	//goods.Freight = detail.FreightFee
	goods.GatherSupplyID = y.SupplyID

	var sku = pmodel.Sku{}
	var skuList []pmodel.Sku

	//goods.Gallery = detail.RESULTDATA.PRODUCTDATA.ProductImage
	/**
	处理轮播图结束
	*/

	goods.MinPrice = goods.Price
	goods.MaxPrice = goods.Price

	if len(goods.Skus) == 0 {
		var options pmodel.Options
		var option pmodel.Option
		option.SpecName = "规格"
		option.SpecItemName = "默认"
		options = append(options, option)
		if len(goods.Skus) > 0 {
			sku.ID = goods.Skus[0].ID
		}
		skuNo, _ := strconv.Atoi(detail.SkuNo)

		sku.Title = "默认"
		sku.Options = options
		sku.Weight = 0
		sku.CostPrice = goods.CostPrice
		sku.Stock = int(goods.Stock)
		//sku.IsDisplay = goods.IsDisplay
		sku.Price = goods.Price
		sku.OriginPrice = goods.OriginPrice
		sku.GuidePrice = goods.GuidePrice
		sku.ActivityPrice = goods.ActivityPrice
		sku.OriginalSkuID = int64(skuNo)
		skuList = append(skuList, sku)

	}

	goods.Skus = skuList

	//---------处理属性json数组结束
	//goods.Desc=detail.Description

	if len(goods.Skus) > 0 {
		listGoods = append(listGoods, goods)

	}

	return
}

// 商品组装
func (y *CrossSupply) CommodityAssemblyLocalUpdate(detail model.CrossTempProduct, cateId1, cateId2, cateId3 int) (err error, listGoods []*pmodel.Product) {

	//sourceCode := 100
	//var goods pservice.ProductForUpdate
	//err = source.DB().Where("source_goods_id =  ? and source=?", detail.RESULTDATA.PRODUCTDATA.ProductId, 100).Preload("Skus").First(&goods).Error
	////
	////
	////goods:=goodsa.(pservice.ProductForUpdate)
	////product:=goodsa.(*pmodel.Product)
	//
	//goods.SourceGoodsID = uint(detail.RESULTDATA.PRODUCTDATA.ProductId)
	//var brand = new(catemodel.Brand)
	//if detail.RESULTDATA.PRODUCTDATA.Brand != "" {
	//	brand.Name = detail.RESULTDATA.PRODUCTDATA.Brand
	//	brand.Source = sourceCode
	//	err = source.DB().Where(brand).FirstOrCreate(&brand).Error
	//	goods.BrandID = brand.ID
	//}
	//var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	//var elem model.Goods
	//elem.AgreementPrice = uint(detail.RESULTDATA.PRODUCTDATA.RetailPrice)
	//elem.GuidePrice = uint(detail.RESULTDATA.PRODUCTDATA.MarketPrice)
	//elem.ActivityPrice = uint(detail.RESULTDATA.PRODUCTDATA.MarketPrice)
	//elem.Source = sourceCode
	//
	//err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
	//
	//stock, stockErr := y.GetStock(detail.RESULTDATA.PRODUCTDATA.ProductId)
	//if stockErr != nil {
	//	return
	//}
	//if stock.RESPONSESTATUS != "true" || stock.RESULTDATA.StockStatus != true {
	//	err = errors.New("库存无效")
	//	return
	//}
	//
	//goods.Title = detail.RESULTDATA.PRODUCTDATA.Name
	//goods.OriginPrice = originPrice
	//goods.Price = salePrice
	//goods.CostPrice = costPrice
	//goods.ActivityPrice = activityPrice
	//goods.GuidePrice = guidePrice
	//goods.Stock = uint(stock.RESULTDATA.StockNum)
	//goods.SingleOption = 1
	////goods.Sales = elem.Sale
	////status, _ := strconv.Atoi(detail.RESULTDATA.PRODUCTDATA.Status)
	////goods.IsDisplay = status
	//goods.ImageUrl = detail.RESULTDATA.PRODUCTDATA.ThumbnailImage
	//goods.Unit = "个"
	////goods.SourceGoodsID = uint(detail.RESULTDATA.PRODUCTDATA.ProductId)
	//goods.Source = sourceCode
	//goods.DetailImages = detail.RESULTDATA.PRODUCTDATA.ProductDescription
	//
	//cateList := strings.Split(detail.CategoryNames, ",")
	//var cate1, cate2, cate3 catemodel.Category
	//display := 1
	//
	//var dat model.SupplySetting
	//err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	//if err != nil {
	//	fmt.Println("获取供应链key设置失败")
	//	return
	//}
	//
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//
	//if dat.UpdateInfo.CateGory == 1 {
	//
	//	if len(cateList) > 0 && cateList[0] != "" {
	//
	//		cate1.IsDisplay = &display
	//		cate1.ParentID = 0
	//		cate1.Level = 1
	//		cate1.Name = cateList[0]
	//		source.DB().Where("name=? and level=? and parent_id=?", cateList[0], 1, 0).FirstOrCreate(&cate1)
	//	}
	//
	//	if len(cateList) > 1 && cateList[1] != "" {
	//		cate2.IsDisplay = &display
	//		cate2.ParentID = cate1.ID
	//		cate2.Level = 2
	//		cate2.Name = cateList[1]
	//		source.DB().Where("name=? and level=? and parent_id=?", cateList[1], 2, cate1.ID).FirstOrCreate(&cate2)
	//	}
	//
	//	if len(cateList) > 2 && cateList[2] != "" {
	//		cate3.IsDisplay = &display
	//		cate3.ParentID = cate2.ID
	//		cate3.Level = 3
	//		cate3.Name = cateList[2]
	//		source.DB().Where("name=? and level=? and parent_id=?", cateList[2], 3, cate2.ID).FirstOrCreate(&cate3)
	//	}
	//	//
	//
	//	goods.Category1ID = cate1.ID
	//	goods.Category2ID = cate2.ID
	//	goods.Category3ID = cate3.ID
	//}
	//
	//if cateId1 > 0 {
	//	goods.Category1ID = uint(cateId1)
	//}
	//if cateId2 > 0 {
	//	goods.Category2ID = uint(cateId2)
	//}
	//if cateId3 > 0 {
	//	goods.Category3ID = uint(cateId3)
	//}
	//
	////}
	//
	//goods.FreightType = 2
	//goods.GatherSupplyID = y.SupplyID
	//
	//var sku = pservice.Sku{}
	//var skuList []pservice.Sku
	//
	//goods.Gallery = detail.RESULTDATA.PRODUCTDATA.ProductImage
	///**
	//处理轮播图结束
	//*/
	//
	//goods.MinPrice = goods.Price
	//goods.MaxPrice = goods.Price
	//
	////if len(goods.Skus) == 0 {
	//var options pmodel.Options
	//var option pmodel.Option
	//option.SpecName = "规格"
	//option.SpecItemName = "默认"
	//options = append(options, option)
	//if len(goods.Skus) > 0 {
	//	sku.ID = goods.Skus[0].ID
	//}
	//
	//sku.Title = "默认"
	//sku.Options = options
	//sku.Weight = 0
	//sku.CostPrice = goods.CostPrice
	//sku.Stock = int(goods.Stock)
	////sku.IsDisplay = goods.IsDisplay
	//sku.Price = goods.Price
	//sku.OriginPrice = goods.OriginPrice
	//sku.GuidePrice = goods.GuidePrice
	//sku.ActivityPrice = goods.ActivityPrice
	//sku.OriginalSkuID = int(goods.SourceGoodsID)
	//skuList = append(skuList, sku)
	//
	////}
	//
	//goods.Skus = skuList
	//
	////---------处理属性json数组结束
	////goods.Desc=detail.Description
	//if goods.ID > 0 {
	//	err = pservice.UpdateProduct(goods)
	//	if err != nil {
	//		log.Log().Error("更新商品错误 update", zap.Any("err", err))
	//		return
	//	}
	//	source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", GlobalOrderSN).Updates(map[string]interface{}{
	//		"repeat_quantity": gorm.Expr("repeat_quantity + ?", 1),
	//	})
	//}
	//
	return
}

var GlobalOrderSN string

// 批量获取商品详情
func (*CrossSupply) BatchGetGoodsDetails(ids string) (err error, data map[int]model.GoodsDetail) {

	return
}

func (*CrossSupply) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	defer wg.Done()
	var result *stbz.APIResult
	result, err = stbz.API(
		1,
		"/v2/Category/Lists",
		map[string]string{},
		g.Map{"page": i, "limit": info.Limit, "source": info.Source},
	)
	fmt.Println("循环：", i, info.Limit, info.Source)
	if result.Data != nil {
		datas := result.Data.(map[string]interface{})
		var cateItem []model.Category
		cateJson := datas["data"]
		mJson, _ := json.Marshal(cateJson)
		stringJson := string(mJson)
		err = json.Unmarshal([]byte(stringJson), &cateItem)
		mutex.Lock()
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}
		mutex.Unlock()

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

var (
	category []model.Category
	mutex    sync.Mutex
)

//
//func ( *CrossSupply) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
//
//	var ids = GoodsData.Data.GoodsIds
//	if len(ids) == 0 {
//		return
//	}
//
//	var data map[int]model.GoodsDetail
//	stringIds := GetIds(ids)
//	for _, item := range ids {
//		var product pservice.ProductForUpdate
//
//		err = source.DB().Where("source_goods_id =  ?", item).Preload("Skus").First(&product).Error
//		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
//			fmt.Println("回调查询商品不存在")
//			return
//		}
//		var stbz = Stbz{}
//		err = stbz.InitSetting(product.GatherSupplyID)
//		err, data = stbz.BatchGetGoodsDetails(stringIds)
//		_, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(product.GatherSupplyID)))
//		err = json.Unmarshal([]byte(setting.Value), &dat)
//		break
//	}
//
//	for _, item := range ids {
//		var product pservice.ProductForUpdate
//
//		err = source.DB().Where("source_goods_id =  ?", item).Preload("Skus").First(&product).Error
//		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
//			fmt.Println("回调查询商品不存在")
//			continue
//		}
//
//		var goods model.Goods
//
//		goods.Source = data[item].Source
//		goods.GuidePrice = data[item].GuidePrice
//		goods.AgreementPrice = data[item].AgreementPrice
//		goods.ActivityPrice = data[item].SalePrice
//		var pcostPrice, psalePrice uint
//		err, pcostPrice, psalePrice = GetPricingPrice(goods, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))
//		if dat.UpdateInfo.OriginalPrice == 1 {
//			product.OriginPrice = data[item].MarketPrice
//		}
//		if dat.UpdateInfo.CostPrice == 1 {
//			product.CostPrice = pcostPrice
//		}
//		if dat.UpdateInfo.CurrentPrice == 1 {
//			product.Price = psalePrice
//		}
//
//		//if SupplySettings.UpdateInfo.BaseInfo==1{
//		//	product.Title=data[item].
//		//}
//		var skuList []pservice.Sku
//		for _, skuItem := range data[item].Specs.Options {
//			var sku pservice.Sku
//
//			source.DB().Where("original_sku_id=? and product_id=?", skuItem.ID, product.ID).First(&sku)
//
//			if skuItem.Status == 1 {
//				var options pmodel.Options
//				var option pmodel.Option
//				var skuTitle string
//				var skutitle string
//				for index, specName := range data[item].Specs.Names {
//					svids := strings.Split(skuItem.SpecValueIds, "_")
//					if len(svids) > index {
//						valueName := GetKeyValueB(data[item].Specs.Values, svids[index], specName.ID)
//						if valueName != "" {
//							option.SpecName = specName.Name
//							option.SpecItemName = valueName
//							skuTitle = skuTitle + valueName
//							skutitle = skutitle + valueName + "/"
//							options = append(options, option)
//						}
//					}
//				}
//
//				if skuTitle == "" {
//					continue
//				}
//
//				var skuElem model.Goods
//
//				skuElem.Source = data[item].Source
//				skuElem.CostPrice = skuItem.CostPrice
//				skuElem.GuidePrice = skuItem.GuidePrice
//				skuElem.SalePrice = skuItem.SalePrice
//				skuElem.ActivityPrice = skuItem.ActivityPrice
//				skuElem.AgreementPrice = skuItem.AgreementPrice
//				skuElem.MarketPrice = skuItem.MarketPrice
//				var costPrice, salePrice uint
//				err, costPrice, salePrice = GetPricingPrice(skuElem, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))
//
//				sku.Title = skuTitle
//				sku.Options = options
//				sku.Weight = skuItem.Weight
//				if sku.ProductID == 0 {
//					sku.ProductID = product.ID
//				}
//
//				sku.CostPrice = costPrice
//				sku.Stock = skuItem.Stock
//				sku.IsDisplay = skuItem.Status
//				sku.Price = salePrice
//				sku.OriginPrice = skuItem.MarketPrice
//				sku.GuidePrice = skuItem.GuidePrice
//				sku.ActivityPrice = skuItem.ActivityPrice
//				sku.OriginalSkuID = skuItem.ID
//
//				skuList = append(skuList, sku)
//
//			}
//
//		}
//		product.Skus = skuList
//		err = pservice.UpdateProduct(product)
//		if err != nil {
//			log.Log().Error("供应商商品回调修改失败", zap.Any("err", err))
//			log.Log().Error("供应商商品回调修改失败", zap.Any("product", product))
//			return
//		}
//
//		err = callback2.DeleteGoodsMsg(GoodsData.MsgID)
//		if err != nil {
//			log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
//		}
//
//	}
//
//	return
//}
