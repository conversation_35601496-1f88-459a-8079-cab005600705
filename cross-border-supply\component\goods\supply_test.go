package goods

import (
	"fmt"
	"testing"
	"time"
)

func TestCrossSupply_GetYzhCategoryListAll(a *testing.T) {

	startTime := "2021-11-24 15:04:05"
	timeLayout := "2006-01-02 15:04:05"  //转化所需模板
	loc, _ := time.LoadLocation("Local") //获取时区
	tmp, _ := time.ParseInLocation(timeLayout, startTime, loc)

	t := tmp.Unix()

	endTime := time.Unix(t, 0).AddDate(0, 0, 40).Format("2006-01-02 15:04:05")

	nowTime := time.Now().Format(timeLayout)

	if nowTime > "2022-02-31 16:59:59" {
		fmt.Println("333")
	}
	fmt.Println(nowTime)

	fmt.Println(endTime)

	return

	var cross CrossSupply

	cross.InitSetting(17)

	cross.GetCategoryListAll()
}

func TestCrossSupply_ForDateUpdate(t *testing.T) {

	var cross CrossSupply

	cross.InitSetting(17)

	//cross.GetCategoryListAll()
	startTime := "2022-03-31 16:59:59"

	cross.ForDateUpdate(startTime)
}
