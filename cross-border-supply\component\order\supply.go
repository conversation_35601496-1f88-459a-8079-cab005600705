package order

import (
	afterSalesModel "after-sales/model"
	"cross-border-supply/common"
	cpub "cross-border-supply/component/public"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	url2 "net/url"
	model3 "order/model"
	callback2 "public-supply/callback"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	gsetting "public-supply/setting"
	"strconv"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type CrossSupply struct {
	Dat      *model.SupplySetting
	SupplyID uint
	Key      string
	ReqData  url2.Values
	ShopId   int
}

func (y *CrossSupply) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (y *CrossSupply) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (y *CrossSupply) GetAllAddress() (err error, data interface{}) {
	return
}

func (y *CrossSupply) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (y *CrossSupply) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (y *CrossSupply) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (y *CrossSupply) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (y *CrossSupply) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

func (y *CrossSupply) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID
	y.Key = "yzh"
	var setting model2.SysSetting

	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.Dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if y.Dat.BaseInfo.AppKey == "" && y.Dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	common.YZH_HTTP_URL = y.Dat.BaseInfo.ApiUrl

	reqData := url2.Values{}

	reqData.Add("app_id", y.Dat.BaseInfo.AppKey)
	reqData.Add("version", "1.0")
	reqData.Add("charset", "UTF-8")
	reqData.Add("timestamp", time.Now().Format("2006-01-02 15:04:05"))
	reqData.Add("sign_type", "RSA2")
	reqData.Add("format", "json")

	y.ReqData = reqData
	if y.ShopId == 0 {
		err, y.ShopId = y.GetShopId()
	}

	return
}

type ResShopInfo struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	Content []struct {
		ChannelNo   int    `json:"channelNo"`
		ChannelName string `json:"channelName"`
	} `json:"content"`
}

func (y *CrossSupply) GetShopId() (err error, shopId int) {

	y.ReqData.Del("sign")

	var reqMaps = make(map[string]interface{})
	y.ReqData.Set("method", "shop.query")

	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)
	url := string(common.YZH_HTTP_URL)
	var resData []byte
	err, resData = utils.PostForm(url, y.ReqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}

	var ShopInfo ResShopInfo
	err = json.Unmarshal(resData, &ShopInfo)
	if err != nil {
		return
	}
	if len(ShopInfo.Content) > 0 {
		shopId = ShopInfo.Content[0].ChannelNo
		fmt.Println("", string(resData))
	}

	return
}

// 订单前置校验  返回运费
func (y *CrossSupply) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.BeforeCheck) {
	//var saleRes response.ResSaleBeforeCheck
	res.Code = 1
	var skuList []string
	for _, item := range request.Skus {
		id := strconv.Itoa(int(item.Sku.Sku))
		skuList = append(skuList, id)
	}
	fmt.Println(skuList)
	y.ReqData.Del("sign")
	var reqMaps = make(map[string]interface{})
	y.ReqData.Set("method", "stock.query")
	reqMaps["channelNo"] = y.ShopId
	reqMaps["skuNos"] = skuList
	reqMaps["pageNo"] = 1
	reqMaps["pageSize"] = 100
	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	fmt.Println("json", string(reqJson))
	nnnf, _ := json.Marshal(y.ReqData)
	fmt.Println("singstring ", string(nnnf))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)
	url := common.YZH_HTTP_URL
	var resData []byte
	err, resData = utils.PostForm(url, y.ReqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}
	var stokRes StokRes
	err = json.Unmarshal(resData, &stokRes)
	if err != nil {
		return err, response.BeforeCheck{}
	}
	if stokRes.Code != "0" {
		err = errors.New(stokRes.Msg)
		return
	}
	for _, itemA := range request.Skus {
		for _, itemB := range stokRes.Content.List {
			id, _ := strconv.Atoi(itemB.SkuNo)
			if (itemA.Sku.Sku == int64(id)) && (itemB.Quantity >= itemA.Number) {
				res.Skus = append(res.Skus, uint(itemA.Sku.Sku))
			} else {
				res.Code = 0
				res.Msg = "存在库存不足商品"
				return
			}
		}
	}

	fmt.Println("", string(resData))

	//err, saleRes = y.SaleBeforeCheck(request)
	//if err != nil {
	//	return
	//}
	//
	//if saleRes.Code == 0 {
	//	res.Msg = saleRes.Msg
	//	return
	//}
	//
	//var pids string
	//for _, item := range request.Skus {
	//	id := strconv.Itoa(int(item.Sku.Sku))
	//	num := strconv.Itoa(item.Number)
	//	pids += id + "_" + num + ","
	//}
	//pids = pids[:len(pids)-1]
	//
	//if pids == "" {
	//	res.Code = 0
	//	res.Msg = "请求sku异常"
	//	return
	//}

	//var addressData model.ReqAddress
	//address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description
	//fmt.Println("全地址：", address)
	//err, addressData = y.GetAreaCodeByAddress(address)
	//if addressData.RESPONSESTATUS == "false" {
	//	log.Log().Info("yzh供应链商品解析省市区id接口失败1", zap.Any("info", addressData))
	//	return
	//
	//}
	//reqAddress := strconv.Itoa(addressData.RESULTDATA.ProvinceId) + "_" + strconv.Itoa(addressData.RESULTDATA.CityId) + "_" + strconv.Itoa(addressData.RESULTDATA.CountyId)
	//
	//url := string(common.YZH_HTTP_URL + common.YZH_GOODSSTORAGE)
	//var resData []byte
	//headerData := make(map[string]string)
	//timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//reqData := url2.Values{}
	//reqData.Add("wid", y.dat.BaseInfo.AppKey)
	//reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	//reqData.Add("timestamp", timeUnix)
	//reqData.Add("pid_nums", pids)
	//reqData.Add("address", reqAddress)
	//err, resData = utils.PostForm(url, reqData, headerData)
	//fmt.Println(string(resData))
	//aaa, err := json.Marshal(reqData)
	//fmt.Println(string(aaa))
	//if err != nil {
	//	log.Log().Error("yzh 获取地址", zap.Any("info", err))
	//	return
	//}
	//
	//var stockStatusList model.StockNumber
	//err = json.Unmarshal(resData, &stockStatusList)
	//if err != nil {
	//	log.Log().Error("SaleBeforeCheck 解析失败1", zap.Any("info", resData))
	//	log.Log().Error("SaleBeforeCheck 解析失败", zap.Any("info", err))
	//	return
	//}
	//
	//if stockStatusList.RESPONSESTATUS == "false" {
	//	res.Code = 1
	//	res.Msg = "请求检测库存错误"
	//	log.Log().Error("OrderBeforeCheck RESPONSESTATUS 检测库存错误", zap.Any("info", stockStatusList))
	//	return
	//}
	//
	//for _, item := range stockStatusList.RESULTDATA {
	//	if item.StockStatus == false {
	//		res.Code = 0
	//		res.Msg = "存在库存不足商品"
	//
	//	} else {
	//		res.Skus = append(res.Skus, uint(item.ProductId))
	//	}
	//}

	return
}

type SkuItem struct {
	SkuNo    string `json:"skuNo"`
	Quantity int    `json:"quantity"`
}
type StokRes struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	Content struct {
		List  []SkuItem `json:"list"`
		Total int       `json:"total"`
	} `json:"content"`
}

// 商品是否可售前置校验
func (y *CrossSupply) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.ResSaleBeforeCheck) {

	for _, itemA := range request.Skus {
		res.Data.Available = append(res.Data.Available, uint(itemA.Sku.Sku))
	}

	return

}

// 确认下单
func (y *CrossSupply) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	y.ReqData.Del("sign")
	log.Log().Info("cross供应链商品准备下单", zap.Any("info", request))
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description
	log.Log().Info("cross供应链商品准备下单全地址", zap.Any("info", address))
	var skuList []SkuItem

	for _, skuItem := range request.Skus {
		strSku := strconv.Itoa(int(skuItem.Sku.Sku))
		skuList = append(skuList, SkuItem{
			SkuNo:    strSku,
			Quantity: skuItem.Number,
		})
	}

	var reqMaps = make(map[string]interface{})
	y.ReqData.Set("method", "order.save")
	reqMaps["channelNo"] = y.ShopId
	reqMaps["outNo"] = request.OrderSn.OrderSn
	reqMaps["receiverName"] = request.RequestSaleBeforeCheck.Address.Consignee
	reqMaps["mobPhone"] = request.RequestSaleBeforeCheck.Address.Phone
	reqMaps["provinceName"] = request.RequestSaleBeforeCheck.Address.Province
	reqMaps["cityName"] = request.RequestSaleBeforeCheck.Address.City
	reqMaps["areaName"] = request.RequestSaleBeforeCheck.Address.Area
	reqMaps["address"] = request.RequestSaleBeforeCheck.Address.Description
	reqMaps["goodsList"] = skuList
	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	fmt.Println("json", string(reqJson))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)
	url := common.YZH_HTTP_URL
	var resData []byte
	err, resData = utils.PostForm(url, y.ReqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}

	var orderRes OrderRes
	err = json.Unmarshal(resData, &orderRes)
	if err != nil {
		return err, nil
	}

	log.Log().Info("cross 下单请求返回结果", zap.Any("info", string(resData)))

	if orderRes.Content.OrderNo != "" {
		var order model3.Order
		order.GatherSupplySN = orderRes.Content.OrderNo
		order.GatherSupplyType = 5
		err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
		if err != nil {
			log.Log().Info("cross 保存三方单号失败", zap.Any("info", err))
		}
	}

	return
}

type OrderRes struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	Content struct {
		OutNo   string `json:"outNo"`
		OrderNo string `json:"orderNo"`
	} `json:"content"`
}

func (y *CrossSupply) OrderDetail(orderSn string) (err error, orderDetail ResCrossOrderDetail) {

	y.ReqData.Del("sign")
	y.ReqData.Set("method", "order.query")
	var reqMaps = make(map[string]interface{})
	reqMaps["channelNo"] = y.ShopId
	reqMaps["orderNo"] = orderSn

	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	fmt.Println("json", string(reqJson))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)
	url := common.YZH_HTTP_URL
	var resData []byte
	err, resData = utils.PostForm(url, y.ReqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}

	var crossOrderDetail ResCrossOrderDetail
	err = json.Unmarshal(resData, &crossOrderDetail)
	if err != nil {
		return
	}
	return
}

// 物流查询
func (y *CrossSupply) ExpressQuery(request request.RequestExpress) (err error, data interface{}) {

	y.ReqData.Del("sign")
	y.ReqData.Set("method", "order.query")
	var reqMaps = make(map[string]interface{})
	reqMaps["channelNo"] = y.ShopId
	reqMaps["orderNo"] = request.OrderSn

	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	fmt.Println("json", string(reqJson))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)
	url := common.YZH_HTTP_URL
	var resData []byte
	err, resData = utils.PostForm(url, y.ReqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}

	var crossOrderDetail ResCrossOrderDetail
	err = json.Unmarshal(resData, &crossOrderDetail)
	if err != nil {
		return err, nil
	}

	fmt.Println(string(resData))
	expresInfo := make(map[string]string)
	status := strconv.Itoa(crossOrderDetail.Content.List[0].OrderState)
	expresInfo["status"] = status
	if crossOrderDetail.Content.List[0].OrderState == 55 {

		name := crossOrderDetail.Content.List[0].LogisticsInfoDtoList[0].LogisticsName
		err, expresInfo["code"] = ExpressList(name)
		expresInfo["no"] = crossOrderDetail.Content.List[0].LogisticsInfoDtoList[0].LogisticsNo

	}

	data = expresInfo
	return

}

type ResCrossOrderDetail struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	Content struct {
		List []struct {
			OutNo              string      `json:"outNo"`
			OrderNo            string      `json:"orderNo"`
			OrderTotalAmount   string      `json:"orderTotalAmount"`
			TaxesTotalAmount   string      `json:"taxesTotalAmount"`
			FreightTotalAmount string      `json:"freightTotalAmount"`
			GoodsTotalAmount   string      `json:"goodsTotalAmount"`
			OrderState         int         `json:"orderState"`
			OrderStateDesc     string      `json:"orderStateDesc"`
			Quantity           int         `json:"quantity"`
			OrderRemark        interface{} `json:"orderRemark"`
			CreateTime         string      `json:"createTime"`
			OrderGoodsList     []struct {
				GoodsNo            string `json:"goodsNo"`
				SkuNo              string `json:"skuNo"`
				GoodsModel         string `json:"goodsModel"`
				GoodsName          string `json:"goodsName"`
				Quantity           string `json:"quantity"`
				SalePropertiesName string `json:"salePropertiesName"`
				CostPrice          string `json:"costPrice"`
				FreightFee         string `json:"freightFee"`
				TaxesFee           string `json:"taxesFee"`
			} `json:"orderGoodsList"`
			LogisticsInfoDtoList []struct {
				LogisticsName string `json:"logisticsName"`
				LogisticsNo   string `json:"logisticsNo"`
				DeliveryTime  string `json:"deliveryTime"`
			} `json:"logisticsInfoDtoList"`
		} `json:"list"`
		Total int `json:"total"`
	} `json:"content"`
}
