package order

import (
	"public-supply/request"
	"testing"
)

func TestYzh_ImportGoodsRun(t *testing.T) {

}

func TestCrossSupply_ExpressQuery(t *testing.T) {
	y := &CrossSupply{}
	//var info request.GetGoodsSearch
	y.InitSetting(18)
	//var request request.RequestExpress
	//request.OrderSn = "OP2022033000002"
	//y.ExpressQuery(request)

	var requesta request.RequestSaleBeforeCheck

	//var stringSku []string
	var goodsspus request.GoodsSpus
	var goodsspu request.GoodsSpu

	goodsspu.Sku.Sku = 870000099456998
	goodsspu.Number = 1
	goodsspus = append(goodsspus, goodsspu)

	//
	requesta.Skus = goodsspus
	//

	y.OrderBeforeCheck(requesta)
}
