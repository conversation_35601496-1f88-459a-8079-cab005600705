package public

import (
	"bytes"
	catemodel "category/model"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"github.com/gookit/color"
	"github.com/xingliuhua/leaf"
	"gorm.io/gorm"

	url2 "net/url"
	pmodel "product/model"
	pMq "product/mq"
	"public-supply/model"
	setting2 "public-supply/setting"
	"sort"
	"strconv"
	"strings"
	"sync"
	"yz-go/source"
)

func base64Encode(src []byte) []byte {
	return []byte(base64.StdEncoding.EncodeToString(src))
}

func GetSign(params url2.Values, privateKey string) (sign string) {
	//jsonParams, _ := json.Marshal(params)
	//jsonStr := string(jsonParams)
	//sortStr := strings.Split(jsonStr, "")
	//sort.Strings(sortStr)
	var keys []string
	b := bytes.Buffer{}
	for k, _ := range params {

		keys = append(keys, k)
	}

	sort.Strings(keys)

	for _, v := range keys {
		b.WriteString(v)
		b.WriteString("=")
		b.WriteString(params.Get(v))
		b.WriteString("&")
	}
	p := b.String()
	p = strings.TrimRight(p, "&")
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

	fmt.Println(string(p))
	dataSign := RsaSign(p, privateKey, crypto.SHA256)
	//sign1:=RsaSign("","",crypto.SHA256)

	//fmt.Println(sign1)
	//var str string
	//for _, s := range sortStr {
	//	str += s
	//}

	//debyte := base64Encode([]byte(dataSign))
	sign = dataSign
	return
}

const (
	PEM_BEGIN = "-----BEGIN RSA PRIVATE KEY-----\n"
	PEM_END   = "\n-----END RSA PRIVATE KEY-----"
)

func RsaSign(signContent string, privateKey string, hash crypto.Hash) string {
	shaNew := hash.New()
	shaNew.Write([]byte(signContent))
	hashed := shaNew.Sum(nil)
	priKey, err := ParsePrivateKey(privateKey)
	if err != nil {
		//panic(err)
		fmt.Println("rsa sign 失败", err)
	}

	signature, err := rsa.SignPKCS1v15(rand.Reader, priKey, hash, hashed)
	if err != nil {
		//panic(err)
		fmt.Println("SignPKCS1v15 失败", err)
	}
	return base64.StdEncoding.EncodeToString(signature)
}

func ParsePrivateKey(privateKey string) (*rsa.PrivateKey, error) {
	privateKey = FormatPrivateKey(privateKey)
	// 2、解码私钥字节，生成加密对象
	block, _ := pem.Decode([]byte(privateKey))
	if block == nil {
		return nil, errors.New("私钥信息错误！")
	}
	// 3、解析DER编码的私钥，生成私钥对象
	priKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return priKey, nil
}

func FormatPrivateKey(privateKey string) string {
	if !strings.HasPrefix(privateKey, PEM_BEGIN) {
		privateKey = PEM_BEGIN + privateKey
	}
	if !strings.HasSuffix(privateKey, PEM_END) {
		privateKey = privateKey + PEM_END
	}
	return privateKey
}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}

func GetIdArr(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}

func YzhGetIdArr(list []model.CorssSupply) (arrIds []int) {
	//for _, elem := range list {
	//	arrIds = append(arrIds, elem.RESULTDATA.PRODUCTDATA.ProductId)
	//}
	return

}

// 分割数组，根据传入的数组和分割大小，将数组分割为大小等于指定大小的多个数组，如果不够分，则最后一个数组元素小于其他数组
func SplitArray(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func GetPricingPrice(elem model.Goods, key string) (err error, costPrice uint, salePrice, originPrice, activityPrice, guidePrice uint) {
	var dat model.SupplySetting
	err, setting := setting2.GetSetting(key)
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}

	var intX uint64

	if dat.Pricing.Strategy == 2 { //本地定价策略关闭
		if elem.Source == 2 { //京东

			//销售价计算
			if dat.Pricing.JDSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.JDSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.JDSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.JDSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.JDSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.JDSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.JDCostPrice == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.JDCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.JDCostPrice == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.JDCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

		}

		if elem.Source == 1 { //云仓

			//销售价计算
			if dat.Pricing.YCSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
				color.Info.Println("云仓销售指导系数", intX)

			}
			if dat.Pricing.YCSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓销售价协议系数", intX)
			}
			if dat.Pricing.YCSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.YCSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓销售营销系数", intX)

			}
			//销售价计算结束

			//成本价
			if dat.Pricing.YCCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓成本协议系数", intX)

			}
			if dat.Pricing.YCCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓成本营销系数", intX)

			}

			//成本价计算结束

		}

		if elem.Source == 6 { //阿里定价策略

			//销售价计算
			if dat.Pricing.ALSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.ALSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.ALSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.ALSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.ALSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.ALSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.ALCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.ALCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.ALCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.ALCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

		}

		if elem.Source == 7 { //天猫

			//销售价计算
			if dat.Pricing.TMSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.TMSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.TMSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.TMCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

		}

		if elem.Source == 0 { //中台本地

			//销售价计算
			if dat.Pricing.SupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.SupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

		}

		if elem.Source == 100 || elem.Source == 101 { //中台本地

			//供货价

			if dat.Pricing.YzhSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.YzhSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			//建议零售价
			if dat.Pricing.YzhSupplyOrigin == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyOriginAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyOrigin == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyOriginMarketing, 10, 32)
				originPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//建议零售价
			if dat.Pricing.YzhSupplyActivity == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyActivity == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyActivityMarketing, 10, 32)
				activityPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//建议零售价
			if dat.Pricing.YzhSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == 102 { //跨境

			//供货价

			if dat.Pricing.YzhSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.YzhSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			//建议零售价
			if dat.Pricing.YzhSupplyOrigin == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyOriginAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyOrigin == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyOriginMarketing, 10, 32)
				originPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//建议零售价
			if dat.Pricing.YzhSupplyActivity == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyActivity == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyActivityMarketing, 10, 32)
				activityPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//建议零售价
			if dat.Pricing.YzhSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.YzhSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

	} else {
		salePrice = elem.GuidePrice
		costPrice = elem.AgreementPrice
	}

	return

}

var (
	category []model.Category
	mutex    sync.Mutex
)

//获取本地策略价格

func GetArrIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}
func GetSkuPrice(skuList []pmodel.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}

func FindChildCategory(id uint, pid uint) {

	for _, elem := range category {
		var paid uint
		if elem.Source == 2 || elem.Source == 6 || elem.Source == 7 || elem.Source == 1 {
			paid = elem.ThirdId
		}
		if elem.Source == 0 {
			paid = elem.Id
		}

		if elem.ParentId == id {
			var catei = new(catemodel.Category)
			catei.Name = elem.Title
			catei.Level = elem.Level + 1
			catei.ParentID = pid
			catei.Source = elem.Source
			catei.Sort = elem.Sort
			catei.IsDisplay = elem.State
			err, cid := CreateCategory(catei)
			fmt.Println("寻找parentid=", id)
			if id > 0 {

				FindChildCategory(paid, cid)
			}
			if err != nil {
				fmt.Println("插入失败", elem.Title, elem.Id, err)
			}

		}

	}

}

func CreateCategory(category *catemodel.Category) (err error, id uint) {

	var fCategory *catemodel.Category
	err = source.DB().Where("level = ? ", category.Level).Where("name = ?", category.Name).First(&fCategory).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = source.DB().Create(&category).Error

		if err != nil {
			return
		}

	}

	return err, category.ID

}

func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func GetKeyValueB(Values []model.SpecsValue, specValueIds string, speceNameId int) (list string) {

	for _, itemValue := range Values {
		if specValueIds == strconv.Itoa(itemValue.ID) && speceNameId == itemValue.SpecNameID {
			list = itemValue.Name
			return
		}
	}
	return
}
func CreateGoods(goodsList []*pmodel.Product) (err error) {

	for _, goods := range goodsList {

		err = source.DB().Create(&goods).Error
		if err != nil {
			fmt.Println("插入失败", err)
		}

		err = source.DB().Create(&model.SupplyGoods{
			SupplyGoodsID:  goods.SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		}).Error

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)

	}

	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}

type MapEntryHandler func(string, string)
