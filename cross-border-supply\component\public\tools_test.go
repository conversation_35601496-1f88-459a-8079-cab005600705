package public

import (
	url2 "net/url"
	"testing"
	"time"
)

func TestGetSign(t *testing.T) {
	//timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	var reqData = url2.Values{} /*业务公共参数*/
	reqData.Add("app_id", "20220322955876860896477184")
	reqData.Add("method", "user.userinfo.get")
	reqData.Add("version", "1.0")
	reqData.Add("charset", "UTF-8")
	reqData.Add("timestamp", time.Now().Format("2006-01-02 15:04:05"))

	/*业务公共参数*/
	reqData.Add("channelNo", "3046")
	reqData.Add("startTime", time.Now().Format("2006-01-02 15:04:05"))
	reqData.Add("endTime", time.Now().Format("2006-01-02 15:04:05"))
	GetSign(reqData, "")
}
