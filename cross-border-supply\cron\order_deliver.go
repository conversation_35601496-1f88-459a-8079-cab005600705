package cron

import (
	"cross-border-supply/component/order"
	gmodel "cross-border-supply/component/order"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	v1 "order/api/v1"
	omodel "order/model"
	orderRequest2 "order/request"
	"product/model"
	pubmodel "public-supply/model"
	"public-supply/request"
	setting2 "public-supply/setting"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func CrossOrderDeliverCronRun() {
	log.Log().Info("crossOrderDeliverCronRun 开始启动订单发货状态同步", zap.Any("info", "start"))

	var gatherList []model.GatherSupply
	err := source.DB().Where("`category_id` = ?", 5).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}
	log.Log().Info("crossOrderDeliverCronRun 订单发货状态同步", zap.Any("info", err))

	for _, v := range gatherList {
		log.Log().Info("gatherList for ", zap.Any("info", v))

		OrderCronTask(int(uint(v.ID)))
	}

}

var taskMap = make(map[int]bool)

func OrderCronTask(taskID int) {
	log.Log().Info("OrderCronTask  开始", zap.Any("info", "start"))

	fmt.Println("OrderCronTask", taskID)

	var dat pubmodel.SupplySetting
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {

		return
	}

	var cronStr string

	//cronStr = "0 0 0,15,18,21 * * ?"
	cronStr = "*/600 * * * * *"

	cron.PushTask(cron.Task{
		Key:  "crossorderdeliver" + strconv.Itoa(taskID),
		Name: "cross订单发货定时更新" + strconv.Itoa(taskID),
		Spec: cronStr, //"0/3 * * * * *"
		Handle: func(task cron.Task) {
			OrderTaskRun(taskID)

		},
		Status: cron.ENABLED,
	})

}

var OrdertaskMap = make(map[int]bool)

func OrderTaskRun(taskID int) {
	log.Log().Info("orderdeliver当前状态", zap.Any("info", taskMap[taskID]), zap.Any("info", taskID))

	fmt.Println("orderdeliver当前状态", taskMap[taskID], taskID)
	if OrdertaskMap[taskID] == false {
		OrdertaskMap[taskID] = true
		CrossOrderDeliverNoCron(uint(taskID))
		OrdertaskMap[taskID] = false
	}

}

func CrossOrderDeliverNoCron(gatherSupplyID uint) {
	fmt.Println("开始cross发货检测")
	log.Log().Info("开始cross发货检测", zap.Any("info", "33"))

	var cross order.CrossSupply
	err := cross.InitSetting(gatherSupplyID)
	if err != nil {
		log.Log().Info("cross init 初始化失败", zap.Any("info", err))
		return
	}
	var deliverOrder []omodel.Order
	err = source.DB().Preload("OrderItems").Where("status=? and gather_supply_id=? and gather_supply_sn!=''  ", 1, gatherSupplyID).Find(&deliverOrder).Error
	log.Log().Info("开始cross发货检测  crossOrderDeliverNoCron", zap.Any("info", deliverOrder))

	if err != nil {
		log.Log().Info("供应链代发货订单查询", zap.Any("info", err))
		return
	}

	for _, od := range deliverOrder {
		var orderDetail gmodel.ResCrossOrderDetail
		log.Log().Info("开始cross发货检测333  crossOrderDeliverNoCron", zap.Any("info", od.GatherSupplySN))

		//err, orderDetail = cross.ExpressQuery(od.GatherSupplySN)
		log.Log().Info("开始cross发货检测-OrderDetail", zap.Any("info", orderDetail))

		var data interface{}
		var request request.RequestExpress
		request.OrderSn = od.GatherSupplySN
		err, data = cross.ExpressQuery(request)
		log.Log().Info("开始cross发货检测-ExpressQuery 000", zap.Any("info", data))

		if data == nil {
			log.Log().Error("cross 查询订单物流code 错误，data nil")
			continue
		}

		info := data.(map[string]string)
		if err != nil {
			log.Log().Info("开始cross发货检测-err错误", zap.Any("info", err))
			continue
		}

		if info["status"] != "55" {
			log.Log().Info("开始cross发货检测-未发货", zap.Any("info", info))
			continue
		}

		if info["no"] == "" {
			log.Log().Info("开始cross发货检测-快递号空", zap.Any("info", orderDetail))
			continue
		}
		var orderRequest v1.HandleOrderRequest
		var orderItemIds []orderRequest2.OrderItemSendInfo
		var orderItems []omodel.OrderItem
		err = source.DB().Debug().Model(omodel.OrderItem{}).Where("order_id=?", od.ID).Find(&orderItems).Error

		log.Log().Error("cross 查询订单orderItem listarr", zap.Any("info", orderItems))

		if err != nil {
			log.Log().Error("cross 查询订单orderItem 错误", zap.Any("info", err))
			continue
		}
		for _, orderItem := range orderItems {
			orderItemIds = append(orderItemIds, orderRequest2.OrderItemSendInfo{
				ID:  orderItem.ID,
				Num: orderItem.Qty,
			})
		}
		orderRequest.OrderID = od.ID
		orderRequest.ExpressNo = info["no"]
		orderRequest.OrderItemIDs = orderItemIds
		orderRequest.CompanyCode = info["code"]
		log.Log().Info("cross 发货信息", zap.Any("info", orderRequest))
		err = order.ExpressSent(orderRequest)
		if err != nil {
			log.Log().Error("cross ExpressSent", zap.Any("info", err))
		}
	}

}
