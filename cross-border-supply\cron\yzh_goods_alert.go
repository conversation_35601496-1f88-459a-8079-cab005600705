package cron

import (
	common2 "cross-border-supply/common"
	"cross-border-supply/component/order"
	cpub "cross-border-supply/component/public"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"product/model"
	"product/mq"
	model2 "public-supply/model"
	"strconv"
	"yz-go/cron"

	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

func PushCrossGoodsAlertSaleHandle() {
	log.Log().Info("cron更新cross商品上下架状态start")

	var gatherList model.GatherSupply
	err := source.DB().Where("category_id = ?", 5).Where("deleted_at is null").First(&gatherList).Error
	if err != nil {
		return
	}

	gatherID = uint(gatherList.ID)
	CreateCronTaskSale()

}

func CreateCronTaskSale() {
	log.Log().Info("cron更新cross商品上下架状态CreateCronTaskSale")

	var cronStr string
	cronStr = "0 0 01 * * ?"
	//cronStr = "0/5 * * * * *"
	cron.PushTask(cron.Task{
		Key:  "crossgoodsaleupdate",
		Name: "cross商品sale定时更新任务",
		Spec: cronStr, //"0/3 * * * * *"
		Handle: func(task cron.Task) {
			CrossStockCheck()

		},
		Status: cron.ENABLED,
	})

}

var gatherID uint
var AllProduct = make(map[int]int)
var AllProductStatus = make(map[int]int)

func CrossStockCheck() {

	//fmt.Println("cross任务", 333444)
	//fmt.Println("cross任务", gatherID)
	//return
	AllProduct = make(map[int]int)
	AllProductStatus = make(map[int]int)

	var products []model.Product

	source.DB().Select("id,source_goods_id,is_display").Where("source=?", 102).Find(&products)
	if len(products) == 0 {
		return
	}
	var skuids []int
	for _, crossItem := range products {
		skuNo := crossItem.SourceGoodsID
		skuids = append(skuids, int(skuNo))
		AllProduct[int(skuNo)] = int(crossItem.ID)
		AllProductStatus[int(skuNo)] = crossItem.IsDisplay
	}
	list := cpub.SplitArray(skuids, 100)
	for _, item := range list {
		CrossStokePost(item)
	}

}

func SynergeticProcess(item []int) (err error) {

	var y = order.CrossSupply{}

	y.InitSetting(18)

	y.ReqData.Del("sign")
	var reqMaps = make(map[string]interface{})
	y.ReqData.Set("method", "goods.query")

	/*业务公共参数*/
	//startTime:=time.Now().Format("2006-01-02 15:04:05")
	//t := time.Now().Unix()
	//t += 86400 //增加一天
	//endTime:=time.Unix(t, 0).Format("2006-01-02 15:04:05")

	reqMaps["channelNo"] = y.ShopId
	//reqMaps["startTime"] = cross_startTime
	//reqMaps["endTime"] = cross_endTime
	reqMaps["skuNos"] = item
	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)

	url := string(common2.YZH_HTTP_URL)
	var resData []byte
	err, resData = utils.PostForm(url, y.ReqData, nil)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}
	fmt.Println("", string(resData))
	var goodsDetail model2.CorssSupply
	err = json.Unmarshal(resData, &goodsDetail)
	if err != nil {
		log.Log().Error("解析错误", zap.Any("err", err))
	}
	fmt.Println(string(resData))

	return
}
func CrossStokePost(skuList []int) (err error) {
	var y = order.CrossSupply{}

	y.InitSetting(gatherID)
	y.ReqData.Del("sign")
	var reqMaps = make(map[string]interface{})
	y.ReqData.Set("method", "stock.query")
	reqMaps["channelNo"] = y.ShopId
	reqMaps["skuNos"] = skuList
	reqMaps["pageNo"] = 1
	reqMaps["pageSize"] = 100
	reqJson, _ := json.Marshal(reqMaps)
	y.ReqData.Set("biz_content", string(reqJson))
	sign := cpub.GetSign(y.ReqData, y.Dat.BaseInfo.AppSecret)
	y.ReqData.Set("sign", sign)
	url := common2.YZH_HTTP_URL
	var resData []byte
	err, resData = utils.PostForm(string(url), y.ReqData, nil)

	fmt.Println(y.ReqData)
	if err != nil {
		log.Log().Error("请求错误", zap.Any("err", err))
	}
	var stokRes order.StokRes
	err = json.Unmarshal(resData, &stokRes)
	if err != nil {
		log.Log().Info("下架跨境商品解析库存数据失败", zap.Any("info", string(resData)))
		return
	}

	if stokRes.Code == "0" && stokRes.Msg == "success" {
		var EmptySku, StockSku []int
		for _, skuItem := range stokRes.Content.List {
			skuID, _ := strconv.Atoi(skuItem.SkuNo)
			if skuItem.Quantity == 0 {
				if AllProductStatus[skuID] == 1 {
					EmptySku = append(EmptySku, skuID)
				}
			} else {
				if AllProductStatus[skuID] == 0 {
					StockSku = append(StockSku, skuID)
				}
			}
		}
		if len(EmptySku) > 0 {
			var colum = make(map[string]interface{})
			colum["is_display"] = 0
			log.Log().Info("下架跨境商品", zap.Any("info", EmptySku))
			source.DB().Table("products").Where("source=? and source_goods_id in (?)", 102, EmptySku).UpdateColumns(&colum)
			for _, item := range EmptySku {
				AutoSaleProduct(1, uint(AllProduct[item]))
			}
		}

		if len(StockSku) > 0 {
			var colum = make(map[string]interface{})
			colum["is_display"] = 1
			log.Log().Info("上架跨境商品", zap.Any("info", StockSku))
			source.DB().Table("products").Where("source=? and source_goods_id in (?)", 102, StockSku).UpdateColumns(&colum)
			for _, item := range StockSku {
				AutoSaleProduct(2, uint(AllProduct[item]))
			}
		}

	}

	return
}

func AutoSaleProduct(SaleType, id uint) (err error) {
	var productMessageType mq.ProductMessageType //队列消息类型
	if SaleType == 1 {
		productMessageType = mq.Undercarriage
	} else if SaleType == 2 {
		productMessageType = mq.OnSale
	}
	err = mq.PublishMessage(id, productMessageType, 0)
	if err != nil {
		return
	}

	return

}

//import (
//	"cross-border-supply/component/goods"
//	"encoding/json"
//	"fmt"
//	"go.uber.org/zap"
//	"product/model"
//	pubmodel "public-supply/model"
//	setting2 "public-supply/setting"
//	"strconv"
//	"yz-go/component/log"
//	"yz-go/cron"
//	"yz-go/source"
//)
//
//func PushYzhGoodsAlertHandle() {
//	var gatherList []model.GatherSupply
//	err := source.DB().Where("category_id = ?", 4).Where("deleted_at is null").Find(&gatherList).Error
//	if err != nil {
//		return
//	}
//
//	for _, v := range gatherList {
//
//		CreateCronTask(v.ID)
//
//	}
//}
//
//func CreateCronTask(taskID int) {
//
//	fmt.Println("CreateCronTask", taskID)
//
//	var dat pubmodel.SupplySetting
//	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(taskID))
//	if err != nil {
//		fmt.Println("获取供应链key设置失败")
//		return
//	}
//
//	err = json.Unmarshal([]byte(setting.Value), &dat)
//	if err != nil {
//
//		return
//	}
//
//	var cronStr string
//	if dat.UpdateInfo.Cron != "" {
//		cronStr = dat.UpdateInfo.Cron
//	} else {
//		cronStr = "0 0 03,19 * * ?"
//	}
//
//	cron.PushTask(cron.Task{
//		Key:  "yzhgoodsalert" + strconv.Itoa(taskID),
//		Name: "YZH商品定时更新" + strconv.Itoa(taskID),
//		Spec: cronStr, //"0/3 * * * * *"
//		Handle: func(task cron.Task) {
//			TaskRun(taskID)
//
//		},
//		Status: cron.ENABLED,
//	})
//
//}
//
//var taskMap = make(map[int]bool)
//
//func TaskRun(taskID int) {
//	fmt.Println("当前状态", taskMap[taskID])
//	if taskMap[taskID] == false {
//		taskMap[taskID] = true
//		YzhGoodsUpdate(uint(taskID))
//		taskMap[taskID] = false
//	}
//
//}
//
////func YzhGoodsAlertCronRun() {
////
////	var gatherList []model.GatherSupply
////	err := source.DB().Where("`category_id` = ?", 4).Find(&gatherList).Error
////	if err != nil {
////		return
////	}
////
////	for _, v := range gatherList {
////		YzhGoodsUpdate(uint(v.ID))
////	}
////
////}
//
//func YzhGoodsUpdate(supplyId uint) (err error) {
//	var y = new(goods.Yzh)
//
//	err = y.InitSetting(supplyId)
//	if err != nil {
//		return err
//	}
//
//	var yzhProduct []model.Product
//	err = source.DB().Where("source =100    and gather_supply_id=?", supplyId).Find(&yzhProduct).Error
//	if err != nil {
//		return
//	}
//	if len(yzhProduct) > 0 {
//
//		for _, item := range yzhProduct {
//			if item.ID == 0 {
//				return
//			}
//
//			var yzhGoods pubmodel.YzhProduct
//			err = source.DB().Where("product_id=?", item.SourceGoodsID).First(&yzhGoods).Error
//			if err != nil {
//				log.Log().Error("查询yzh商品数据错误", zap.Any("err", err))
//				continue
//			}
//			var detail pubmodel.YzhGoodsDetail
//			detail.RESULTDATA.PRODUCTDATA = yzhGoods
//			detail.CategoryNames = yzhGoods.CateNames
//			//var goodsDetail []*model.Product
//			var cateId1, cateId2, cateId3 int
//			if detail.RESULTDATA.PRODUCTDATA.ID == 0 {
//				return
//			}
//			go y.CommodityAssemblyLocal(detail, cateId1, cateId2, cateId3)
//
//		}
//
//	}
//
//	return
//
//}
