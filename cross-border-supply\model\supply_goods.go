package model

import (
	"product/model"
	"yz-go/source"
)

type YzhAllGoodsId struct {
	RESPONSESTATUS string             `json:"RESPONSE_STATUS"`
	TOTALAMOUNT    int                `json:"TOTAL_AMOUNT"`
	RESULTDATA     map[string][]int64 `json:"RESULT_DATA"`
}

type GoodsStock struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		ProductId   int  `json:"product_id"`
		StockNum    uint `json:"stock_num"`
		StockStatus bool `json:"stock_status"`
	} `json:"RESULT_DATA"`
}

type YzhProduct struct {
	source.Model
	ProductId      int     `json:"productId"`
	Name           string  `json:"name"`
	Type           string  `json:"type"`
	ThumbnailImage string  `json:"thumbnailImage"`
	Brand          string  `json:"brand"`
	ProductCate    int     `json:"productCate"`
	ProductCode    string  `json:"productCode"`
	Status         string  `json:"status"`
	MarketPrice    float32 `json:"marketPrice"`
	RetailPrice    float32 `json:"retailPrice"`
	ProductPlace   string  `json:"productPlace"`
	//	Features           string        `json:"features"`
	Hot                bool          `json:"hot"`
	CreateTime         string        `json:"createTime"`
	Is7ToReturn        bool          `json:"is7ToReturn"`
	ProductImage       model.Gallery `json:"productImage" gorm:"type:text"`
	ProductDescription string        `json:"productDescription" gorm:"type:text"`
	Cate1              uint          `json:"cate1" `
	Cate2              uint          `json:"cate2" `
	Cate3              uint          `json:"cate3" `
	CateNames          string        `json:"cate_names" `
	ActivityRate       float64       `json:"activity_rate" form:"activity_rate"`
}

type PRODUCTIMAGE struct {
	ImageUrl  string `json:"imageUrl"`
	OrderSort int    `json:"orderSort"`
}

type RESULTANTS struct {
	PRODUCTDATA        YzhProduct     `json:"PRODUCT_DATA"`
	PRODUCTIMAGE       []PRODUCTIMAGE `json:"PRODUCT_IMAGE"`
	PRODUCTDESCRIPTION string         `json:"PRODUCT_DESCRIPTION"`
}
type YzhGoodsDetail struct {
	RESPONSESTATUS string     `json:"RESPONSE_STATUS"`
	RESULTDATA     RESULTANTS `json:"RESULT_DATA"`
	CategoryNames  string     `json:"category_names"`
}

type RESULTDATA struct {
	ID       uint   `json:"id"`
	Code     uint   `json:"code"`
	Name     string `json:"name"`
	ParentId uint   `json:"parentId"`
	Level    int    `json:"level"`
	Status   bool   `json:"status"`
}

type YzhCateGory struct {
	RESPONSESTATUS string       `json:"RESPONSE_STATUS"`
	RESULTDATA     []RESULTDATA `json:"RESULT_DATA"`
}
type YzhCateGoryDetail struct {
	RESPONSESTATUS string     `json:"RESPONSE_STATUS"`
	RESULTDATA     RESULTDATA `json:"RESULT_DATA"`
}

type SaleStatus struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     []struct {
		Status    bool   `json:"status"`
		Message   string `json:"message"`
		ProductID uint   `json:"product_id"`
	} `json:"RESULT_DATA"`
}

type StockNumber struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     []struct {
		ProductId   int  `json:"product_id"`
		StockStatus bool `json:"stock_status"`
	} `json:"RESULT_DATA"`
}
