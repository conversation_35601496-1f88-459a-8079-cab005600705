package model

type OrderEntry struct {
	ProductId int     `json:"product_id"`
	Num       int     `json:"num"`
	SoldPrice float64 `json:"sold_price"`
}

type OrderResultData struct {
	OrderSplit        bool         `json:"order_split"`
	OrderKey          string       `json:"order_key"`
	OrderTotalPrice   float64      `json:"order_total_price"`
	OrderProductPrice float64      `json:"order_product_price"`
	OrderShipmentFee  float64      `json:"order_shipment_fee"`
	OrderEntry        []OrderEntry `json:"order_entry"`
}

type OrderRes struct {
	RESPONSESTATUS string          `json:"RESPONSE_STATUS"`
	RESULT_DATA    OrderResultData `json:"RESULT_DATA"`
}

type ReqAddress struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		NationId   int    `json:"nationId"`
		ProvinceId int    `json:"provinceId"`
		CityId     int    `json:"cityId"`
		CountyId   int    `json:"countyId"`
		TownId     int    `json:"townId"`
		Nation     string `json:"nation"`
		Province   string `json:"province"`
		City       string `json:"city"`
		County     string `json:"county"`
		Town       string `json:"town"`
	} `json:"RESULT_DATA"`
}

type OrderDetail struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		ThirdOrder        string  `json:"third_order"`
		Key               string  `json:"key"`
		Status            string  `json:"status"`
		OrderTotalPrice   float64 `json:"order_total_price"`
		OrderProductPrice float64 `json:"order_product_price"`
		OrderShipmentFee  float64 `json:"order_shipment_fee"`
		ReceiverName      string  `json:"receiver_name"`
		Province          int     `json:"province"`
		City              int     `json:"city"`
		County            int     `json:"county"`
		Town              int     `json:"town"`
		Address           string  `json:"address"`
		Mobile            string  `json:"mobile"`
		Email             string  `json:"email"`
		Remark            string  `json:"remark"`
		CreateTime        string  `json:"create_time"`
		OrderEntry        []struct {
			ProductId int     `json:"product_id"`
			Num       int     `json:"num"`
			SoldPrice float64 `json:"sold_price"`
		} `json:"order_entry"`
		CHILDORDERS []struct {
			ThirdOrder        string  `json:"third_order"`
			Key               string  `json:"key"`
			Status            string  `json:"status"`
			OrderTotalPrice   float64 `json:"order_total_price"`
			OrderProductPrice float64 `json:"order_product_price"`
			OrderShipmentFee  float64 `json:"order_shipment_fee"`
			ReceiverName      string  `json:"receiver_name"`
			Province          int     `json:"province"`
			City              int     `json:"city"`
			County            int     `json:"county"`
			Town              int     `json:"town"`
			Address           string  `json:"address"`
			Mobile            string  `json:"mobile"`
			Email             string  `json:"email"`
			Remark            string  `json:"remark"`
			CreateTime        string  `json:"create_time"`
			OrderEntry        []struct {
				ProductId int     `json:"product_id"`
				Num       int     `json:"num"`
				SoldPrice float64 `json:"sold_price"`
			} `json:"order_entry"`
		} `json:"CHILD_ORDERS"`
	} `json:"RESULT_DATA"`
}

type OrderTrack struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		ThirdOrder    string `json:"third_order"`
		ShipmentName  string `json:"shipment_name"`
		ShipmentOrder string `json:"shipment_order"`
	} `json:"RESULT_DATA"`
}
