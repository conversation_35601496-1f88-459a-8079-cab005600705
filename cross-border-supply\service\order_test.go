package service

//
//import (
//	"fmt"
//	"yunzhonghe-supply/component/order"
//	"yunzhonghe-supply/request"
//
//	"testing"
//)
//
//func TestGoodsStorageAdd(t *testing.T) {
//	TestInitSetting()
//
//	//ExpressCode("京东国际快递")
//	//
//	//return
//
//	var request request.RequestExpress
//	request.OrderSn = "180391279062"
//	request.Sku = "3235816"
//
//	//str := strconv.Quote("{\"orderSn\":\"180144346038\"}")
//
//	var orderClass = order.NewOrder("stbz")
//
//	_, aaa := orderClass.ExpressQuery(request)
//
//	fmt.Println(aaa)
//	//var productSourceId []int64
//	//source.DB().Model(model.Product{}).Where("source_goods_id >0 and gather_supply_id=1").Pluck("source_goods_id", &productSourceId)
//	//
//	//if len(productSourceId) > 0 {
//	//	var GoodsStorage []int64
//	//	source.DB().Model(model2.GoodsStorage{}).Where("supply_id=1").Pluck("source_goods_id", &GoodsStorage)
//	//	difference := collection.Collect(productSourceId).Diff(GoodsStorage).ToIntArray()
//	//
//	//	if len(difference) <= 0 {
//	//
//	//		return
//	//	}
//	//	listArr := SplitArray(difference, 30)
//	//	for _, arrItem := range listArr {
//	//		fmt.Println(arrItem)
//	//		_, info := GoodsStorageAdd(arrItem, 1)
//	//		fmt.Println(info)
//	//	}
//	//
//	//}
//
//}
