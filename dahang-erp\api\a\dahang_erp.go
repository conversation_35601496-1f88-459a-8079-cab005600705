package a

import (
	"dahang-erp/common"
	"dahang-erp/model"
	"dahang-erp/request"
	"dahang-erp/service"
	"dahang-erp/setting"
	"encoding/base64"
	"encoding/json"
	"errors"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"path"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// @Tags 大行ERP
// @Summary 获取大行ERP配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取大行ERP配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func GetSysDaHangErpSetting(c *gin.Context) {
	var sys setting.SysSetting
	_ = c.ShouldBindJSON(&sys)
	err, daHangErpSetting := setting.GetSysDaHangErpSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var configData interface{}
	jsonConfig, _ := json.Marshal(&daHangErpSetting)
	configData = base64.StdEncoding.EncodeToString(jsonConfig)

	yzResponse.OkWithData(configData, c)

}

// @Tags 大行ERP
// @Summary 保存 大行ERP配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存获取大行ERP配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/saveSysDaHangErpSetting [get]
func SaveSysDaHangErpSetting(c *gin.Context) {
	var saveSaveSysDaHangErpSetting request.SaveSaveSysDaHangErpSetting
	_ = c.ShouldBindJSON(&saveSaveSysDaHangErpSetting)

	var sys setting.SysSetting

	sysByte, err := base64.StdEncoding.DecodeString(saveSaveSysDaHangErpSetting.Data)
	if err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	_ = json.Unmarshal(sysByte, &sys)

	sys.Key = setting.Key
	if sys.Value.Username != "" {
		if sys.Value.Username == "" {
			yzResponse.FailWithMessage("请配置Username", c)
			return
		}
		if sys.Value.Password == "" {
			yzResponse.FailWithMessage("请配置Password", c)
			return
		}
		if sys.Value.Url == "" {
			yzResponse.FailWithMessage("请配置请求地址", c)
			return
		}
		sys.Value.Username = strings.TrimSpace(sys.Value.Username)
		sys.Value.Password = strings.TrimSpace(sys.Value.Password)
		sys.Value.Url = strings.TrimSpace(sys.Value.Url)

	}
	err = setting.SaveSysDaHangErpSetting(sys)
	if err != nil {
		yzResponse.FailWithMessage("保存失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithData("保存成功", c)

}

// @Tags 大行ERP
// @Summary 获取大行ERP配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取大行ERP配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func Sync(c *gin.Context) {

	var erpRequest common.ErpRequest
	_ = c.ShouldBindJSON(&erpRequest)
	err := service.Sync(erpRequest)
	if err != nil {
		log.Log().Error("大昌行同步", zap.Any("大昌行同步", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("同步开始，请等待", c)

}

// @Tags 大行ERP
// @Summary 获取大行ERP配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取大行ERP配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func SyncApp(c *gin.Context) {

	var erpRequest common.ErpRequest
	_ = c.ShouldBindJSON(&erpRequest)
	err := service.SyncApp(erpRequest)
	if err != nil {
		log.Log().Error("大昌行同步", zap.Any("大昌行同步", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("同步开始，请等待", c)

}

// @Tags 大行ERP
// @Summary 推送订单到大昌行
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取推送订单到大昌行"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func PushOrder(c *gin.Context) {
	var pushOrder request.PushOrder
	err := c.ShouldBindJSON(&pushOrder)
	if err != nil {
		yzResponse.FailWithMessage("参数错误"+err.Error(), c)
		return
	}
	err, rd := common.Initial()
	if err != nil {
		yzResponse.FailWithMessage("基础设置错误"+err.Error(), c)
		return
	}
	err = service.PushOrder(pushOrder, rd)
	if err != nil {
		yzResponse.FailWithMessage("推送失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithData(pushOrder, c)

}

// @Tags 大行ERP
// @Summary 根据采购端id推送订单到大昌行
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取根据采购端id推送订单到大昌行"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func PushOrderByApplicationId(c *gin.Context) {
	var pushOrder request.PushOrderByApplicationId
	err := c.ShouldBindJSON(&pushOrder)
	if err != nil {
		yzResponse.FailWithMessage("参数错误"+err.Error(), c)
		return
	}
	err = service.PushOrderByApplicationId(pushOrder)
	if err != nil {
		yzResponse.FailWithMessage("推送失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("推送为异步请等待几分钟，请勿短时间重复点击", c)

}

// UploadExcelPushOrder @Tags 大行ERP
// @Summary 根据采购端id推送订单到大昌行
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取根据采购端id推送订单到大昌行"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func UploadExcelPushOrder(c *gin.Context) {
	var _, header, err = c.Request.FormFile("file")
	if err != nil {
		log.Log().Error("接收文件失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("接收文件失败", c)
		return
	}
	err, rd := common.Initial()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//log.Log().Error("aaa", zap.Any("aaa", header))
	ext := path.Ext(header.Filename)

	if ext != ".xlsx" && ext != ".xls" {
		yzResponse.FailWithMessage("上传文件格式不正确", c)
		return
	}
	url, _, err := common.UploadFile(header, c)
	//local := &upload.Local{}
	//_, url, err := local.UploadFile(header)
	if err != nil {
		log.Log().Error("大昌行上传文件失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("上传文件失败", c)
		return
	}

	f, err := excelize.OpenFile("./data/goSupply/" + url)
	if err != nil {
		log.Log().Info("大昌行读取文件失败", zap.Any("msg", err.Error()))
		yzResponse.FailWithMessage("大昌行读取文件失败", c)
		return
	}
	//读取某个表单的所有数据
	rows := f.GetRows("Sheet1")
	if len(rows) == 1 {
		yzResponse.FailWithMessage("请填写需要推送的数据", c)
		return
	}
	if len(rows) > 10 {
		go service.ExcelPushOrder(rows, rd)
	} else {
		err = service.ExcelPushOrder(rows, rd)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		yzResponse.OkWithMessage("推送成功", c)
		return
	}

	log.Log().Error("上传文件!", zap.Any("url", url))
	//err = service.PushOrder(pushOrder)
	//if err != nil {
	//	yzResponse.FailWithMessage("推送失败"+err.Error(), c)
	//	return
	//}
	yzResponse.OkWithMessage("推送为异步请等待一段时间，请勿短时间重复点击", c)

}

// GetDaHangErpProductList @Tags 大行ERP
// @Summary  已同步商品列表
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "已同步商品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getDaHangErpProductList [get]
func GetDaHangErpProductList(c *gin.Context) {
	var pageInfo request.GetDaHangErpProductSearch
	_ = c.ShouldBindQuery(&pageInfo)
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}
	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 10
	}

	err, list, total := service.GetDaHangErpProductList(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// UpdateDahangErpProcudeProductId  @Tags 大行ERP
// @Summary  修改企业购对应的中台商品id
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "修改企业购对应的中台商品id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/updateDahangErpProcudeProductId [get]
func UpdateDahangErpProcudeProductId(c *gin.Context) {
	var pageInfo model.DaHangErpProduct
	_ = c.ShouldBindJSON(&pageInfo)

	err := service.UpdateDahangErpProcudeProductId(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
		return
	}
}

// UpdateDahangErpProcudeProductId  @Tags 大行ERP
// @Summary  修改企业购对应的中台商品id
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "修改企业购对应的中台商品id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/updateDahangErpProcudeProductId [get]
func DeleteDahangErpId(c *gin.Context) {
	var pageInfo model.DaHangErpProduct
	_ = c.ShouldBindJSON(&pageInfo)

	err := service.DeleteDahangErpId(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
		return
	}
}

// GetThirdProductList @Tags 大行ERP
// @Summary  第三方商品列表
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "第三方商品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getDaHangErpProductList [get]
func GetThirdProductList(c *gin.Context) {
	var pageInfo common.ErpRequest
	_ = c.ShouldBindJSON(&pageInfo)
	if pageInfo.Current == 0 {
		pageInfo.Current = 1
	}
	if pageInfo.Size == 0 {
		pageInfo.Size = 10
	}

	err, rd := common.Initial()
	if err != nil {
		yzResponse.FailWithMessage("基础设置错误"+err.Error(), c)
		return
	}
	err, data := rd.GoodsPageList(pageInfo)

	yzResponse.OkWithData(data, c)
	//err, list, total := service.GetDaHangErpProductList(pageInfo)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//} else {
	//	yzResponse.OkWithDetailed(yzResponse.PageResult{
	//		List:     list,
	//		Total:    total,
	//		Page:     pageInfo.Page,
	//		PageSize: pageInfo.PageSize,
	//	}, "获取成功", c)
	//}
}

// GetProductList @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取Product列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
func GetProductList(c *gin.Context) {
	var pageInfo request.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetProductInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// Export  @Tags 大行ERP
// @Summary  导出
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "导出"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getDaHangErpProductList [get]
func Export(c *gin.Context) {
	var pageInfo request.GetDaHangErpProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, total := service.Export(pageInfo)
	if err != nil {
		log.Log().Error("导出失败", zap.Any("err", err))
		yzResponse.FailWithMessage("导出失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("导出"+strconv.FormatInt(total, 10)+"条记录中请等待", c)
	}
}
func ExportProductRecordList(c *gin.Context) {
	var pageInfo request.ProductExportRecordRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, total, data := service.GetProductExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func DeleteProductExportRecord(c *gin.Context) {
	var daHangErpItem model.DaHangErpProductExportRecord
	err := c.ShouldBindJSON(&daHangErpItem)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.DeleteProductExportRecord(daHangErpItem.ID)
	if err != nil {
		yzResponse.FailWithMessage("删除失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)

}

// GetDaHangErpApplicationList @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取Product列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
func GetDaHangErpApplicationList(c *gin.Context) {
	var pageInfo request.DaHangErpApplicationListSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetDaHangErpApplicationList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func DaHangErpItemDelete(c *gin.Context) {
	var daHangErpItem model.DaHangErpItem
	err := c.ShouldBindJSON(&daHangErpItem)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.DaHangErpItemDelete(daHangErpItem.ID)
	if err != nil {
		yzResponse.FailWithMessage("删除失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)

}

func DaHangErpItemSave(c *gin.Context) {
	var daHangErpItem model.DaHangErpItem
	err := c.ShouldBindJSON(&daHangErpItem)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.DaHangErpItemSave(daHangErpItem)
	if err != nil {
		yzResponse.FailWithMessage("修改失败"+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)

}

// GetDaHangErpCustListByEventCode @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProductSearch true "分页获取Product列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
func GetDaHangErpCustListByEventCode(c *gin.Context) {
	var pageInfo request.DaHangErpCustListSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetDaHangErpCustListByEventCode(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
