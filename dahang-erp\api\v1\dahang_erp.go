package v1

import (
	"dahang-erp/common"
	"dahang-erp/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
)

// @Tags 大行ERP
// @Summary 获取大行ERP配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取大行ERP配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /daHangErp/getSysDaHangErpSetting [get]
func Sync(c *gin.Context) {
	var erpRequest common.ErpRequest
	t := time.Now()

	tm2 := time.Date(t.Year(), t.Month(), t.Day()-1, 0, 0, 0, 0, t.Location())
	erpRequest.StartTime = tm2.Format("2006-01-02 15:04:05")
	log.Log().Info("大昌行同步", zap.Any("erpRequest", erpRequest))
	err := service.Sync(erpRequest)

	if err != nil {
		log.Log().Error("大昌行同步", zap.Any("大昌行同步", err))
	}
	//yzResponse.Ok(c)

}
