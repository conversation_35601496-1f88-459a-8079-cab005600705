package common

import (
	"bytes"
	"dahang-erp/setting"
	"encoding/base64"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
	"yz-go/component/log"
)

type APIResult struct {
	Code    int         `json:"code"`
	Success bool        `json:"success"`
	Time    string      `json:"time"`
	TraceID string      `json:"traceId"`
	Msg     string      `json:"msg"`
	Data    interface{} `json:"data"`
}

type RequestUrl string

type RequestData struct {
	Query  map[string]string  `json:"query"`  //请求query参数
	Body   []byte             `json:"body"`   //请求body参数
	Url    string             `json:"Url"`    //请求链接
	Method string             `json:"method"` //请求方式
	Config setting.SysSetting `json:"config"`
	Host   string             `json:"host"` //请求链接
}

const (
	CustomerApi      RequestUrl = "sync/mdm/customer"  //客户主数据
	SupplierApi      RequestUrl = "sync/mdm/supplier"  //供应商主数据
	ItemApi          RequestUrl = "sync/activity/item" //活动项目
	SoApi            RequestUrl = "sync/order/so"      //接收外部订单
	GoodsListApi     RequestUrl = "sync/mdm/item"      //商品列表	GoodsListApi RequestUrl = "sync/mdm/item"      //商品列表
	GoodsListPageApi RequestUrl = "sync/mdm/item/page" //商品列表带分页的 current是页数默认1，size是当页数量默认10

)

func Initial() (err error, res RequestData) {
	err, res.Config = VerifySysShareLiveSetting()
	if err != nil {
		return
	}

	if strings.HasSuffix(res.Config.Value.Url, "/") == false {
		res.Config.Value.Url += "/"
	}
	//res.Host = "https://dchyoupin.elitescloud.com/yst/pur/"
	res.Host = res.Config.Value.Url
	return
}

/*
*
客户主数据
*/
func (rd RequestData) Customer(request ErpRequest) (err error, result *APIResult) {
	rd.Url = string(CustomerApi)
	rd.Body, err = json.Marshal(request)
	result, err = rd.ClientPost()
	return
}

// 供应商data 结构
type SupplierDatas []SupplierData
type SupplierData struct {
	SuppCode   string `json:"suppCode"`
	SuppName   string `json:"suppName"`
	SuppStatus string `json:"suppStatus"`
	OuCode     string `json:"ouCode" form:"ouCode" gorm:"column:ouCode;comment:公司编码;index;"` // 公司编码
	OuName     string `json:"ouName" form:"ouName" gorm:"column:ouName;comment:公司名称;"`       // 公司名称
}

type ErpRequest struct {
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Current   int    `json:"current"` //页码
	Size      int    `json:"size"`    //每页个数   目前只有sync/mdm/item/page 有分页

}

/*
*
供应商主数据
*/
func (rd RequestData) Supplier(request ErpRequest) (err error, data SupplierDatas) {
	rd.Url = string(SupplierApi)
	var result *APIResult
	rd.Body, err = json.Marshal(request)

	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("大昌行erp 请求供应商主数据失败", zap.Any("错误信息", err))
		err = errors.New("请求供应商主数据失败" + err.Error())

		return
	}
	if result.Success == false || result.Code != 200 {
		log.Log().Error("大昌行erp 请求供应商主数据失败", zap.Any("返回信息", result))
		err = errors.New("请求供应商主数据失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	err = json.Unmarshal(resultData, &data)
	if err != nil {
		log.Log().Error("大昌行erp 请求供应商主数据失败：转结构体失败", zap.Any("错误信息", err))
		return
	}
	return
}

type ItemDatas []ItemData

type ItemData struct {
	EventCode string `json:"eventCode"`
	EventName string `json:"eventName"`
	EventType string `json:"eventType"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
	Customers []Customer
	OuCode    string `json:"ouCode" form:"ouCode" gorm:"column:ouCode;comment:公司编码;index;"` // 公司编码
	OuName    string `json:"ouName" form:"ouName" gorm:"column:ouName;comment:公司名称;"`       // 公司名称
}
type Customer struct {
	CustCode  string `json:"custCode"`
	CustName  string `json:"custName"`
	EventCode string `json:"eventCode"`
	EventName string `json:"eventName"`
}

/*
*
活动项目
*/
func (rd RequestData) Item(request ErpRequest) (err error, itemDatas ItemDatas) {
	var result *APIResult
	rd.Url = string(ItemApi)
	rd.Body, err = json.Marshal(request)
	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("大昌行erp 请求活动项目数据失败", zap.Any("错误信息", err))
		err = errors.New("大昌行erp 请求活动项目数据失败" + err.Error())
		return
	}
	if result.Success == false || result.Code != 200 {
		log.Log().Error("大昌行erp 请求活动项目数据失败", zap.Any("返回信息", result))
		err = errors.New("请求活动项目数据失败" + result.Msg)
		return
	}

	resultData, _ := json.Marshal(result.Data)
	err = json.Unmarshal(resultData, &itemDatas)
	if err != nil {
		log.Log().Error("大昌行erp 请求活动项目数据失败：转结构体失败", zap.Any("错误信息", err))
		return
	}
	return
}

type PushOrderRequsets []PushOrderRequset

type PushOrderRequset struct {
	// 收货详细地址
	Address string `json:"address,omitempty"`
	// 市
	City string `json:"city"`
	// 完成时间
	CloseTime string `json:"closeTime"`
	// 客户编码
	CustCode string `json:"custCode"`
	//isRepush=‘Y’

	IsRepush string `json:"isRepush"` //更新时传Y

	OuCode string `json:"ouCode"` //公司编码
	OuName string `json:"ouName"` //公司名称

	// 客户名称
	CustName string `json:"custName"`
	// 发货时间
	DeliveryTime string `json:"deliveryTime"`
	// 发货仓库编码
	DeliveryWhCode *string `json:"deliveryWhCode,omitempty"`
	// 发货仓库名称
	DeliveryWhName *string `json:"deliveryWhName,omitempty"`
	// 区/县
	District string `json:"district"`
	// 员工姓名
	EmpName string `json:"empName,omitempty"`
	// 员工工号
	EmpNumber *string `json:"empNumber,omitempty"`
	// 员工手机号
	EmpPhone string `json:"empPhone,omitempty"`
	// 项目编码
	EventCode string `json:"eventCode"`
	// 项目名称
	EventName string `json:"eventName"`
	// 税收分类编码
	InvoiceCode string `json:"invoiceCode,omitempty"`
	// 开票名称
	InvoiceName string `json:"invoiceName,omitempty"`
	// 商品简码
	ItemAbbr string `json:"itemAbbr,omitempty"`
	// 商品编码
	ItemCode string `json:"itemCode"`
	// 商品名称
	ItemName string `json:"itemName"`
	// 商品来源
	ItemSource string `json:"itemSource"`
	// 行号
	LineNo int64 `json:"lineNo"`
	// 商城单号
	MallOrderNo string `json:"mallOrderNo"`
	// 会员id
	MemberID string `json:"memberId"`
	// 会员昵称
	MemberNickName string `json:"memberNickName"`
	// 订单号
	OrderCode    string `json:"orderCode"`
	ThirdOrderId string `json:"thirdOrderId"` //订单id

	// 订单状态
	OrderStatus string `json:"orderStatus"`
	// 下单时间
	OrderTime string `json:"orderTime"`
	// 订单类型
	OrderType string `json:"orderType"`
	// 实付金额
	PayAmt float64 `json:"payAmt"`
	// 支付方式
	PayMethod string `json:"payMethod"`
	// 支付单号
	PayOrderNo string `json:"payOrderNo,omitempty"`
	// 实付积分
	PayPoints float64 `json:"payPoints"`
	// 支付状态
	PayStatus string `json:"payStatus,omitempty"`
	// 付款时间
	PayTime string `json:"payTime"`
	// 省
	Province string `json:"province"`
	// 采购总价
	PurAmt float64 `json:"purAmt"`
	// 采购单价
	PurPrice float64 `json:"purPrice"`
	// 采购税率
	PurRate int `json:"purRate"`
	// 商品数量
	Qty uint `json:"qty,omitempty"`
	// 联系电话
	RecipientMobile string `json:"recipientMobile"`
	// 收货人姓名
	RecipientName string `json:"recipientName"`
	// 销售总价
	SaleAmt float64 `json:"saleAmt"`
	// 销售单价
	SalePrice float64 `json:"salePrice"`
	// 销售税率
	SaleRate int `json:"saleRate"`
	// 运费
	ShippingFee float64 `json:"shippingFee"`
	// 子订单号
	SonOrderNo string `json:"sonOrderNo"`
	// 来源分类
	SourceCategory string `json:"sourceCategory"`
	// 规格型号
	Spec string `json:"spec"`
	// 供应商编码
	SuppCode string `json:"suppCode"`
	// 供应商名称
	SuppName string `json:"suppName"`
	// 快递信息列表
	ThirdExpressSaveVOS []ThirdExpressSaveVO `json:"thirdExpressSaveVOS"`
	// 商品单位
	Uom string `json:"uom"`
	//是否助农
	IsHelpFarming string `json:"isHelpFarming"`
}

type ThirdExpressSaveVO struct {
	// 快递公司编码
	ExpressCode string `json:"expressCode"`
	// 快递公司名称
	ExpressName string `json:"expressName"`
	// 快递单号
	ExpressNo string `json:"expressNo"`
}

type PushOrderData struct {
	Code    int    `json:"code"`
	Success string `json:"success"`
	Time    string `json:"time"`
	TraceID string `json:"traceId"`
	Msg     string `json:"msg"`
}

/*
*
推送订单/更新订单  -- (参数未写待完成)
*/
func (rd RequestData) PushOrder(pushOrderRequsets PushOrderRequsets) (err error) {
	var result *APIResult
	rd.Url = string(SoApi)
	rd.Body, err = json.Marshal(pushOrderRequsets)
	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("大昌行erp 请求推送订单失败", zap.Any("错误信息", err))
		err = errors.New("大昌行erp 请求推送订单失败" + err.Error())
		return
	}
	if result.Success == false || result.Code != 200 {
		log.Log().Error("大昌行erp 请求推送订单失败", zap.Any("返回信息", result))
		err = errors.New("请求推送订单失败" + result.Msg)
		return
	}

	return
}

// 商品列表结构体
type GoodsListDatas []GoodsListData
type GoodsListData struct {
	ItemCode      string `json:"itemCode"`
	ItemName      string `json:"itemName"`
	ItemAbbr      string `json:"itemAbbr"`
	ItemStatus    string `json:"itemStatus"`
	SuppCode      string `json:"suppCode"`
	SuppName      string `json:"suppName"`
	IsHelpFarming string `json:"isHelpFarming"`
	// 是否第三方编码
	Cat4 string `json:"cat4"` //第三方说不用了
	// 第三方编码
	ItemCateCode2 string `json:"itemCateCode2"` //第三方说不用了
}

/*
*
商品列表
*/
func (rd RequestData) GoodsList(request ErpRequest) (err error, data GoodsListDatas) {
	var result *APIResult
	rd.Url = string(GoodsListApi)
	rd.Body, err = json.Marshal(request)
	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("大昌行erp 请求商品列表数据失败", zap.Any("错误信息", err))
		err = errors.New("请求商品列表数据失败" + err.Error())

		return
	}
	if result.Success == false || result.Code != 200 {
		log.Log().Error("大昌行erp 请求商品列表数据失败", zap.Any("返回信息", result))
		err = errors.New("请求商品列表数据失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	err = json.Unmarshal(resultData, &data)
	if err != nil {
		log.Log().Error("大昌行erp 请求商品列表数据失败：转结构体失败", zap.Any("错误信息", err))
		return
	}
	return
}

type GoodsPageList struct {
	Total          string      `json:"total"`
	AggregatedData interface{} `json:"aggregatedData"`
	Records        []struct {
		ID               string      `json:"id"`
		ItemSupplySource interface{} `json:"itemSupplySource"`
		ItemCode         string      `json:"itemCode"`
		ItemType2        string      `json:"itemType2"`
		ItemType2Name    interface{} `json:"itemType2Name"`
		ItemName         string      `json:"itemName"`
		Spec             string      `json:"spec"`
		GuaranteeDays    int         `json:"guaranteeDays"`
		OuterCode        string      `json:"outerCode"`
		OuterCodeName    interface{} `json:"outerCodeName"`
		ItemName2        string      `json:"itemName2"`
		ItemType         string      `json:"itemType"`
		ItemTypeName     interface{} `json:"itemTypeName"`
		ItemGroup2       interface{} `json:"itemGroup2"`
		ItemStatus       string      `json:"itemStatus"`
		ItemStatusName   interface{} `json:"itemStatusName"`
		ItemStatus2      string      `json:"itemStatus2"`
		ItemStatus2Name  string      `json:"itemStatus2Name"`
		ItemAttr         interface{} `json:"itemAttr"`
		ItemAttr2        string      `json:"itemAttr2"`
		IsHelpFarming    string      `json:"isHelpFarming"`
		LotFlag          bool        `json:"lotFlag"`
		ItemSource       string      `json:"itemSource"`
		ItemSourceName   interface{} `json:"itemSourceName"`
		ItemType3        string      `json:"itemType3"`
		ItemType3Name    interface{} `json:"itemType3Name"`
		ItemType4        string      `json:"itemType4"`
		ItemType4Name    interface{} `json:"itemType4Name"`
		SuppID           string      `json:"suppId"`
		SuppCode         string      `json:"suppCode"`
		SuppName         string      `json:"suppName"`
		SpuCode2         interface{} `json:"spuCode2"`
		SpuCode2Name     string      `json:"spuCode2Name"`
		Es1              interface{} `json:"es1"`
		Es1Name          string      `json:"es1Name"`
		Es2              interface{} `json:"es2"`
		Es2Name          string      `json:"es2Name"`
		Es3              string      `json:"es3"`
		Es3Name          interface{} `json:"es3Name"`
		Brand            string      `json:"brand"`
		BrandName        string      `json:"brandName"`
		Brand2           string      `json:"brand2"`
		Brand2Name       string      `json:"brand2Name"`
		ItemCateCode     string      `json:"itemCateCode"`
		ItemCatePath     []struct {
			ID        string      `json:"id"`
			Pid       string      `json:"pid"`
			Code      interface{} `json:"code"`
			Path      interface{} `json:"path"`
			SortNo    int         `json:"sortNo"`
			Level     int         `json:"level"`
			IsLeaf    interface{} `json:"isLeaf"`
			TreeNodes []struct {
				ID        string      `json:"id"`
				Pid       string      `json:"pid"`
				Code      interface{} `json:"code"`
				Path      interface{} `json:"path"`
				SortNo    int         `json:"sortNo"`
				Level     int         `json:"level"`
				IsLeaf    interface{} `json:"isLeaf"`
				TreeNodes []struct {
					ID           string      `json:"id"`
					Pid          string      `json:"pid"`
					Code         interface{} `json:"code"`
					Path         interface{} `json:"path"`
					SortNo       int         `json:"sortNo"`
					Level        int         `json:"level"`
					IsLeaf       interface{} `json:"isLeaf"`
					TreeNodes    interface{} `json:"treeNodes"`
					ItemCateCode string      `json:"itemCateCode"`
					ItemCateName string      `json:"itemCateName"`
				} `json:"treeNodes"`
				ItemCateCode string `json:"itemCateCode"`
				ItemCateName string `json:"itemCateName"`
			} `json:"treeNodes"`
			ItemCateCode string `json:"itemCateCode"`
			ItemCateName string `json:"itemCateName"`
		} `json:"itemCatePath"`
		StoreCondition     string      `json:"storeCondition"`
		StoreConditionName string      `json:"storeConditionName"`
		TransCondition     string      `json:"transCondition"`
		TransConditionName string      `json:"transConditionName"`
		TaxType            string      `json:"taxType"`
		En1                interface{} `json:"en1"`
		TaxRate            interface{} `json:"taxRate"`
		TaxRateName        string      `json:"taxRateName"`
		TaxRateNo          string      `json:"taxRateNo"`
		TaxRateNoName      interface{} `json:"taxRateNoName"`
		En2                interface{} `json:"en2"`
		TaxRate2           interface{} `json:"taxRate2"`
		TaxRate2Name       string      `json:"taxRate2Name"`
		TaxRateNo2         string      `json:"taxRateNo2"`
		TaxRateNo2Name     interface{} `json:"taxRateNo2Name"`
		Uom                string      `json:"uom"`
		UomName            string      `json:"uomName"`
		Uom2               string      `json:"uom2"`
		Uom2Name           string      `json:"uom2Name"`
		PurcUom            string      `json:"purcUom"`
		PurcUomName        string      `json:"purcUomName"`
		ShipUom            interface{} `json:"shipUom"`
		ShipUomName        string      `json:"shipUomName"`
		PricingUom         string      `json:"pricingUom"`
		PricingUomName     string      `json:"pricingUomName"`
		En3                interface{} `json:"en3"`
		Cat                string      `json:"cat"`
		Remark             interface{} `json:"remark"`
		CreateTime         string      `json:"createTime"`
		Creator            string      `json:"creator"`
		ApplyBomList       interface{} `json:"applyBomList"`
		BomList            interface{} `json:"bomList"`
		ItemCateCode2      string      `json:"itemCateCode2"`
		Cat4               string      `json:"cat4"`
		Cat5               interface{} `json:"cat5"`
		Cat6               interface{} `json:"cat6"`
		Price8             float64     `json:"price8"`
		Price9             float64     `json:"price9"`
		Eb1                bool        `json:"eb1"`
	} `json:"records"`
	Empty    bool `json:"empty"`
	NotEmpty bool `json:"notEmpty"`
}

/*
*
商品列表
*/
func (rd RequestData) GoodsPageList(request ErpRequest) (err error, data GoodsPageList) {
	var result *APIResult
	rd.Url = string(GoodsListPageApi)
	rd.Body, err = json.Marshal(request)
	result, err = rd.ClientPost()
	if err != nil {
		log.Log().Error("大昌行erp 请求商品列表数据失败", zap.Any("错误信息", err))
		err = errors.New("请求商品列表数据失败" + err.Error())

		return
	}
	if result.Success == false || result.Code != 200 {
		log.Log().Error("大昌行erp 请求商品列表数据失败", zap.Any("返回信息", result))
		err = errors.New("请求商品列表数据失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	err = json.Unmarshal(resultData, &data)
	if err != nil {
		log.Log().Error("大昌行erp 请求商品列表数据失败：转结构体失败", zap.Any("错误信息", err))
		return
	}
	return
}

func (rd RequestData) ClientPost() (result *APIResult, err error) {
	client := &http.Client{Timeout: 5 * time.Minute}
	//if Request.Body = nil
	body := bytes.NewReader(rd.Body)

	req, _ := http.NewRequest("POST", rd.Host+rd.Url, body)

	req = rd.SetClentHeader(req)

	resp, err := client.Do(req)

	if err != nil {
		return
	}
	defer resp.Body.Close()

	res, _ := ioutil.ReadAll(resp.Body)
	err = json.Unmarshal(res, &result)

	if err != nil {
		log.Log().Error("JSON异常", zap.Any("err", err), zap.Any("data", rd))
	}

	//log.Log().Info("im返回",zap.Any("Url",rd.Url),zap.Any("result",result))
	return
}

func (rd RequestData) ClientGet() (result *APIResult, err error) {
	client := &http.Client{Timeout: 5 * time.Minute}
	//if Request.Body = nil
	body := bytes.NewReader(rd.Body)

	req, _ := http.NewRequest("GET", rd.Host+rd.Url, body)

	req = rd.SetClentHeader(req)

	resp, err := client.Do(req)

	if err != nil {
		return
	}
	defer resp.Body.Close()

	res, _ := ioutil.ReadAll(resp.Body)
	err = json.Unmarshal(res, &result)

	if err != nil {
		log.Log().Error("JSON异常", zap.Any("err", err), zap.Any("data", rd))
		return
	}

	//log.Log().Info("im返回",zap.Any("Url",rd.Url),zap.Any("result",result))
	return
}

func (rd RequestData) SetClentHeader(request *http.Request) (req *http.Request) {
	authorization := base64.StdEncoding.EncodeToString([]byte(rd.Config.Value.Username + ":" + rd.Config.Value.Password))
	request.Header.Add("Authorization", "Basic "+authorization)
	request.Header.Add("Content-Type", "application/json;charset=utf-8")
	return request
}
