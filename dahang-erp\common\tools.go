package common

import (
	"dahang-erp/setting"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"mime/multipart"
	"os"
	"path"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

/**
  检验基础设置必须参数是否设置
*/
func VerifySysShareLiveSetting() (err error, sysSetting setting.SysSetting) {

	err = source.DB().Where("`key` = ?", setting.Key).First(&sysSetting).Error
	if err != nil {
		err = errors.New("请先配置基础设置")
		return
	}
	if sysSetting.Value.Username == "" {
		err = errors.New("请在基础设置中配置Username")
		return
	}
	if sysSetting.Value.Password == "" {
		err = errors.New("请在基础设置中配置Password")
		return
	}
	if sysSetting.Value.Url == "" {
		err = errors.New("请在基础设置中配置Url")
		return
	}

	return
}
func Fen2Yuan(price uint) float64 {
	d := decimal.New(1, 2) //分除以100得到元

	result := decimal.NewFromInt(int64(price)).DivRound(d, 2).String()
	//result := decimal.NewFromInt(int64(price)).String()

	floatvar, _ := strconv.ParseFloat(result, 64)

	return floatvar
}

type GetShopOrderDataResponse struct {
	Result int    `json:"result"`
	Msg    string `json:"msg"`
	Data   []struct {
		OrderSn    string `json:"order_sn"`
		Price      string `json:"price"`
		GoodsPrice string `json:"goods_price"`
		GoodsTotal int    `json:"goods_total"`
		StatusName string `json:"status_name"`
		PayName    string `json:"pay_name"`
		Nickname   string `json:"nickname"`
		Mobile     string `json:"mobile"`
		Username   string `json:"username"`
		Realname   string `json:"realname"`
		GroupName  string `json:"group_name"`

		OrderItem []struct {
			Total          int    `json:"total"`
			Title          string `json:"title"`
			GoodsPrice     string `json:"goods_price"`
			GoodsCostPrice string `json:"goods_cost_price"`
			Price          string `json:"price"`
			PaymentAmount  string `json:"payment_amount"`
			Point          string `json:"point,omitempty"`
			YzOptionID     uint   `json:"yz_option_id"`
		} `json:"order_item"`
	} `json:"data"`
}

//获取商城订单详情
func GetShopOrderData(orderSns []string, link string) (err error, data GetShopOrderDataResponse) {
	var result []byte
	var requestParams = make(map[string]interface{})
	requestParams["orderSns"] = orderSns
	var head = make(map[string]string)
	head["Content-Type"] = "application/json"
	err, result = utils.Post(link, requestParams, head)
	if err != nil {
		err = errors.New("获取积分抵扣金额，实付金额失败" + err.Error())
		return
	}
	err = json.Unmarshal(result, &data)
	if err != nil {
		err = errors.New("获取积分抵扣金额，实付金额失败:数据格式转换失败" + err.Error())
		return
	}
	if len(data.Data) == 0 {
		err = errors.New("获取积分抵扣金额，实付金额失败,未返回任何数据,访问地址填写错误")
		return
	}

	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@author: [ccfish86](https://github.com/ccfish86)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@object: *Local
//@function: UploadFile
//@description: 上传文件
//@param: file *multipart.FileHeader
//@return: string, string, error

func UploadFile(file *multipart.FileHeader, c *gin.Context) (p string, fullP string, err error) {
	// 读取文件后缀
	//if config.Config().Local.Path == "" {
	configPath := "./data/goSupply/uploads/dachanghangerp"

	// 尝试创建此路径
	mkdirErr := os.MkdirAll(configPath, os.ModePerm)
	if mkdirErr != nil {
		log.Log().Error("function os.MkdirAll() Filed", zap.Any("err", mkdirErr.Error()))
		return "", "", errors.New("function os.MkdirAll() Filed, err:" + mkdirErr.Error())
	}
	ext := path.Ext(file.Filename)
	var fileName = strconv.FormatInt(time.Now().Unix(), 10) + ext
	fmt.Println(configPath + "/" + fileName)
	// 上传文件到指定的路径
	err = c.SaveUploadedFile(file, configPath+"/"+fileName)
	if err != nil {
		return
	}
	filename := fileName
	p = "uploads/dachanghangerp/" + filename
	fullP = config.Config().Local.Host + p
	return p, fullP, nil
}
