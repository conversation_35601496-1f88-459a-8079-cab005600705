package order

import (
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	model4 "product/model"
	callback2 "public-supply/callback"
	"public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	"reflect"
	"time"

	"yz-go/component/log"
	"yz-go/source"
)

type DahangErp struct{}

func (s *DahangErp) GetAllAddress() (err error, data interface{}) {
	return
}

func (*DahangErp) InitSetting(gatherSupplyID uint) (err error) {

	return
}

type BeforeCheckCache struct {
	RequestKey    string
	ErrorCache    error
	ExpiredAt     int64
	ResponseCache response.BeforeCheck
}

func (s *DahangErp) OrderBeforeCheck(requestData request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	err, data = s.orderBeforeCheck(requestData)
	return
}

// 订单前置校验
func (*DahangErp) orderBeforeCheck(requestData request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {

	//for _, item := range requestData.LocalSkus {
	//	var localSku model4.Sku
	//	err = source.DB().Preload(clause.Associations).Where("id=?", item.Sku.Sku).First(&localSku).Error
	//		if err != nil {
	//		err = errors.New("规格不存在"+err.Error())
	//		return
	//	}
	//	if localSku.ProductID == 0 {
	//		err = errors.New("规格已删除")
	//		return
	//	}
	//	if localSku.Product.IsDisplay == 0 {
	//		err = errors.New("商品已下架")
	//		return
	//	}
	//
	//}
	data.Code = 1
	return

}

// 商品是否可售前置校验
func (*DahangErp) SaleBeforeCheck(requestData request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {

	for _, item := range requestData.LocalSkus {
		var localSku model4.Sku
		err = source.DB().Preload(clause.Associations).Where("id=?", item.Sku.Sku).First(&localSku).Error
		if err != nil {
			resData.Data.Ban = append(resData.Data.Ban, uint(item.Sku.Sku))
			continue
		}
		if localSku.ProductID == 0 {
			resData.Data.Ban = append(resData.Data.Ban, uint(item.Sku.Sku))
			continue
		}
		if localSku.Stock < item.Number {
			resData.Data.Ban = append(resData.Data.Ban, uint(item.Sku.Sku))
			continue
		}
		resData.Data.Available = append(resData.Data.Available, uint(item.Sku.Sku))
	}
	resData.IsOriginalSkuID = 1
	fmt.Println("返回可售数据", resData)

	return

}

// 确认下单
func (*DahangErp) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	//var result *stbz.APIResult
	//
	//log.Log().Info("供应链商品准备下单", zap.Any("info", request))
	//result, err = stbz.API(
	//	stbz.Method.POST,
	//	string(common.CONFIRM_ORDER),
	//	map[string]string{},
	//	g.Map{"orderSn": request.OrderSn.OrderSn, "spu": request.Skus, "address": request.Address},
	//)
	//jsonResult, _ := json.Marshal(result)
	//log.Log().Info("stbz供应链商品下单返回：", zap.Any("info", result))
	//log.Log().Info("stbz供应链商品下单返回：", zap.Any("info1", jsonResult))
	//info = result
	//
	//var jsonData []byte
	//jsonData, err = json.Marshal(info)
	//log.Log().Info("stbz 下单回调数据", zap.Any("info", jsonData))
	//log.Log().Info("stbz 下单回调数据1", zap.Any("info1", string(jsonData)))
	//var stbzRes StbzRes
	//err = json.Unmarshal(jsonData, &stbzRes)
	//if err != nil {
	//	return err, nil
	//}
	//log.Log().Info("stbz 下单回调数据2", zap.Any("info1", stbzRes))
	//var order orderModel.Order
	//
	//if result.Code == 1 {
	//	order.GatherSupplySN = request.OrderSn.OrderSn
	//	order.GatherSupplyType = 1
	//} else {
	//	order.GatherSupplyMsg = stbzRes.Msg
	//}
	//err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
	//if err != nil {
	//	log.Log().Info("Stbz 保存三方单号失败", zap.Any("info", err))
	//}
	return
}

type StbzRes struct {
	Code int64        `json:"code"`
	Msg  string       `json:"msg"`
	Data StbzOrderRes `json:"data"`
}

type StbzOrderRes struct {
	sn      string `json:"sn"`
	errorId string `json:"errorId"`
}

// 物流查询
func (*DahangErp) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.EXPRESS),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn, "sku": request.Sku},
	)

	info = result

	return

}

func (*DahangErp) AllOrderList() (err error, deliverOrderList []model.DeliverOrderList) {

	maps := make(map[string]map[string]interface{})
	form := make(map[string]interface{})

	timeLayout := "2006-01-02 15:04:05"
	// string转化为时间，layout必须为 "2006-01-02 15:04:05"
	times, _ := time.Parse(timeLayout, "2021-12-20 08:37:18")
	timee, _ := time.Parse(timeLayout, "2021-12-30 08:37:18")
	timeS := times.Unix()
	timeE := timee.Unix()
	form["from"] = timeS
	form["to"] = timeE

	maps["createdTime"] = form

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		"/v2/order",
		map[string]string{},
		g.Map{"search": maps, "page": 1, "limit": 100},
	)
	resOrderList, Marshalerr := json.Marshal(result.Data)
	if Marshalerr != nil {
		log.Log().Error("查询胜天半子所有订单解析返回错误", zap.Any("err", Marshalerr))
	}
	var orderList model.StbzOrderList
	err = json.Unmarshal(resOrderList, &orderList)
	if err != nil {
		return
	}

	for _, item := range orderList.List {
		for _, Children := range item.Children {
			for _, Goods := range Children.Goods {
				if Goods.DeliverName != "" {
					deliverOrderList = append(deliverOrderList, model.DeliverOrderList{
						OrderSn:     item.ThirdSn,
						Sku:         Goods.Sku,
						DeliverName: Goods.DeliverName,
						DeliverNo:   Goods.DeliverNo,
					})
				}

			}
		}
	}

	fmt.Println("3333")

	return
}

func StructToMapDemo(obj interface{}) map[string]interface{} {
	obj1 := reflect.TypeOf(obj)
	obj2 := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < obj1.NumField(); i++ {
		data[obj1.Field(i).Name] = obj2.Field(i).Interface()
	}
	return data
}

func (*DahangErp) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(common.AFTER_SALE_BEFORE_CHECK),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn.OrderSn, "sku": request.Sku.Sku},
	)

	info = result

	aaa, err := json.Marshal(info)
	fmt.Println(string(aaa))

	return

}

func (*DahangErp) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.GET,
		string(common.AFTER_SALE_PICTURE),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn.OrderSn, "sku": request.Sku, "pictures": request.Pictures},
	)

	info = result

	return

}

func (*DahangErp) AfterSale(request request.AfterSale) (err error, info interface{}) {

	var result *stbz.APIResult
	result, err = stbz.API(
		stbz.Method.POST,
		string(common.AFTER_SALE),
		map[string]string{},
		g.Map{"orderSn": request.OrderSn.OrderSn, "sku": request.Sku.Sku, "num": request.Num, "goodsFee": request.GoodsFee, "logisticFee": request.LogisticFee, "serviceTypeCode": request.ServiceTypeCode,
			"pickTypeCode": request.PickTypeCode, "packageTypeCode": request.PackageTypeCode, "returnTypeCode": request.ReturnTypeCode, "reasonsTypeCode": request.ReasonsTypeCode,
			"reasonsDescription": request.ReasonsDescription, "serviceTime": request.ServiceTime, "vouchers": request.Vouchers, "userInfo": request.UserInfo,
		},
	)

	info = result

	return

}

var Message OrderMessage

type OrderMessage struct {
	MessageType int    `json:"message_type"`
	Sku         string `json:"sku"`
	OrderSN     string `json:"order_sn"`
}

const (
	Sent      int = iota
	Receive   int = iota
	Completed int = iota
	Closed    int = iota
)

// 发货
func (*DahangErp) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	//Message.MessageType = Sent
	//
	//var orderInfo orderModel.Order
	//err = source.DB().Where("order_sn =  ?", OrderData.Data.OrderSn).First(&orderInfo).Error
	//var ids []orderRequest2.OrderItemSendInfo
	//var orderItem orderModel.OrderItem
	//err = source.DB().Where("order_id=? and original_sku_id=?", orderInfo.ID, OrderData.Data.Sku).First(&orderItem).Error
	//if err != nil {
	//	log.Log().Error("发货orderItem异常", zap.Any("info", err.Error()))
	//	return
	//}
	//ids = append(ids, orderRequest2.OrderItemSendInfo{
	//	ID:  orderItem.ID,
	//	Num: orderItem.Qty,
	//})
	//if len(ids) == 0 {
	//	log.Log().Error("order  ids   null 发货错误", zap.Any("info", err.Error()))
	//	return
	//}
	//var orderRequest v1.HandleOrderRequest
	//var info interface{}
	//var code string
	//var requestExpress request.RequestExpress
	//var expressInfo ExpressInfo
	//requestExpress.OrderSn = OrderData.Data.OrderSn
	//requestExpress.Sku = strconv.FormatInt(OrderData.Data.Sku, 10)
	//var orderClass = Stbz{}
	//err = orderClass.InitSetting(orderInfo.GatherSupplyID)
	//if err != nil {
	//	log.Log().Error("OrderDelivery init  初始化失败", zap.Any("info", err.Error()))
	//	return
	//}
	//log.Log().Info("查询物流信息请求数据", zap.Any("info", requestExpress))
	//err, info = orderClass.ExpressQuery(requestExpress)
	//log.Log().Info("查询物流信息返回数据", zap.Any("info", info))
	//if err != nil {
	//	log.Log().Error("查询物流信息错误", zap.Any("info", err.Error()))
	//	return
	//}
	//var jsonData []byte
	//jsonData, err = json.Marshal(info)
	//err = json.Unmarshal(jsonData, &expressInfo)
	//if err != nil {
	//	log.Log().Error("解析物流信息错误1", zap.Any("info", err.Error()))
	//	return
	//}
	//log.Log().Error("查询物流信息返回，解析后数据", zap.Any("info", expressInfo))
	////if expressInfo.Data.Info.No == "" || expressInfo.Data.Info.Name == "" {
	////	log.Log().Error("获取物流信息错误", zap.Any("info", expressInfo))
	////	return
	////}
	//
	//err, code = ExpressList(expressInfo.Data.Info.Name)
	//if err != nil {
	//	log.Log().Error("查询物流信息错误3", zap.Any("info", err.Error()))
	//	return
	//}
	//orderRequest.OrderID = orderInfo.ID
	//orderRequest.ExpressNo = expressInfo.Data.Info.No
	//orderRequest.OrderItemIDs = ids
	//orderRequest.CompanyCode = code
	//err = ExpressSent(orderRequest)
	//if err != nil {
	//	return err
	//}
	//
	//err = callback2.DeleteOrderMsg(OrderData.MsgID)
	//if err != nil {
	//	log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
	//}
	return
}
func (*DahangErp) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}
