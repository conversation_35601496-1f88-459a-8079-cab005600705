module dahang-erp

go 1.21

require (
	application v1.0.0
	gin-vue-admin v1.0.0
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/chenhg5/collection v0.0.0-20200925143926-f403b87088f9
	github.com/gin-gonic/gin v1.6.3
	github.com/gogf/gf v1.16.9
	github.com/shopspring/decimal v1.3.1
	github.com/writethesky/stbz-sdk-golang v1.0.1
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	order v1.0.0
	payment v1.0.0
	product v1.0.0
	public-supply v1.0.0
	shipping v1.0.0
	supplier v1.0.0
	user v1.0.0
	yz-go v1.0.0
)

replace (
	after-sales => ../after-sales
	application => ../application
	category => ../category
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order => ../order
	payment => ../payment
	product => ../product
	public-supply => ../public-supply
	purchase-account => ../purchase-account
	region => ../region
	sales => ../sales
	shipping => ../shipping
	shop => ../shop
	supplier => ../supplier
	surface-single => ../surface-single
	user => ../user
	wechatpay => ../wechat-pay
	wechatpay-go-main => ../wechatpay-go-main
	yz-go v1.0.0 => ../yz-go

)
