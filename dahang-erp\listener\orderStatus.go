package listener

import (
	"dahang-erp/model"
	"dahang-erp/service"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func OrderStatusListener() {

	mq.PushHandles("orderStatusUpdateOrderDaHangErp", func(data mq.OrderMessage) (err error) {
		//订单支付时标识是否需要推送
		if data.MessageType == mq.Paid {
			var order service.Order
			err = source.DB().Where("id = ?", data.OrderID).Preload("OrderItems").Preload("DaHangErpItem").First(&order).Error
			if err != nil {
				//没有订单跳过a
				err = nil
				return
			}
			//代表不是大昌行导入的采购端产生的订单
			if order.DaHangErpItem.ID == 0 {
				log.Log().Error("查询供应商记录表并非大昌行创建的采购端下的订单", zap.Any("order", order.ID))

				return nil
			}
			var daHangErpOrder model.DaHangErpOrder
			err = source.DB().Where("order_id = ?", order.ID).First(&daHangErpOrder).Error

			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("查询记录表非空错误", zap.Any("err", err))
				return nil
			}
			//如果没有记录则保存记录
			if daHangErpOrder.ID == 0 {
				var PushCount int
				var msg string
				err, PushCount, msg = service.CreateDaHangErpOrder(order)
				daHangErpOrder.OrderId = order.ID
				if err != nil {
					daHangErpOrder.Status = -2
					daHangErpOrder.ErrorMsg = err.Error()
				} else {
					daHangErpOrder.Status = 0
					if PushCount == 0 {
						daHangErpOrder.Status = -2
						daHangErpOrder.ErrorMsg = "下单商品都不是大昌行API返回的商品"
					}
					if msg != "" {
						daHangErpOrder.ErrorMsg = msg
					}
				}
				err = source.DB().Create(&daHangErpOrder).Error
				if err != nil {
					log.Log().Error("创建订单记录失败", zap.Any("err", err))
					err = nil
				}
			}
		}
		//订单完成时自动推送
		if data.MessageType == mq.Received {
			//var pushOrder request.PushOrder
			//pushOrder.OrderId = data.OrderID
			//var rd common.RequestData
			//err, rd = common.Initial()
			//if err != nil {
			//	log.Log().Error("推送订单失败-基础设置未设置", zap.Any("err", err), zap.Any("OrderID", data.OrderID))
			//	return nil
			//}
			//err = service.PushOrder(pushOrder, rd)
			//if err != nil {
			//	log.Log().Error("推送订单失败", zap.Any("err", err), zap.Any("OrderID", data.OrderID))
			//	err = nil
			//}
		}
		return nil
	})
}
