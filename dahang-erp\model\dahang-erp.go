package model

import (
	"yz-go/source"
)

// 大昌行API客户信息
type DaHangErpCust struct {
	source.Model
	CustCode  string `json:"cust_code" form:"cust_code" gorm:"column:cust_code;comment:客户编码;index;"`    // 客户编码
	CustName  string `json:"cust_name" form:"cust_name" gorm:"column:cust_name;comment:客户名称;"`          // 客户名称
	EventCode string `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"` //活动编码
	EventName string `json:"event_name" form:"event_name" gorm:"column:event_name;comment:活动名称;"`       // 活动名称
	UserId    uint   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:会员id;"`                // 会员id
}

// 大昌行API项目信息
type DaHangErpItem struct {
	source.Model
	EventCode     string `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"`        //活动编码
	EventName     string `json:"event_name" form:"event_name" gorm:"column:event_name;comment:活动名称;"`              // 活动名称
	ApplicationId uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:采购端id;"` //采购端id

	OuCode string `json:"ou_code" form:"ou_code" gorm:"column:ou_code;comment:公司编码;index;"` // 公司编码
	OuName string `json:"ou_name" form:"ou_name" gorm:"column:ou_name;comment:公司名称;"`       // 公司名称
}

// 大昌行供应商信息记录表
type DaHangErpSupplier struct {
	source.Model
	SuppCode string `json:"supp_code" form:"supp_code" gorm:"column:supp_code;comment:供应商编码;index;"` // 供应商编码
	SuppName string `json:"supp_name" form:"supp_name" gorm:"column:supp_name;comment:供应商名称;"`       // 供应商名称

	SupplierId uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:中台供应商id;"` // 中台供应商id

	OuCode string `json:"ou_code" form:"ou_code" gorm:"column:ou_code;comment:公司编码;index;"` // 公司编码
	OuName string `json:"ou_name" form:"ou_name" gorm:"column:ou_name;comment:公司名称;"`       // 公司名称
}

// 大昌行供应商信息记录表
type DaHangErpProduct struct {
	source.Model
	ItemCode      string `json:"item_code" form:"item_code" gorm:"column:item_code;comment:商品编码;index;"`                 // 商品名称
	ItemName      string `json:"item_name" form:"item_name" gorm:"column:item_name;comment:商品名称;"`                       // 商品名称
	IsHelpFarming string `json:"is_help_farming" form:"is_help_farming" gorm:"column:is_help_farming;comment:是否助农0是1否;"` // 是否助农0是1否

	ProductId uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;"` // 商品id

	Cat4 string `json:"cat_4" form:"cat_4" gorm:"column:cat_4;comment:是否第三方编码1是 第三方API是string返回的;"` // 是否第三方编码

	ItemCateCode2 string `json:"item_cate_code_2" form:"item_cate_code_2" gorm:"column:item_cate_code_2;comment:第三方编码"` // 第三方编码

	SuppCode string `json:"supp_code" form:"supp_code" gorm:"column:supp_code;comment:供应商编码;index;"` // 供应商编码
	SuppName string `json:"supp_name" form:"supp_name" gorm:"column:supp_name;comment:供应商名称;"`       // 供应商名称

}

// 大昌行订单推送记录表
type DaHangErpOrder struct {
	source.Model
	OrderId       uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`                               // 订单id
	Status        int    `json:"status" form:"status" gorm:"column:status;comment:状态-2（不是导入采购端下单的商品无需推送）-1推送失败 0待推送 1部分推送 2全部推送;"`  // 商品id
	ErrorMsg      string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"`                                  //错误内容
	PushErrorMsg  string `json:"push_error_msg" form:"push_error_msg" gorm:"column:push_error_msg;comment:推送错误内容;"`                 //错误内容
	PushErrorData string `json:"push_error_data" form:"push_error_data" gorm:"column:push_error_data;comment:相册图json数组;type:text;"` // 相册图json数组
}

// 大昌行子订单推送记录表
type DaHangErpOrderItem struct {
	source.Model
	OrderId     uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`                                 // 订单id
	OrderItemId uint   `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:子订单id;"`                       //子订单id
	Status      int    `json:"status" form:"status" gorm:"column:status;comment:状态-2（不是导入采购端，供应商，商品，下单的无需推送）-1推送失败 0待推送 1已推送/已更新;"` // 商品id
	ErrorMsg    string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"`                                    //错误内容
	IsPush      int    `json:"is_push" form:"is_push" gorm:"column:is_push;default:0;comment:状态0待推送 1已推送;"`
}

type DaHangErpProductExportRecord struct {
	source.Model
	Link         string `json:"link"`
	StatusString string `json:"status_string"`
	ProductCount int64  `json:"product_count"`
	ErrorMsg     string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"` //错误内容
}
