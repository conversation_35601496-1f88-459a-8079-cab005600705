package model

import (
	_ "embed"
	"encoding/json"
	"errors"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"gorm.io/gorm"
	"public-supply/common"
	publicModel "public-supply/model"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string
var PLUGINID = 35

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		DaHangErpCust{},
		DaHangErpItem{},
		DaHangErpSupplier{},
		DaHangErpProduct{},
		DaHangErpOrder{},
		DaHangErpOrderItem{},
		DaHangErpProductExportRecord{},
	)

	// 菜单,权限
	menus := []model.SysMenu{}
	//sysBaseMenu := []model.SysBaseMenu{}
	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)

	if collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(PLUGINID) == true || utils.LocalEnv() != true {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}
	res := source.DB().Migrator().HasTable(&publicModel.GatherSupply{})
	if res == true {
		var supply publicModel.GatherSupply
		err = source.DB().Unscoped().Where("category_id = ?", common.DACHANGHANGERP).First(&supply).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
			return
		}
		if supply.ID == 0 {
			supply.Name = "大昌行erp"
			supply.CategoryID = common.DACHANGHANGERP
			err = source.DB().Create(&supply).Error
			if err != nil {
				err = nil
				return
			}
			err = source.DB().Delete(&supply).Error
			if err != nil {
				err = nil
				return
			}
		}
	}
	return
}
