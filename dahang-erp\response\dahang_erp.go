package response

import (
	applicationModel "application/model"
	"dahang-erp/model"
	productModel "product/model"
	supplierModel "supplier/model"

	productResponse "product/response"

	"yz-go/source"
)

type DaHangErpProductList struct {
	model.DaHangErpProduct
	Product           productModel.Product    `json:"product" gorm:"foreignKey:ProductId;references:ID"`
	DaHangErpSupplier model.DaHangErpSupplier `json:"daHangErpSupplier" gorm:"foreignKey:supp_code;references:supp_code"`
}

func (DaHangErpProductList) TableName() string {
	return "da_hang_erp_products"
}

func (ProductInfoList) TableName() string {
	return "products"
}

type ProductInfoList struct {
	ID        uint              `json:"id" form:"id" gorm:"primarykey"`
	UpdatedAt *source.LocalTime `json:"updated_at"`
	CreatedAt *source.LocalTime `json:"created_at" gorm:"index;"`
	Title     string            `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"` // 标题
	// 三级分类

	OriginPrice   uint `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice    uint `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	Price         uint `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价、给采购端的协议价
	CostPrice     uint `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	ActivityPrice uint `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	MinBuyQty     uint `json:"min_buy_qty" form:"min_buy_qty" gorm:"column:min_buy_qty;default:0;comment:最小起订量;"`    // 最小起订量

	//如果有activity_price时，那么cost_price为activity_price，否则为price
	Stock        uint   `json:"stock" form:"stock" gorm:"column:stock;default:0;comment:库存数量;index"`           // 库存数量
	Sales        uint   `json:"sales" form:"sales" gorm:"column:sales;comment:销量;"`                            // 销量
	FeedbackRate int    `json:"feedback_rate" form:"feedback_rate" gorm:"column:feedback_rate;comment:好评率;"`   // 好评率
	Sn           string `json:"sn" form:"sn" gorm:"column:sn;comment:产品编号;type:text;"`                         // 产品编号
	Code         string `json:"code" form:"code" gorm:"column:code;comment:自定义编码;type:varchar(255);size:255;"` // 自定义编码

	IsNew        int `json:"is_new" form:"is_new" gorm:"column:is_new;comment:新品（1是0否）;type:smallint;size:1;index;"`                   // 新品（1是0否）
	IsRecommend  int `json:"is_recommend" form:"is_recommend" gorm:"column:is_recommend;comment:推荐（1是0否）;type:smallint;size:1;index;"` // 推荐（1是0否）
	IsHot        int `json:"is_hot" form:"is_hot" gorm:"column:is_hot;comment:热销（1是0否）;type:smallint;size:1;index;"`                   // 热销（1是0否）
	IsPromotion  int `json:"is_promotion" form:"is_promotion" gorm:"column:is_promotion;comment:促销（1是0否）;type:smallint;size:1;index;"` // 促销（1是0否）
	IsDisplay    int `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"`       // 上架（1是0否）
	StatusLock   int `json:"status_lock" form:"status_lock" gorm:"column:status_lock;comment:锁定（1是0否）;type:smallint;size:1;index;"`    // 锁定（1是0否）
	SingleOption int `json:"single_option" form:"single_option" gorm:"column:single_option;comment:单规格(1是0否);type:smallint;size:1;"`   // 单规格（1是0否）

	ImageUrl       string `json:"image_url" gorm:"column:image_url;comment:图片url;"`                                     // 图片url
	Unit           string `json:"unit" form:"unit" gorm:"column:unit;comment:单位(件,个);type:varchar(255);size:255;"`      // 单位(件,个)
	Barcode        string `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"` // 条形码
	FreightType    int    `json:"freight_type" form:"freight_type" gorm:"column:freight_type;type:smallint;comment:运费类型（0统一，1模板， 2第三方运费，3包邮）;"`
	MaxPrice       uint   `json:"maxPrice" form:"maxPrice" gorm:"column:max_price;comment:最高价(单位:分);"` // 最高价(单位:分)
	MinPrice       uint   `json:"minPrice" form:"minPrice" gorm:"column:min_price;comment:最低价(单位:分);"` // 最低价(单位:分)
	MaxCostPrice   uint   `json:"maxCostPrice" form:"maxCostPrice" gorm:"-"`                           // 最高价(单位:分)
	MinCostPrice   uint   `json:"minCostPrice" form:"minCostPrice" gorm:"-"`                           // 最低价(单位:分)
	MaxOriginPrice uint   `json:"maxOriginPrice" form:"maxOriginPrice" gorm:"-"`                       // 最高价(单位:分)
	MinOriginPrice uint   `json:"minOriginPrice" form:"minOriginPrice" gorm:"-"`                       // 最低价(单位:分)

	Supplier     productModel.Supplier     `json:"supplier,omitempty" form:"supplier"`                                       // 供应商
	GatherSupply productModel.GatherSupply `json:"gather_supply" form:"supplier"`                                            // 供应链
	AliProduct   productModel.AliProduct   `json:"ali_product" form:"ali_product" gorm:"foreignKey:ProductID;references:ID"` // 阿里商品

	BrandID           uint               `json:"brand_id" form:"brand_id" gorm:"index"`                                                        // 品牌
	Brand             productModel.Brand `json:"brand"`                                                                                        // 品牌
	SupplierID        uint               `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                // 供应商id
	GatherSupplyID    uint               `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	Category1ID       uint               `json:"category1_id" form:"category1_id" gorm:"index"`                                                // 一级分类
	Category2ID       uint               `json:"category2_id" form:"category2_id" gorm:"index"`                                                // 二级分类
	Category3ID       uint               `json:"category3_id" form:"category3_id" gorm:"index"`
	FreightTemplateID uint               `json:"freight_template_id" form:"freight_template_id" gorm:"column:freight_template_id;comment:配送模板id;"` // 配送模板id

	Source        int                            `json:"source" form:"source" gorm:"column:source;comment:商品来源;type:int;"`                           //     1云仓 2京东 6阿里 7天猫  8苏宁 100YZH 99永源 98中台云仓 101中台 102跨境 103dwd
	Sort          int                            `json:"sort" form:"sort" gorm:"column:sort;comment:商品排序;type:int;"`                                 //     商品排序（序号小的在前）
	SourceGoodsID uint                           `json:"source_goods_id" form:"source_goods_id" gorm:"column:source_goods_id;index;comment:商品来源ID;"` // 商品来源
	LocationID    uint                           `json:"location_id" form:"location_id" gorm:"column:location_id;index;comment:站点ID;"`               // 商品来源
	Freeze        uint                           `json:"freeze" form:"freeze" gorm:"column:freeze;comment:是否冻结;default:0;"`                          // 冻结
	ShopLevel     int                            `json:"shop_level" form:"shop_level" gorm:"column:shop_level;default:0;type:int(2);"`
	DesLevel      int                            `json:"des_level" form:"des_level" gorm:"column:des_level;default:0;type:int(2);"`
	ExpressLevel  int                            `json:"express_level" form:"express_level" gorm:"column:express_level;default:0;type:int(2);"`
	Level         int                            `json:"level" form:"level" gorm:"column:level;default:0;type:int(2);"`
	ChildTitle    string                         `json:"child_title" form:"child_title" gorm:"column:child_title;comment:简称;type:varchar(255);size:255;index;"` // 简称
	BillPosition  int                            `json:"bill_position" form:"bill_position"  gorm:"column:bill_position;default:1;"`                            //发票信息存储位置  1商品本体 2sku
	ProductVerify productModel.ProductVerifyResp `json:"product_verify" gorm:"foreignKey:ID;references:ProductID"`
	Skus          []productResponse.Sku          `json:"skus" form:"skus" gorm:"foreignKey:ProductID;references:ID"` // sku数组
	productModel.ProductBill
	MinProfitRate       float64                      `json:"min_profit_rate" form:"min_profit_rate" gorm:"-"` //利润率
	MaxProfitRate       float64                      `json:"max_profit_rate" form:"max_profit_rate" gorm:"-"` //利润率
	SupplyLine          string                       `json:"supply_line"`
	ProfitRate          float64                      `json:"profit_rate"`
	ShopName            string                       `json:"shop_name"` //阿里商品的店铺
	SourceGoodsIDString string                       `json:"source_goods_id_string" form:"source_goods_id_string"`
	SupplierSourceID    uint                         `json:"supplier_source_id" form:"supplier_source_id"`
	SupplierSource      supplierModel.SupplierSource `json:"supplier_source" form:"supplier_source" gorm:"foreignKey:ID;references:supplier_source_id"`
}

// 大昌行API项目信息
type DaHangErpItem struct {
	source.Model
	OuCode        string                            `json:"ou_code" form:"ou_code" gorm:"column:ou_code;comment:公司编码;index;"`                 // 公司编码
	OuName        string                            `json:"ou_name" form:"ou_name" gorm:"column:ou_name;comment:公司名称;"`                       // 公司名称
	EventCode     string                            `json:"event_code" form:"event_code" gorm:"column:event_code;comment:活动编码;index;"`        //活动编码
	EventName     string                            `json:"event_name" form:"event_name" gorm:"column:event_name;comment:活动名称;"`              // 活动名称
	ApplicationId uint                              `json:"application_id" form:"application_id" gorm:"column:application_id;comment:采购端id;"` //采购端id
	Application   applicationModel.ApplicationModel `json:"application" gorm:"foreignKey:ApplicationId;references:ID"`
}
