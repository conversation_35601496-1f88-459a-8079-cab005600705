package setting

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/model"
	"yz-go/source"
)

type SysSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}

var Key = "dahang_erp"

type Value struct {
	Username string `mapstructure:"username" json:"username" yaml:"username"` //
	Password string `mapstructure:"password" json:"password" yaml:"password"` //SecretKey
	// 供应商编码
	SuppCode string `json:"suppCode" json:"suppCode" yaml:"suppCode"`
	// 供应商名称
	SuppName string `json:"suppName" json:"suppName" yaml:"suppName"`
	Url      string `json:"url" json:"url" yaml:"url"`
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func GetSysDaHangErpSetting() (err error, sysSetting SysSetting) {
	err = source.DB().Where("`key` = ?", Key).First(&sysSetting).Error
	return
}

func SaveSysDaHangErpSetting(data SysSetting) (err error) {
	_, sysSetting := GetSysDaHangErpSetting()
	if sysSetting.ID != 0 {
		data.ID = sysSetting.ID
	}
	if data.ID != 0 {
		err = source.DB().Updates(&data).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	return err
}
