package v1

import (
	"distributor/model"
	"distributor/request"
	"distributor/service"
	"errors"
	"github.com/gin-gonic/gin"
	"strconv"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

// 验证会员是否为分销商
func VerifyIdentity(c *gin.Context) {
	uid := v1.GetUserID(c)
	if err, res := service.VerifyIdentity(uid); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"res": res}, c)
	}
}

// 获取分销数据
func GetCenterInfo(c *gin.Context) {
	var pageInfo request.ApiAwardSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 等级名称, 分红设置
	uid := v1.GetUserID(c)
	pageInfo.Uid = uid
	var distributor model.Distributor
	err, distributor = service.GetDistributorByUserId(uid)
	if err != nil {
		err = errors.New("查询分销商失败")
		yzResponse.FailWithMessage(err.Error(), c)
	}
	resData := make(map[string]string)
	resData["level_name"] = distributor.LevelInfo.Name
	shopSettleInfo := distributor.LevelInfo.ShopSettleInfo
	shopAwardSetting := "自营订单:"
	if shopSettleInfo.AmountSwitch == 1 {
		shopAwardSetting += "[订单实际支付金额]"
		if shopSettleInfo.CostSwitch == 1 {
			shopAwardSetting += "减[成本]"
		}
		if shopSettleInfo.FreightSwitch == 1 {
			shopAwardSetting += "减[运费]"
		}
		shopAwardSetting += strconv.FormatFloat(float64(shopSettleInfo.FormulaRatio)/100, 'f', 2, 64) + "%"
	}
	if shopSettleInfo.BuyServiceSwitch == 1 {
		shopAwardSetting += " 采购服务费" + strconv.FormatFloat(float64(shopSettleInfo.BuyServiceRatio)/100, 'f', 2, 64) + "%"
	}
	resData["shop_settle"] = shopAwardSetting

	supplierSettleInfo := distributor.LevelInfo.SupplierSettleInfo
	supplierAwardSetting := "供应商订单:"
	if supplierSettleInfo.AmountSwitch == 1 {
		supplierAwardSetting += "[订单实际支付金额]"
		if supplierSettleInfo.CostSwitch == 1 {
			supplierAwardSetting += "减[成本]"
		}
		if supplierSettleInfo.FreightSwitch == 1 {
			supplierAwardSetting += "减[运费]"
		}
		supplierAwardSetting += strconv.FormatFloat(float64(supplierSettleInfo.FormulaRatio)/100, 'f', 2, 64) + "%"
	}
	if supplierSettleInfo.SupplierRebateSwitch == 1 {
		supplierAwardSetting += " 供应商扣点" + strconv.FormatFloat(float64(supplierSettleInfo.SupplierRebateRatio)/100, 'f', 2, 64) + "%"
	}
	if supplierSettleInfo.BuyServiceSwitch == 1 {
		supplierAwardSetting += " 采购服务费" + strconv.FormatFloat(float64(supplierSettleInfo.BuyServiceRatio)/100, 'f', 2, 64) + "%"
	}
	resData["supplier_settle"] = supplierAwardSetting

	supplySettleInfo := distributor.LevelInfo.SupplySettleInfo
	supplyAwardSetting := "供应链订单:"
	if supplySettleInfo.AmountSwitch == 1 {
		supplyAwardSetting += "[订单实际支付金额]"
		if supplySettleInfo.DealSwitch == 1 {
			supplyAwardSetting += "减[协议价]"
		}
		if supplySettleInfo.FreightSwitch == 1 {
			supplyAwardSetting += "减[运费]"
		}
		supplyAwardSetting += strconv.FormatFloat(float64(supplySettleInfo.FormulaRatio)/100, 'f', 2, 64) + "%"
	}
	if supplySettleInfo.BuyServiceSwitch == 1 {
		supplyAwardSetting += " 采购服务费" + strconv.FormatFloat(float64(supplySettleInfo.BuyServiceRatio)/100, 'f', 2, 64) + "%"
	}
	resData["supply_settle"] = supplyAwardSetting
	// 累计支付订单金额, 已完成订单金额
	var paidAmountTotal, receivedAmountTotal uint64
	err, paidAmountTotal, receivedAmountTotal = service.GetChildOrderInfo(uid)
	if err != nil {
		err = errors.New("查询直推下级订单数据失败")
		yzResponse.FailWithMessage(err.Error(), c)
	}
	resData["paid_amount_total"] = strconv.FormatUint(paidAmountTotal, 10)
	resData["received_amount_total"] = strconv.FormatUint(receivedAmountTotal, 10)
	// 今日分成, 累计分成, 已结算分成, 未结算分成
	var todayAmountTotal, settleAmountTotal, finishAmountTotal, waitAmountTotal int
	err, todayAmountTotal, settleAmountTotal, finishAmountTotal, waitAmountTotal = service.GetStatistic(uid)
	if err != nil {
		err = errors.New("统计分成数据失败")
		yzResponse.FailWithMessage(err.Error(), c)
	}
	resData["today_amount_total"] = strconv.Itoa(todayAmountTotal)
	resData["settle_amount_total"] = strconv.Itoa(settleAmountTotal)
	resData["finish_amount_total"] = strconv.Itoa(finishAmountTotal)
	resData["wait_amount_total"] = strconv.Itoa(waitAmountTotal)
	// 搜索, 订单号,下单会员id/手机号, 分成状态, 分成类型
	// 分红金额合计(随搜索条件变动)
	// 列表:时间, 订单号, 下单会员, 订单金额, 分红基数, 分成比例, 分成金额, 分成状态
	if err, list, total, amountTotal := service.GetAwardListByApi(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"center_info":  resData,
			"list":         list,
			"total":        total,
			"amount_total": amountTotal,
			"page":         pageInfo.Page,
			"pageSize":     pageInfo.PageSize,
		}, "获取成功", c)
	}
}
