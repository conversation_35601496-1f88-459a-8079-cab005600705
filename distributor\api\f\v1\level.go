package v1

import (
	"distributor/service"
	"github.com/gin-gonic/gin"
	ufv1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

func GetRenewLevels(c *gin.Context) {
	err, levels := service.GetRenewLevels(ufv1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, setting := service.GetSetting()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"levels": levels, "equity_statement": setting.Values.EquityStatement}, c)
}
