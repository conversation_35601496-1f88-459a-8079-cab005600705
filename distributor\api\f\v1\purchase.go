package v1

import (
	"distributor/model"
	"distributor/request"
	"distributor/service"
	"github.com/gin-gonic/gin"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"time"
	"trade/checkout"
	"trade/confirm"
	ufv1 "user/api/f/v1"
	"yz-go/cache"
	yzResponse "yz-go/response"
)

func GetPurchaseRecord(c *gin.Context) {
	var req request.PurchaseRecordListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	req.Uid = ufv1.GetUserID(c)
	if err, list, total := service.GetPurchaseRecordList(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

type ConfirmRequest struct {
	ProductID uint `json:"product_id" form:"product_id" query:"product_id"`
}

func Purchase(c *gin.Context) {
	var req ConfirmRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 查询规格id
	err, skuID := model.GetSkuID(req.ProductID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 分销商付费接口已经是机构，不能再成为分销商
	userID := ufv1.GetUserID(c)
	var exist bool
	err, exist = model.CheckIsInstitution(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if exist {
		yzResponse.FailWithMessage("您已是代理，不能再成为分销商", c)
		return
	}
	one := 1
	zero := 0
	// 生成购买id
	var id, buyID int64
	id, err = cache.GetID("product_buy")
	if err != nil {
		yzResponse.FailWithMessage("buy_id生成失败", c)
		return
	}
	buyID = id*int64(1e10) + time.Now().Unix()
	// 插入购物车记录
	shoppingCart := shoppingCartModel.ShoppingCart{
		UserID:  userID,
		SkuID:   skuID,
		Qty:     uint(1),
		Status:  &zero,
		Checked: &one,
		BuyID:   uint(buyID),
	}
	err = shoppingCartService.CreateShoppingCart(shoppingCart)
	if err != nil {
		yzResponse.FailWithMessage("添加购物车失败", c)
		return
	}
	// 读取购物车记录
	var shoppingCarts checkout.ShoppingCarts
	err, shoppingCarts = checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID)})
	if err != nil {
		yzResponse.FailWithMessage("读取购物车记录失败", c)
		return
	}
	if len(shoppingCarts) == 0 {
		yzResponse.FailWithMessage("请选择要结算的商品", c)
		return
	}
	// 结算信息
	err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
	if err != nil {
		yzResponse.FailWithMessage("结算失败", c)
		return
	}
	// 下单
	var confirmInfo confirm.Confirm
	err, confirmInfo = confirm.ShoppingCartConfirm(checkoutInfo)
	if err != nil {
		yzResponse.FailWithMessage("下单失败", c)
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID)})
	if err != nil {
		yzResponse.FailWithMessage("清空购物车失败", c)
		return
	}
	var orderIDs []uint
	for _, o := range confirmInfo.Orders {
		orderIDs = append(orderIDs, o.ID)
	}
	yzResponse.OkWithDetailed(gin.H{"order_ids": orderIDs}, "操作成功", c)
	return
}
