### 获取基础设置
GET {{api}}/distributor/findSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

### 会员搜索
GET {{api}}/user/getUserListBySearch?keyword=15945596368
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MTQzNzAzOSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjQwODMxMjM5fQ.prbxWBl7-deaDiayLLp9mbOjDPTewzaRiFsRXmfAi-8
x-User-Id: 1

### 更新基础设置
PUT {{api}}/distributor/updateSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "id": 27,
  "value": {
    "plugin_switch": 1,
    "settle_days": 7,
    "product_switch": 1
  }
}

### 查询分销
GET {{api}}/distributor/findDistributor
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MTE3NTM3NCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjQwNTY5NTc0fQ.Yh9sN90Zt3iJby_hBW1b2jNrTkGK38On7FgbhgKi210
x-User-Id: 1

{
  "id": 1
}

### 更新分销
PUT {{api}}/distributor/updateDistributor
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MTE3NTM3NCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjQwNTY5NTc0fQ.Yh9sN90Zt3iJby_hBW1b2jNrTkGK38On7FgbhgKi210
x-User-Id: 1

{
  "id": 1,
  "uid": 16,
  "level_id": 2
}

### 创建分销等级
POST {{api}}/distributor/createLevel
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "weight": 3,
  "name": "等级三",
  "upgrade_code": 1,
  "order_amount_switch": 1,
  "order_amount_total": 10,
  "order_count_switch": 1,
  "order_count_total": 1000,
  "upgrade_products": [
    {
      "product_id": 17727,
      "product_image": "//img13.360buyimg.com/n1/jfs/t1/208407/32/5033/224380/6166531cE1b21cde4/a93611a6fa99be71.jpg"
    },
    {
      "product_id": 17726,
      "product_image": "http://localhost:8080/apiuploads/file/934b535800b1cba8f96a5d72f72f1611_20210913164150.png"
    }
  ],
  "shop_settle_info": {
    "amount_switch": 1,
    "cost_switch": 1,
    "freight_switch": 1,
    "formula_ratio": 25,
    "buy_service_switch": 1,
    "buy_service_ratio": 40
  },
  "supplier_settle_info": {
    "amount_switch": 1,
    "cost_switch": 1,
    "freight_switch": 1,
    "formula_ratio": 25,
    "supplier_rebate_switch": 1,
    "supplier_rebate_ratio": 30,
    "buy_service_switch": 1,
    "buy_service_ratio": 40
  },
  "supply_settle_info": {
    "amount_switch": 1,
    "deal_switch": 0,
    "freight_switch": 1,
    "formula_ratio": 34,
    "buy_service_switch": 1,
    "buy_service_ratio": 66
  }
}

### 查询分销等级
GET {{api}}/distributor/findLevel
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "id": 1
}

### 删除分销等级
DELETE {{api}}/distributor/deleteLevel
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "id": 1
}

### 更新分销等级
PUT {{api}}/distributor/updateLevel
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "id": 2,
  "weight": 2,
  "name": "等级二呀",
  "upgrade_code": 1,
  "order_amount_switch": 1,
  "order_amount_total": 10,
  "order_count_switch": 1,
  "order_count_total": 1000,
  "upgrade_products": [
    {
      "product_id": 17727,
      "product_image": "//img13.360buyimg.com/n1/jfs/t1/208407/32/5033/224380/6166531cE1b21cde4/a93611a6fa99be71.jpg"
    },
    {
      "product_id": 17726,
      "product_image": "http://localhost:8080/apiuploads/file/934b535800b1cba8f96a5d72f72f1611_20210913164150.png"
    }
  ],
  "shop_settle_info": {
    "amount_switch": 1,
    "cost_switch": 1,
    "freight_switch": 1,
    "formula_ratio": 25,
    "buy_service_switch": 1,
    "buy_service_ratio": 40
  },
  "supplier_settle_info": {
    "amount_switch": 1,
    "cost_switch": 1,
    "freight_switch": 1,
    "formula_ratio": 25,
    "supplier_rebate_switch": 1,
    "supplier_rebate_ratio": 30,
    "buy_service_switch": 1,
    "buy_service_ratio": 40
  },
  "supply_settle_info": {
    "amount_switch": 1,
    "deal_switch": 0,
    "freight_switch": 1,
    "formula_ratio": 34,
    "buy_service_switch": 1,
    "buy_service_ratio": 66
  }
}

### 分页获取分销等级列表
GET{{api}}/distributor/getLevelList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "pageSize": 10,
  "page": 1
}

### 获取全部分销等级无分页
GET{{api}}/distributor/getAllLevel
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

### 分页获取分销分成列表
GET{{api}}/distributor/getAwardList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "pageSize": 10,
  "page": 1
}

### 导出分销分成列表
GET{{api}}/distributor/exportAwardList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

### 创建或修改分销商
POST {{api}}/distributor/addOrEditDistributor
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "create_type": 1,
  "uid": 16,
  "level_id": 3
}

### 创建或修改分销商
POST {{api}}/distributor/addOrEditDistributor
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "create_type": 2,
  "level_id": 3,
  "source_type": 1
}

### 分页获取分销商列表
GET{{api}}/distributor/getDistributorList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1

{
  "pageSize": 10,
  "page": 1
}

### 导出分销商列表
GET{{api}}/distributor/exportDistributorList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2M4YjJlMTEtZjMzYi00YTdiLWI0MTYtZTRkOGI0MzRlMTJjIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY0MDMzOTA3MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjM5NzMzMjcxfQ.WHuMdehiB8G00MWpLkPOrWYgtodfBN4j4JoaIlU4tCA
x-User-Id: 1


### 前端验证会员是否为分销商
GET {{api}}/api/distributor/verifyIdentity
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWNkZDUwYTAtN2EwNC00ODZjLWJiNDUtMGVkOGUwN2I0MTg4IiwiVXNlcm5hbWUiOiIiLCJJRCI6MTIsIkFwcElEIjowLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNjQwNTk1MzQ4LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE2Mzk5ODk1NDh9.q3Wm4Auj0X_wa8Qs2vN6GDF4KCtL1i7kEP9E_70RNZM
x-User-Id: 12

### 前端获取分销数据
GET {{api}}/api/distributor/getCenterInfo?page=1&pageSize=10
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAwIiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NTcwMDk5NDUsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY1NjQwNDE0NX0.DtK9fsmSffMaaF6gnqQeFFUNs9TckWWQ6oxdOHfY_A8
x-User-Id: 12

### 前端获取分销等级
POST {{api}}/distributor/fixMq
Content-Type: application/json

{
    "id": 1
}