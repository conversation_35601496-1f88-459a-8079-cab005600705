package v1

import (
	"distributor/service"
	"github.com/gin-gonic/gin"
	"yz-go/request"
	yzResponse "yz-go/response"
)

func Compensate(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	err = service.Compensate(req.Id, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("补偿成功", c)
}

func CompensateSettle(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.Compensate(req.Id, 1)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("补偿成功", c)
}
