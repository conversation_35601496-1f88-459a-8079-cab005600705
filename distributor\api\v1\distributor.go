package v1

import (
	"distributor/request"
	"distributor/service"
	"github.com/gin-gonic/gin"
	request2 "yz-go/request"
	yzResponse "yz-go/response"
)

// 查询分销商
func FindDistributor(c *gin.Context) {
	var distributor service.Distributor
	err := c.ShouldBindJSON(&distributor)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reAgency := service.FindDistributor(distributor.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"distributor": reAgency}, c)
	}
}

// 修改分销商
func UpdateDistributor(c *gin.Context) {
	var distributor service.Distributor
	err := c.ShouldBindJSON(&distributor)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateDistributor(distributor); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// 黑名单
func Blacklist(c *gin.Context) {
	var req request2.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.Blacklist(req.Id); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// 创建或修改分销商
func AddOrEditDistributor(c *gin.Context) {
	var distributorByBatch request.DistributorByBatch
	err := c.ShouldBindJSON(&distributorByBatch)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.AddOrEditDistributor(distributorByBatch); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("保存成功", c)
	}
}

// 分页获取分销商列表
func GetDistributorList(c *gin.Context) {
	var pageInfo request.DistributorSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total, amountTotal := service.GetDistributorList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"list":         list,
			"total":        total,
			"amount_total": amountTotal,
			"page":         pageInfo.Page,
			"pageSize":     pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// 导出分销商
func ExportDistributorList(c *gin.Context) {
	var pageInfo request.DistributorSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportDistributorList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
