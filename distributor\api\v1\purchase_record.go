package v1

import (
	"distributor/request"
	"distributor/service"
	"github.com/gin-gonic/gin"
	yzResponse "yz-go/response"
)

// GetPurchaseRecordList 获取分销商开通记录列表
func GetPurchaseRecordList(c *gin.Context) {
	var req request.PurchaseRecordListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetPurchaseRecordList(req); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}
