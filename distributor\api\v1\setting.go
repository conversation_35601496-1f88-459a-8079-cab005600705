package v1

import (
	"distributor/award"
	dmq "distributor/award_mq"
	"distributor/model"
	"distributor/service"
	"distributor/upgrade"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"order/mq"
	"yz-go/request"
	yzResponse "yz-go/response"
)

func FixUpgrade(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = upgrade.OrderHandle(req.Id)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("分销升级成功", c)
}

func Fix(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	// 分销商是否有分成
	var isAward bool
	err, isAward = award.Handle(req.Id, 1)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if !isAward {
		err = dmq.PublishMessage(req.Id, "", 1, dmq.Award)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}
	yzResponse.OkWithMessage("补发消息成功", c)
}

func FixMq(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = mq.PublishMessage(req.Id, mq.Received, 0)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("补发消息成功", c)
}

// 查询Setting
func FindSetting(c *gin.Context) {
	err, setting := service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

// 更新Setting
func UpdateSetting(c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = service.SaveSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}
