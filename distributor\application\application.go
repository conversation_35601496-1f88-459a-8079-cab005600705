package application

import (
	"yz-go/source"
)

type Application struct {
	source.Model
	MemberId int `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;"`
}

func (Application) TableName() string {
	return "application"
}

// 获取采购会员ids
func GetUserIds() (err error, userIds []uint) {
	err = source.DB().Model(&Application{}).Pluck("member_id", &userIds).Error
	return
}

// 获取不是分销商的会员ids
func GetUserIdsNotByDistributorUserIds(distributorUserIds []uint) (err error, userIds []uint) {
	err = source.DB().Model(&Application{}).Where("member_id NOT IN ?", distributorUserIds).Pluck("member_id", &userIds).Error
	return
}

// 获取采购会员是分销商身份的总数
func GetTotalByUserIds(userIds []uint) (err error, total int64) {
	err = source.DB().Model(&Application{}).Where("member_id IN ?", userIds).Count(&total).Error
	return
}
