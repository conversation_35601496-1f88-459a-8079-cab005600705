package compensate

import (
	"distributor/award"
	"distributor/model"
	"distributor/service"
	"errors"
	"fmt"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"yz-go/source"
)

func CanSettleHandle(uid uint) error {
	// 分批处理数据
	const batchSize = 500
	var processedCount int

	for {
		// 查询一批数据（基于游标的分页）
		var awards []model.DistributorCompensateAward
		err := source.DB().Model(&model.DistributorCompensateAward{}).
			Where("uid = ?", uid).
			Order("id ASC"). // 按主键排序
			Limit(batchSize).
			Offset(processedCount).
			Find(&awards).Error
		if err != nil {
			return fmt.Errorf("query compensate awards failed: %w", err)
		}

		if len(awards) == 0 {
			break
		}

		// 转换数据结构
		var newAwards []model.DistributorAward
		for _, ca := range awards {
			var awardItem model.DistributorAward
			if err := copier.Copy(&awardItem, &ca); err != nil {
				return fmt.Errorf("copy award failed: %w", err)
			}
			awardItem.ID = 0
			newAwards = append(newAwards, awardItem)
		}

		// 批量创建新记录
		if err := source.DB().CreateInBatches(newAwards, batchSize).Error; err != nil {
			return fmt.Errorf("create awards failed: %w", err)
		}

		// 收集当前批次的 ID 列表
		var ids []uint
		for _, ca := range awards {
			ids = append(ids, ca.ID)
		}

		// 删除已处理的补偿记录
		if err := source.DB().Unscoped().Where("uid = ? AND id IN (?)", uid, ids).
			Delete(&model.DistributorCompensateAward{}).Error; err != nil {
			return fmt.Errorf("delete compensate awards failed: %w", err)
		}

		processedCount += len(awards)
		if len(awards) < batchSize {
			break
		}
	}

	return nil
}

func Handle(uid uint) (err error) {
	// 查询会员是否存在
	var user model.User
	err = source.DB().Where("id = ?", uid).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("会员不存在")
		}
		return fmt.Errorf("查询会员失败: %w", err)
	}
	if user.ID == 0 {
		return errors.New("会员不存在")
	}
	// 查询会员发展的所有下线ids
	var childIds []uint
	err = source.DB().Model(&model.User{}).Where("parent_id = ?", uid).Pluck("id", &childIds).Error
	if err != nil {
		return fmt.Errorf("查询下线会员失败: %w", err)
	}
	if len(childIds) == 0 {
		return errors.New("会员没有下线")
	}
	// 查询会员是否为分销商
	var distributor model.Distributor
	err, distributor = service.GetDistributorByUserId(uid)
	if err != nil {
		return fmt.Errorf("查询分销商信息失败: %w", err)
	}
	if distributor.ID == 0 {
		return errors.New("会员不是分销商")
	}
	// 查询会员升级供应链
	var supply award.GatherSupply
	gerr := source.DB().Unscoped().Model(&award.GatherSupply{}).Where("category_id = ?", 12).First(&supply).Error
	if gerr != nil && !errors.Is(gerr, gorm.ErrRecordNotFound) {
		return fmt.Errorf("查询供应链失败: %w", gerr)
	}

	const batchSize = 500
	var processedCount int
	for {
		var orders []award.Order
		if err := source.DB().Model(&award.Order{}).
			Where("user_id IN ? AND status >= ?", childIds, 1).
			//Where("user_id IN ?", childIds).
			Order("id ASC").
			Limit(batchSize).
			Offset(processedCount).
			Find(&orders).Error; err != nil {
			return fmt.Errorf("查询订单失败: %w", err)
		}

		if len(orders) == 0 {
			break
		}

		// 处理订单
		for _, order := range orders {
			// 检查是否已处理过该订单
			var count int64
			if err := source.DB().Model(&model.DistributorAward{}).
				Where("order_id = ? AND uid = ? AND child_uid = ?",
					order.ID, uid, order.UserID).
				Count(&count).Error; err != nil {
				return fmt.Errorf("检查订单奖励记录失败: %w", err)
			}
			if count > 0 {
				continue
			}

			// 确定结算状态 2:不可以 1：可以
			canSettle := 2
			if order.Status == 3 {
				canSettle = 1
			}

			// 处理订单奖励
			if err := orderHandle(order, distributor, supply.ID, 0, canSettle); err != nil {
				return fmt.Errorf("处理订单奖励失败 [OrderID: %d]: %w", order.ID, err)
			}
		}
		processedCount += len(orders)
		if len(orders) < batchSize {
			break
		}
	}
	return
}

func orderHandle(order award.Order, distributor model.Distributor, supplyID uint, settleDays, canSettle int) (err error) {
	// 修改分销商的累计推荐订单总数和累计推荐订单总额
	distributor.RecommendOrderCountTotal += 1
	distributor.RecommendOrderAmountTotal += order.Amount
	err = updateDistributor(distributor)
	if err != nil {
		return
	}
	if order.SupplierID != 0 {
		// 供应商订单奖励
		err = supplierAward(distributor, order, settleDays, canSettle)
		if err != nil {
			return
		}
	}
	// order.gather_supply_id 有值 为供应链订单
	if order.GatherSupplyID != 0 {
		// 判断是否为会员升级订单
		if supplyID == order.GatherSupplyID {
			// 会员升级订单奖励
			err = userUpgradeAward(distributor, order, settleDays, canSettle)
			if err != nil {
				return
			}
		} else {
			// 供应链订单奖励
			err = supplyAward(distributor, order, settleDays, canSettle)
			if err != nil {
				return
			}
		}
	}
	// order.supplier_id = 0 order.gather_supply_id = 0 为自营订单
	if order.SupplierID == 0 && order.GatherSupplyID == 0 {
		// 自营订单奖励
		err = shopAward(distributor, order, settleDays, canSettle)
		if err != nil {
			return
		}
	}
	return
}

// 供应商订单奖励
func supplierAward(distributor model.Distributor, order award.Order, settleDays, canSettle int) (err error) {
	// 分销商等级->供应商订单奖励设置
	supplierSettleInfo := distributor.LevelInfo.SupplierSettleInfo
	var awards []model.DistributorCompensateAward
	// 订单实付金额
	if supplierSettleInfo.AmountSwitch == 1 {
		// 分成基数
		settleAmount := int64(order.Amount)
		// 减成本
		if supplierSettleInfo.CostSwitch == 1 {
			settleAmount -= int64(order.CostAmount)
		}
		// 减运费
		if supplierSettleInfo.FreightSwitch == 1 {
			settleAmount -= int64(order.Freight)
		}
		if settleAmount <= 0 {
			return
		}
		// 比例
		if supplierSettleInfo.FormulaRatio <= 0 {
			return
		}
		// 分成金额
		awardAmount := settleAmount * int64(supplierSettleInfo.FormulaRatio) / 10000
		if awardAmount <= 0 {
			return
		}
		var awardRecord model.DistributorCompensateAward
		awardRecord.Uid = distributor.Uid
		awardRecord.ChildUid = order.UserID
		awardRecord.LevelID = distributor.LevelID
		awardRecord.LevelName = distributor.LevelInfo.Name
		awardRecord.OrderID = order.ID
		awardRecord.OrderType = model.OrderTypeSupplier
		awardRecord.OrderSN = order.OrderSN
		awardRecord.OrderAmount = order.Amount
		awardRecord.SettleAmount = uint(settleAmount)
		awardRecord.SettleType = model.SettleTypeOrder
		awardRecord.Ratio = supplierSettleInfo.FormulaRatio
		awardRecord.Amount = uint(awardAmount)
		awardRecord.Status = model.Wait
		awardRecord.SettleDays = settleDays
		awardRecord.CanSettle = canSettle
		awards = append(awards, awardRecord)
	}
	// 供应商扣点
	if supplierSettleInfo.SupplierRebateSwitch == 1 {
		// 分成基数
		var settleAmount int64
		err, settleAmount = getSupplierSettleAmount(order)
		if err != nil {
			return
		}
		if settleAmount > 0 && supplierSettleInfo.SupplierRebateRatio > 0 {
			awardAmount := settleAmount * int64(supplierSettleInfo.SupplierRebateRatio) / 10000
			if awardAmount > 0 {
				var awardRecord model.DistributorCompensateAward
				awardRecord.Uid = distributor.Uid
				awardRecord.ChildUid = order.UserID
				awardRecord.LevelID = distributor.LevelID
				awardRecord.LevelName = distributor.LevelInfo.Name
				awardRecord.OrderID = order.ID
				awardRecord.OrderType = model.OrderTypeSupplier
				awardRecord.OrderSN = order.OrderSN
				awardRecord.OrderAmount = order.Amount
				awardRecord.SettleAmount = uint(settleAmount)
				awardRecord.SettleType = model.SettleTypeSupplier
				awardRecord.Ratio = supplierSettleInfo.SupplierRebateRatio
				awardRecord.Amount = uint(awardAmount)
				awardRecord.Status = model.Wait
				awardRecord.SettleDays = settleDays
				awardRecord.CanSettle = canSettle
				awards = append(awards, awardRecord)
			}
		}
	}
	// 采购技术服务费
	if supplierSettleInfo.BuyServiceSwitch == 1 {
		// 分成基数
		settleAmount := order.TechnicalServicesFee
		if settleAmount > 0 && supplierSettleInfo.BuyServiceRatio > 0 {
			awardAmount := settleAmount * uint(supplierSettleInfo.BuyServiceRatio) / 10000
			if awardAmount > 0 {
				var awardRecord model.DistributorCompensateAward
				awardRecord.Uid = distributor.Uid
				awardRecord.ChildUid = order.UserID
				awardRecord.LevelID = distributor.LevelID
				awardRecord.LevelName = distributor.LevelInfo.Name
				awardRecord.OrderID = order.ID
				awardRecord.OrderType = model.OrderTypeSupplier
				awardRecord.OrderSN = order.OrderSN
				awardRecord.OrderAmount = order.Amount
				awardRecord.SettleAmount = settleAmount
				awardRecord.SettleType = model.SettleTypeService
				awardRecord.Ratio = supplierSettleInfo.BuyServiceRatio
				awardRecord.Amount = awardAmount
				awardRecord.Status = model.Wait
				awardRecord.SettleDays = settleDays
				awardRecord.CanSettle = canSettle
				awards = append(awards, awardRecord)
			}
		}
	}
	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		//if setting.Values.Layers == 2 {
		//	err = IndirectAwards(distributor, awards, order)
		//} else {
		//	err = sendMq(order.ID, awards)
		//}
		//isAward = true
	}
	return
}

// 会员升级订单奖励
func userUpgradeAward(distributor model.Distributor, order award.Order, settleDays, canSettle int) (err error) {
	var supplySettleInfo model.MeituanDisSettleInfo
	// 分销商等级->会员升级订单奖励设置
	supplySettleInfo = distributor.LevelInfo.UserUpgradeSettleInfo
	// 分成基数
	settleAmount := int64(order.Amount)
	// 分成金额
	awardAmount := settleAmount * int64(supplySettleInfo.AmountRatio) / 10000
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()", zap.Any("settleAmount", settleAmount))
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()", zap.Any("awardAmount", awardAmount))
	// log.Log().Error("分销监听订单完成后事件userUpgradeAward()", zap.Any("AmountRatio", supplySettleInfo.AmountRatio))
	if awardAmount <= 0 {
		// log.Log().Error("分销监听订单完成后事件userUpgradeAward()分成金额小于等于0")
		return
	}
	var awards []model.DistributorCompensateAward
	// 产生奖励
	var awardRecord model.DistributorCompensateAward
	awardRecord.Uid = distributor.Uid
	awardRecord.ChildUid = order.UserID
	awardRecord.LevelID = distributor.LevelID
	awardRecord.LevelName = distributor.LevelInfo.Name
	awardRecord.OrderID = order.ID
	awardRecord.OrderType = model.OrderTypeUserUpgrade
	awardRecord.OrderSN = order.OrderSN
	awardRecord.OrderAmount = order.Amount
	awardRecord.SettleAmount = uint(settleAmount)
	awardRecord.SettleType = model.SettleTypeOrder
	awardRecord.Ratio = supplySettleInfo.AmountRatio
	awardRecord.Amount = uint(awardAmount)
	awardRecord.Status = model.Wait
	awardRecord.SettleDays = settleDays
	awardRecord.CanSettle = canSettle
	awards = append(awards, awardRecord)
	if len(awards) > 0 {
		// log.Log().Error("分销监听订单完成后事件userUpgradeAward()产生奖励", zap.Any("awards", awards))
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			// log.Log().Error("分销监听订单完成后事件userUpgradeAward()产生奖励失败", zap.Any("err", err))
			return
		}
		//if setting.Values.Layers == 2 {
		//	err = IndirectAwards(distributor, awards, order)
		//} else {
		//	err = sendMq(order.ID, awards)
		//}
		//isAward = true
	}

	return
}

// 供应链订单奖励
func supplyAward(distributor model.Distributor, order award.Order, settleDays, canSettle int) (err error) {
	// 数字权益订单
	var supply award.GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	var supplySettleInfo model.SupplySettleInfo
	if supply.ID == order.GatherSupplyID {
		// 分销商等级->数字权益订单奖励设置
		supplySettleInfo = distributor.LevelInfo.EquitySettleInfo
	} else {
		// 分销商等级->供应链订单奖励设置
		supplySettleInfo = distributor.LevelInfo.SupplySettleInfo
	}

	var awards []model.DistributorCompensateAward
	// 订单实付金额
	if supplySettleInfo.AmountSwitch == 1 {
		// 分成基数
		settleAmount := int64(order.Amount)
		// 减协议价
		if supplySettleInfo.DealSwitch == 1 {
			settleAmount -= int64(order.SupplyAmount)
		}
		// 减运费
		if supplySettleInfo.FreightSwitch == 1 {
			settleAmount -= int64(order.Freight)
		}
		if settleAmount <= 0 {
			return
		}
		// 比例
		if supplySettleInfo.FormulaRatio <= 0 {
			return
		}
		// 分成金额
		awardAmount := settleAmount * int64(supplySettleInfo.FormulaRatio) / 10000
		if awardAmount <= 0 {
			return
		}
		var awardRecord model.DistributorCompensateAward
		awardRecord.Uid = distributor.Uid
		awardRecord.ChildUid = order.UserID
		awardRecord.LevelID = distributor.LevelID
		awardRecord.LevelName = distributor.LevelInfo.Name
		awardRecord.OrderID = order.ID
		awardRecord.OrderType = model.OrderTypeSupply
		awardRecord.OrderSN = order.OrderSN
		awardRecord.OrderAmount = order.Amount
		awardRecord.SettleAmount = uint(settleAmount)
		awardRecord.SettleType = model.SettleTypeOrder
		awardRecord.Ratio = supplySettleInfo.FormulaRatio
		awardRecord.Amount = uint(awardAmount)
		awardRecord.Status = model.Wait
		awardRecord.SettleDays = settleDays
		awardRecord.CanSettle = canSettle
		awards = append(awards, awardRecord)
	}
	// 采购技术服务费
	if supplySettleInfo.BuyServiceSwitch == 1 {
		// 分成基数
		settleAmount := order.TechnicalServicesFee
		if settleAmount > 0 && supplySettleInfo.BuyServiceRatio > 0 {
			awardAmount := settleAmount * uint(supplySettleInfo.BuyServiceRatio) / 10000
			if awardAmount > 0 {
				var awardRecord model.DistributorCompensateAward
				awardRecord.Uid = distributor.Uid
				awardRecord.ChildUid = order.UserID
				awardRecord.LevelID = distributor.LevelID
				awardRecord.LevelName = distributor.LevelInfo.Name
				awardRecord.OrderID = order.ID
				awardRecord.OrderType = model.OrderTypeSupply
				awardRecord.OrderSN = order.OrderSN
				awardRecord.OrderAmount = order.Amount
				awardRecord.SettleAmount = settleAmount
				awardRecord.SettleType = model.SettleTypeService
				awardRecord.Ratio = supplySettleInfo.BuyServiceRatio
				awardRecord.Amount = awardAmount
				awardRecord.Status = model.Wait
				awardRecord.SettleDays = settleDays
				awardRecord.CanSettle = canSettle
				awards = append(awards, awardRecord)
			}
		}
	}
	if len(awards) > 0 {
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		//if setting.Values.Layers == 2 {
		//	var o Order
		//	o.ID = order.ID
		//	err = IndirectAwards(distributor, awards, o)
		//} else {
		//	err = sendMq(order.ID, awards)
		//}
		//isAward = true
	}
	return
}

func shopAward(distributor model.Distributor, order award.Order, settleDays, canSettle int) (err error) {
	// 分销商等级->自营订单奖励设置
	shopSettleInfo := distributor.LevelInfo.ShopSettleInfo
	var awards []model.DistributorCompensateAward
	// 订单实付金额
	if shopSettleInfo.AmountSwitch == 1 {
		// 分成基数
		settleAmount := int64(order.Amount)
		// 减成本
		if shopSettleInfo.CostSwitch == 1 {
			settleAmount -= int64(order.CostAmount)
		}
		// 减运费
		if shopSettleInfo.FreightSwitch == 1 {
			settleAmount -= int64(order.Freight)
		}
		if settleAmount <= 0 {
			return
		}
		// 比例
		if shopSettleInfo.FormulaRatio <= 0 {
			return
		}
		// 分成金额
		awardAmount := settleAmount * int64(shopSettleInfo.FormulaRatio) / 10000
		if awardAmount <= 0 {
			return
		}
		var awardRecord model.DistributorCompensateAward
		awardRecord.Uid = distributor.Uid
		awardRecord.ChildUid = order.UserID
		awardRecord.LevelID = distributor.LevelID
		awardRecord.LevelName = distributor.LevelInfo.Name
		awardRecord.OrderID = order.ID
		awardRecord.OrderType = model.OrderTypeShop
		awardRecord.OrderSN = order.OrderSN
		awardRecord.OrderAmount = order.Amount
		awardRecord.SettleAmount = uint(settleAmount)
		awardRecord.SettleType = model.SettleTypeOrder
		awardRecord.Ratio = shopSettleInfo.FormulaRatio
		awardRecord.Amount = uint(awardAmount)
		awardRecord.Status = model.Wait
		awardRecord.SettleDays = settleDays
		awardRecord.CanSettle = canSettle
		awards = append(awards, awardRecord)
	}
	// 采购技术服务费
	if shopSettleInfo.BuyServiceSwitch == 1 {
		// 分成基数
		settleAmount := order.TechnicalServicesFee
		if settleAmount > 0 && shopSettleInfo.BuyServiceRatio > 0 {
			// 分成金额
			awardAmount := settleAmount * uint(shopSettleInfo.BuyServiceRatio) / 10000
			if awardAmount > 0 {
				var awardRecord model.DistributorCompensateAward
				awardRecord.Uid = distributor.Uid
				awardRecord.ChildUid = order.UserID
				awardRecord.LevelID = distributor.LevelID
				awardRecord.LevelName = distributor.LevelInfo.Name
				awardRecord.OrderID = order.ID
				awardRecord.OrderType = model.OrderTypeShop
				awardRecord.OrderSN = order.OrderSN
				awardRecord.OrderAmount = order.Amount
				awardRecord.SettleAmount = settleAmount
				awardRecord.SettleType = model.SettleTypeService
				awardRecord.Ratio = shopSettleInfo.BuyServiceRatio
				awardRecord.Amount = awardAmount
				awardRecord.Status = model.Wait
				awardRecord.SettleDays = settleDays
				awardRecord.CanSettle = canSettle
				awards = append(awards, awardRecord)
			}
		}
	}
	if len(awards) > 0 {
		// log.Log().Error("分销监听订单完成后事件shopAward()产生奖励", zap.Any("awards", awards))
		// 产生奖励
		err = source.DB().Create(&awards).Error
		if err != nil {
			return
		}
		//if setting.Values.Layers == 2 {
		//	err = IndirectAwards(distributor, awards, order)
		//} else {
		//	err = sendMq(order.ID, awards)
		//}
		//isAward = true
	}
	return
}

// 修改分销商
func updateDistributor(distributor model.Distributor) (err error) {
	err = source.DB().Omit("LevelInfo").Updates(&distributor).Error
	return
}

// 获取供应商订单结算金额
func getSupplierSettleAmount(order award.Order) (err error, settleAmount int64) {
	var supplierOrder SupplierSettlement
	err, supplierOrder = getSupplierOrder(order.ID)
	if err != nil {
		return
	}
	// 结算方式 0:订单金额 1:供货价 + 运费
	if supplierOrder.SettlementType == 2 {
		// (110 - 5) * 500
		settleAmount = (int64(order.Amount) - int64(order.TechnicalServicesFee)) * int64(supplierOrder.DeductionRatio) / 10000
	} else {
		// (110 + 0 - 5) * 500
		settleAmount = (int64(order.SupplyAmount) + int64(order.Freight) - int64(order.TechnicalServicesFee)) * int64(supplierOrder.DeductionRatio) / 10000
	}
	return
}

// 通过订单id获取供应商订单
func getSupplierOrder(orderId uint) (err error, supplierOrder SupplierSettlement) {
	err = source.DB().Where("order_id = ?", orderId).First(&supplierOrder).Error
	return
}

type SupplierSettlement struct {
	source.Model
	OrderID        uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	SettlementType int  `json:"settlement_type"  gorm:"default:0"` //   0订单   1  供货价
	DeductionRatio int  `json:"deduction_ratio"  gorm:"default:0"` //  供应商扣点
}
