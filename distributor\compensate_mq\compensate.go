package compensate_mq

import (
	"encoding/json"
	"fmt"
	"github.com/streadway/amqp"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	"yz-go/mq"
	"yz-go/source"
)

const (
	EXCHANGE = "compensateE"
	KEY      = "compensate"
)

var publishRabbitMQ *amqp.Channel

func init() {
	handles = map[string]Handle{}
	mq.PushConsumer(ConsumeRabbitMQ)
}

func getPublishRabbitMQ() *amqp.Channel {
	if publishRabbitMQ == nil {
		var err error
		publishRabbitMQ = source.MQ()
		if publishRabbitMQ == nil {
			fmt.Println(err)
			return nil
		}
		// 定义话题交换机
		err = publishRabbitMQ.ExchangeDeclare(
			EXCHANGE,
			"topic", //话题类型
			true,
			false,
			//true表示这个exchange不可以被client用来推送消息，仅用来进行exchange和exchange之间的绑定
			false,
			false,
			nil,
		)

		if err != nil {
			fmt.Println(err)
			return nil
		}
	}
	return publishRabbitMQ
}

func PublishMessage(uid uint, canSettle int) (err error) {
	message, err := json.Marshal(CompensateMessage{
		Uid:       uid,
		CanSettle: canSettle,
	})

	if err != nil {
		return
	}
	key := KEY
	err = getPublishRabbitMQ().Publish(
		EXCHANGE,
		key,
		//如果为true，根据exchange类型和routkey规则，如果无法找到符合条件的队列，那么会把发送的消息返回给发送者
		false,
		//如果为true，当exchange发送消息到队列侯发现队列上没有绑定消费者，则会把消息发还给发送者
		false,
		amqp.Publishing{
			ContentType:  "text/plain",
			Body:         message,
			DeliveryMode: amqp.Persistent,
		},
	)
	return
}

type CompensateMessage struct {
	Uid       uint `json:"uid"`
	CanSettle int  `json:"can_settle"`
}

type Handle func(msg CompensateMessage) error

var handles map[string]Handle

func PushHandles(queueName string, handle Handle) {
	handles[queueName] = handle
	getPublishRabbitMQ()
}

func ConsumeHandle(queueName string, handle Handle) {
	var err error
	var consumeRabbitMQ *amqp.Channel
	consumeRabbitMQ = source.MQ()
	// 定义队列
	_, err = consumeRabbitMQ.QueueDeclare(queueName, true, false, false, false, nil)
	if err != nil {
		fmt.Println(err)
		log.Log().Info("创建订单channel失败：", zap.Any("createOrderQueue", err))

	}
	// 绑定队列
	err = consumeRabbitMQ.QueueBind(
		queueName, // queue name
		KEY,       // routing key
		EXCHANGE,  // exchange
		false,
		nil)
	if err != nil {
		log.Log().Info("订单channel绑定队列失败：", zap.Any("bingOrderQueue", err))

	}
	msgs, err := consumeRabbitMQ.Consume(
		queueName,
		//用来区分多个消费者
		"",
		//是否自动应答
		false,
		//是否具有排他性
		false,
		//如果设置为true，表示不能将同一个connection中发送的消息传递给这个connection中的消费者
		false,
		//消息队列是否阻塞
		false,
		nil,
	)
	if err != nil {
		log.Log().Info("订单channel绑定消费者失败：", zap.Any("OrderConsume", err))
	}
	forever := make(chan bool)
	for msg := range msgs {
		message := CompensateMessage{}
		err = json.Unmarshal(msg.Body, &message)
		if err != nil {
			log.Log().Info("订单消费者消息解码失败：", zap.Any("OrderConsumeUnmarshal", err))
			// 拒绝消息并重试
			err = msg.Reject(true)
			if err != nil {
				fmt.Println(err)
			}
		} else {
			err = handle(message)
			if err != nil {
				log.Log().Info("订单消费者消费失败：", zap.Any("OrderConsumeFailed", err))
				// 拒绝消息并重试
				err = msg.Reject(true)
				if err != nil {
					fmt.Println(err)
				}
			} else {
				// 成功接收消息
				err = msg.Ack(false)
				if err != nil {
					log.Log().Info("订单消费者消费失败：", zap.Any("OrderConsumeAckFailed", err))
				}
			}
		}
	}
	<-forever
	return
}
func ConsumeRabbitMQ() {
	go KeepConsumerAlive()
}

var keeperCh *amqp.Channel

func getKeeperCh() *amqp.Channel {
	if keeperCh == nil {
		keeperCh = source.MQ()
	}
	return keeperCh
}
func createConsumer() {
	ch := getKeeperCh()
	var err error
	var queue amqp.Queue
	for queueName, handle := range handles {
		if ch == nil {
			return
		}
		// 定义队列
		queue, err = ch.QueueDeclare(queueName, true, false, false, false, nil)
		if err != nil {
			fmt.Println(err)
		}
		if queue.Consumers == 0 {
			fmt.Println(fmt.Sprintf("队列%s的消费者数量不足1个，生成新的消费者", queueName))

			go ConsumeHandle(queueName, handle)
		}
	}
}
func KeepConsumerAlive() {
	for {
		createConsumer()
		time.Sleep(time.Minute)
	}
}
