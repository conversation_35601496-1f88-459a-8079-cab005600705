package cps_listener

import (
	"yz-go/component/log"
)

// InitCpsListener 初始化所有CPS订单消费者
func InitCpsListener() {
	log.Log().Info("初始化CPS订单消费者")

	// 初始化拼多多订单消费者
	PushPddCpsOrderSettleHandles()

	// 初始化淘宝订单消费者
	PushTaobaoCpsOrderSettleHandles()

	// 初始化京东订单消费者
	PushJdCpsOrderSettleHandles()

	// 初始化唯品会订单消费者
	PushVipCpsOrderSettleHandles()

	// 初始化拼多多订单消费者
	PushPddOrderSettleHandles()

	// 初始化淘宝订单消费者
	PushTaobaoOrderSettleHandles()

	// 初始化京东订单消费者
	PushJdOrderSettleHandles()

	// 初始化唯品会订单消费者
	PushVipOrderSettleHandles()

	log.Log().Info("CPS订单消费者初始化完成")
}
