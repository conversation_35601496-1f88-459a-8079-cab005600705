package cron

import (
	"distributor/service"
	"errors"
	incomeModel "finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushSettleHandle() {
	task := cron.Task{
		Key:  "distributorSettleAward",
		Name: "分销分成结算",
		// 每半小时执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			Settle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func Settle() {
	//log.Log().Info("分销分成结算,开始")
	// 获取可以结算的奖励[]
	err, awards := service.GetCanSettleAwards()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//log.Log().Info("未找到待结算奖励奖励,返回")
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
	for _, award := range awards {
		//log.Log().Info("分销分成结算,奖励id[" + strconv.Itoa(int(award.ID)) + "]")
		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 奖励结算
			err = service.SettleAwardByTx(tx, award)
			if err != nil {
				log.Log().Info("结算失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}
			// 增加收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(award.Uid)
			income.OrderSn = int(award.OrderSN)
			income.IncomeType = incomeModel.Distributor
			income.Amount = award.Amount
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				//log.Log().Info("增加收入失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}
			return err
		})
		if err != nil {
			//log.Log().Info("结算失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		//log.Log().Info("分销分成结算,成功")
	}
}
