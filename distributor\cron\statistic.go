package cron

import (
	"distributor/model"
	"yz-go/cron"
	"yz-go/source"
)

func PushStatisticHandle() {
	task := cron.Task{
		Key:  "distributorStatistic",
		Name: "分销商分成统计",
		// 每半小时执行一次
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			Statistic()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// 分销商统计数据结构体
type DistributorStats struct {
	TotalAmount   uint
	WaitAmount    uint
	SettledAmount uint
}

func Statistic() {
	// 每次查询一万条记录
	batchSize := 500
	stats := make(map[uint]DistributorStats)
	var offset int
	var err error
	for {
		var records []model.DistributorAward
		err = source.DB().Model(&model.DistributorAward{}).Limit(batchSize).Offset(offset).Find(&records).Error
		if err != nil {
			break
		}
		if len(records) == 0 {
			break
		}
		for _, record := range records {
			if _, exists := stats[record.Uid]; !exists {
				stats[record.Uid] = DistributorStats{}
			}
			stat := stats[record.Uid]
			stat.TotalAmount += record.Amount
			if record.Status == model.Wait {
				stat.WaitAmount += record.Amount
			} else if record.Status == model.Settled {
				stat.SettledAmount += record.Amount
			}
			stats[record.Uid] = stat
		}
		offset += batchSize
	}
	// 批量更新分销商统计数据
	var updates []map[string]interface{}
	for uid, stat := range stats {
		updates = append(updates, map[string]interface{}{
			"uid":                  uid,
			"settle_amount_total":  stat.TotalAmount,
			"wait_settle_amount":   stat.WaitAmount,
			"finish_settle_amount": stat.SettledAmount,
		})
	}
	if len(updates) > 0 {
		err = source.BatchUpdate(updates, "distributors", "uid")
		if err != nil {
			return
		}
	}
}
