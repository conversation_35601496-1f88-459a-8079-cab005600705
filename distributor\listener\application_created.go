package listener

import (
	"application/mq"
	"distributor/upgrade"
	"go.uber.org/zap"
	"yz-go/component/log"
)

func PushApplicationCreatedHandles() {
	mq.PushHandles("applicationCreatedDistributorUpdate", func(applicationMsg mq.ApplicationMessage) (err error) {
		//log.Log().Info("分销商升级监听执行(采购创建)会员id[" + strconv.Itoa(int(applicationMsg.UserID)) + "]")
		// 新增采购会员升级
		err = upgrade.ApplicationHandle(applicationMsg.UserID)
		if err != nil {
			//log.Log().Info("分销升级失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		return nil
	})
}
