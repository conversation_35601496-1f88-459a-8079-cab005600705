package listener

import (
	"distributor/compensate"
	"distributor/compensate_mq"
	"go.uber.org/zap"
	"yz-go/component/log"
)

func PushCompensateAwardHandles() {
	compensate_mq.PushHandles("compensateAward", func(msg compensate_mq.CompensateMessage) (err error) {
		if msg.CanSettle == 1 {
			err = compensate.CanSettleHandle(msg.Uid)
			if err != nil {
				log.Log().<PERSON><PERSON>r("compensateAward-CanSettleHandle"+err.<PERSON>rror(), zap.Any("err", err))
				return nil
			}
		} else {
			err = compensate.Handle(msg.Uid)
			if err != nil {
				log.Log().<PERSON><PERSON>r("compensateAward-Handle"+err.<PERSON><PERSON><PERSON>(), zap.Any("err", err))
				return nil
			}
		}
		return nil
	})
}
