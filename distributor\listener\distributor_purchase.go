package listener

import (
	"distributor/award"
	"distributor/model"
	"distributor/mq"
	"distributor/service"
	"distributor/user"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
)

func DistributorPurchaseListener() {
	mq.PushHandles("DistributorPurchaseAward", func(msg mq.PurchaseMessage) error {
		if msg.MessageType != mq.Purchase {
			return nil
		}
		// 通过msg.Uid查询会员信息
		var err error
		// 分销基础设置
		var setting model.Setting
		err, setting = service.GetSetting()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			log.Log().Error("DistributorPurchaseAward->查询分销基础设置：", zap.Any("err", err))
			return nil
		}
		// 查询订单
		var order award.Order
		err, order = award.GetOrderById(msg.OrderID)
		if err != nil {
			log.Log().Error("DistributorPurchaseAward->查询订单失败：", zap.Any("OrderID", msg.OrderID))
			return nil
		}
		var parentID uint
		err, parentID = user.GetParentIdByUserId(msg.Uid)
		if err != nil {
			log.Log().Error("DistributorPurchaseAward->查询会员上级失败：", zap.Any("uid", msg.Uid))
			return nil
		}
		// 通过会员上级id查询上级是否是分销商
		var distributor model.Distributor
		err, distributor = service.GetDistributorByUserId(parentID)
		if err != nil {
			log.Log().Error("DistributorPurchaseAward->查询会员上级是不是分销商失败：", zap.Any("parentID", parentID))
			return nil
		}
		if distributor.ID == 0 || distributor.LevelID == 0 {
			log.Log().Error("DistributorPurchaseAward->上级不是分销商或者没有等级：", zap.Any("parentID", parentID))
			return nil
		}
		// todo 推荐分销商升级的订单要不要统计到上级分销商的订单中
		// 如果上级是分销商, 上级获得直推分销商奖励, 比例为分销商等级配置的直推分销商奖励比例
		var settleInfo model.MeituanDisSettleInfo
		settleInfo = distributor.LevelInfo.DistributorUpgradeSettleInfo
		if settleInfo.AmountRatio == 0 {
			log.Log().Error("DistributorPurchaseAward->上级分销等级奖励比例为0：", zap.Any("AmountRatio", settleInfo.AmountRatio))
			return nil
		}
		// 分成基数
		settleAmount := int64(order.Amount)
		// 分成金额
		awardAmount := settleAmount * int64(settleInfo.AmountRatio) / 10000
		if awardAmount <= 0 {
			log.Log().Error("DistributorPurchaseAward->分成金额为0：", zap.Any("awardAmount", awardAmount))
			return nil
		}
		// 产生奖励
		var distributorAward model.DistributorAward
		distributorAward.Uid = parentID
		distributorAward.ChildUid = msg.Uid
		distributorAward.LevelID = distributor.LevelID
		distributorAward.LevelName = distributor.LevelInfo.Name
		distributorAward.OrderID = msg.OrderID
		distributorAward.OrderType = model.OrderTypeDistributorPurchase
		distributorAward.OrderSN = order.OrderSN
		distributorAward.OrderAmount = order.Amount
		distributorAward.SettleAmount = uint(settleAmount)
		distributorAward.SettleType = model.SettleTypeDistributorPurchase
		distributorAward.Ratio = settleInfo.AmountRatio
		distributorAward.Amount = uint(awardAmount)
		distributorAward.Status = model.Wait
		distributorAward.SettleDays = setting.Values.SettleDays
		err = source.DB().Create(&distributorAward).Error
		if err != nil {
			log.Log().Error("DistributorPurchaseAward->产生奖励失败：", zap.Any("err", err))
			return nil
		}
		return nil
	})
}
