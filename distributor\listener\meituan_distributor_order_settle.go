package listener

import (
	"distributor/award"
	"distributor/award_mq"
	"go.uber.org/zap"
	"meituan-distributor/mq"
	"yz-go/component/log"
)

func PushMeituanDisOrderSettleHandles() {
	mq.PushHandles("meituanDisSettleDistributorAward", func(orderMsg mq.CpsOrderMessage) (err error) {
		//log.Log().Info("cps分销商奖励监听执行(完成后)订单id[" + strconv.Itoa(int(orderMsg.OrderID)) + "]")
		if orderMsg.MessageType != mq.Settle {
			//log.Log().Info("不是订单完成后事件,返回")
			return nil
		}
		// 分销商是否有分成
		var isAward bool
		// 产生奖励
		err, isAward = award.HandleMeituanDistributor(orderMsg.OrderID)
		if !isAward {
			log.Log().Error("meituanDisSettleDistributorAward没有产生分销奖励,发送消息给机构")
			// 发送消息给机构
			err = award_mq.PublishMessage(orderMsg.OrderID, "", 1, award_mq.Award)
			if err != nil {
				return
			}
		}
		if err != nil {
			//log.Log().Info("cps产生分销奖励[失效]失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}

		return nil
	})
}
