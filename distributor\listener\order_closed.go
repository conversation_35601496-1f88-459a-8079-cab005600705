package listener

import (
	"distributor/service"
	"go.uber.org/zap"
	"order/mq"
	"yz-go/component/log"
)

func PushOrderClosedHandles() {
	mq.PushHandles("orderClosedDistributorAward", func(orderMsg mq.OrderMessage) (err error) {
		//log.Log().Info("分销商奖励监听执行(关闭后)订单id[" + strconv.Itoa(int(orderMsg.OrderID)) + "]")
		if orderMsg.MessageType != mq.Closed {
			//log.Log().Info("不是订单关闭后事件,返回")
			return nil
		}
		// 通过订单id 更改奖励[]失效
		err = service.UpdateAwardLostByOrderId(orderMsg.OrderID)
		if err != nil {
			//log.Log().Info("修改状态[失效]失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		return nil
	})
}
