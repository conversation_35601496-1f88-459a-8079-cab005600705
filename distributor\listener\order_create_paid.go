package listener

import (
	"distributor/model"
	"distributor/service"
	"distributor/upgrade"
	"go.uber.org/zap"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func PushOrderPaidHandles() {
	mq.PushHandles("orderPaidDistributorUpdate", func(orderMsg mq.OrderMessage) (err error) {
		if orderMsg.MessageType == mq.Created {
			var order upgrade.Order
			// 通过订单id获取订单
			err, order = upgrade.GetOrderById(orderMsg.OrderID)
			if err != nil {
				//log.Log().Info("未找到订单,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return nil
			}
			err = upgrade.RecordPurchase(order)
			if err != nil {
				//log.Log().Info("未找到订单,返回")
				log.Log().Error("分销监听订单创建事件产生开通记录失败", zap.Any("err", err))
				return nil
			}
			// 保存当前基础设置在order_request表中
			var setting model.Setting
			err, setting = service.GetSetting()
			if err != nil {
				log.Log().Error("分销监听订单支付后事件，查询基础设置失败", zap.Any("err", err))
				return nil
			}
			orderRequest := model.DistributorOrderRequest{
				OrderID: order.ID,
				Value:   setting.Values,
			}
			err = source.DB().Create(&orderRequest).Error
			if err != nil {
				log.Log().Error("分销监听订单支付后事件，保存基础设置失败", zap.Any("err", err))
				return nil
			}
			return nil
		}
		if orderMsg.MessageType == mq.Paid {
			var order upgrade.Order
			// 通过订单id获取订单
			err, order = upgrade.GetOrderById(orderMsg.OrderID)
			if err != nil {
				//log.Log().Info("未找到订单,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return nil
			}
			// 通过订单升级
			err = upgrade.OrderHandle(order.UserID)
			if err != nil {
				log.Log().Error("分销升级失败", zap.Any("err", err))
				return nil
			}
			// 开通
			err = upgrade.OrderPurchase(order)
			if err != nil {
				//log.Log().Info("未找到订单,返回")
				log.Log().Error("分销监听订单支付事件开通失败", zap.Any("err", err))
			}
			return nil
		}
		return nil
	})
}
