package listener

import (
	"distributor/award"
	"distributor/award_mq"
	"distributor/model"
	"go.uber.org/zap"
	"order/mq"
	"yz-go/component/log"
)

func PushOrderPaidCreateAwardHandles() {
	mq.PushHandles("orderPaidCreateAward", func(orderMsg mq.OrderMessage) (err error) {
		if orderMsg.MessageType != mq.Paid {
			return nil
		}
		// 查询分销基础设置
		err, setting := model.GetOrderRequestByOrderId(orderMsg.OrderID)
		if err != nil {
			log.Log().Error("分销监听订单支付后事件，查询基础设置失败", zap.Any("err", err))
			return nil
		}
		// 如果产生分成是在订单完成后，返回
		if setting.Value.AwardByStatus != 1 {
			return nil
		}
		// 分销商是否有分成
		var isAward bool
		// 订单支付后产生奖励，不能结算。需要等到订单完成后才能结算
		err, isAward = award.Handle(orderMsg.OrderID, 2)
		if !isAward {
			log.Log().Error("orderPaidCreateAward没有产生分销奖励,发送消息给机构")
			// 发送消息给机构
			err = award_mq.PublishMessage(orderMsg.OrderID, "", 2, award_mq.Award)
			if err != nil {
				return
			}
		}
		if err != nil {
			log.Log().Error("orderPaidCreateAward产生分销奖励[失效]失败", zap.Any("err", err))
			return nil
		}
		return nil
	})
}
