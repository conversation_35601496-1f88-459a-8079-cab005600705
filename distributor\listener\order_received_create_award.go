package listener

import (
	"distributor/award"
	"distributor/award_mq"
	"distributor/model"
	"distributor/service"
	"go.uber.org/zap"
	"order/mq"
	"yz-go/component/log"
)

func PushOrderReceivedHandles() {
	mq.PushHandles("orderReceivedCreateAward", func(orderMsg mq.OrderMessage) (err error) {
		log.Log().Error("分销监听订单完成后事件", zap.Any("orderMsg", orderMsg))
		if orderMsg.MessageType != mq.Received {
			//log.Log().Info("不是订单完成后事件,返回")
			return nil
		}
		// 查询分销基础设置
		err, setting := model.GetOrderRequestByOrderId(orderMsg.OrderID)
		if err != nil {
			log.Log().Error("分销监听订单完成后事件，查询基础设置失败", zap.Any("err", err))
			return nil
		}
		// 如果产生分成是在订单支付后，修改状态为可结算；否则产生可以直接结算的奖励
		if setting.Value.AwardByStatus == 1 {
			// 通过订单id查询已产生的分成，修改状态为可结算
			err = service.UpdateAwardCanSettleByOrderId(orderMsg.OrderID)
			if err != nil {
				log.Log().Error("orderReceivedCreateAward更改奖励可结算状态失败", zap.Any("err", err))
				return nil
			}
		} else {
			// 分销商是否有分成
			var isAward bool
			// 产生奖励
			err, isAward = award.Handle(orderMsg.OrderID, 1)
			if !isAward {
				log.Log().Error("orderReceivedCreateAward没有产生分销奖励,发送消息给机构")
				// 发送消息给机构
				err = award_mq.PublishMessage(orderMsg.OrderID, "", 1, award_mq.Award)
				if err != nil {
					return
				}
			}
			if err != nil {
				log.Log().Error("orderReceivedCreateAward产生分销奖励[失效]失败", zap.Any("err", err))
				return nil
			}
			
		}

		return nil
	})
}
