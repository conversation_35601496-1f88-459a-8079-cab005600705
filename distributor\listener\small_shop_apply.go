package listener

import (
	mq "small-shop/apply_mq"
)

func PushAddDisTeamSma() {
	mq.<PERSON>ush<PERSON><PERSON>les("distributorAddTeamSma", func(msg mq.ApplyMessage) (err error) {
		/*if msg.MessageType != mq.Apply {
			return nil
		}
		// 通过msg.Uid查询会员信息
		var user model.User
		err, user = service.GetUserByUid(msg.Uid)
		if err != nil || user.ID == 0 {
			return nil
		}

		// 通过user.parent_id查询上级ins_dis_id
		var pInsDis model.InstitutionDistributor
		err, pInsDis = service.GetInsDistributorByUid(user.ParentId)
		if err != nil || pInsDis.ID == 0 {
			return nil
		}
		// 增加团队数据
		var exist bool
		err, exist = service.CheckInsDistributorChildExistByUidAndType(msg.Uid, 1)
		if err != nil {
			return nil
		}
		if exist {
			return nil
		}
		err = service.AddInsDistributorChild(pInsDis.ID, pInsDis.Uid, msg.Uid, msg.SourceID, msg.SourceLevelID, 1)
		if err != nil {
			return nil
		}
		// 修改insDis的团队分销商数量
		pInsDis.TSC += 1
		err = service.UpdateInsDistributor(pInsDis)
		if err != nil {
			return nil
		}*/
		return nil
	})
}
