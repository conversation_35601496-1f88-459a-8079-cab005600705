package model

import (
	"distributor/user"
	"gorm.io/gorm"
	"yz-go/source"
)

const (
	_ = iota
	Direct
	Indirect
)

const (
	NoCan = 2
	Can   = 1
)

const (
	Wait = iota
	Settled
	Lost = -1
)

const (
	_ = iota
	OrderTypeShop
	OrderTypeSupplier
	OrderTypeSupply
	OrderTypeCps
	OrderTypeJhCps
	OrderTypeMeituanDistributor
	OrderTypeUserUpgrade
	OrderTypeDistributorPurchase
	OrderTypeDouYinGroup //抖音团购
	OrderTypePddEcCps
	OrderTypeJdEcCps
	OrderTypeTaobaoEcCps
	OrderTypeVipEcCps
	OrderTypePddCps
	OrderTypeJdCps
	OrderTypeTaobaoCps
	OrderTypeVipCps
)

const (
	_ = iota
	SettleTypeOrder
	SettleTypeService
	SettleTypeSupplier
	SettleTypeDistributorPurchase
)

type DistributorCompensateAward struct {
	source.Model
	Uid            uint              `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	ChildUid       uint              `json:"child_uid" gorm:"column:child_uid;comment:下级会员id;index;"`
	LevelID        uint              `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	LevelName      string            `json:"level_name" gorm:"level_name;comment:产生奖励时的等级名称;type:varchar(50);size:50;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	OrderType      int               `json:"order_type" gorm:"column:order_type;comment:订单类型:1商城订单2供应商订单3供应链订单4抖音cps订单"`
	OrderTypeName  string            `json:"order_type_name" gorm:"-"`
	OrderSN        uint              `json:"order_sn" gorm:"column:order_sn;comment:编号;"`
	OrderAmount    uint              `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	SettleAmount   uint              `json:"settle_amount" gorm:"column:settle_amount;comment:分成基数(分);"`
	SettleType     int               `json:"settle_type" gorm:"column:settle_type;comment:分成类型1:订单分成2:采购技术服务费分成3:供应商扣点分成;"`
	SettleTypeName string            `json:"settle_type_name" gorm:"-"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分成比例;"`
	ChildRatio     int               `json:"child_ratio" gorm:"column:child_ratio;comment:下级分成比例;default:0;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:已失效;index;"`
	CanSettle      int               `json:"can_settle" gorm:"column:can_settle;comment:是否可以结算 2:不可以 1：可以;default:1;"`
	Layers         int               `json:"layers" gorm:"column:layers;comment:客户关系 1:直推客户 2：间推客户;default:1;type:smallint;size:3;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
}

type DistributorAwardMigration struct {
	source.Model
	Uid            uint              `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	ChildUid       uint              `json:"child_uid" gorm:"column:child_uid;comment:下级会员id;index;"`
	LevelID        uint              `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	LevelName      string            `json:"level_name" gorm:"level_name;comment:产生奖励时的等级名称;type:varchar(50);size:50;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	OrderType      int               `json:"order_type" gorm:"column:order_type;comment:订单类型:1商城订单2供应商订单3供应链订单4抖音cps订单"`
	OrderTypeName  string            `json:"order_type_name" gorm:"-"`
	OrderSN        uint              `json:"order_sn" gorm:"column:order_sn;comment:编号;"`
	OrderAmount    uint              `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	SettleAmount   uint              `json:"settle_amount" gorm:"column:settle_amount;comment:分成基数(分);"`
	SettleType     int               `json:"settle_type" gorm:"column:settle_type;comment:分成类型1:订单分成2:采购技术服务费分成3:供应商扣点分成;"`
	SettleTypeName string            `json:"settle_type_name" gorm:"-"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分成比例;"`
	ChildRatio     int               `json:"child_ratio" gorm:"column:child_ratio;comment:下级分成比例;default:0;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:已失效;index;"`
	CanSettle      int               `json:"can_settle" gorm:"column:can_settle;comment:是否可以结算 2:不可以 1：可以;default:1;"`
	Layers         int               `json:"layers" gorm:"column:layers;comment:客户关系 1:直推客户 2：间推客户;default:1;type:smallint;size:3;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
}

func (DistributorAwardMigration) TableName() string {
	return "distributor_awards"
}

type DistributorAward struct {
	source.Model
	Uid            uint              `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	ChildUid       uint              `json:"child_uid" gorm:"column:child_uid;comment:下级会员id;index;"`
	LevelID        uint              `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	LevelName      string            `json:"level_name" gorm:"level_name;comment:产生奖励时的等级名称;type:varchar(50);size:50;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	OrderType      int               `json:"order_type" gorm:"column:order_type;comment:订单类型:1商城订单2供应商订单3供应链订单4抖音cps订单"`
	OrderTypeName  string            `json:"order_type_name" gorm:"-"`
	OrderSN        uint              `json:"order_sn" gorm:"column:order_sn;comment:编号;"`
	OrderAmount    uint              `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	SettleAmount   uint              `json:"settle_amount" gorm:"column:settle_amount;comment:分成基数(分);"`
	SettleType     int               `json:"settle_type" gorm:"column:settle_type;comment:分成类型1:订单分成2:采购技术服务费分成3:供应商扣点分成;"`
	SettleTypeName string            `json:"settle_type_name" gorm:"-"`
	Ratio          int               `json:"ratio" gorm:"column:ratio;comment:分成比例;"`
	ChildRatio     int               `json:"child_ratio" gorm:"column:child_ratio;comment:下级分成比例;default:0;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:已失效;index;"`
	CanSettle      int               `json:"can_settle" gorm:"column:can_settle;comment:是否可以结算 2:不可以 1：可以;default:1;"`
	Layers         int               `json:"layers" gorm:"column:layers;comment:客户关系 1:直推客户 2：间推客户;default:1;type:smallint;size:3;"`
	StatusName     string            `json:"status_name" gorm:"-"`
	LayersName     string            `json:"layers_name" gorm:"-"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	StatementAt    *source.LocalTime `json:"statement_at"` // 结算时间
	UserInfo       user.User         `json:"user_info" gorm:"foreignKey:Uid"`
	ChildUserInfo  user.User         `json:"child_user_info" gorm:"foreignKey:ChildUid"`
}

func GetOrderTypeName(orderType int) string {
	if orderType == OrderTypeShop {
		return "自营订单"
	} else if orderType == OrderTypeSupplier {
		return "供应商订单"
	} else if orderType == OrderTypeSupply {
		return "供应链订单"
	} else if orderType == OrderTypeCps {
		return "抖音cps订单"
	} else if orderType == OrderTypeJhCps {
		return "聚合cps订单"
	} else if orderType == OrderTypeMeituanDistributor {
		return "美团分销商订单"
	} else if orderType == OrderTypeDistributorPurchase {
		return "开通分销商"
	} else if orderType == OrderTypeDouYinGroup {
		return "抖音团购订单"
	} else {
		return "用户升级订单"
	}
}

func GetSettleTypeName(settleType int) string {
	if settleType == SettleTypeOrder {
		return "订单分成"
	} else if settleType == SettleTypeService {
		return "采购技术服务费分成"
	} else if settleType == SettleTypeSupplier {
		return "供应商扣点分成"
	} else if settleType == SettleTypeDistributorPurchase {
		return "开通分销商分成"
	} else {
		return "供应商扣点分成"
	}
}

func GetStatusName(status int) string {
	if status == Settled {
		return "已结算"
	} else if status == Lost {
		return "已失效"
	} else {
		return "未结算"
	}
}

func (award *DistributorAward) AfterFind(tx *gorm.DB) (err error) {
	award.OrderTypeName = GetOrderTypeName(award.OrderType)
	award.SettleTypeName = GetSettleTypeName(award.SettleType)
	award.StatusName = GetStatusName(award.Status)
	if award.Layers == Direct {
		award.LayersName = "直推客户"
	} else {
		award.LayersName = "间推客户"
	}
	return
}
