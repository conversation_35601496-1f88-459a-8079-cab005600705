package model

import (
	"distributor/user"
	"gorm.io/gorm"
	"yz-go/source"
)

type DistributorMigration struct {
	source.Model
	Uid                       uint `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	LevelID                   uint `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	RecommendOrderCountTotal  int  `json:"recommend_order_count_total" gorm:"column:recommend_order_count_total;comment:累计推荐订单总数;"`
	RecommendOrderAmountTotal uint `json:"recommend_order_amount_total" gorm:"column:recommend_order_amount_total;comment:累计推荐订单总额;"`
	Blacklist                 int  `json:"blacklist" gorm:"column:blacklist;comment:黑名单;"`
	SettleAmountTotal         uint `json:"settle_amount_total" gorm:"column:settle_amount_total;comment:累计分成金额;"`
	FinishSettleAmount        uint `json:"finish_settle_amount" gorm:"column:finish_settle_amount;comment:已结算金额;"`
	WaitSettleAmount          uint `json:"wait_settle_amount"  gorm:"column:wait_settle_amount;comment:未结算金额;"`
}

func (DistributorMigration) TableName() string {
	return "distributors"
}

type Distributor struct {
	source.Model
	Uid                       uint      `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	LevelID                   uint      `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	RecommendOrderCountTotal  int       `json:"recommend_order_count_total" gorm:"column:recommend_order_count_total;comment:累计推荐订单总数;"`
	RecommendOrderAmountTotal uint      `json:"recommend_order_amount_total" gorm:"column:recommend_order_amount_total;comment:累计推荐订单总额;"`
	Blacklist                 int       `json:"blacklist" gorm:"column:blacklist;comment:黑名单,1黑名单0白名单;"`
	SettleAmountTotal         uint      `json:"settle_amount_total" gorm:"column:settle_amount_total;comment:累计分成金额;"`
	FinishSettleAmount        uint      `json:"finish_settle_amount" gorm:"column:finish_settle_amount;comment:已结算金额;"`
	WaitSettleAmount          uint      `json:"wait_settle_amount"  gorm:"column:wait_settle_amount;comment:未结算金额;"`
	UserInfo                  user.User `json:"user_info" gorm:"foreignKey:Uid"`
	LevelInfo                 Level     `json:"level_info" gorm:"foreignKey:LevelID"`
}

func (d *Distributor) AfterFind(tx *gorm.DB) (err error) {
	err, d.SettleAmountTotal = getAllAmountTotalByUid(tx, d.Uid)
	if err != nil {
		return
	}
	err, d.FinishSettleAmount = getAmountTotalByUidAndStatus(tx, d.Uid, Settled)
	if err != nil {
		return
	}
	err, d.WaitSettleAmount = getAmountTotalByUidAndStatus(tx, d.Uid, Wait)
	if err != nil {
		return
	}
	return
}

// 通过会员id和分成状态获取分成总额
func getAmountTotalByUidAndStatus(tx *gorm.DB, uid uint, status int) (err error, amountTotal uint) {
	err = tx.Model(&DistributorAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ? AND status = ?", uid, status).First(&amountTotal).Error
	return
}

// 通过会员id获取全部分成总额
func getAllAmountTotalByUid(tx *gorm.DB, uid uint) (err error, amountTotal uint) {
	err = tx.Model(&DistributorAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ?", uid).First(&amountTotal).Error
	return
}

type Level struct {
	source.Model
	Name                  string                `json:"name" gorm:"name;comment:等级名称;type:varchar(50);size:50;"`
	Weight                int                   `json:"weight" gorm:"column:weight;comment:等级权重;"`
	ShopSettleInfo        ShopSettleInfo        `json:"shop_settle_info" gorm:"column:shop_settle_info;comment:自营订单结算详情;type:json;"`
	SupplierSettleInfo    SupplierSettleInfo    `json:"supplier_settle_info" gorm:"column:supplier_settle_info;comment:供应商订单结算详情;type:json;"`
	SupplySettleInfo      SupplySettleInfo      `json:"supply_settle_info" gorm:"column:supply_settle_info;comment:供应链订单结算详情;type:json;"`
	CpsSettleInfo         CpsSettleInfo         `json:"cps_settle_info" gorm:"column:cps_settle_info;comment:cps订单结算详情;type:json;"`
	DouYinGroupSettleInfo DouYinGroupSettleInfo `json:"dou_yin_group_settle_info" gorm:"column:dou_yin_group_settle_info;comment:抖音团购订单结算详情;type:json;"`

	JhCpsSettleInfo              JhCpsSettleInfo      `json:"jhcps_settle_info" gorm:"column:jhcps_settle_info;comment:cps订单结算详情;type:json;"`
	MeituanDisSettleInfo         MeituanDisSettleInfo `json:"meituan_distributor_settle_info" gorm:"column:meituan_distributor_settle_info;comment:美团分销订单结算详情;type:json;"`
	EquitySettleInfo             SupplySettleInfo     `json:"equity_settle_info" gorm:"column:equity_settle_info;comment:数字权益订单结算详情;type:json;"`
	UserUpgradeSettleInfo        MeituanDisSettleInfo `json:"user_upgrade_settle_info" gorm:"column:user_upgrade_settle_info;comment:会员升级订单结算详情;type:json;"`
	DistributorUpgradeSettleInfo MeituanDisSettleInfo `json:"distributor_upgrade_settle_info" gorm:"column:distributor_upgrade_settle_info;comment:直推分销商奖励设置;type:json;"`

	IShopSettleInfo        ShopSettleInfo        `json:"i_shop_settle_info" gorm:"column:i_shop_settle_info;comment:间推自营订单结算详情;type:json;"`
	ISupplierSettleInfo    SupplierSettleInfo    `json:"i_supplier_settle_info" gorm:"column:i_supplier_settle_info;comment:间推供应商订单结算详情;type:json;"`
	ISupplySettleInfo      SupplySettleInfo      `json:"i_supply_settle_info" gorm:"column:i_supply_settle_info;comment:间推供应链订单结算详情;type:json;"`
	ICpsSettleInfo         CpsSettleInfo         `json:"i_cps_settle_info" gorm:"column:i_cps_settle_info;comment:间推cps订单结算详情;type:json;"`
	IDouYinGroupSettleInfo DouYinGroupSettleInfo `json:"i_dou_yin_group_settle_info" gorm:"column:i_dou_yin_group_settle_info;comment:间推抖音团购订单结算详情;type:json;"`
	IJhCpsSettleInfo       JhCpsSettleInfo       `json:"i_jhcps_settle_info" gorm:"column:i_jhcps_settle_info;comment:间推聚推联盟订单结算详情;type:json;"`
	IEquitySettleInfo      SupplySettleInfo      `json:"i_equity_settle_info" gorm:"column:i_equity_settle_info;comment:间推数字权益订单结算详情;type:json;"`
	IMeituanDisSettleInfo  MeituanDisSettleInfo  `json:"i_meituan_distributor_settle_info" gorm:"column:i_meituan_distributor_settle_info;comment:间推美团分销订单结算详情;type:json;"`
	IUserUpgradeSettleInfo MeituanDisSettleInfo  `json:"i_user_upgrade_settle_info" gorm:"column:i_user_upgrade_settle_info;comment:间推会员升级订单结算详情;type:json;"`
}

func (Level) TableName() string {
	return "distributor_levels"
}
