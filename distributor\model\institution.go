package model

import (
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
)

// Institution 代理表
type Institution struct {
	source.Model
	Uid uint `json:"uid" form:"uid" gorm:"column:uid;comment:用户ID;"`
}

// CheckIsInstitution 检查会员是否已经是代理
func CheckIsInstitution(uid uint) (error, bool) {
	var institution Institution
	err := source.DB().Where("uid = ?", uid).First(&institution).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, false
	}
	if err != nil {
		return err, false
	}
	return nil, true
}
