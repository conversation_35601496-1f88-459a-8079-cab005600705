package model

import (
	"database/sql/driver"
	"distributor/pay_product"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"public-supply/common"
	"strconv"
	yzgoSetting "yz-go/setting"
	"yz-go/source"
)

const (
	// 或
	UpgradeCodeOR = 1
	// 与
	UpgradeCodeWITH = 2
)

type DistributorLevelMigration struct {
	source.Model
	Weight                       int                   `json:"weight" gorm:"column:weight;comment:等级权重;"`
	Name                         string                `json:"name" gorm:"name;comment:等级名称;type:varchar(50);size:50;"`
	UpgradeCode                  int                   `json:"upgrade_code" gorm:"upgrade_code;comment:升级方式1:或2:与;"`
	PayUpgrade                   int                   `json:"pay_upgrade" gorm:"pay_upgrade;comment:付费升级是否勾选;"`
	PayAmount                    int                   `json:"pay_amount" gorm:"pay_amount;comment:付费金额;"`
	OrderAmountSwitch            int                   `json:"order_amount_switch" gorm:"order_amount_switch;comment:累计订单金额是否勾选;"`
	OrderAmountTotal             int                   `json:"order_amount_total" gorm:"order_amount_total;comment:累计订单金额;"`
	OrderCountSwitch             int                   `json:"order_count_switch" gorm:"order_count_switch;comment:累计订单数量是否勾选;"`
	OrderCountTotal              int                   `json:"order_count_total" gorm:"order_count_total;comment:累计订单数量;"`
	RecommendUserSwitch          int                   `json:"recommend_user_switch" gorm:"recommend_user_switch;comment:推荐会员采购数量是否勾选;"`
	RecommendUserTotal           int                   `json:"recommend_user_total" gorm:"recommend_user_total;comment:推荐会员采购数量;"`
	RecommendOrderAmountSwitch   int                   `json:"recommend_order_amount_switch" gorm:"recommend_order_amount_switch;comment:推荐采购订单金额是否勾选;"`
	RecommendOrderAmountTotal    int                   `json:"recommend_order_amount_total" gorm:"recommend_order_amount_total;comment:推荐采购订单金额;"`
	RecommendOrderCountSwitch    int                   `json:"recommend_order_count_switch" gorm:"recommend_order_count_switch;comment:推荐采购订单数量是否勾选;"`
	RecommendOrderCountTotal     int                   `json:"recommend_order_count_total" gorm:"recommend_order_count_total;comment:推荐采购订单数量;"`
	BuyProductSwitch             int                   `json:"buy_product_switch" gorm:"buy_product_switch;comment:购买指定商品是否勾选;"`
	ShopSettleInfo               ShopSettleInfo        `json:"shop_settle_info" gorm:"column:shop_settle_info;comment:自营订单结算详情;type:json;"`
	SupplierSettleInfo           SupplierSettleInfo    `json:"supplier_settle_info" gorm:"column:supplier_settle_info;comment:供应商订单结算详情;type:json;"`
	SupplySettleInfo             SupplySettleInfo      `json:"supply_settle_info" gorm:"column:supply_settle_info;comment:供应链订单结算详情;type:json;"`
	CpsSettleInfo                CpsSettleInfo         `json:"cps_settle_info" gorm:"column:cps_settle_info;comment:cps订单结算详情;type:json;"`
	JhCpsSettleInfo              JhCpsSettleInfo       `json:"jhcps_settle_info" gorm:"column:jhcps_settle_info;comment:聚推联盟订单结算详情;type:json;"`
	EquitySettleInfo             SupplySettleInfo      `json:"equity_settle_info" gorm:"column:equity_settle_info;comment:数字权益订单结算详情;type:json;"`
	MeituanDisSettleInfo         MeituanDisSettleInfo  `json:"meituan_distributor_settle_info" gorm:"column:meituan_distributor_settle_info;comment:美团分销订单结算详情;type:json;"`
	UserUpgradeSettleInfo        MeituanDisSettleInfo  `json:"user_upgrade_settle_info" gorm:"column:user_upgrade_settle_info;comment:会员升级订单结算详情;type:json;"`
	DistributorUpgradeSettleInfo MeituanDisSettleInfo  `json:"distributor_upgrade_settle_info" gorm:"column:distributor_upgrade_settle_info;comment:直推分销商奖励设置;type:json;"`
	IShopSettleInfo              ShopSettleInfo        `json:"i_shop_settle_info" gorm:"column:i_shop_settle_info;comment:间推自营订单结算详情;type:json;"`
	ISupplierSettleInfo          SupplierSettleInfo    `json:"i_supplier_settle_info" gorm:"column:i_supplier_settle_info;comment:间推供应商订单结算详情;type:json;"`
	ISupplySettleInfo            SupplySettleInfo      `json:"i_supply_settle_info" gorm:"column:i_supply_settle_info;comment:间推供应链订单结算详情;type:json;"`
	ICpsSettleInfo               CpsSettleInfo         `json:"i_cps_settle_info" gorm:"column:i_cps_settle_info;comment:间推cps订单结算详情;type:json;"`
	IDouYinGroupSettleInfo       DouYinGroupSettleInfo `json:"i_dou_yin_group_settle_info" gorm:"column:i_dou_yin_group_settle_info;comment:间推抖音团购订单结算详情;type:json;"`
	DouYinGroupSettleInfo        DouYinGroupSettleInfo `json:"dou_yin_group_settle_info" gorm:"column:dou_yin_group_settle_info;comment:抖音团购订单结算详情;type:json;"`

	IJhCpsSettleInfo       JhCpsSettleInfo      `json:"i_jhcps_settle_info" gorm:"column:i_jhcps_settle_info;comment:间推聚推联盟订单结算详情;type:json;"`
	IEquitySettleInfo      SupplySettleInfo     `json:"i_equity_settle_info" gorm:"column:i_equity_settle_info;comment:间推数字权益订单结算详情;type:json;"`
	IMeituanDisSettleInfo  MeituanDisSettleInfo `json:"i_meituan_distributor_settle_info" gorm:"column:i_meituan_distributor_settle_info;comment:间推美团分销订单结算详情;type:json;"`
	IUserUpgradeSettleInfo MeituanDisSettleInfo `json:"i_user_upgrade_settle_info" gorm:"column:i_user_upgrade_settle_info;comment:间推会员升级订单结算详情;type:json;"`
	// 关联商品id
	PayProductID uint `json:"pay_product_id" form:"pay_product_id" gorm:"column:pay_product_id;comment:关联商品id;"`
}

func (DistributorLevelMigration) TableName() string {
	return "distributor_levels"
}

func (dl *DistributorLevel) AfterSave(tx *gorm.DB) (err error) {
	// 等级未开启付费通道,或者付费金额等于0
	if dl.PayUpgrade != 1 || dl.PayAmount == 0 {
		return
	}
	var payProduct pay_product.ProductForUpdate
	dl.PayProduct.Price = uint(dl.PayAmount)
	if dl.PayProductID == 0 {
		sku := pay_product.Sku{
			Title:         "默认",
			Price:         dl.PayProduct.Price,
			CostPrice:     dl.PayProduct.Price,
			OriginPrice:   dl.PayProduct.Price,
			GuidePrice:    dl.PayProduct.Price,
			ActivityPrice: dl.PayProduct.Price,
			Stock:         99999,
			Weight:        0,
			Sn:            "",
			OriginalSkuID: 0,
			Options:       pay_product.Options{{SpecName: "规格", SpecItemName: "默认"}},
		}

		payProduct.Skus = append(payProduct.Skus, sku)
	} else {
		err = tx.Preload("Skus").Where("id = ?", dl.PayProductID).First(&payProduct).Error
		if err != nil && len(payProduct.Skus) == 0 {
			return
		}
		payProduct.Skus[0].Price = dl.PayProduct.Price
		payProduct.Skus[0].CostPrice = dl.PayProduct.Price
		payProduct.Skus[0].OriginPrice = dl.PayProduct.Price
		payProduct.Skus[0].GuidePrice = dl.PayProduct.Price
		payProduct.Skus[0].ActivityPrice = dl.PayProduct.Price
		payProduct.Skus[0].Stock = 99999
	}
	payProduct.Price = dl.PayProduct.Price
	payProduct.CostPrice = dl.PayProduct.Price
	payProduct.OriginPrice = dl.PayProduct.Price
	payProduct.GuidePrice = dl.PayProduct.Price
	payProduct.ActivityPrice = dl.PayProduct.Price
	payProduct.Title = dl.Name
	payProduct.Stock = 99999
	payProduct.IsPlugin = 1
	payProduct.IsDisplay = 1
	payProduct.NotDiscount = 1
	payProduct.NotFee = 1
	err, payProduct.GatherSupplyID = getGatherSupplyID(tx)
	if err != nil {
		return
	}

	// 更新产品并添加或删除修改的关联sku与规格、规格项
	err = tx.Model(&pay_product.ProductForUpdate{}).Session(&gorm.Session{FullSaveAssociations: true}).Where("id = ?", payProduct.ID).Save(&payProduct).Error
	if err != nil {
		return
	}
	if payProduct.Skus[0].ID != 0 {
		// 修改规格
		err = tx.Model(&pay_product.Sku{}).Where("id = ?", payProduct.Skus[0].ID).Save(&payProduct.Skus[0]).Error
		if err != nil {
			return
		}
	}

	if payProduct.ID != 0 {
		err = tx.Model(&DistributorLevel{}).Where("id = ?", dl.ID).Update("pay_product_id", payProduct.ID).Error
		if err != nil {
			return
		}
	}
	return
}

func GetSupplyID() (err error, supplyID uint) {
	var supply GatherSupply
	err = source.DB().Unscoped().Model(&GatherSupply{}).Where("category_id = ?", common.LEVEL_BIND_PRODUCT).First(&supply).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return err, 0
	}
	if supply.ID == 0 {
		err = errors.New("查询供应链失败")
		return err, 0
	}
	return err, supply.ID
}

func getGatherSupplyID(tx *gorm.DB) (err error, supplyID uint) {
	var supply GatherSupply
	err = tx.Unscoped().Model(&GatherSupply{}).Where("category_id = ?", common.LEVEL_BIND_PRODUCT).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err, 0
	}
	if supply.ID != 0 {
		return err, supply.ID
	}
	if supply.ID == 0 {
		supply.Name = "会员等级权益-private"
		supply.CategoryID = common.LEVEL_BIND_PRODUCT
		err = tx.Create(&supply).Error
		if err != nil {
			err = nil
			return err, 0
		}
		supplyID = supply.ID
		err = tx.Delete(&supply).Error
		if err != nil {
			err = nil
			return err, 0
		}
		var setting yzgoSetting.SysSetting
		setting.Key = "gatherSupply" + strconv.Itoa(int(supplyID))
		setting.Value = "{}"
		source.DB().Create(&setting)
	}
	return err, supply.ID
}

type GatherSupply struct {
	source.Model
	Name       string `json:"name" form:"name" gorm:"column:name;comment:名;type:varchar(255);size:255;index;"` // 名
	CategoryID uint   `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
}

type DistributorLevel struct {
	source.Model
	Weight                       int                              `json:"weight" gorm:"column:weight;comment:等级权重;"`
	Name                         string                           `json:"name" gorm:"name;comment:等级名称;type:varchar(50);size:50;"`
	UpgradeCode                  int                              `json:"upgrade_code" gorm:"upgrade_code;comment:升级方式1:或2:与;"`
	PayUpgrade                   int                              `json:"pay_upgrade" gorm:"pay_upgrade;comment:付费升级;"`
	PayAmount                    int                              `json:"pay_amount" gorm:"pay_amount;comment:付费金额;"`
	OrderAmountSwitch            int                              `json:"order_amount_switch" gorm:"order_amount_switch;comment:累计订单金额是否勾选;"`
	OrderAmountTotal             int                              `json:"order_amount_total" gorm:"order_amount_total;comment:累计订单金额;"`
	OrderCountSwitch             int                              `json:"order_count_switch" gorm:"order_count_switch;comment:累计订单数量是否勾选;"`
	OrderCountTotal              int                              `json:"order_count_total" gorm:"order_count_total;comment:累计订单数量;"`
	RecommendUserSwitch          int                              `json:"recommend_user_switch" gorm:"recommend_user_switch;comment:推荐会员采购数量是否勾选;"`
	RecommendUserTotal           int                              `json:"recommend_user_total" gorm:"recommend_user_total;comment:推荐会员采购数量;"`
	RecommendOrderAmountSwitch   int                              `json:"recommend_order_amount_switch" gorm:"recommend_order_amount_switch;comment:推荐采购订单金额是否勾选;"`
	RecommendOrderAmountTotal    int                              `json:"recommend_order_amount_total" gorm:"recommend_order_amount_total;comment:推荐采购订单金额;"`
	RecommendOrderCountSwitch    int                              `json:"recommend_order_count_switch" gorm:"recommend_order_count_switch;comment:推荐采购订单数量是否勾选;"`
	RecommendOrderCountTotal     int                              `json:"recommend_order_count_total" gorm:"recommend_order_count_total;comment:推荐采购订单数量;"`
	BuyProductSwitch             int                              `json:"buy_product_switch" gorm:"buy_product_switch;comment:购买指定商品是否勾选;"`
	UpgradeProducts              []DistributorLevelUpgradeProduct `json:"upgrade_products"`
	ShopSettleInfo               ShopSettleInfo                   `json:"shop_settle_info" gorm:"column:shop_settle_info;comment:自营订单结算详情;type:json;"`
	SupplierSettleInfo           SupplierSettleInfo               `json:"supplier_settle_info" gorm:"column:supplier_settle_info;comment:供应商订单结算详情;type:json;"`
	SupplySettleInfo             SupplySettleInfo                 `json:"supply_settle_info" gorm:"column:supply_settle_info;comment:供应链订单结算详情;type:json;"`
	CpsSettleInfo                CpsSettleInfo                    `json:"cps_settle_info" gorm:"column:cps_settle_info;comment:cps订单结算详情;type:json;"`
	JhCpsSettleInfo              JhCpsSettleInfo                  `json:"jhcps_settle_info" gorm:"column:jhcps_settle_info;comment:聚推联盟订单结算详情;type:json;"`
	EquitySettleInfo             SupplySettleInfo                 `json:"equity_settle_info" gorm:"column:equity_settle_info;comment:数字权益订单结算详情;type:json;"`
	MeituanDisSettleInfo         MeituanDisSettleInfo             `json:"meituan_distributor_settle_info" gorm:"column:meituan_distributor_settle_info;comment:美团分销订单结算详情;type:json;"`
	UserUpgradeSettleInfo        MeituanDisSettleInfo             `json:"user_upgrade_settle_info" gorm:"column:user_upgrade_settle_info;comment:会员升级订单结算详情;type:json;"`
	DistributorUpgradeSettleInfo MeituanDisSettleInfo             `json:"distributor_upgrade_settle_info" gorm:"column:distributor_upgrade_settle_info;comment:直推分销商奖励设置;type:json;"`
	IShopSettleInfo              ShopSettleInfo                   `json:"i_shop_settle_info" gorm:"column:i_shop_settle_info;comment:间推自营订单结算详情;type:json;"`
	ISupplierSettleInfo          SupplierSettleInfo               `json:"i_supplier_settle_info" gorm:"column:i_supplier_settle_info;comment:间推供应商订单结算详情;type:json;"`
	ISupplySettleInfo            SupplySettleInfo                 `json:"i_supply_settle_info" gorm:"column:i_supply_settle_info;comment:间推供应链订单结算详情;type:json;"`
	ICpsSettleInfo               CpsSettleInfo                    `json:"i_cps_settle_info" gorm:"column:i_cps_settle_info;comment:间推cps订单结算详情;type:json;"`
	IJhCpsSettleInfo             JhCpsSettleInfo                  `json:"i_jhcps_settle_info" gorm:"column:i_jhcps_settle_info;comment:间推聚推联盟订单结算详情;type:json;"`
	IEquitySettleInfo            SupplySettleInfo                 `json:"i_equity_settle_info" gorm:"column:i_equity_settle_info;comment:间推数字权益订单结算详情;type:json;"`
	IMeituanDisSettleInfo        MeituanDisSettleInfo             `json:"i_meituan_distributor_settle_info" gorm:"column:i_meituan_distributor_settle_info;comment:间推美团分销订单结算详情;type:json;"`
	IUserUpgradeSettleInfo       MeituanDisSettleInfo             `json:"i_user_upgrade_settle_info" gorm:"column:i_user_upgrade_settle_info;comment:间推会员升级订单结算详情;type:json;"`
	// 关联商品id
	PayProductID uint `json:"pay_product_id" form:"pay_product_id" gorm:"column:pay_product_id;comment:关联商品id;"`
	// 关联的付费商品
	PayProduct pay_product.ProductForUpdate `json:"pay_product" gorm:"foreignKey:PayProductID"`
	// 类型 1升级2续费
	PurchaseType           int                   `json:"purchase_type" form:"purchase_type" gorm:"-"`
	IDouYinGroupSettleInfo DouYinGroupSettleInfo `json:"i_dou_yin_group_settle_info" gorm:"column:i_dou_yin_group_settle_info;comment:间推抖音团购订单结算详情;type:json;"`
	DouYinGroupSettleInfo  DouYinGroupSettleInfo `json:"dou_yin_group_settle_info" gorm:"column:dou_yin_group_settle_info;comment:抖音团购订单结算详情;type:json;"`
}

type ShopSettleInfo struct {
	// 计算方式 订单实付金额
	AmountSwitch int `json:"amount_switch"`
	// 计算方式 减成本
	CostSwitch int `json:"cost_switch"`
	// 计算方式 减运费
	FreightSwitch int `json:"freight_switch"`
	// 比例
	FormulaRatio int `json:"formula_ratio"`
	// 采购技术服务费
	BuyServiceSwitch int `json:"buy_service_switch"`
	// 比例
	BuyServiceRatio int `json:"buy_service_ratio"`
}

func (value ShopSettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *ShopSettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type SupplierSettleInfo struct {
	// 计算方式 订单实付金额
	AmountSwitch int `json:"amount_switch"`
	// 计算方式 减成本
	CostSwitch int `json:"cost_switch"`
	// 计算方式 减运费
	FreightSwitch int `json:"freight_switch"`
	// 比例
	FormulaRatio int `json:"formula_ratio"`
	// 供应商扣点
	SupplierRebateSwitch int `json:"supplier_rebate_switch"`
	// 比例
	SupplierRebateRatio int `json:"supplier_rebate_ratio"`
	// 采购技术服务费
	BuyServiceSwitch int `json:"buy_service_switch"`
	// 比例
	BuyServiceRatio int `json:"buy_service_ratio"`
}

func (value SupplierSettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *SupplierSettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type SupplySettleInfo struct {
	// 计算方式 订单实付金额
	AmountSwitch int `json:"amount_switch"`
	// 计算方式 减协议价
	DealSwitch int `json:"deal_switch"`
	// 计算方式 减运费
	FreightSwitch int `json:"freight_switch"`
	// 比例
	FormulaRatio int `json:"formula_ratio"`
	// 采购技术服务费
	BuyServiceSwitch int `json:"buy_service_switch"`
	// 比例
	BuyServiceRatio int `json:"buy_service_ratio"`
}

func (value SupplySettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *SupplySettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (value DouYinGroupSettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *DouYinGroupSettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type CpsSettleInfo struct {
	AmountRatio int `json:"amount_ratio"`
}
type DouYinGroupSettleInfo struct {
	AmountRatio int `json:"amount_ratio"`
}

func (value CpsSettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *CpsSettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type JhCpsSettleInfo struct {
	AmountRatio int `json:"amount_ratio"`
}

func (value JhCpsSettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *JhCpsSettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type MeituanDisSettleInfo struct {
	AmountRatio int `json:"amount_ratio"`
}

func (value MeituanDisSettleInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *MeituanDisSettleInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type DistributorLevelUpgradeProduct struct {
	source.Model
	DistributorLevelID uint   `json:"distributor_level_id" form:"distributor_level_id" gorm:"column:distributor_level_id;comment:distributor_level.id;"`
	ProductID          uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;"`
	ProductImage       string `json:"product_image" form:"product_image" gorm:"column:product_image;comment:商品图片;"`
}
