package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"yz-go/source"
	"yz-go/utils"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		DistributorMigration{},
		DistributorLevelMigration{},
		DistributorLevelUpgradeProduct{},
		DistributorAwardMigration{},
		DistributorPurchaseRecordMigration{},
		DistributorOrderRequest{},
		DistributorCompensateAward{},
	)

	var menus []model.SysMenu
	err = json.Unmarshal([]byte(menu), &menus)

	if collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(2) == true || utils.LocalEnv() == false {
		model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	}

	return
}
