package model

import (
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"yz-go/source"
)

type DistributorOrderRequest struct {
	source.Model
	OrderID uint  `json:"order_id" gorm:"column:order_id;comment:订单id;index;"`
	Value   Value `json:"value" gorm:"type:json;comment:订单请求数据;"`
}

// 通过订单id获取订单请求数据
func GetOrderRequestByOrderId(orderId uint) (err error, orderRequest DistributorOrderRequest) {
	err = source.DB().Where("order_id = ?", orderId).First(&orderRequest).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}
