package model

import "yz-go/source"

// DistributorPurchaseRecordMigration 分销商开通记录迁移表
type DistributorPurchaseRecordMigration struct {
	source.Model
	// 会员id
	Uid uint `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	// 开通前分销商等级id
	BeforeLevelID uint `json:"before_level_id" gorm:"column:before_level_id;comment:开通前分销商等级id;"`
	// 开通前分销商等级名称
	BeforeLevelName string `json:"before_level_name" gorm:"column:before_level_name;comment:开通前分销商等级名称;type:varchar(255);size:255;"`
	// 开通后分销商等级id
	AfterLevelID uint `json:"after_level_id" gorm:"column:after_level_id;comment:开通后分销商等级id;"`
	// 开通后分销商等级名称
	AfterLevelName string `json:"after_level_name" gorm:"column:after_level_name;comment:开通后分销商等级名称;type:varchar(255);size:255;"`
	// 订单id
	OrderID uint `json:"order_id" gorm:"column:order_id;comment:中台订单id;"`
	// 订单总金额(分)
	OrderAmount uint `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	// 支付状态:0待支付1已支付-1已退款
	OrderStatus int8 `json:"order_status" gorm:"column:order_status;comment:支付状态:0待支付1已支付-1已退款;type:smallint(3);size:3;"`
	// 订单状态
	OrderStatusName string `json:"order_status_name" gorm:"column:order_status_name;comment:订单状态;type:varchar(255);size:255;"`
	// 订单支付方式
	OrderPayName string `json:"order_pay_name" gorm:"column:order_pay_name;comment:订单支付方式;type:varchar(255);size:255;"`
	// 上级会员id
	ParentID uint `json:"parent_id" gorm:"column:parent_id;comment:上级会员id;"`
}

// TableName 表名
func (DistributorPurchaseRecordMigration) TableName() string {
	return "distributor_purchase_records"
}

// DistributorPurchaseRecord 分销商开通记录表
type DistributorPurchaseRecord struct {
	source.Model
	// 会员id
	Uid uint `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	// 开通前分销商等级id
	BeforeLevelID uint `json:"before_level_id" gorm:"column:before_level_id;comment:开通前分销商等级id;"`
	// 开通前分销商等级名称
	BeforeLevelName string `json:"before_level_name" gorm:"column:before_level_name;comment:开通前分销商等级名称;type:varchar;size:255;"`
	// 开通后分销商等级id
	AfterLevelID uint `json:"after_level_id" gorm:"column:after_level_id;comment:开通后分销商等级id;"`
	// 开通后分销商等级名称
	AfterLevelName string `json:"after_level_name" gorm:"column:after_level_name;comment:开通后分销商等级名称;type:varchar;size:255;"`
	// 订单id
	OrderID uint `json:"order_id" gorm:"column:order_id;comment:中台订单id;"`
	// 订单总金额(分)
	OrderAmount uint `json:"order_amount" gorm:"column:order_amount;comment:订单总金额(分);"`
	// 支付状态:1待支付2已支付-1已退款
	OrderStatus int `json:"order_status" gorm:"column:order_status;comment:支付状态:1待支付2已支付-1已退款;type:smallint;size:3;"`
	// 订单状态
	OrderStatusName string `json:"order_status_name" gorm:"column:order_status_name;comment:订单状态;type:varchar;size:255;"`
	// 订单支付方式
	OrderPayName string `json:"order_pay_name" gorm:"column:order_pay_name;comment:订单支付方式;type:varchar;size:255;"`
	// 上级会员id
	ParentID uint `json:"parent_id" gorm:"column:parent_id;comment:上级会员id;"`
	UserInfo User `json:"user_info" gorm:"foreignKey:Uid;references:ID;comment:用户信息"`
}

type User struct {
	source.Model
	CreatedAt *source.LocalTime `json:"created_at" gorm:"<-:create"`
	Mobile    string            `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar    string            `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username  string            `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName  string            `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	LevelID   uint              `json:"level_id" form:"level_id"`
	ParentId  uint              `json:"parent_id" form:"parent_id" gorm:"comment:推荐会员id;"`
}
