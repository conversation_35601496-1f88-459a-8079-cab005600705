package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type Setting struct {
	source.Model
	Key    string `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values Value  `json:"value" gorm:"column:value;comment:值;type:json;"`
}

func (Setting) TableName() string {
	return "sys_settings"
}

type Value struct {
	PluginSwitch  int `json:"plugin_switch"`  // 是否开启分销
	SettleDays    int `json:"settle_days"`    // 结算天数
	ProductSwitch int `json:"product_switch"` // 商品是否开启分销
	Layers        int `json:"layers"`         // 提成层级
	// 订单状态：默认完成后，0完成后，1付款后
	AwardByStatus int `json:"award_by_status"`
	// 推广中心是否显示直推小商店
	ShowDirectShop int `json:"show_direct_shop"`
	// 会员中心是否显示分销商付费升级入口
	ShowUpgrade int `json:"show_upgrade"`
	// 权益说明
	EquityStatement string `json:"equity_statement" gorm:"type:text;"`
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
