package order

import (
	orderModel "order/model"
	"yz-go/source"
)

type Order struct {
	source.Model
	Status orderModel.OrderStatus `json:"status"`
	Amount uint                   `json:"amount"`
	UserID uint                   `json:"user_id"`
}

// 获取已支付订单的会员ids
func GetUserIdsByPaid() (err error, userIds []uint) {
	err = source.DB().Model(&Order{}).Distinct("user_id").Where("status > ?", orderModel.WaitPay).Pluck("user_id", &userIds).Error
	return
}

// 获取不是分销商的会员ids
func GetUserIdsNotByDistributorUserIds(distributorUserIds []uint) (err error, userIds []uint) {
	err = source.DB().Model(&Order{}).Distinct("user_id").Where("status > ? AND user_id NOT IN ?", orderModel.WaitPay, distributorUserIds).Pluck("user_id", &userIds).Error
	return
}

// 通过会员ids获取订单总额
func GetAmountTotalByUserIds(childIds []uint, status int8) (err error, total uint64) {
	err = source.DB().Model(&Order{}).Select("COALESCE(SUM(amount), 0)").Where("user_id IN ? AND status = ?", childIds, status).First(&total).Error
	return
}