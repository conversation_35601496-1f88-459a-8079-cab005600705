package request

import yzRequest "yz-go/request"

type LevelSearch struct {
	yzRequest.PageInfo
}

const (
	_ = iota
	SourceAllUser
	SourceOrderPaidUser
	SourceSupplierUser
	SourceApplication
	SourceUserLevel
	SourceDistributorLevel
)

type DistributorByBatch struct {
	// 创建类型 1:单个 2:批量
	CreateType int `json:"create_type" form:"create_type"`
	// 会员id
	Uid uint `json:"uid" form:"uid"`
	// 等级id
	LevelID uint `json:"level_id" form:"level_id"`
	// 会员来源 1:全部会员 2:已消费会员(有已支付订单) 3:供应商 4:采购端会员 5:会员等级 6:分销商等级
	SourceType int `json:"source_type" form:"source_type"`
	// source_type = 5时 会员等级id
	UserLevelID uint `json:"user_level_id" form:"user_level_id"`
	// source_type = 6时 分销等级id
	DistributorLevelID uint `json:"distributor_level_id" form:"distributor_level_id"`
}

type DistributorSearch struct {
	Uid     uint   `json:"uid" form:"uid"`           // 会员id
	Member  string `json:"member" form:"member"`     // 会员昵称,姓名,手机号
	LevelID uint   `json:"level_id" form:"level_id"` // 等级id
	StartAT string `json:"start_at" form:"start_at"` // 成为时间
	EndAT   string `json:"end_at" form:"end_at"`     // 成为时间
	yzRequest.PageInfo
}

type AwardSearch struct {
	Uid        uint   `json:"uid" form:"uid"`                  // 会员id
	Member     string `json:"member" form:"member"`            // 会员昵称,姓名,手机号
	LevelID    uint   `json:"level_id" form:"level_id"`        // 等级id
	OrderSn    uint   `json:"order_sn"  form:"order_sn"`       // 订单编号
	SettleType int    `json:"settle_type"  form:"settle_type"` // 分成类型
	OrderType  int    `json:"order_type"  form:"settle_type"`  // 订单类型
	Status     int    `json:"status"  form:"status"`           // 分成状态
	StartAT    string `json:"start_at" form:"start_at"`        // 分成时间
	EndAT      string `json:"end_at" form:"end_at"`            // 分成时间
	yzRequest.PageInfo
}

type ApiAwardSearch struct {
	Uid        uint   `json:"uid" form:"uid"`                  // 会员id
	OrderSn    uint   `json:"order_sn"  form:"order_sn"`       // 订单编号
	Member     string `json:"member" form:"member"`            // 会员昵称,姓名,手机号
	Status     *int   `json:"status"  form:"status"`           // 分成状态
	SettleType int    `json:"settle_type"  form:"settle_type"` // 分成类型
	yzRequest.PageInfo
}

// PurchaseRecordListRequest 分销开通记录列表查询
type PurchaseRecordListRequest struct {
	// 会员id
	Uid uint `json:"uid" form:"uid" query:"uid"`
	// 手机号
	Mobile string `json:"mobile" form:"mobile" query:"mobile"`
	// 昵称
	Nickname string `json:"nickname" form:"nickname" query:"nickname"`
	// 推荐人手机号
	ParentMobile string `json:"parent_mobile" form:"parent_mobile" query:"parent_mobile"`
	// 开通前分销商等级id
	BeforeLevelID uint `json:"before_level_id" form:"before_level_id" query:"before_level_id"`
	// 开通后分销商等级id
	AfterLevelID uint `json:"after_level_id" form:"after_level_id" query:"after_level_id"`
	// 支付状态
	OrderStatus int `json:"order_status" form:"order_status" query:"order_status"`
	// 创建时间-开始
	StartTime string `json:"start_time" form:"start_time" query:"start_time"`
	// 创建时间-结束
	EndTime string `json:"end_time" form:"end_time" query:"end_time"`
	yzRequest.PageInfo
}
