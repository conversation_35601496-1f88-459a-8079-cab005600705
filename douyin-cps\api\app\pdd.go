package app

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	"net/http"
	v1 "user/api/f/v1"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// PddConvert 商品转链
func PddConvert(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = utils.GetAppID(c)
	request["shop_id"] = utils.GetAppShopID(c)
	if err, data := service.PddConvert(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddConvert 商品转链
func PddConvertPC(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = 0
	request["shop_id"] = 0
	request["app_user_id"] = v1.GetUserID(c)
	if err, data := service.PddConvert(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddUrlGenerate 多多进宝推广链接生成
func PddUrlGenerate(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.PddUrlGenerate(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddUrlConvert 链接解析转链
func PddUrlConvert(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.PddUrlConvert(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddResourceConvert 活动转链
func PddResourceConvert(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = utils.GetAppID(c)

	if err, data := service.PddResourceConvert(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddPidGenerate 创建推广位
func PddPidGenerate(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.PddPidGenerate(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddPidQuery 查询推广位
func PddPidQuery(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.PddPidQuery(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddOrderList 订单列表
func PddOrderList(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.PddOrderList(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddOrderDetail 订单详情
func PddOrderDetail(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.PddOrderDetail(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddOrderDetail 订单详情
func PromUrlGenerate(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = utils.GetAppID(c)
	if err, data := service.PromUrlGenerate(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// PddOrderDetail 订单详情
func PromUrlGeneratePC(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = 0
	request["app_user_id"] = v1.GetUserID(c)
	if err, data := service.PromUrlGenerate(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

// Cats 类目
func Cats(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.Cats(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

func GoodsSearch(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = utils.GetAppID(c)
	if err, data := service.GoodsSearch(request, utils.GetAppUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}
func GoodsSearchPC(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["app_id"] = 0
	request["app_user_id"] = v1.GetUserID(c)
	if err, data := service.GoodsSearch(request, v1.GetUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}

func GoodsDetail2(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.GoodsDetail2(request, utils.GetAppUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}
func GoodsDetail2PC(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.GoodsDetail2(request, v1.GetUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}
}
