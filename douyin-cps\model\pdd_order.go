package model

import (
	"finance/model"
	"time"
	"yz-go/source"
)

// PddOrder 拼多多订单模型
type PddOrder struct {
	source.Model
	OrderSn               string  `json:"order_sn" gorm:"column:order_sn;index"`                                   // 推广订单编号
	GoodsId               int64   `json:"goods_id" gorm:"column:goods_id"`                                         // 商品ID
	GoodsName             string  `json:"goods_name" gorm:"column:goods_name;type:varchar(255)"`                   // 商品标题
	GoodsThumbnailUrl     string  `json:"goods_thumbnail_url" gorm:"column:goods_thumbnail_url;type:varchar(255)"` // 商品缩略图
	GoodsPrice            int64   `json:"goods_price" gorm:"column:goods_price"`                                   // 订单中sku的单件价格，单位为分
	GoodsQuantity         int64   `json:"goods_quantity" gorm:"column:goods_quantity"`                             // 购买商品的数量
	OrderAmount           int64   `json:"order_amount" gorm:"column:order_amount"`                                 // 实际支付金额，单位为分
	OrderCreateTime       int64   `json:"order_create_time" gorm:"column:order_create_time"`                       // 订单生成时间，UNIX时间戳
	OrderPayTime          int64   `json:"order_pay_time" gorm:"column:order_pay_time"`                             // 支付时间
	OrderGroupSuccessTime int64   `json:"order_group_success_time" gorm:"column:order_group_success_time"`         // 成团时间
	OrderVerifyTime       int64   `json:"order_verify_time" gorm:"column:order_verify_time"`                       // 审核时间
	OrderSettleTime       int64   `json:"order_settle_time" gorm:"column:order_settle_time"`                       // 结算时间
	OrderModifyAt         int64   `json:"order_modify_at" gorm:"column:order_modify_at"`                           // 最后更新时间
	OrderStatus           int     `json:"order_status" gorm:"column:order_status"`                                 // 订单状态： -1 未支付; 0-已支付；1-已成团；2-确认收货；3-审核成功；4-审核失败（不可提现）；5-已经结算；8-非多多进宝商品（无佣金订单）
	OrderStatusDesc       string  `json:"order_status_desc" gorm:"column:order_status_desc"`                       // 订单状态描述
	PromotionAmount       float64 `json:"promotion_amount" gorm:"column:promotion_amount"`                         // 佣金金额，单位为分
	PId                   string  `json:"p_id" gorm:"column:p_id"`                                                 // 推广位ID
	CustomParameters      string  `json:"custom_parameters" gorm:"column:custom_parameters"`                       // 自定义参数
	Type                  int     `json:"type" gorm:"column:type"`                                                 // 订单类型
	GroupId               int64   `json:"group_id" gorm:"column:group_id"`                                         // 团ID
	AuthDuoId             int64   `json:"auth_duo_id" gorm:"column:auth_duo_id"`                                   // 渠道ID
	ZsDuoId               int64   `json:"zs_duo_id" gorm:"column:zs_duo_id"`                                       // 招商多多客ID
	CpaNew                int     `json:"cpa_new" gorm:"column:cpa_new"`                                           // 是否是 cpa 新用户，1表示是，0表示否
	FailReason            string  `json:"fail_reason" gorm:"column:fail_reason;type:varchar(255)"`                 // 审核失败原因
	MatchChannel          int     `json:"match_channel" gorm:"column:match_channel"`                               // 匹配渠道
	OrderReceiveTime      int64   `json:"order_receive_time" gorm:"column:order_receive_time"`                     // 收货时间
	BatchNo               string  `json:"batch_no" gorm:"column:batch_no"`                                         // 结算批次号
	DuoCouponAmount       int64   `json:"duo_coupon_amount" gorm:"column:duo_coupon_amount"`                       // 多多进宝券金额，单位为分
	SceneAtMarketFee      int64   `json:"scene_at_market_fee" gorm:"column:scene_at_market_fee"`                   // 直播间站外推广费用，单位为分
	AppID                 int     `json:"app_id" gorm:"column:app_id"`                                             // 商城ID
	AppUserID             int     `json:"app_user_id" gorm:"column:app_user_id"`                                   // 商城会员ID
	// 额外字段
	ShopID           int       `json:"shop_id" gorm:"default:0;"`
	SyncTime         time.Time `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus    int       `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	AppProcessStatus int       `json:"app_process_status" gorm:"default:0"`
}

// TableName 设置表名
func (PddOrder) TableName() string {
	return "pdd_orders"
}

type PddOrderModel struct {
	source.Model
	OrderSn               string  `json:"order_sn" gorm:"column:order_sn;index"`                                   // 推广订单编号
	GoodsId               int64   `json:"goods_id" gorm:"column:goods_id"`                                         // 商品ID
	GoodsName             string  `json:"goods_name" gorm:"column:goods_name;type:varchar(255)"`                   // 商品标题
	GoodsThumbnailUrl     string  `json:"goods_thumbnail_url" gorm:"column:goods_thumbnail_url;type:varchar(255)"` // 商品缩略图
	GoodsPrice            int64   `json:"goods_price" gorm:"column:goods_price"`                                   // 订单中sku的单件价格，单位为分
	GoodsQuantity         int64   `json:"goods_quantity" gorm:"column:goods_quantity"`                             // 购买商品的数量
	OrderAmount           int64   `json:"order_amount" gorm:"column:order_amount"`                                 // 实际支付金额，单位为分
	OrderCreateTime       int64   `json:"order_create_time" gorm:"column:order_create_time"`                       // 订单生成时间，UNIX时间戳
	OrderPayTime          int64   `json:"order_pay_time" gorm:"column:order_pay_time"`                             // 支付时间
	OrderGroupSuccessTime int64   `json:"order_group_success_time" gorm:"column:order_group_success_time"`         // 成团时间
	OrderVerifyTime       int64   `json:"order_verify_time" gorm:"column:order_verify_time"`                       // 审核时间
	OrderSettleTime       int64   `json:"order_settle_time" gorm:"column:order_settle_time"`                       // 结算时间
	OrderModifyAt         int64   `json:"order_modify_at" gorm:"column:order_modify_at"`                           // 最后更新时间
	OrderStatus           int     `json:"order_status" gorm:"column:order_status"`                                 // 订单状态： -1 未支付; 0-已支付；1-已成团；2-确认收货；3-审核成功；4-审核失败（不可提现）；5-已经结算；8-非多多进宝商品（无佣金订单）
	OrderStatusDesc       string  `json:"order_status_desc" gorm:"column:order_status_desc"`                       // 订单状态描述
	PromotionAmount       float64 `json:"promotion_amount" gorm:"column:promotion_amount"`                         // 佣金金额，单位为分
	PId                   string  `json:"p_id" gorm:"column:p_id"`                                                 // 推广位ID
	CustomParameters      string  `json:"custom_parameters" gorm:"column:custom_parameters"`                       // 自定义参数
	Type                  int     `json:"type" gorm:"column:type"`                                                 // 订单类型
	GroupId               int64   `json:"group_id" gorm:"column:group_id"`                                         // 团ID
	AuthDuoId             int64   `json:"auth_duo_id" gorm:"column:auth_duo_id"`                                   // 渠道ID
	ZsDuoId               int64   `json:"zs_duo_id" gorm:"column:zs_duo_id"`                                       // 招商多多客ID
	CpaNew                int     `json:"cpa_new" gorm:"column:cpa_new"`                                           // 是否是 cpa 新用户，1表示是，0表示否
	FailReason            string  `json:"fail_reason" gorm:"column:fail_reason;type:varchar(255)"`                 // 审核失败原因
	MatchChannel          int     `json:"match_channel" gorm:"column:match_channel"`                               // 匹配渠道
	OrderReceiveTime      int64   `json:"order_receive_time" gorm:"column:order_receive_time"`                     // 收货时间
	BatchNo               string  `json:"batch_no" gorm:"column:batch_no"`                                         // 结算批次号
	DuoCouponAmount       int64   `json:"duo_coupon_amount" gorm:"column:duo_coupon_amount"`                       // 多多进宝券金额，单位为分
	SceneAtMarketFee      int64   `json:"scene_at_market_fee" gorm:"column:scene_at_market_fee"`                   // 直播间站外推广费用，单位为分
	AppID                 int     `json:"app_id" gorm:"column:app_id"`                                             // 商城ID
	AppUserID             int     `json:"app_user_id" gorm:"column:app_user_id"`                                   // 商城会员ID
	ShopID                int     `json:"shop_id" gorm:"default:0;"`

	// 额外字段
	SyncTime         time.Time   `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus    int         `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	AppProcessStatus int         `json:"app_process_status" gorm:"default:0"`
	Application      Application `json:"application" gorm:"foreignKey:AppID;references:ID"`
	User             model.User  `json:"user" gorm:"foreignKey:AppUserID;references:ID"`
}

// TableName 设置表名
func (PddOrderModel) TableName() string {
	return "pdd_orders"
}

// PddOrderListResponse 拼多多订单列表响应结构
type PddOrderListResponse struct {
	Code int `json:"code"`
	Data struct {
		List      []PddOrder `json:"list"`
		Total     int        `json:"total"`
		Page      int        `json:"page"`
		PageSize  int        `json:"page_size"`
		Statistic struct {
			Total      int `json:"total"`
			WaitSettle int `json:"wait_settle"`
			Settled    int `json:"settled"`
			Invalid    int `json:"invalid"`
		} `json:"statistic"`
		Status struct {
			Field1 string `json:"-1"`
			Field2 string `json:"0"`
			Field3 string `json:"1"`
			Field4 string `json:"2"`
			Field5 string `json:"3"`
			Field6 string `json:"4"`
			Field7 string `json:"5"`
			Field8 string `json:"8"`
		} `json:"status"`
	} `json:"data"`
	Msg string `json:"msg"`
}
