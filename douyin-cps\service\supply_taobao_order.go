package service

import (
	"douyin-cps/model"
	"douyin-cps/utils"
	"encoding/json"
	model2 "finance/model"
	"fmt"
	"go.uber.org/zap"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

// 淘宝订单列表响应结构
type SupplyTaobaoOrderListResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Orders struct {
			List     []model.SupplyTaobaoOrder `json:"list"`
			Total    int64                     `json:"total"`
			Page     int                       `json:"page"`
			PageSize int                       `json:"page_size"`
		} `json:"orders"`
		Statistic struct {
			Total      int64 `json:"total"`
			WaitSettle int64 `json:"wait_settle"`
			Settled    int64 `json:"settled"`
			Invalid    int64 `json:"invalid"`
		} `json:"statistic"`
	} `json:"data"`
}

// SyncTaobaoOrdersByDateRange 根据日期范围同步淘宝订单
func SyncTaobaoOrdersByDateRange(startTime, endTime string) error {
	log.Log().Info("开始同步淘宝订单",
		zap.String("开始时间", startTime),
		zap.String("结束时间", endTime))

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		log.Log().Error("获取API密钥失败", zap.Error(err))
		return err
	}

	// 构建请求参数
	requestParams := map[string]interface{}{
		"page":       1,
		"page_size":  100,
		"start_time": startTime,
		"end_time":   endTime,
	}

	// 发送请求
	err, result := utils.NewSelfAPIRequest(setting.EcCpsHost, setting.EcCpsAppKey, setting.EcCpsAppSecret).Execute("/app/ecCpsCtrl/taobaoOrder/list", requestParams)
	if err != nil {
		log.Log().Error("请求淘宝订单列表接口失败", zap.Error(err))
		return err
	}

	// 解析响应
	var response SupplyTaobaoOrderListResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		log.Log().Error("解析响应数据失败", zap.Error(err))
		return err
	}

	// 检查响应状态
	if response.Code != 0 {
		log.Log().Error("接口返回错误", zap.String("错误信息", response.Msg))
		return fmt.Errorf("接口返回错误: %s", response.Msg)
	}

	// 处理订单数据
	orders := response.Data.Orders.List
	if len(orders) == 0 {
		log.Log().Info("没有新的订单数据")
		return nil
	}

	log.Log().Info("获取到订单数据", zap.Int("数量", len(orders)))

	// 获取现有订单ID
	var existingOrders []model.SupplyTaobaoOrder
	err = source.DB().Select("id, trade_id").Find(&existingOrders).Error
	if err != nil {
		log.Log().Error("查询现有订单失败", zap.Error(err))
		return err
	}

	// 创建订单ID映射
	existingOrderMap := make(map[string]model.SupplyTaobaoOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.TradeId] = order
	}

	// 分离新订单和需要更新的订单
	var newOrders []model.SupplyTaobaoOrder
	var updateOrders []model.SupplyTaobaoOrder

	for _, order := range orders {
		if _, exists := existingOrderMap[order.TradeId]; !exists {
			// 新订单
			order.SyncTime = time.Now()
			order.ProcessStatus = 0
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.TradeId]
			dbOrder.TkStatus = order.TkStatus
			dbOrder.RefundTag = order.RefundTag
			dbOrder.TkEarningTime = order.TkEarningTime
			dbOrder.PubShareFee = order.PubShareFee
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		err = source.DB().CreateInBatches(&newOrders, 50).Error
		if err != nil {
			log.Log().Error("批量创建订单失败", zap.Error(err))
			return err
		}
		log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		for _, order := range updateOrders {
			err = source.DB().Model(&model.SupplyTaobaoOrder{}).Where("trade_id = ?", order.TradeId).Updates(map[string]interface{}{
				"tk_status":       order.TkStatus,
				"refund_tag":      order.RefundTag,
				"tk_earning_time": order.TkEarningTime,
				"pub_share_fee":   order.PubShareFee,
				"sync_time":       time.Now(),
			}).Error

			if err != nil {
				log.Log().Error("更新订单失败", zap.String("订单ID", order.TradeId), zap.Error(err))
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	log.Log().Info("同步淘宝订单完成",
		zap.Int("新增订单", len(newOrders)),
		zap.Int("更新订单", len(updateOrders)))

	return nil
}

// 淘宝订单列表请求参数
type SupplyTaobaoOrderListRequest struct {
	Page      int    `form:"page" json:"page" binding:"required"`           // 页码
	PageSize  int    `form:"page_size" json:"page_size" binding:"required"` // 每页数量
	TradeId   string `form:"trade_id" json:"trade_id"`                      // 订单号
	Status    *int   `form:"status" json:"status"`                          // 订单状态
	StartTime string `form:"start_time" json:"start_time"`                  // 开始时间
	EndTime   string `form:"end_time" json:"end_time"`                      // 结束时间
	AppID     *uint  `json:"app_id"`
	UserID    uint   `json:"user_id"`
	ShopID    *uint  `json:"shop_id"`
}

// 淘宝订单统计数据
type SupplyTaobaoOrderStatistic struct {
	Total      int64 `json:"total"`       // 总订单数
	WaitSettle int64 `json:"wait_settle"` // 待结算订单数
	Settled    int64 `json:"settled"`     // 已结算订单数
	Invalid    int64 `json:"invalid"`     // 无效/已失效订单数
}

// 淘宝订单列表结果
type SupplyTaobaoOrderListResult struct {
	Orders    interface{}                `json:"orders"`    // 订单列表（包含分页信息）
	Statistic SupplyTaobaoOrderStatistic `json:"statistic"` // 统计数据
}

// GetSupplyTaobaoOrderList 获取供应链淘宝订单列表
func GetSupplyTaobaoOrderList(req SupplyTaobaoOrderListRequest) (error, SupplyTaobaoOrderListResult) {
	var result SupplyTaobaoOrderListResult

	// 构建查询
	db := source.DB().Model(&model.SupplyTaobaoOrder{})
	if req.AppID != nil {
		db = db.Where("app_id = ?", req.AppID)

	}
	if req.ShopID != nil {
		db = db.Where("shop_id = ?", req.ShopID)

	}

	// 条件查询
	if req.TradeId != "" {
		db = db.Where("trade_id = ?", req.TradeId)
	}
	if req.Status != nil {
		db = db.Where("tk_status = ?", *req.Status)
	}
	if req.StartTime != "" {
		db = db.Where("sync_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("sync_time <= ?", req.EndTime)
	}

	// 统计数据
	var statistic SupplyTaobaoOrderStatistic
	// 总订单数
	db.Count(&statistic.Total)
	// 待结算订单数 (状态为12-付款，且未维权的订单)
	source.DB().Model(&model.SupplyTaobaoOrder{}).Where("tk_status = ? AND refund_tag = ?", 12, 0).Count(&statistic.WaitSettle)
	// 已结算订单数 (状态为3-结算)
	source.DB().Model(&model.SupplyTaobaoOrder{}).Where("tk_status = ?", 3).Count(&statistic.Settled)
	// 无效/已失效订单数 (状态为13-失效)
	source.DB().Model(&model.SupplyTaobaoOrder{}).Where("tk_status = ?", 13).Count(&statistic.Invalid)

	// 分页查询
	var list []model.SupplyTaobaoOrder
	err := db.Order("sync_time DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Find(&list).Error
	if err != nil {
		log.Log().Error("查询供应链淘宝订单列表失败", zap.Error(err))
		return fmt.Errorf("查询失败: %v", err), result
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, req.UserID).Error
	if err != nil {
		return fmt.Errorf("查询taobao订单列表失败: %v", err), result
	}
	if *req.AppID > 0 {
		for key, item := range list {
			// 将计算结果转回字符串，保留两位小数
			list[key].PubShareCommission = item.PubShareCommission * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		}
	}

	// 返回结果
	pageResult := map[string]interface{}{
		"list":      list,
		"total":     statistic.Total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}

	result.Orders = pageResult
	result.Statistic = statistic

	return nil, result
}

// GetSupplyTaobaoOrderDetail 获取供应链淘宝订单详情
func GetSupplyTaobaoOrderDetail(id string) (error, model.SupplyTaobaoOrder) {
	var order model.SupplyTaobaoOrder
	err := source.DB().Where("id = ? OR trade_id = ?", id, id).First(&order).Error
	if err != nil {
		log.Log().Error("查询供应链淘宝订单详情失败", zap.Error(err))
		return fmt.Errorf("查询失败: %v", err), order
	}

	return nil, order
}

// ExportSupplyTaobaoOrders 导出供应链淘宝订单
func ExportSupplyTaobaoOrders(req SupplyTaobaoOrderListRequest) error {
	// 构建查询
	db := source.DB().Model(&model.SupplyTaobaoOrder{})

	// 条件查询
	if req.TradeId != "" {
		db = db.Where("trade_id = ?", req.TradeId)
	}
	if req.Status != nil {
		db = db.Where("tk_status = ?", *req.Status)
	}
	if req.StartTime != "" {
		db = db.Where("tk_create_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		db = db.Where("tk_create_time <= ?", req.EndTime)
	}

	// 查询数据
	var list []model.SupplyTaobaoOrder
	err := db.Order("sync_time DESC").Find(&list).Error
	if err != nil {
		log.Log().Error("查询供应链淘宝订单列表失败", zap.Error(err))
		return fmt.Errorf("查询失败: %v", err)
	}

	// 调用导出服务
	return ExportTaobaoOrders(list)
}

// ExportTaobaoOrders 导出淘宝订单到CSV文件
func ExportTaobaoOrders(orders []model.SupplyTaobaoOrder) error {
	// 这里可以实现导出功能，或者调用已有的导出功能
	// 为简化示例，这里只返回成功
	log.Log().Info("导出淘宝订单", zap.Int("数量", len(orders)))
	return nil
}
