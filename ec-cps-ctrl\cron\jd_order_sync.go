package cron

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 同步配置
type JDSyncConfig struct {
	LookBackHours   int           // 查询过去多少小时的订单
	LookBackMinutes int           // 查询过去多少小时的订单
	PageSize        int           // 每页数量
	RetryCount      int           // 重试次数
	RetryInterval   time.Duration // 重试间隔
	WorkerCount     int           // 工作协程数量
	BatchSize       int           // 批量插入大小
	SyncInterval    time.Duration // 同步间隔
}

// 默认配置
var jdDefaultConfig = JDSyncConfig{
	LookBackHours:   3,
	LookBackMinutes: 3,
	PageSize:        100,
	RetryCount:      3,
	RetryInterval:   time.Second * 2,
	WorkerCount:     5,
	BatchSize:       50,
	SyncInterval:    time.Minute * 5,
}

// 同步京东订单 - 支持48小时分时段查询
func SyncJDOrders(config JDSyncConfig) error {
	startTime := time.Now()
	log.Log().Info("开始同步京东订单",
		zap.Int("查询过去小时", config.LookBackHours),
		zap.String("策略", "分时段查询(每次1小时)"))

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return fmt.Errorf("获取API密钥失败: %v", err)
	}

	// 创建订单通道和等待组
	orderChan := make(chan []model.JDOrder, 20) // 增加缓冲区，支持更多时段
	var wg sync.WaitGroup

	// 启动工作协程处理订单
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			processJDOrdersWorker(orderChan, config.BatchSize, workerID)
		}(i)
	}

	// 分时段获取订单数据
	go func() {
		defer close(orderChan)
		totalOrders, totalPages, err := fetchJDOrdersInHourlySegments(setting, config, orderChan)
		if err != nil {
			log.Log().Error("获取京东订单失败", zap.Error(err))
		} else {
			log.Log().Info("订单获取完成",
				zap.Int("总订单数", totalOrders),
				zap.Int("总页数", totalPages))
		}
	}()

	// 等待所有工作协程完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Log().Info("京东订单同步完成", zap.Duration("耗时", elapsedTime))
	return nil
}

// fetchJDOrdersInHourlySegments 分时段获取京东订单（每次查询1小时）
func fetchJDOrdersInHourlySegments(setting model.CpsValue, config JDSyncConfig, orderChan chan<- []model.JDOrder) (int, int, error) {
	// 创建HTTP客户端
	client := resty.New().SetRetryCount(config.RetryCount).SetRetryWaitTime(config.RetryInterval)

	// 计算总的查询时间范围
	endTime := time.Now()
	totalStartTime := endTime.Add(-time.Duration(config.LookBackMinutes) * time.Minute)

	log.Log().Info("开始分时段查询",
		zap.String("总开始时间", totalStartTime.Format("2006-01-02 15:04:05")),
		zap.String("总结束时间", endTime.Format("2006-01-02 15:04:05")),
		zap.Int("总时段数", config.LookBackMinutes))

	totalPages := 0
	totalOrders := 0

	// 按小时分段查询（从最新时间往前推）
	//for i := 0; i < config.LookBackMinutes; i++ {
	// 计算当前时段的时间范围（每段1小时）
	segmentEndTime := endTime
	segmentStartTime := totalStartTime

	log.Log().Info("查询时段",
		zap.Int("时段", 1),
		zap.String("开始时间", segmentStartTime.Format("2006-01-02 15:04:05")),
		zap.String("结束时间", segmentEndTime.Format("2006-01-02 15:04:05")))

	// 查询当前时段的订单
	segmentPages, segmentOrders, err := fetchJDOrdersForTimeSegment(
		client, setting, config, segmentStartTime, segmentEndTime, orderChan)

	if err != nil {
		log.Log().Error("查询时段失败",
			zap.Int("时段", 1),
			zap.Error(err))
		// 继续查询下一个时段，不中断整个过程
		return 0, 0, err
	}

	totalPages += segmentPages
	totalOrders += segmentOrders

	log.Log().Info("时段查询完成",
		zap.Int("时段", 1),
		zap.Int("页数", segmentPages),
		zap.Int("订单数", segmentOrders))

	// 添加短暂延迟，避免API调用过于频繁
	time.Sleep(200 * time.Millisecond)
	//}

	log.Log().Info("所有时段查询完成",
		zap.Int("总页数", totalPages),
		zap.Int("总订单数", totalOrders))

	return totalOrders, totalPages, nil
}

// fetchJDOrdersForTimeSegment 查询指定时间段的订单（1小时内）
func fetchJDOrdersForTimeSegment(client *resty.Client, setting model.CpsValue, config JDSyncConfig,
	startTime, endTime time.Time, orderChan chan<- []model.JDOrder) (int, int, error) {

	pageIndex := 1
	totalPages := 0
	totalOrders := 0

	for {
		var requestParams = map[string]string{
			"apikey":    setting.ApiKey,
			"pageIndex": fmt.Sprintf("%d", pageIndex),
			"pageSize":  fmt.Sprintf("%d", config.PageSize),
			"type":      "3", // 更新时间
			"startTime": startTime.Format("2006-01-02 15:04:05"),
			"endTime":   endTime.Format("2006-01-02 15:04:05"),
		}
		// 请求当前页数据
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetQueryParams(requestParams).
			Post("http://api.tbk.dingdanxia.com/jd/order_details2")

		if err != nil {
			return totalPages, totalOrders, fmt.Errorf("请求第%d页失败: %v", pageIndex, err)
		}

		var orderResp model.JDOrderResponse
		if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
			return totalPages, totalOrders, fmt.Errorf("解析第%d页数据失败: %v", pageIndex, err)
		}

		if orderResp.Code != 200 {
			jsonData, _ := json.Marshal(&requestParams)
			return totalPages, totalOrders, fmt.Errorf("接口返回错误(第%d页): %s;request:%s;result:%s", pageIndex, orderResp.Msg, string(jsonData), string(resp.Body()))
		}

		totalPages++

		// 发送数据到通道
		if len(orderResp.Data) > 0 {
			orderChan <- orderResp.Data
			totalOrders += len(orderResp.Data)
			log.Log().Debug("获取页面数据",
				zap.Int("页码", pageIndex),
				zap.Int("订单数量", len(orderResp.Data)))
		}

		// 检查是否还有更多数据
		if !orderResp.HasMore {
			break
		}

		pageIndex++

		// 添加页面间延迟，避免请求过快
		time.Sleep(50 * time.Millisecond)
	}

	return totalPages, totalOrders, nil
}

// 工作协程处理订单
func processJDOrdersWorker(orderChan <-chan []model.JDOrder, batchSize int, workerID int) {
	log.Log().Info("工作协程启动", zap.Int("协程ID", workerID))

	for orders := range orderChan {
		// 分批处理订单
		processJDBatchOrders(orders, batchSize, workerID)
	}

	log.Log().Info("工作协程完成", zap.Int("协程ID", workerID))
}

// 批量处理订单
func processJDBatchOrders(orders []model.JDOrder, batchSize int, workerID int) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理订单", zap.Int("协程ID", workerID), zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderIds []int64
	for _, order := range orders {
		orderIds = append(orderIds, order.OrderId)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.JDOrder
	result := source.DB().Where("order_id IN ?", orderIds).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[int64]model.JDOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderId] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.JDOrder
	var updateOrders []model.JDOrder
	var allOrders []model.JDOrder
	for _, order := range orders {
		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.OrderId]; !exists {
			// 新订单
			var jdRelation model.CpsJDRelation
			source.DB().Where("id = ?", order.PositionId).First(&jdRelation)
			order.AppID = jdRelation.AppID
			order.ParentAppID = jdRelation.ParentAppId
			order.AppUserID = jdRelation.AppUserID
			order.ShopID = jdRelation.ShopID
			newOrders = append(newOrders, order)
			allOrders = append(allOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderId]
			dbOrder.ValidCode = order.ValidCode
			dbOrder.SkuReturnNum = order.SkuReturnNum
			dbOrder.SkuFrozenNum = order.SkuFrozenNum
			dbOrder.FinishTime = order.FinishTime
			dbOrder.ModifyTime = order.ModifyTime
			dbOrder.EstimateCosPrice = order.EstimateCosPrice
			dbOrder.EstimateFee = order.EstimateFee
			dbOrder.ActualCosPrice = order.ActualCosPrice
			dbOrder.ActualFee = order.ActualFee
			dbOrder.PayMonth = order.PayMonth
			dbOrder.ExpressStatus = order.ExpressStatus
			dbOrder.SyncTime = time.Now()
			if dbOrder.ModifyTime != order.ModifyTime {
				updateOrders = append(updateOrders, dbOrder)
				allOrders = append(allOrders, dbOrder)

			}
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.JDOrder{}).Clauses(clause.Returning{}).Where("order_id = ?", order.OrderId).Updates(map[string]interface{}{
					"valid_code":         order.ValidCode,
					"sku_return_num":     order.SkuReturnNum,
					"sku_frozen_num":     order.SkuFrozenNum,
					"finish_time":        order.FinishTime,
					"modify_time":        order.ModifyTime,
					"estimate_cos_price": order.EstimateCosPrice,
					"estimate_fee":       order.EstimateFee,
					"actual_cos_price":   order.ActualCosPrice,
					"actual_fee":         order.ActualFee,
					"pay_month":          order.PayMonth,
					"express_status":     order.ExpressStatus,
					"sync_time":          time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.Int("协程ID", workerID), zap.Int64("订单ID", order.OrderId), zap.Error(err))
				}
			}
			log.Log().Info("成功更新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	// 处理EcCpsOrderModel映射
	processJDEcCpsOrders(allOrders, batchSize, workerID)

	log.Log().Info("完成订单处理", zap.Int("协程ID", workerID), zap.Int("新增数量", len(newOrders)), zap.Int("更新数量", len(updateOrders)))
}

// 创建京东订单同步定时任务
func CreateJDOrderSyncTask(taskID int, cronSpec string) {
	// 使用默认配置
	config := jdDefaultConfig

	// 如果没有指定cron表达式，使用默认的每5分钟执行一次
	if cronSpec == "" {
		cronSpec = "12 */3 * * * *"
	}

	// 注册定时任务
	cron.PushTask(cron.Task{
		Key:  "jdOrderSync" + fmt.Sprintf("%d", taskID),
		Name: "京东订单同步定时任务" + fmt.Sprintf("%d", taskID),
		Spec: cronSpec,
		Handle: func(task cron.Task) {
			if err := SyncJDOrders(config); err != nil {
				log.Log().Error("同步京东订单失败", zap.Int("taskID", taskID), zap.Error(err))
			}
		},
		Status: cron.ENABLED,
	})

	log.Log().Info("京东订单同步定时任务已启动", zap.Int("taskID", taskID), zap.String("执行规则", cronSpec))
}

// 定时任务入口
func InitJDOrderSync() {
	// 使用默认配置
	config := jdDefaultConfig

	// 立即执行一次同步
	go func() {
		log.Log().Info("系统启动，立即执行京东订单同步")
		if err := SyncJDOrders(config); err != nil {
			log.Log().Error("同步京东订单失败", zap.Error(err))
		}
	}()

	// 创建默认的定时任务（ID为0）
	CreateJDOrderSyncTask(0, "0 */1 * * * *")
}

// processJDEcCpsOrders 处理JD订单的EcCpsOrderModel映射
func processJDEcCpsOrders(jdOrders []model.JDOrder, batchSize int, workerID int) {
	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range jdOrders {
		mappers = append(mappers, JDOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processEcCpsOrdersGeneric(mappers, mapJDOrderToEcCpsOrder, batchSize, workerID, "jd")
}
