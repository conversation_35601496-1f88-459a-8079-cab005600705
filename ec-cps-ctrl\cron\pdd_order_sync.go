package cron

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 同步配置
type PddSyncConfig struct {
	LookBackHours   int           // 查询过去多少小时的订单
	LookBackMinutes int           // 查询过去多少小时的订单
	PageSize        int           // 每页数量
	RetryCount      int           // 重试次数
	RetryInterval   time.Duration // 重试间隔
	WorkerCount     int           // 工作协程数量
	BatchSize       int           // 批量插入大小
	SyncInterval    time.Duration // 同步间隔
}

// 默认配置
var pddDefaultConfig = PddSyncConfig{
	LookBackHours:   3,
	LookBackMinutes: 3,
	PageSize:        40, // 建议使用40~50，可以提高成功率，减少超时数量
	RetryCount:      3,
	RetryInterval:   time.Second * 2,
	WorkerCount:     5,
	BatchSize:       50,
	SyncInterval:    time.Minute * 5,
}

// 同步拼多多订单
func SyncPddOrders(config PddSyncConfig) error {
	startTime := time.Now()
	log.Log().Info("开始同步拼多多订单", zap.Int("查询过去小时", config.LookBackMinutes))

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(config.RetryCount).SetRetryWaitTime(config.RetryInterval)

	// 设置接口参数
	now := time.Now()
	queryStartTime := now.Add(-time.Duration(config.LookBackMinutes) * time.Minute)

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return fmt.Errorf("获取API密钥失败: %v", err)
	}

	// 创建订单通道和等待组
	orderChan := make(chan []model.PddOrderData, 10)
	var wg sync.WaitGroup

	// 启动工作协程处理订单
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			processPddOrdersWorker(orderChan, config.BatchSize, workerID)
		}(i)
	}

	// 获取第一页数据
	resp, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(map[string]string{
			"apikey":            setting.ApiKey,
			"page":              "1",
			"page_size":         fmt.Sprintf("%d", config.PageSize),
			"start_update_time": fmt.Sprintf("%d", queryStartTime.Unix()),
			"end_update_time":   fmt.Sprintf("%d", now.Unix()),
		}).
		Post("http://api.tbk.dingdanxia.com/pdd/orderlist")

	if err != nil {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("请求拼多多订单接口失败: %v", err)
	}

	var orderResp model.PddOrderResponse
	if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("解析响应数据失败: %v", err)
	}

	if orderResp.Code != 200 {
		close(orderChan) // 关闭通道，让工作协程退出
		return fmt.Errorf("接口返回错误: %s", orderResp.Msg)
	}

	// 发送第一页数据到通道
	if len(orderResp.Data) > 0 {
		orderChan <- orderResp.Data
		log.Log().Info("获取数据成功", zap.Int("页码", 1), zap.Int("订单数量", len(orderResp.Data)))
	}

	// 获取总页数
	totalPages := 1
	totalResults := orderResp.TotalResults
	pageCount := (totalResults + config.PageSize - 1) / config.PageSize

	// 如果有更多页，继续获取
	for page := 2; page <= pageCount; page++ {
		totalPages++
		resp, err = client.R().
			SetHeader("Content-Type", "application/json").
			SetQueryParams(map[string]string{
				"apikey":            setting.ApiKey,
				"page":              fmt.Sprintf("%d", page),
				"page_size":         fmt.Sprintf("%d", config.PageSize),
				"start_update_time": fmt.Sprintf("%d", queryStartTime.Unix()),
				"end_update_time":   fmt.Sprintf("%d", now.Unix()),
			}).
			Post("http://api.tbk.dingdanxia.com/pdd/orderlist")

		if err != nil {
			log.Log().Error("请求数据失败", zap.Int("页码", page), zap.Error(err))
			break
		}

		if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
			log.Log().Error("解析数据失败", zap.Int("页码", page), zap.Error(err))
			break
		}

		if orderResp.Code != 200 {
			log.Log().Error("接口返回错误", zap.Int("页码", page), zap.String("错误信息", orderResp.Msg))
			break
		}

		if len(orderResp.Data) > 0 {
			orderChan <- orderResp.Data
			log.Log().Info("获取数据成功", zap.Int("页码", page), zap.Int("订单数量", len(orderResp.Data)))
		}
	}

	// 关闭通道，表示没有更多数据
	close(orderChan)

	// 等待所有工作协程完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Log().Info("拼多多订单同步完成", zap.Int("总页数", totalPages), zap.Duration("耗时", elapsedTime))
	return nil
}

// 工作协程处理订单
func processPddOrdersWorker(orderChan <-chan []model.PddOrderData, batchSize int, workerID int) {
	log.Log().Info("工作协程启动", zap.Int("协程ID", workerID))

	for orders := range orderChan {
		// 分批处理订单
		processPddBatchOrders(orders, batchSize, workerID)
	}

	log.Log().Info("工作协程完成", zap.Int("协程ID", workerID))
}

// 批量处理订单
func processPddBatchOrders(orders []model.PddOrderData, batchSize int, workerID int) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理订单", zap.Int("协程ID", workerID), zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderSns []string
	for _, order := range orders {
		orderSns = append(orderSns, order.OrderSn)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.PddOrder
	result := source.DB().Where("order_sn IN ?", orderSns).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.PddOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderSn] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.PddOrder
	var updateOrders []model.PddOrder

	for _, orderData := range orders {
		// 转换为模型订单
		order := convertToPddOrder(orderData)

		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.OrderSn]; !exists {
			// 新订单
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderSn]
			dbOrder.OrderStatus = order.OrderStatus
			dbOrder.OrderStatusDesc = order.OrderStatusDesc
			dbOrder.OrderModifyAt = order.OrderModifyAt
			dbOrder.OrderVerifyTime = order.OrderVerifyTime
			dbOrder.OrderSettleTime = order.OrderSettleTime
			dbOrder.OrderReceiveTime = order.OrderReceiveTime
			dbOrder.PromotionAmount = order.PromotionAmount
			dbOrder.PromotionRate = order.PromotionRate
			dbOrder.FailReason = order.FailReason
			dbOrder.BatchNo = order.BatchNo
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.PddOrder{}).Clauses(clause.Returning{}).Where("order_sn = ?", order.OrderSn).Updates(map[string]interface{}{
					"order_status":       order.OrderStatus,
					"order_status_desc":  order.OrderStatusDesc,
					"order_modify_at":    order.OrderModifyAt,
					"order_verify_time":  order.OrderVerifyTime,
					"order_settle_time":  order.OrderSettleTime,
					"order_receive_time": order.OrderReceiveTime,
					"promotion_amount":   order.PromotionAmount,
					"promotion_rate":     order.PromotionRate,
					"fail_reason":        order.FailReason,
					"batch_no":           order.BatchNo,
					"sync_time":          time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.OrderSn), zap.Error(err))
				}
			}
			log.Log().Info("成功更新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	// 处理EcCpsOrderModel映射
	processPddEcCpsOrders(orders, batchSize, workerID)

	log.Log().Info("完成订单处理", zap.Int("协程ID", workerID),
		zap.Int("新增订单数量", len(newOrders)),
		zap.Int("更新订单数量", len(updateOrders)))
}

// 将API订单数据转换为模型订单
func convertToPddOrder(orderData model.PddOrderData) model.PddOrder {
	// 解析CustomParameters获取AppID、ParentAppID和AppUserID
	var parentAppID, appID, appUserID, shopID int

	// CustomParameters格式可能是"parentAppID_appID_appUserID"或其他格式
	if orderData.CustomParameters != "" {
		parts := strings.Split(orderData.CustomParameters, "_")
		if len(parts) == 3 {
			parentAppID, _ = strconv.Atoi(parts[0])
			appID, _ = strconv.Atoi(parts[1])
			appUserID, _ = strconv.Atoi(parts[2])
		} else if len(parts) == 2 {
			// 如果只有两部分，假设是"appID_appUserID"
			appID, _ = strconv.Atoi(parts[0])
			appUserID, _ = strconv.Atoi(parts[1])
		} else if len(parts) == 1 {
			// 如果只有一部分，尝试解析为appID
			appID, _ = strconv.Atoi(parts[0])
		} else if len(parts) == 4 {
			parentAppID, _ = strconv.Atoi(parts[0])
			appID, _ = strconv.Atoi(parts[1])
			appUserID, _ = strconv.Atoi(parts[2])
			shopID, _ = strconv.Atoi(parts[3])
		}
	}

	return model.PddOrder{
		OrderSn:               orderData.OrderSn,
		GoodsId:               orderData.GoodsId,
		GoodsName:             orderData.GoodsName,
		GoodsThumbnailUrl:     orderData.GoodsThumbnailUrl,
		GoodsPrice:            orderData.GoodsPrice,
		GoodsQuantity:         orderData.GoodsQuantity,
		OrderAmount:           orderData.OrderAmount,
		OrderCreateTime:       orderData.OrderCreateTime,
		OrderPayTime:          orderData.OrderPayTime,
		OrderGroupSuccessTime: orderData.OrderGroupSuccessTime,
		OrderVerifyTime:       orderData.OrderVerifyTime,
		OrderSettleTime:       orderData.OrderSettleTime,
		OrderModifyAt:         orderData.OrderModifyAt,
		OrderStatus:           orderData.OrderStatus,
		OrderStatusDesc:       orderData.OrderStatusDesc,
		PromotionRate:         orderData.PromotionRate,
		PromotionAmount:       orderData.PromotionAmount,
		PId:                   orderData.PId,
		CustomParameters:      orderData.CustomParameters,
		Type:                  orderData.Type,
		GroupId:               orderData.GroupId,
		AuthDuoId:             orderData.AuthDuoId,
		ZsDuoId:               orderData.ZsDuoId,
		CpaNew:                orderData.CpaNew,
		FailReason:            orderData.FailReason,
		MatchChannel:          orderData.MatchChannel,
		OrderReceiveTime:      orderData.OrderReceiveTime,
		BatchNo:               orderData.BatchNo,
		DuoCouponAmount:       orderData.DuoCouponAmount,
		SceneAtMarketFee:      orderData.SceneAtMarketFee,
		// 设置解析出的ID
		ParentAppID: parentAppID,
		AppID:       appID,
		AppUserID:   appUserID,
		ShopID:      shopID,
		SyncTime:    time.Now(),
	}
}

// 创建拼多多订单同步定时任务
func CreatePddOrderSyncTask(taskID int, cronSpec string) {
	// 使用默认配置
	config := pddDefaultConfig

	// 如果没有指定cron表达式，使用默认的每5分钟执行一次
	if cronSpec == "" {
		cronSpec = "0 */5 * * * *"
	}

	// 注册定时任务
	cron.PushTask(cron.Task{
		Key:  "pddOrderSync" + fmt.Sprintf("%d", taskID),
		Name: "拼多多订单同步定时任务" + fmt.Sprintf("%d", taskID),
		Spec: cronSpec,
		Handle: func(task cron.Task) {
			if err := SyncPddOrders(config); err != nil {
				log.Log().Error("同步拼多多订单失败", zap.Int("taskID", taskID), zap.Error(err))
			}
		},
		Status: cron.ENABLED,
	})

	log.Log().Info("拼多多订单同步定时任务已启动", zap.Int("taskID", taskID), zap.String("执行规则", cronSpec))
}

// 定时任务入口
func InitPddOrderSync() {
	// 使用默认配置
	config := pddDefaultConfig

	// 立即执行一次同步
	go func() {
		log.Log().Info("系统启动，立即执行拼多多订单同步")
		if err := SyncPddOrders(config); err != nil {
			log.Log().Error("同步拼多多订单失败", zap.Error(err))
		}
	}()

	// 创建默认的定时任务（ID为0）
	CreatePddOrderSyncTask(0, "0 */1 * * * *")
}

// processPddEcCpsOrders 处理拼多多订单的EcCpsOrderModel映射
func processPddEcCpsOrders(pddOrdersData []model.PddOrderData, batchSize int, workerID int) {
	// 转换为model.PddOrder
	var modelOrders []model.PddOrder
	for _, orderData := range pddOrdersData {
		modelOrders = append(modelOrders, convertToPddOrder(orderData))
	}

	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range modelOrders {
		mappers = append(mappers, PddOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processEcCpsOrdersGeneric(mappers, mapPddOrderToEcCpsOrder, batchSize, workerID, "pdd")
}
