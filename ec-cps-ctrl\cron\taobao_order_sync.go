package cron

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 同步配置
type SyncConfig struct {
	LookBackHours   int           // 查询过去多少小时的订单
	LookBackMinutes int           // 查询过去多少小时的订单
	PageSize        int           // 每页数量
	RetryCount      int           // 重试次数
	RetryInterval   time.Duration // 重试间隔
	WorkerCount     int           // 工作协程数量
	BatchSize       int           // 批量插入大小
	SyncInterval    time.Duration // 同步间隔
}

// 默认配置
var defaultConfig = SyncConfig{
	LookBackHours:   3, // 修改为48小时，追溯过去48小时的订单
	LookBackMinutes: 3, // 修改为48小时，追溯过去48小时的订单
	PageSize:        100,
	RetryCount:      3,
	RetryInterval:   time.Second * 2,
	WorkerCount:     5,
	BatchSize:       50,
	SyncInterval:    time.Minute * 5,
}

// 淘宝订单同步响应结构
type TaobaoOrderResponse struct {
	Code          int           `json:"code"`
	Msg           string        `json:"msg"`
	HasNext       bool          `json:"has_next"`
	HasPre        bool          `json:"has_pre"`
	PageNo        int           `json:"page_no"`
	PageSize      int           `json:"page_size"`
	PositionIndex string        `json:"position_index"`
	Data          []TaobaoOrder `json:"data"`
}

// 淘宝订单结构
type TaobaoOrder struct {
	TradeId            string `json:"trade_id"`             // 订单号
	TradeParentId      string `json:"trade_parent_id"`      // 父订单号
	ItemId             string `json:"item_id"`              // 商品ID
	ItemTitle          string `json:"item_title"`           // 商品标题
	ItemImg            string `json:"item_img"`             // 商品主图
	ItemPrice          string `json:"item_price"`           // 商品单价
	ItemNum            int    `json:"item_num"`             // 商品数量
	TkStatus           int    `json:"tk_status"`            // 订单状态
	OrderType          string `json:"order_type"`           // 订单类型
	FlowSource         string `json:"flow_source"`          // 流量来源
	TkCreateTime       string `json:"tk_create_time"`       // 订单创建时间
	TkPaidTime         string `json:"tk_paid_time"`         // 订单付款时间
	TkEarningTime      string `json:"tk_earning_time"`      // 结算时间
	AlipayTotalPrice   string `json:"alipay_total_price"`   // 付款金额
	PubShareFee        string `json:"pub_share_fee"`        // 预估收入
	SellerShopTitle    string `json:"seller_shop_title"`    // 店铺名称
	IncomeRate         string `json:"income_rate"`          // 佣金比例
	PubId              string `json:"pub_id"`               // 推广者ID
	UnId               string `json:"unid"`                 // 推广位ID
	SiteId             string `json:"site_id"`              // 媒体ID
	AdZoneId           string `json:"adzone_id"`            // 广告位ID
	SiteName           string `json:"site_name"`            // 媒体名称
	AdzoneName         string `json:"adzone_name"`          // 广告位名称
	RefundTag          int    `json:"refund_tag"`           // 是否维权，0-非维权，1-维权
	TerminalType       string `json:"terminal_type"`        // 终端类型
	ClickTime          string `json:"click_time"`           // 点击时间
	TkCommissionRate   string `json:"tk_commission_rate"`   // 佣金比率
	TkCommissionFee    string `json:"tk_commission_fee"`    // 佣金金额
	TkTotalRate        string `json:"tk_total_rate"`        // 总佣金比率
	TotalCommissionFee string `json:"total_commission_fee"` // 总佣金金额
	ItemCategoryName   string `json:"item_category_name"`   // 商品类目名称
	SellerNick         string `json:"seller_nick"`          // 卖家昵称
	SpecialId          string `json:"special_id"`           // 会员运营ID
	RelationId         string `json:"relation_id"`          // 渠道关系ID
}

// 同步淘宝订单 - 支持48小时分时段查询
func SyncTaobaoOrders(config SyncConfig) error {
	startTime := time.Now()
	log.Log().Info("开始同步淘宝订单",
		zap.Int("查询过去小时", config.LookBackMinutes),
		zap.String("策略", "分时段查询(每次1小时)"))

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return fmt.Errorf("获取API密钥失败: %v", err)
	}

	// 创建订单通道和等待组
	orderChan := make(chan []TaobaoOrder, 20) // 增加缓冲区，支持更多时段
	var wg sync.WaitGroup

	// 启动工作协程处理订单
	for i := 0; i < config.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			processOrdersWorker(orderChan, config.BatchSize, workerID)
		}(i)
	}

	// 分时段获取订单数据
	go func() {
		defer close(orderChan)
		totalOrders, totalPages, err := fetchTaobaoOrdersInHourlySegments(setting, config, orderChan)
		if err != nil {
			log.Log().Error("获取淘宝订单失败", zap.Error(err))
		} else {
			log.Log().Info("订单获取完成",
				zap.Int("总订单数", totalOrders),
				zap.Int("总页数", totalPages))
		}
	}()

	// 等待所有工作协程完成
	wg.Wait()

	elapsedTime := time.Since(startTime)
	log.Log().Info("淘宝订单同步完成", zap.Duration("耗时", elapsedTime))
	return nil
}

// fetchTaobaoOrdersInHourlySegments 分时段获取淘宝订单（每次查询1小时）
func fetchTaobaoOrdersInHourlySegments(setting model.CpsValue, config SyncConfig, orderChan chan<- []TaobaoOrder) (int, int, error) {
	// 创建HTTP客户端
	client := resty.New().SetRetryCount(config.RetryCount).SetRetryWaitTime(config.RetryInterval)

	// 计算总的查询时间范围
	endTime := time.Now()
	totalStartTime := endTime.Add(-time.Duration(config.LookBackMinutes) * time.Minute)

	log.Log().Info("开始分时段查询",
		zap.String("总开始时间", totalStartTime.Format("2006-01-02 15:04:05")),
		zap.String("总结束时间", endTime.Format("2006-01-02 15:04:05")),
		zap.Int("总时段数", config.LookBackMinutes))

	totalPages := 0
	totalOrders := 0

	// 按小时分段查询（从最新时间往前推）
	//for i := 0; i < config.LookBackMinutes; i++ {
	// 计算当前时段的时间范围（每段严格1小时）
	segmentEndTime := endTime
	segmentStartTime := totalStartTime

	log.Log().Info("查询时段",
		zap.Int("时段", 1),
		zap.String("开始时间", segmentStartTime.Format("2006-01-02 15:04:05")),
		zap.String("结束时间", segmentEndTime.Format("2006-01-02 15:04:05")))

	// 查询当前时段的订单
	segmentPages, segmentOrders, err := fetchTaobaoOrdersForTimeSegment(
		client, setting, config, segmentStartTime, segmentEndTime, orderChan)

	if err != nil {
		log.Log().Error("查询时段失败",
			zap.Int("时段", 1),
			zap.Error(err))
		// 继续查询下一个时段，不中断整个过程
		return 0, 0, err
	}

	totalPages += segmentPages
	totalOrders += segmentOrders

	log.Log().Info("时段查询完成",
		zap.Int("时段", 1),
		zap.Int("页数", segmentPages),
		zap.Int("订单数", segmentOrders))

	// 添加短暂延迟，避免API调用过于频繁
	time.Sleep(200 * time.Millisecond)
	//}

	log.Log().Info("所有时段查询完成",
		zap.Int("总页数", totalPages),
		zap.Int("总订单数", totalOrders))

	return totalOrders, totalPages, nil
}

// fetchTaobaoOrdersForTimeSegment 查询指定时间段的订单（1小时内）
func fetchTaobaoOrdersForTimeSegment(client *resty.Client, setting model.CpsValue, config SyncConfig,
	startTime, endTime time.Time, orderChan chan<- []TaobaoOrder) (int, int, error) {

	pageNo := 1
	totalPages := 0
	totalOrders := 0
	var positionIndex string

	for {
		// 构建请求参数
		params := map[string]string{
			"apikey":     setting.ApiKey,
			"start_time": startTime.Format("2006-01-02 15:04:05"),
			"end_time":   endTime.Format("2006-01-02 15:04:05"),
			"page_size":  fmt.Sprintf("%d", config.PageSize),
			"page_no":    fmt.Sprintf("%d", pageNo),
			"tk_status":  "12", // 付款状态
		}

		// 第一页使用query_type=4（更新时间），后续页面使用query_type=1
		if pageNo == 1 {
			params["query_type"] = "4" // 更新时间
		} else {
			params["query_type"] = "1"
			if positionIndex != "" {
				params["position_index"] = positionIndex
			}
		}

		// 请求当前页数据
		resp, err := client.R().
			SetHeader("Content-Type", "application/json").
			SetQueryParams(params).
			Post("http://api.tbk.dingdanxia.com/tbk/order_details")

		if err != nil {
			return totalPages, totalOrders, fmt.Errorf("请求第%d页失败: %v", pageNo, err)
		}

		var orderResp TaobaoOrderResponse
		if err := json.Unmarshal(resp.Body(), &orderResp); err != nil {
			return totalPages, totalOrders, fmt.Errorf("解析第%d页数据失败: %v", pageNo, err)
		}

		if orderResp.Code != 200 {
			return totalPages, totalOrders, fmt.Errorf("接口返回错误(第%d页): %s", pageNo, orderResp.Msg)
		}

		totalPages++

		// 发送数据到通道
		if len(orderResp.Data) > 0 {
			orderChan <- orderResp.Data
			totalOrders += len(orderResp.Data)
			log.Log().Debug("获取页面数据",
				zap.Int("页码", pageNo),
				zap.Int("订单数量", len(orderResp.Data)))
		}

		// 检查是否还有更多数据
		if !orderResp.HasNext {
			break
		}

		// 更新分页参数
		pageNo++
		positionIndex = orderResp.PositionIndex

		// 添加页面间延迟，避免请求过快
		time.Sleep(50 * time.Millisecond)
	}

	return totalPages, totalOrders, nil
}

// 工作协程处理订单
func processOrdersWorker(orderChan <-chan []TaobaoOrder, batchSize int, workerID int) {
	log.Log().Info("工作协程启动", zap.Int("协程ID", workerID))

	for orders := range orderChan {
		// 分批处理订单
		processBatchOrders(orders, batchSize, workerID)
	}

	log.Log().Info("工作协程完成", zap.Int("协程ID", workerID))
}

// 批量处理订单
func processBatchOrders(orders []TaobaoOrder, batchSize int, workerID int) {
	if len(orders) == 0 {
		return
	}

	log.Log().Info("开始处理订单", zap.Int("协程ID", workerID), zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var tradeIds []string
	for _, order := range orders {
		tradeIds = append(tradeIds, order.TradeId)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.TaobaoOrder
	result := source.DB().Where("trade_id IN ?", tradeIds).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Int("协程ID", workerID), zap.Error(result.Error))
		return
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.TaobaoOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.TradeId] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.TaobaoOrder
	var updateOrders []model.TaobaoOrder

	for _, order := range orders {
		if _, exists := existingOrderMap[order.TradeId]; !exists {
			// 新订单
			newOrders = append(newOrders, convertToModelOrder(order))
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.TradeId]
			dbOrder.TkStatus = order.TkStatus
			dbOrder.RefundTag = order.RefundTag
			dbOrder.TkEarningTime = order.TkEarningTime
			dbOrder.PubShareFee = order.PubShareFee
			dbOrder.TkCommissionFee = order.TkCommissionFee
			dbOrder.TotalCommissionFee = order.TotalCommissionFee
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Int("协程ID", workerID), zap.Error(err))
			} else {
				log.Log().Info("成功创建新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
			}
		}
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.TaobaoOrder{}).Clauses(clause.Returning{}).Where("trade_id = ?", order.TradeId).Updates(map[string]interface{}{
					"tk_status":            order.TkStatus,
					"refund_tag":           order.RefundTag,
					"tk_earning_time":      order.TkEarningTime,
					"pub_share_fee":        order.PubShareFee,
					"tk_commission_fee":    order.TkCommissionFee,
					"total_commission_fee": order.TotalCommissionFee,
					"sync_time":            time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.Int("协程ID", workerID), zap.String("订单ID", order.TradeId), zap.Error(err))
				}
			}
			log.Log().Info("成功更新订单", zap.Int("协程ID", workerID), zap.Int("数量", len(batch)))
		}
	}

	// 处理EcCpsOrderModel映射
	processTaobaoEcCpsOrders(orders, batchSize, workerID)

	log.Log().Info("完成订单处理", zap.Int("协程ID", workerID), zap.Int("新增数量", len(newOrders)), zap.Int("更新数量", len(updateOrders)))
}

// 将API订单转换为模型订单
func convertToModelOrder(order TaobaoOrder) model.TaobaoOrder {
	var appID, parentAppID, appUserID, shopID int
	var taobaoRelation model.OpenTbAccessToken
	source.DB().Where("relation_id = ?", order.RelationId).First(&taobaoRelation)
	appID = taobaoRelation.AppID
	parentAppID = taobaoRelation.ParentAppId
	appUserID = taobaoRelation.AppUserID
	shopID = taobaoRelation.ShopID
	return model.TaobaoOrder{
		TradeId:            order.TradeId,
		TradeParentId:      order.TradeParentId,
		ItemId:             order.ItemId,
		ItemTitle:          order.ItemTitle,
		ItemImg:            order.ItemImg,
		ItemPrice:          order.ItemPrice,
		ItemNum:            order.ItemNum,
		TkStatus:           order.TkStatus,
		OrderType:          order.OrderType,
		FlowSource:         order.FlowSource,
		TkCreateTime:       order.TkCreateTime,
		TkPaidTime:         order.TkPaidTime,
		TkEarningTime:      order.TkEarningTime,
		AlipayTotalPrice:   order.AlipayTotalPrice,
		PubShareFee:        order.PubShareFee,
		SellerShopTitle:    order.SellerShopTitle,
		IncomeRate:         order.IncomeRate,
		PubId:              order.PubId,
		UnId:               order.UnId,
		SiteId:             order.SiteId,
		AdZoneId:           order.AdZoneId,
		SiteName:           order.SiteName,
		AdzoneName:         order.AdzoneName,
		RefundTag:          order.RefundTag,
		TerminalType:       order.TerminalType,
		ClickTime:          order.ClickTime,
		TkCommissionRate:   order.TkCommissionRate,
		TkCommissionFee:    order.TkCommissionFee,
		TkTotalRate:        order.TkTotalRate,
		TotalCommissionFee: order.TotalCommissionFee,
		ItemCategoryName:   order.ItemCategoryName,
		SellerNick:         order.SellerNick,
		SpecialId:          order.SpecialId,
		RelationId:         order.RelationId,
		SyncTime:           time.Now(),
		AppUserID:          appUserID,
		AppID:              appID,
		ParentAppID:        parentAppID,
		ShopID:             shopID,
	}
}

// 处理订单数据 (兼容旧版本调用)
func processOrders(orders []TaobaoOrder) {
	processBatchOrders(orders, 50, 0)
}

// 创建淘宝订单同步定时任务
func CreateTaobaoOrderSyncTask(taskID int, cronSpec string) {
	// 使用默认配置
	config := defaultConfig

	// 如果没有指定cron表达式，使用默认的每5分钟执行一次
	if cronSpec == "" {
		cronSpec = "0 */5 * * * *"
	}

	// 注册定时任务
	cron.PushTask(cron.Task{
		Key:  "taobaoOrderSync" + fmt.Sprintf("%d", taskID),
		Name: "淘宝订单同步定时任务" + fmt.Sprintf("%d", taskID),
		Spec: cronSpec,
		Handle: func(task cron.Task) {
			if err := SyncTaobaoOrders(config); err != nil {
				log.Log().Error("同步淘宝订单失败", zap.Int("taskID", taskID), zap.Error(err))
			}
		},
		Status: cron.ENABLED,
	})

	log.Log().Info("淘宝订单同步定时任务已启动", zap.Int("taskID", taskID), zap.String("执行规则", cronSpec))
}

// 定时任务入口
func InitTaobaoOrderSync() {
	// 使用默认配置
	config := defaultConfig

	// 立即执行一次同步
	go func() {
		log.Log().Info("系统启动，立即执行淘宝订单同步")
		if err := SyncTaobaoOrders(config); err != nil {
			log.Log().Error("同步淘宝订单失败", zap.Error(err))
		}
	}()

	// 创建默认的定时任务（ID为0）
	CreateTaobaoOrderSyncTask(0, "0 */1 * * * *")
}

// processTaobaoEcCpsOrders 处理淘宝订单的EcCpsOrderModel映射
func processTaobaoEcCpsOrders(taobaoOrders []TaobaoOrder, batchSize int, workerID int) {
	// 转换为model.TaobaoOrder
	var modelOrders []model.TaobaoOrder
	for _, order := range taobaoOrders {
		modelOrders = append(modelOrders, convertToModelOrder(order))
	}

	// 转换为OrderMapper接口
	var mappers []OrderMapper
	for _, order := range modelOrders {
		mappers = append(mappers, TaobaoOrderMapper{Order: order})
	}

	// 使用通用函数处理
	processEcCpsOrdersGeneric(mappers, mapTaobaoOrderToEcCpsOrder, batchSize, workerID, "taobao")
}
