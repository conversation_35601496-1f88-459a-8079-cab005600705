package model

import "yz-go/source"

type OpenTbAccessToken struct {
	source.Model
	AppID       int               `json:"app_id"`        //商城id
	ParentAppId int               `json:"parent_app_id"` //中台id
	AppUserID   int               `json:"app_user_id"`   //商城的用户id
	ShopID      int               `json:"shop_id" gorm:"default:0;"`
	AccessToken string            `json:"access_token"`
	ExpiresIn   *source.LocalTime `json:"expires_in"`
	RelationId  int               `json:"relation_id"`
}

type CpsJDRelation struct {
	source.Model
	AppID       int    `json:"app_id"`        //商城id
	ParentAppId int    `json:"parent_app_id"` //中台id
	AppUserID   int    `json:"app_user_id"`   //商城的用户id
	ShopID      int    `json:"shop_id" gorm:"default:0;"`
	PddPid      string `json:"pdd_pid"` //推广位id

}
