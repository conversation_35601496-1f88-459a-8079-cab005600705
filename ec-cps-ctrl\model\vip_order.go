package model

import (
	"time"
	"yz-go/source"
)

// VipOrder 唯品会订单模型
type VipOrder struct {
	source.Model
	OrderSn                   string `json:"order_sn" gorm:"column:order_sn;index"`                                     // 订单号
	Status                    int16  `json:"status" gorm:"column:status"`                                               // 订单状态:0-不合格，1-待定，2-已完结
	NewCustomer               int16  `json:"new_customer" gorm:"column:new_customer"`                                   // 新老客：0-待定，1-新客，2-老客
	ChannelTag                string `json:"channel_tag" gorm:"column:channel_tag"`                                     // 渠道标识
	OrderTime                 int64  `json:"order_time" gorm:"column:order_time"`                                       // 下单时间 时间戳 单位毫秒
	SignTime                  int64  `json:"sign_time" gorm:"column:sign_time"`                                         // 签收时间 时间戳 单位毫秒
	SettledTime               int64  `json:"settled_time" gorm:"column:settled_time"`                                   // 结算时间 时间戳 单位毫秒
	LastUpdateTime            int64  `json:"last_update_time" gorm:"column:last_update_time"`                           // 订单上次更新时间 时间戳 单位毫秒
	Settled                   int16  `json:"settled" gorm:"column:settled"`                                             // 订单结算状态 0-未结算,1-已结算
	SelfBuy                   int    `json:"self_buy" gorm:"column:self_buy"`                                           // 是否自推自买 0-否，1-是
	OrderSubStatusName        string `json:"order_sub_status_name" gorm:"column:order_sub_status_name"`                 // 订单子状态：流转状态
	Commission                string `json:"commission" gorm:"column:commission"`                                       // 商品总佣金:单位元
	AfterSaleChangeCommission string `json:"after_sale_change_commission" gorm:"column:after_sale_change_commission"`   // 售后订单佣金变动
	AfterSaleChangeGoodsCount int    `json:"after_sale_change_goods_count" gorm:"column:after_sale_change_goods_count"` // 售后订单总商品数量变动
	CommissionEnterTime       int64  `json:"commission_enter_time" gorm:"column:commission_enter_time"`                 // 入账时间，时间戳，单位毫秒
	OrderSource               string `json:"order_source" gorm:"column:order_source"`                                   // 订单来源
	Pid                       string `json:"pid" gorm:"column:pid"`                                                     // 推广PID
	IsPrepay                  int    `json:"is_prepay" gorm:"column:is_prepay"`                                         // 是否预付订单:0-否，1-是
	StatParam                 string `json:"stat_param" gorm:"column:stat_param"`                                       // 自定义统计参数
	TotalCost                 string `json:"total_cost" gorm:"column:total_cost"`                                       // 订单支付金额:单位元
	OrderTrackReason          int    `json:"order_track_reason" gorm:"column:order_track_reason"`                       // 订单归因方式：0-常规推广,1-惊喜红包,2-锁粉,3-超级红包
	IsSplit                   int    `json:"is_split" gorm:"column:is_split"`                                           // 订单拆单标识: 0-否，1-是
	AppID                     uint   `json:"app_id" gorm:"column:app_id"`                                               // 商城ID
	ParentAppID               uint   `json:"parent_app_id" gorm:"column:parent_app_id"`                                 // 中台ID
	AppUserID                 uint   `json:"app_user_id" gorm:"column:app_user_id"`                                     // 商城会员ID
	ShopID                    uint   `json:"shop_id" gorm:"default:0;"`

	// 额外字段
	SyncTime      time.Time        `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int              `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	DetailList    []VipOrderDetail `json:"detail_list" gorm:"-"`                                  // 订单详情，非数据库字段
}

// TableName 设置表名
func (VipOrder) TableName() string {
	return "ec_cps_vip_orders"
}

type VipOrderModel struct {
	source.Model
	OrderSn                   string `json:"order_sn" gorm:"column:order_sn;index"`                                     // 订单号
	Status                    int16  `json:"status" gorm:"column:status"`                                               // 订单状态:0-不合格，1-待定，2-已完结
	NewCustomer               int16  `json:"new_customer" gorm:"column:new_customer"`                                   // 新老客：0-待定，1-新客，2-老客
	ChannelTag                string `json:"channel_tag" gorm:"column:channel_tag"`                                     // 渠道标识
	OrderTime                 int64  `json:"order_time" gorm:"column:order_time"`                                       // 下单时间 时间戳 单位毫秒
	SignTime                  int64  `json:"sign_time" gorm:"column:sign_time"`                                         // 签收时间 时间戳 单位毫秒
	SettledTime               int64  `json:"settled_time" gorm:"column:settled_time"`                                   // 结算时间 时间戳 单位毫秒
	LastUpdateTime            int64  `json:"last_update_time" gorm:"column:last_update_time"`                           // 订单上次更新时间 时间戳 单位毫秒
	Settled                   int16  `json:"settled" gorm:"column:settled"`                                             // 订单结算状态 0-未结算,1-已结算
	SelfBuy                   int    `json:"self_buy" gorm:"column:self_buy"`                                           // 是否自推自买 0-否，1-是
	OrderSubStatusName        string `json:"order_sub_status_name" gorm:"column:order_sub_status_name"`                 // 订单子状态：流转状态
	Commission                string `json:"commission" gorm:"column:commission"`                                       // 商品总佣金:单位元
	AfterSaleChangeCommission string `json:"after_sale_change_commission" gorm:"column:after_sale_change_commission"`   // 售后订单佣金变动
	AfterSaleChangeGoodsCount int    `json:"after_sale_change_goods_count" gorm:"column:after_sale_change_goods_count"` // 售后订单总商品数量变动
	CommissionEnterTime       int64  `json:"commission_enter_time" gorm:"column:commission_enter_time"`                 // 入账时间，时间戳，单位毫秒
	OrderSource               string `json:"order_source" gorm:"column:order_source"`                                   // 订单来源
	Pid                       string `json:"pid" gorm:"column:pid"`                                                     // 推广PID
	IsPrepay                  int    `json:"is_prepay" gorm:"column:is_prepay"`                                         // 是否预付订单:0-否，1-是
	StatParam                 string `json:"stat_param" gorm:"column:stat_param"`                                       // 自定义统计参数
	TotalCost                 string `json:"total_cost" gorm:"column:total_cost"`                                       // 订单支付金额:单位元
	OrderTrackReason          int    `json:"order_track_reason" gorm:"column:order_track_reason"`                       // 订单归因方式：0-常规推广,1-惊喜红包,2-锁粉,3-超级红包
	IsSplit                   int    `json:"is_split" gorm:"column:is_split"`                                           // 订单拆单标识: 0-否，1-是
	AppID                     uint   `json:"app_id" gorm:"column:app_id"`                                               // 商城ID
	ParentAppID               uint   `json:"parent_app_id" gorm:"column:parent_app_id"`                                 // 中台ID
	AppUserID                 uint   `json:"app_user_id" gorm:"column:app_user_id"`                                     // 商城会员ID
	ShopID                    int    `json:"shop_id" gorm:"default:0;"`

	// 额外字段
	SyncTime      time.Time        `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int              `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
	DetailList    []VipOrderDetail `json:"detail_list" gorm:"-"`                                  // 订单详情，非数据库字段
	Application   Application      `json:"application" gorm:"foreignKey:AppID;references:ID"`
}

// TableName 设置表名
func (VipOrderModel) TableName() string {
	return "ec_cps_vip_orders"
}

// VipOrderDetail 唯品会订单商品明细
type VipOrderDetail struct {
	source.Model
	OrderSn                    string `json:"order_sn" gorm:"column:order_sn;index"`                                       // 订单号
	GoodsId                    string `json:"goods_id" gorm:"column:goods_id"`                                             // 商品id
	GoodsName                  string `json:"goods_name" gorm:"column:goods_name;type:varchar(255)"`                       // 商品名称
	GoodsThumb                 string `json:"goods_thumb" gorm:"column:goods_thumb;type:varchar(255)"`                     // 商品缩略图
	GoodsCount                 int    `json:"goods_count" gorm:"column:goods_count"`                                       // 商品数量
	CommissionTotalCost        string `json:"commission_total_cost" gorm:"column:commission_total_cost"`                   // 商品计佣金额
	CommissionRate             string `json:"commission_rate" gorm:"column:commission_rate"`                               // 商品佣金比例
	Commission                 string `json:"commission" gorm:"column:commission"`                                         // 商品佣金金额
	CommCode                   string `json:"comm_code" gorm:"column:comm_code"`                                           // 佣金编码：对应商品二级分类
	CommName                   string `json:"comm_name" gorm:"column:comm_name"`                                           // 佣金方案名称
	OrderSource                string `json:"order_source" gorm:"column:order_source"`                                     // 订单来源
	AfterSaleChangedCommission string `json:"after_sale_changed_commission" gorm:"column:after_sale_changed_commission"`   // 商品佣金售后变动
	AfterSaleChangedGoodsCount int    `json:"after_sale_changed_goods_count" gorm:"column:after_sale_changed_goods_count"` // 商品数量售后变动
	AfterSaleSn                string `json:"after_sale_sn" gorm:"column:after_sale_sn"`                                   // 商品售后单号
	AfterSaleStatus            int    `json:"after_sale_status" gorm:"column:after_sale_status"`                           // 商品售后状态：1-售后中，2-售后完成，3-售后取消
	AfterSaleType              int    `json:"after_sale_type" gorm:"column:after_sale_type"`                               // 售后类型：1-退货，2-换货
	AfterSaleFinishTime        int64  `json:"after_sale_finish_time" gorm:"column:after_sale_finish_time"`                 // 售后完成时间，时间戳，单位：毫秒
	SizeId                     string `json:"size_id" gorm:"column:size_id"`                                               // 商品尺码
	Status                     int16  `json:"status" gorm:"column:status"`                                                 // 商品状态：0-不合格，1-待定，2-已完结
	GoodsFinalPrice            string `json:"goods_final_price" gorm:"column:goods_final_price"`                           // 商品成交价
	BrandStoreSn               string `json:"brand_store_sn" gorm:"column:brand_store_sn"`                                 // 品牌编号
	BrandStoreName             string `json:"brand_store_name" gorm:"column:brand_store_name"`                             // 品牌名称
	SpuId                      string `json:"spu_id" gorm:"column:spu_id"`                                                 // 商品spuId
}

// TableName 设置表名
func (VipOrderDetail) TableName() string {
	return "ec_cps_vip_order_details"
}

// VipOrderResponse 唯品会订单查询响应结构
type VipOrderResponse struct {
	Code         int            `json:"code"`
	Msg          string         `json:"msg"`
	TotalResults int            `json:"total_results"`
	Data         []VipOrderData `json:"data"`
}

// VipOrderData 唯品会订单数据
type VipOrderData struct {
	OrderSn                   string               `json:"orderSn"`                   // 订单号
	Status                    int16                `json:"status"`                    // 订单状态:0-不合格，1-待定，2-已完结
	NewCustomer               int16                `json:"newCustomer"`               // 新老客：0-待定，1-新客，2-老客
	ChannelTag                string               `json:"channelTag"`                // 渠道标识
	OrderTime                 int64                `json:"orderTime"`                 // 下单时间 时间戳 单位毫秒
	SignTime                  int64                `json:"signTime"`                  // 签收时间 时间戳 单位毫秒
	SettledTime               int64                `json:"settledTime"`               // 结算时间 时间戳 单位毫秒
	DetailList                []VipOrderDetailData `json:"detailList"`                // 商品明细
	LastUpdateTime            int64                `json:"lastUpdateTime"`            // 订单上次更新时间 时间戳 单位毫秒
	Settled                   int16                `json:"settled"`                   // 订单结算状态 0-未结算,1-已结算
	SelfBuy                   int                  `json:"selfBuy"`                   // 是否自推自买 0-否，1-是
	OrderSubStatusName        string               `json:"orderSubStatusName"`        // 订单子状态：流转状态
	Commission                string               `json:"commission"`                // 商品总佣金:单位元
	AfterSaleChangeCommission string               `json:"afterSaleChangeCommission"` // 售后订单佣金变动
	AfterSaleChangeGoodsCount int                  `json:"afterSaleChangeGoodsCount"` // 售后订单总商品数量变动
	CommissionEnterTime       int64                `json:"commissionEnterTime"`       // 入账时间，时间戳，单位毫秒
	OrderSource               string               `json:"orderSource"`               // 订单来源
	Pid                       string               `json:"pid"`                       // 推广PID
	IsPrepay                  int                  `json:"isPrepay"`                  // 是否预付订单:0-否，1-是
	StatParam                 string               `json:"statParam"`                 // 自定义统计参数
	TotalCost                 string               `json:"totalCost"`                 // 订单支付金额:单位元
	OrderTrackReason          int                  `json:"orderTrackReason"`          // 订单归因方式：0-常规推广,1-惊喜红包,2-锁粉,3-超级红包
	IsSplit                   int                  `json:"isSplit"`                   // 订单拆单标识: 0-否，1-是
}

// VipOrderDetailData 唯品会订单商品明细数据
type VipOrderDetailData struct {
	GoodsId                    string `json:"goodsId"`                    // 商品id
	GoodsName                  string `json:"goodsName"`                  // 商品名称
	GoodsThumb                 string `json:"goodsThumb"`                 // 商品缩略图
	GoodsCount                 int    `json:"goodsCount"`                 // 商品数量
	CommissionTotalCost        string `json:"commissionTotalCost"`        // 商品计佣金额
	CommissionRate             string `json:"commissionRate"`             // 商品佣金比例
	Commission                 string `json:"commission"`                 // 商品佣金金额
	CommCode                   string `json:"commCode"`                   // 佣金编码：对应商品二级分类
	CommName                   string `json:"commName"`                   // 佣金方案名称
	OrderSource                string `json:"orderSource"`                // 订单来源
	AfterSaleChangedCommission string `json:"afterSaleChangedCommission"` // 商品佣金售后变动
	AfterSaleChangedGoodsCount int    `json:"afterSaleChangedGoodsCount"` // 商品数量售后变动
	AfterSaleSn                string `json:"afterSaleSn"`                // 商品售后单号
	AfterSaleStatus            int    `json:"afterSaleStatus"`            // 商品售后状态：1-售后中，2-售后完成，3-售后取消
	AfterSaleType              int    `json:"afterSaleType"`              // 售后类型：1-退货，2-换货
	AfterSaleFinishTime        int64  `json:"afterSaleFinishTime"`        // 售后完成时间，时间戳，单位：毫秒
	SizeId                     string `json:"sizeId"`                     // 商品尺码
	Status                     int16  `json:"status"`                     // 商品状态：0-不合格，1-待定，2-已完结
	GoodsFinalPrice            string `json:"goodsFinalPrice"`            // 商品成交价
	BrandStoreSn               string `json:"brandStoreSn"`               // 品牌编号
	BrandStoreName             string `json:"brandStoreName"`             // 品牌名称
	SpuId                      string `json:"spuId"`                      // 商品spuId
}
