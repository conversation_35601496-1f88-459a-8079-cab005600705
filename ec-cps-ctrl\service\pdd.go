package service

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	"errors"
	model2 "finance/model"
	"github.com/spf13/cast"
	"yz-go/source"
)

const (
	PddConvertUrl         = "http://api.tbk.dingdanxia.com/pdd/convert"
	PddUrlGenerateUrl     = "http://api.tbk.dingdanxia.com/pdd/urlgenerate"
	PddUrlConvertUrl      = "http://api.tbk.dingdanxia.com/pdd/url_convert"
	PddResourceConvertUrl = "http://api.tbk.dingdanxia.com/pdd/resource_convert"
	PddPidGenerateUrl     = "http://api.tbk.dingdanxia.com/pdd/pidgenerate"
	PddPidQueryUrl        = "http://api.tbk.dingdanxia.com/pdd/pidquery"
	PddOrderListUrl       = "http://api.tbk.dingdanxia.com/pdd/orderlist"
	PddOrderDetailUrl     = "http://api.tbk.dingdanxia.com/pdd/order_detail"
	GoodsSearchUrl        = "http://api.tbk.dingdanxia.com/pdd/goods_search"
	GoodsDetail2Url       = "http://api.tbk.dingdanxia.com/pdd/goods_detail2"
	CatsUrl               = "http://api.tbk.dingdanxia.com/pdd/cats"
	PromUrlGenerateUrl    = "http://api.tbk.dingdanxia.com/pdd/prom_url_generate"
)

// PddConvert 商品转链
func PddConvert(request map[string]interface{}) (err error, response interface{}) {
	if _, ok := request["parent_app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_user_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	request["custom_parameters"] = cast.ToString(request["parent_app_id"]) + "_" + cast.ToString(request["app_id"]) + "_" + cast.ToString(request["app_user_id"])
	var jdRelation model.CpsJDRelation
	jdRelation.AppID = cast.ToInt(request["app_id"])
	jdRelation.ParentAppId = cast.ToInt(request["parent_app_id"])
	jdRelation.AppUserID = cast.ToInt(request["app_user_id"])
	err = source.DB().Where("app_id = ? and parent_app_id = ? and app_user_id = ?", jdRelation.AppID, jdRelation.ParentAppId, jdRelation.AppUserID).FirstOrCreate(&jdRelation).Error
	if err != nil {
		return
	}
	// 如果setting中没有PID，则尝试获取PID
	if jdRelation.PddPid == "" {
		// 获取PID
		err, pidResponse := GetPddPid(cast.ToString(request["custom_parameters"]))
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}

		// 保存PID到setting
		jdRelation.PddPid = pidResponse.PID
		err = source.DB().Model(&model.CpsJDRelation{}).Where("id = ?", jdRelation.ID).Update("pdd_pid", &pidResponse.PID).Error
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}
	}

	request["p_id"] = jdRelation.PddPid

	return TbxRequest(PddConvertUrl, request)
}

// PddUrlGenerate 多多进宝推广链接生成
func PddUrlGenerate(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddUrlGenerateUrl, request)
}

// PddUrlConvert 链接解析转链
func PddUrlConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddUrlConvertUrl, request)
}

// PddResourceConvert 活动转链
func PddResourceConvert(request map[string]interface{}) (err error, response interface{}) {
	if _, ok := request["parent_app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_user_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	request["custom_parameters"] = cast.ToString(request["parent_app_id"]) + "_" + cast.ToString(request["app_id"]) + "_" + cast.ToString(request["app_user_id"])
	var jdRelation model.CpsJDRelation
	jdRelation.AppID = cast.ToInt(request["app_id"])
	jdRelation.ParentAppId = cast.ToInt(request["parent_app_id"])
	jdRelation.AppUserID = cast.ToInt(request["app_user_id"])
	err = source.DB().Where("app_id = ? and parent_app_id = ? and app_user_id = ?", jdRelation.AppID, jdRelation.ParentAppId, jdRelation.AppUserID).FirstOrCreate(&jdRelation).Error
	if err != nil {
		return
	}
	// 如果setting中没有PID，则尝试获取PID
	if jdRelation.PddPid == "" {
		// 获取PID
		err, pidResponse := GetPddPid(cast.ToString(request["custom_parameters"]))
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}

		// 保存PID到setting
		jdRelation.PddPid = pidResponse.PID
		err = source.DB().Model(&model.CpsJDRelation{}).Where("id = ?", jdRelation.ID).Update("pdd_pid", &pidResponse.PID).Error
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}
	}

	request["pid"] = jdRelation.PddPid

	return TbxRequest(PddResourceConvertUrl, request)
}

// PddPidGenerate 创建推广位
func PddPidGenerate(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddPidGenerateUrl, request)
}

// PddPidQuery 查询推广位
func PddPidQuery(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddPidQueryUrl, request)
}

// PddOrderList 订单列表
func PddOrderList(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddOrderListUrl, request)
}

// PddOrderDetail 订单详情
func PddOrderDetail(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddOrderDetailUrl, request)
}

type TbkPddGoodsSearchConfirm struct {
	Code int `json:"code"`
}
type TbkPddGoodsSearchResponse struct {
	Code int `json:"code"`
	Data []struct {
		ActivityTags                []int         `json:"activity_tags"`
		ActivityType                int           `json:"activity_type,omitempty"`
		BrandName                   string        `json:"brand_name"`
		CatIds                      []int         `json:"cat_ids"`
		CategoryId                  int           `json:"category_id"`
		CategoryName                string        `json:"category_name"`
		CouponDiscount              int           `json:"coupon_discount"`
		CouponEndTime               int           `json:"coupon_end_time"`
		CouponMinOrderAmount        int           `json:"coupon_min_order_amount"`
		CouponRemainQuantity        int           `json:"coupon_remain_quantity"`
		CouponStartTime             int           `json:"coupon_start_time"`
		CouponTotalQuantity         int           `json:"coupon_total_quantity"`
		DescTxt                     string        `json:"desc_txt"`
		GoodsDesc                   string        `json:"goods_desc"`
		GoodsId                     int64         `json:"goods_id"`
		GoodsImageUrl               string        `json:"goods_image_url"`
		GoodsName                   string        `json:"goods_name"`
		GoodsSign                   string        `json:"goods_sign"`
		GoodsThumbnailUrl           string        `json:"goods_thumbnail_url"`
		HasCoupon                   bool          `json:"has_coupon"`
		HasMallCoupon               bool          `json:"has_mall_coupon"`
		HasMaterial                 bool          `json:"has_material"`
		IsMultiGroup                bool          `json:"is_multi_group"`
		LgstTxt                     string        `json:"lgst_txt"`
		MallCouponDiscountPct       int           `json:"mall_coupon_discount_pct"`
		MallCouponEndTime           int           `json:"mall_coupon_end_time"`
		MallCouponId                int           `json:"mall_coupon_id"`
		MallCouponMaxDiscountAmount int           `json:"mall_coupon_max_discount_amount"`
		MallCouponMinOrderAmount    int           `json:"mall_coupon_min_order_amount"`
		MallCouponRemainQuantity    int           `json:"mall_coupon_remain_quantity"`
		MallCouponStartTime         int           `json:"mall_coupon_start_time"`
		MallCouponTotalQuantity     int           `json:"mall_coupon_total_quantity"`
		MallCps                     int           `json:"mall_cps"`
		MallId                      int           `json:"mall_id"`
		MallName                    string        `json:"mall_name"`
		MerchantType                int           `json:"merchant_type"`
		MinGroupPrice               int           `json:"min_group_price"`
		MinNormalPrice              int           `json:"min_normal_price"`
		OnlySceneAuth               bool          `json:"only_scene_auth"`
		OptId                       int           `json:"opt_id"`
		OptIds                      []int         `json:"opt_ids"`
		OptName                     string        `json:"opt_name"`
		PlanType                    int           `json:"plan_type"`
		PlatformDiscountList        []interface{} `json:"platform_discount_list"`
		PredictPromotionRate        int           `json:"predict_promotion_rate"`
		PromotionRate               int           `json:"promotion_rate"`
		SalesTip                    string        `json:"sales_tip"`
		SearchId                    string        `json:"search_id"`
		ServTxt                     string        `json:"serv_txt"`
		ServiceTags                 []int         `json:"service_tags"`
		ShareRate                   int           `json:"share_rate"`
		SubsidyGoodsType            int           `json:"subsidy_goods_type"`
		UnifiedTags                 []string      `json:"unified_tags"`
		ZsDuoId                     int           `json:"zs_duo_id"`
		EcCpsInfo                   struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	Msg          string `json:"msg"`
	TotalResults int    `json:"total_results"`
}

func GoodsSearch(request map[string]interface{}, userID uint) (err error, response interface{}) {
	if _, ok := request["parent_app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_user_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	request["custom_parameters"] = cast.ToString(request["parent_app_id"]) + "_" + cast.ToString(request["app_id"]) + "_" + cast.ToString(request["app_user_id"])
	var jdRelation model.CpsJDRelation
	jdRelation.AppID = cast.ToInt(request["app_id"])
	jdRelation.ParentAppId = cast.ToInt(request["parent_app_id"])
	jdRelation.AppUserID = cast.ToInt(request["app_user_id"])
	err = source.DB().Where("app_id = ? and parent_app_id = ? and app_user_id = ?", jdRelation.AppID, jdRelation.ParentAppId, jdRelation.AppUserID).FirstOrCreate(&jdRelation).Error
	if err != nil {
		return
	}
	// 如果setting中没有PID，则尝试获取PID
	if jdRelation.PddPid == "" {
		// 获取PID
		err, pidResponse := GetPddPid(cast.ToString(request["custom_parameters"]))
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}

		// 保存PID到setting
		jdRelation.PddPid = pidResponse.PID
		err = source.DB().Model(&model.CpsJDRelation{}).Where("id = ?", jdRelation.ID).Update("pdd_pid", &pidResponse.PID).Error
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}
	}

	request["pid"] = jdRelation.PddPid

	err, result := TbxRequest(GoodsSearchUrl, request)
	if err != nil {
		return
	}

	var materialResponseConfirm TbkPddGoodsSearchConfirm
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}
	err = json.Unmarshal(jsonData, &materialResponseConfirm)
	if err != nil {
		return
	}
	if materialResponseConfirm.Code != 200 {
		return err, result
	}
	var materialResponse TbkPddGoodsSearchResponse
	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {

		// 计算新的佣金金额
		newCommissionRate := float64(item.PromotionRate*10) * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		newCommissionAmount := float64(item.MinGroupPrice) / 100
		var rate = float64(item.PromotionRate) / 1000
		newCommissionAmount = newCommissionAmount * rate
		newCommissionAmount = newCommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
		materialResponse.Data[key].PromotionRate = 0
	}
	return nil, materialResponse
}

type TbkPddGoodsDetail2 struct {
	Code int `json:"code"`
	Data []struct {
		ActivityTags                []int         `json:"activity_tags"`
		BrandName                   string        `json:"brand_name"`
		CatId                       int           `json:"cat_id"`
		CatIds                      []int         `json:"cat_ids"`
		CategoryId                  int           `json:"category_id"`
		CategoryName                string        `json:"category_name"`
		CouponDiscount              int           `json:"coupon_discount"`
		CouponEndTime               int           `json:"coupon_end_time"`
		CouponMinOrderAmount        int           `json:"coupon_min_order_amount"`
		CouponRemainQuantity        int           `json:"coupon_remain_quantity"`
		CouponStartTime             int           `json:"coupon_start_time"`
		CouponTotalQuantity         int           `json:"coupon_total_quantity"`
		DescTxt                     string        `json:"desc_txt"`
		GoodsDesc                   string        `json:"goods_desc"`
		GoodsGalleryUrls            []string      `json:"goods_gallery_urls"`
		GoodsId                     int64         `json:"goods_id"`
		GoodsImageUrl               string        `json:"goods_image_url"`
		GoodsName                   string        `json:"goods_name"`
		GoodsSign                   string        `json:"goods_sign"`
		GoodsThumbnailUrl           string        `json:"goods_thumbnail_url"`
		HasCoupon                   bool          `json:"has_coupon"`
		HasMallCoupon               bool          `json:"has_mall_coupon"`
		IsMultiGroup                bool          `json:"is_multi_group"`
		LgstTxt                     string        `json:"lgst_txt"`
		MallCouponDiscountPct       int           `json:"mall_coupon_discount_pct"`
		MallCouponEndTime           int           `json:"mall_coupon_end_time"`
		MallCouponMaxDiscountAmount int           `json:"mall_coupon_max_discount_amount"`
		MallCouponMinOrderAmount    int           `json:"mall_coupon_min_order_amount"`
		MallCouponRemainQuantity    int           `json:"mall_coupon_remain_quantity"`
		MallCouponStartTime         int           `json:"mall_coupon_start_time"`
		MallCouponTotalQuantity     int           `json:"mall_coupon_total_quantity"`
		MallCps                     int           `json:"mall_cps"`
		MallId                      int           `json:"mall_id"`
		MallImgUrl                  string        `json:"mall_img_url"`
		MallName                    string        `json:"mall_name"`
		MaterialList                []interface{} `json:"material_list"`
		MerchantType                int           `json:"merchant_type"`
		MinGroupPrice               int           `json:"min_group_price"`
		MinNormalPrice              int           `json:"min_normal_price"`
		OnlySceneAuth               bool          `json:"only_scene_auth"`
		OptId                       int           `json:"opt_id"`
		OptIds                      []int         `json:"opt_ids"`
		OptName                     string        `json:"opt_name"`
		PlanType                    int           `json:"plan_type"`
		PlatformDiscountList        []interface{} `json:"platform_discount_list"`
		PredictPromotionRate        int           `json:"predict_promotion_rate"`
		PromotionRate               int           `json:"promotion_rate"`
		SalesTip                    string        `json:"sales_tip"`
		ServTxt                     string        `json:"serv_txt"`
		ServiceTags                 []int         `json:"service_tags"`
		ShareRate                   int           `json:"share_rate"`
		SubsidyGoodsType            int           `json:"subsidy_goods_type"`
		UnifiedTags                 []string      `json:"unified_tags"`
		VideoUrls                   []string      `json:"video_urls"`
		ZsDuoId                     int           `json:"zs_duo_id"`
		EcCpsInfo                   struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	Msg string `json:"msg"`
}

func GoodsDetail2(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(GoodsDetail2Url, request)
	if err != nil {
		return
	}

	var materialResponse TbkPddGoodsDetail2
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {

		// 计算新的佣金金额
		newCommissionRate := float64(item.PromotionRate*10) * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		newCommissionAmount := float64(item.MinGroupPrice) / 100
		var rate = float64(item.PromotionRate) / 1000
		newCommissionAmount = newCommissionAmount * rate
		newCommissionAmount = newCommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000
		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
		materialResponse.Data[key].PromotionRate = 0
	}
	return nil, materialResponse
}
func Cats(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(CatsUrl, request)
}

func PromUrlGenerate(request map[string]interface{}) (err error, response interface{}) {
	if _, ok := request["parent_app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["app_user_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	if _, ok := request["shop_id"]; !ok {
		return errors.New("中台归因关系不正确"), nil
	}
	request["custom_parameters"] = cast.ToString(request["parent_app_id"]) + "_" + cast.ToString(request["app_id"]) + "_" + cast.ToString(request["app_user_id"]) + "_" + cast.ToString(request["shop_id"])
	var jdRelation model.CpsJDRelation
	jdRelation.AppID = cast.ToInt(request["app_id"])
	jdRelation.ParentAppId = cast.ToInt(request["parent_app_id"])
	jdRelation.AppUserID = cast.ToInt(request["app_user_id"])
	jdRelation.ShopID = cast.ToInt(request["shop_id"])
	err = source.DB().Where("app_id = ? and parent_app_id = ? and app_user_id = ? and shop_id = ?", jdRelation.AppID, jdRelation.ParentAppId, jdRelation.AppUserID, jdRelation.ShopID).FirstOrCreate(&jdRelation).Error
	if err != nil {
		return
	}
	// 如果setting中没有PID，则尝试获取PID
	if jdRelation.PddPid == "" {
		// 获取PID
		err, pidResponse := GetPddPid(cast.ToString(request["custom_parameters"]))
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}

		// 保存PID到setting
		jdRelation.PddPid = pidResponse.PID
		err = source.DB().Model(&model.CpsJDRelation{}).Where("id = ?", jdRelation.ID).Update("pdd_pid", &pidResponse.PID).Error
		if err != nil {
			return errors.New("获取拼多多PID失败: " + err.Error()), nil
		}
	}
	var requestParams = make(map[string]interface{})
	requestParams["p_id_list"] = jdRelation.PddPid
	requestParams["channel_type"] = 10
	requestParams["custom_parameters"] = request["custom_parameters"]
	requestParams["generate_we_app"] = true
	return TbxRequest(PromUrlGenerateUrl, requestParams)
}

// PddPidResponse 拼多多PID生成响应
type PddPidResponse struct {
	CreateTime int64  `json:"create_time"` // 推广位创建时间
	PidName    string `json:"pid_name"`    // 推广位名称
	PID        string `json:"p_id"`        // 推广位ID
}

// GetPddPid 获取拼多多PID
func GetPddPid(customParameter string) (error, PddPidResponse) {
	var response PddPidResponse

	// 获取API密钥
	err, setting := model.GetCpsSetting()
	if err != nil {
		return err, response
	}

	// 构建请求参数
	request := map[string]interface{}{
		"apikey":         setting.ApiKey,
		"number":         1,                         // 生成1个推广位
		"p_id_name_list": []string{customParameter}, // 推广位名称
		"media_id":       setting.PddMediaId,        // 推广位名称
	}

	// 发送请求
	err, resp := TbxRequest(PddPidGenerateUrl, request)
	if err != nil {
		return err, response
	}

	// 解析响应
	respData, ok := resp.(map[string]interface{})
	if !ok {
		return errors.New("响应格式错误"), response
	}

	// 获取data数组
	dataArray, ok := respData["data"].([]interface{})
	if !ok || len(dataArray) == 0 {
		return errors.New("未获取到PID"), response
	}

	// 获取第一个PID信息
	pidInfo, ok := dataArray[0].(map[string]interface{})
	if !ok {
		return errors.New("PID信息格式错误"), response
	}

	// 解析PID信息
	if createTime, ok := pidInfo["create_time"].(float64); ok {
		response.CreateTime = int64(createTime)
	}

	if pidName, ok := pidInfo["pid_name"].(string); ok {
		response.PidName = pidName
	}

	if pid, ok := pidInfo["p_id"].(string); ok {
		response.PID = pid
	} else {
		return errors.New("未获取到有效的PID"), response
	}

	return nil, response
}
