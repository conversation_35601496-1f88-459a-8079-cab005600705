package service

import (
	"ec-cps-ctrl/model"
	"encoding/json"
	model2 "finance/model"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"yz-go/component/log"
	"yz-go/source"
)

// PddOrderListRequest 拼多多订单列表请求参数
type PddOrderListRequest struct {
	Page            int    `form:"page" json:"page" `                            // 页码
	PageSize        int    `form:"page_size" json:"page_size" `                  // 每页数量
	OrderSn         string `form:"order_sn" json:"order_sn"`                     // 订单号
	Status          *int   `form:"status" json:"status"`                         // 订单状态
	StartTime       string `form:"start_time" json:"start_time"`                 // 开始时间
	EndTime         string `form:"end_time" json:"end_time"`                     // 结束时间
	ParentAppID     uint   `form:"parent_app_id" json:"parent_app_id"`           // 中台ID
	ParentAppUserID uint   `form:"parent_app_user_id" json:"parent_app_user_id"` // 中台ID
}

// PddOrderListResult 拼多多订单列表结果
type PddOrderListResult struct {
	List      []PddOrder        `json:"list"`      // 订单列表
	Total     int64             `json:"total"`     // 总数
	Page      int               `json:"page"`      // 当前页码
	PageSize  int               `json:"page_size"` // 每页数量
	Statistic PddOrderStatistic `json:"statistic"` // 统计数据
	Status    map[int]string    `json:"status"`    // 状态映射
}

// PddOrderStatistic 拼多多订单统计
type PddOrderStatistic struct {
	Total      int64 `json:"total"`       // 总订单数
	WaitSettle int64 `json:"wait_settle"` // 待结算订单数
	Settled    int64 `json:"settled"`     // 已结算订单数
	Invalid    int64 `json:"invalid"`     // 无效/已失效订单数
}

// PddOrderQueryRequest 拼多多订单查询请求
type PddOrderQueryRequest struct {
	ApiKey          string `form:"apikey" json:"apikey"`                       // API密钥
	StartUpdateTime string `form:"start_update_time" json:"start_update_time"` // 开始时间（秒级时间戳）
	EndUpdateTime   string `form:"end_update_time" json:"end_update_time"`     // 结束时间（秒级时间戳）
	Page            string `form:"page" json:"page"`                           // 页码
	PageSize        string `form:"page_size" json:"page_size"`                 // 每页数量
}

// PddOrderQueryResponse 拼多多订单查询响应
type PddOrderQueryResponse struct {
	Code         int                  `json:"code"`
	Msg          string               `json:"msg"`
	TotalResults int                  `json:"total_results"`
	Data         []model.PddOrderData `json:"data"`
}

// 获取订单状态映射
func getPddOrderStatusMap() map[int]string {
	return map[int]string{
		-1: "未支付",
		0:  "已支付",
		1:  "已成团",
		2:  "确认收货",
		3:  "审核成功",
		4:  "审核失败",
		5:  "已结算",
		8:  "非多多进宝商品",
	}
}

type PddOrder struct {
	source.Model
	OrderSn               string  `json:"order_sn" gorm:"column:order_sn;index"`                                   // 推广订单编号
	GoodsId               int64   `json:"goods_id" gorm:"column:goods_id"`                                         // 商品ID
	GoodsName             string  `json:"goods_name" gorm:"column:goods_name;type:varchar(255)"`                   // 商品标题
	GoodsThumbnailUrl     string  `json:"goods_thumbnail_url" gorm:"column:goods_thumbnail_url;type:varchar(255)"` // 商品缩略图
	GoodsPrice            int64   `json:"goods_price" gorm:"column:goods_price"`                                   // 订单中sku的单件价格，单位为分
	GoodsQuantity         int64   `json:"goods_quantity" gorm:"column:goods_quantity"`                             // 购买商品的数量
	OrderAmount           int64   `json:"order_amount" gorm:"column:order_amount"`                                 // 实际支付金额，单位为分
	OrderCreateTime       int64   `json:"order_create_time" gorm:"column:order_create_time"`                       // 订单生成时间，UNIX时间戳
	OrderPayTime          int64   `json:"order_pay_time" gorm:"column:order_pay_time"`                             // 支付时间
	OrderGroupSuccessTime int64   `json:"order_group_success_time" gorm:"column:order_group_success_time"`         // 成团时间
	OrderVerifyTime       int64   `json:"order_verify_time" gorm:"column:order_verify_time"`                       // 审核时间
	OrderSettleTime       int64   `json:"order_settle_time" gorm:"column:order_settle_time"`                       // 结算时间
	OrderModifyAt         int64   `json:"order_modify_at" gorm:"column:order_modify_at"`                           // 最后更新时间
	OrderStatus           int     `json:"order_status" gorm:"column:order_status"`                                 // 订单状态： -1 未支付; 0-已支付；1-已成团；2-确认收货；3-审核成功；4-审核失败（不可提现）；5-已经结算；8-非多多进宝商品（无佣金订单）
	OrderStatusDesc       string  `json:"order_status_desc" gorm:"column:order_status_desc"`                       // 订单状态描述
	PromotionAmount       float64 `json:"promotion_amount" gorm:"column:promotion_amount"`                         // 佣金金额，单位为分
	PId                   string  `json:"p_id" gorm:"column:p_id"`                                                 // 推广位ID
	CustomParameters      string  `json:"custom_parameters" gorm:"column:custom_parameters"`                       // 自定义参数
	Type                  int     `json:"type" gorm:"column:type"`                                                 // 订单类型
	GroupId               int64   `json:"group_id" gorm:"column:group_id"`                                         // 团ID
	AuthDuoId             int64   `json:"auth_duo_id" gorm:"column:auth_duo_id"`                                   // 渠道ID
	ZsDuoId               int64   `json:"zs_duo_id" gorm:"column:zs_duo_id"`                                       // 招商多多客ID
	CpaNew                int     `json:"cpa_new" gorm:"column:cpa_new"`                                           // 是否是 cpa 新用户，1表示是，0表示否
	FailReason            string  `json:"fail_reason" gorm:"column:fail_reason;type:varchar(255)"`                 // 审核失败原因
	MatchChannel          int     `json:"match_channel" gorm:"column:match_channel"`                               // 匹配渠道
	OrderReceiveTime      int64   `json:"order_receive_time" gorm:"column:order_receive_time"`                     // 收货时间
	BatchNo               string  `json:"batch_no" gorm:"column:batch_no"`                                         // 结算批次号
	DuoCouponAmount       int64   `json:"duo_coupon_amount" gorm:"column:duo_coupon_amount"`                       // 多多进宝券金额，单位为分
	SceneAtMarketFee      int64   `json:"scene_at_market_fee" gorm:"column:scene_at_market_fee"`                   // 直播间站外推广费用，单位为分
	AppID                 int     `json:"app_id" gorm:"column:app_id"`                                             // 商城ID
	ParentAppID           int     `json:"parent_app_id" gorm:"column:parent_app_id"`                               // 中台ID
	AppUserID             int     `json:"app_user_id" gorm:"column:app_user_id"`                                   // 商城会员ID
	ShopID                int     `json:"shop_id" gorm:"column:shop_id"`                                           // 商城会员ID

	// 额外字段
	SyncTime      time.Time `json:"sync_time" gorm:"column:sync_time"`                     // 同步时间
	ProcessStatus int       `json:"process_status" gorm:"column:process_status;default:0"` // 处理状态：0-未处理，1-已处理
}

func (PddOrder) TableName() string {
	return "ec_cps_pdd_orders"
}

// GetPddOrderList 获取拼多多订单列表
func GetPddOrderList(req PddOrderListRequest) (error, PddOrderListResult) {
	var result PddOrderListResult

	// 构建查询
	db := source.DB().Model(&PddOrder{})

	// 添加中台ID过滤
	if req.ParentAppID > 0 {
		db = db.Where("parent_app_id = ?", req.ParentAppID)
	}

	// 条件查询
	if req.OrderSn != "" {
		db = db.Where("order_sn = ?", req.OrderSn)
	}
	if req.Status != nil {
		db = db.Where("order_status = ?", *req.Status)
	}
	if req.StartTime != "" {
		// 将时间字符串转换为秒级时间戳
		db = db.Where("sync_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		// 将时间字符串转换为秒级时间戳
		db = db.Where("sync_time <= ?", req.EndTime)

	}

	// 统计数据
	var statistic PddOrderStatistic
	// 总订单数
	db.Count(&statistic.Total)
	// 待结算订单数 (状态为1-已成团或2-确认收货或3-审核成功)
	source.DB().Model(&model.PddOrder{}).Where("order_status IN ?", []int{1, 2, 3}).Count(&statistic.WaitSettle)
	// 已结算订单数 (状态为5-已结算)
	source.DB().Model(&model.PddOrder{}).Where("order_status = ?", 5).Count(&statistic.Settled)
	// 无效/已失效订单数 (状态为4-审核失败或8-非多多进宝商品)
	source.DB().Model(&model.PddOrder{}).Where("order_status IN ?", []int{4, 8}).Count(&statistic.Invalid)

	// 分页查询
	var list []PddOrder
	err := db.Order("sync_time DESC").Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize).Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询拼多多订单列表失败: %v", err), result
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, req.ParentAppUserID).Error
	if err != nil {
		return fmt.Errorf("查询拼多多订单列表失败: %v", err), result
	}
	if req.ParentAppID > 0 {
		for key, item := range list {
			// 将计算结果转回字符串，保留两位小数
			newCommissionAmount := item.PromotionAmount / 100
			newCommissionAmount = newCommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000
			list[key].PromotionAmount = newCommissionAmount
		}
	}

	// 设置结果
	result.List = list
	result.Total = statistic.Total
	result.Page = req.Page
	result.PageSize = req.PageSize
	result.Statistic = statistic
	result.Status = getPddOrderStatusMap()

	return nil, result
}

// GetPddOrderDetail 获取拼多多订单详情
func GetPddOrderDetail(id int) (error, model.PddOrder) {
	var order model.PddOrder
	err := source.DB().First(&order, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("订单不存在"), order
		}
		return fmt.Errorf("查询拼多多订单详情失败: %v", err), order
	}

	return nil, order
}

// ExportPddOrders 导出拼多多订单
func ExportPddOrders(orders []model.PddOrder) error {
	if len(orders) == 0 {
		return fmt.Errorf("没有可导出的订单数据")
	}

	// 这里可以实现导出功能，或者调用已有的导出功能
	// 为简化示例，这里只记录日志
	log.Log().Info("导出拼多多订单", zap.Int("数量", len(orders)))
	return nil
}

// ExportPddOrdersByQuery 根据查询条件导出拼多多订单
func ExportPddOrdersByQuery(req PddOrderListRequest) error {
	// 构建查询
	db := source.DB().Model(&model.PddOrder{})

	// 添加中台ID过滤
	if req.ParentAppID > 0 {
		db = db.Where("parent_app_id = ?", req.ParentAppID)
	}

	// 条件查询
	if req.OrderSn != "" {
		db = db.Where("order_sn = ?", req.OrderSn)
	}
	if req.Status != nil {
		db = db.Where("order_status = ?", *req.Status)
	}
	if req.StartTime != "" {
		// 将时间字符串转换为秒级时间戳
		startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime)
		if err == nil {
			db = db.Where("order_create_time >= ?", startTime.Unix())
		}
	}
	if req.EndTime != "" {
		// 将时间字符串转换为秒级时间戳
		endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime)
		if err == nil {
			db = db.Where("order_create_time <= ?", endTime.Unix())
		}
	}

	// 查询数据
	var list []model.PddOrder
	err := db.Order("sync_time DESC").Find(&list).Error
	if err != nil {
		return fmt.Errorf("查询拼多多订单列表失败: %v", err)
	}

	// 导出数据
	return ExportPddOrders(list)
}

// QueryPddOrders 直接查询拼多多订单接口
func QueryPddOrders(req PddOrderQueryRequest) (err error, resp PddOrderQueryResponse, syncErr error) {
	// 获取API密钥
	if req.ApiKey == "" {
		err, setting := model.GetCpsSetting()
		if err != nil {
			return err, resp, nil
		}
		req.ApiKey = setting.ApiKey
	}

	// 设置默认值
	if req.Page == "" {
		req.Page = "1" // 默认第一页
	}
	if req.PageSize == "" {
		req.PageSize = "40" // 默认每页40条
	}

	// 如果未指定时间范围，默认查询最近24小时
	if req.StartUpdateTime == "" || req.EndUpdateTime == "" {
		now := time.Now()
		req.EndUpdateTime = fmt.Sprintf("%d", now.Unix())
		req.StartUpdateTime = fmt.Sprintf("%d", now.Add(-24*time.Hour).Unix())
	}

	// 构建查询参数
	queryParams := map[string]string{
		"apikey":            req.ApiKey,
		"page":              req.Page,
		"page_size":         req.PageSize,
		"start_update_time": req.StartUpdateTime,
		"end_update_time":   req.EndUpdateTime,
	}

	// 创建HTTP客户端
	client := resty.New().SetRetryCount(3).SetRetryWaitTime(2 * time.Second)

	// 发送请求
	response, err := client.R().
		SetHeader("Content-Type", "application/json").
		SetQueryParams(queryParams).
		Post("http://api.tbk.dingdanxia.com/pdd/orderlist")

	if err != nil {
		return fmt.Errorf("请求拼多多订单接口失败: %v", err), resp, nil
	}

	// 解析响应
	if err := json.Unmarshal(response.Body(), &resp); err != nil {
		return fmt.Errorf("解析响应数据失败: %v", err), resp, nil
	}

	// 检查响应状态
	if resp.Code != 200 {
		return fmt.Errorf("接口返回错误: %s", resp.Msg), resp, nil
	}

	// 同步订单到数据库（异步处理）
	go func() {
		syncErr = SyncPddOrdersToDatabase(resp.Data)
	}()

	return nil, resp, syncErr
}

// SyncPddOrdersToDatabase 同步拼多多订单到数据库
func SyncPddOrdersToDatabase(orders []model.PddOrderData) error {
	if len(orders) == 0 {
		return nil
	}

	log.Log().Info("开始同步拼多多订单到数据库", zap.Int("订单数量", len(orders)))

	// 收集所有订单ID
	var orderSns []string
	for _, order := range orders {
		orderSns = append(orderSns, order.OrderSn)
	}

	// 查询数据库中已存在的订单
	var existingOrders []model.PddOrder
	result := source.DB().Where("order_sn IN ?", orderSns).Find(&existingOrders)
	if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
		log.Log().Error("查询现有订单失败", zap.Error(result.Error))
		return result.Error
	}

	// 将现有订单映射到map中便于快速查找
	existingOrderMap := make(map[string]model.PddOrder)
	for _, order := range existingOrders {
		existingOrderMap[order.OrderSn] = order
	}

	// 准备新订单和需要更新的订单
	var newOrders []model.PddOrder
	var updateOrders []model.PddOrder

	for _, orderData := range orders {
		// 转换为模型订单
		order := convertToPddOrder(orderData)

		// 设置同步时间
		order.SyncTime = time.Now()

		if _, exists := existingOrderMap[order.OrderSn]; !exists {
			// 新订单
			newOrders = append(newOrders, order)
		} else {
			// 需要更新的订单
			dbOrder := existingOrderMap[order.OrderSn]
			dbOrder.OrderStatus = order.OrderStatus
			dbOrder.OrderStatusDesc = order.OrderStatusDesc
			dbOrder.OrderModifyAt = order.OrderModifyAt
			dbOrder.OrderVerifyTime = order.OrderVerifyTime
			dbOrder.OrderSettleTime = order.OrderSettleTime
			dbOrder.OrderReceiveTime = order.OrderReceiveTime
			dbOrder.PromotionAmount = order.PromotionAmount
			dbOrder.PromotionRate = order.PromotionRate
			dbOrder.FailReason = order.FailReason
			dbOrder.BatchNo = order.BatchNo
			dbOrder.AppID = order.AppID
			dbOrder.ParentAppID = order.ParentAppID
			dbOrder.AppUserID = order.AppUserID
			dbOrder.SyncTime = time.Now()
			updateOrders = append(updateOrders, dbOrder)
		}
	}

	// 批量创建新订单
	if len(newOrders) > 0 {
		// 分批插入以避免一次插入过多数据
		batchSize := 50
		for i := 0; i < len(newOrders); i += batchSize {
			end := i + batchSize
			if end > len(newOrders) {
				end = len(newOrders)
			}

			batch := newOrders[i:end]
			if err := source.DB().Create(&batch).Error; err != nil {
				log.Log().Error("批量创建订单失败", zap.Error(err))
				return err
			}
		}
		log.Log().Info("成功创建新订单", zap.Int("数量", len(newOrders)))
	}

	// 批量更新订单
	if len(updateOrders) > 0 {
		// 分批更新
		batchSize := 50
		for i := 0; i < len(updateOrders); i += batchSize {
			end := i + batchSize
			if end > len(updateOrders) {
				end = len(updateOrders)
			}

			batch := updateOrders[i:end]
			for _, order := range batch {
				// 使用条件更新来避免并发问题
				if err := source.DB().Model(&model.PddOrder{}).Where("order_sn = ?", order.OrderSn).Updates(map[string]interface{}{
					"order_status":       order.OrderStatus,
					"order_status_desc":  order.OrderStatusDesc,
					"order_modify_at":    order.OrderModifyAt,
					"order_verify_time":  order.OrderVerifyTime,
					"order_settle_time":  order.OrderSettleTime,
					"order_receive_time": order.OrderReceiveTime,
					"promotion_amount":   order.PromotionAmount,
					"promotion_rate":     order.PromotionRate,
					"fail_reason":        order.FailReason,
					"batch_no":           order.BatchNo,
					"app_id":             order.AppID,
					"parent_app_id":      order.ParentAppID,
					"app_user_id":        order.AppUserID,
					"sync_time":          time.Now(),
				}).Error; err != nil {
					log.Log().Error("更新订单失败", zap.String("订单ID", order.OrderSn), zap.Error(err))
					return err
				}
			}
		}
		log.Log().Info("成功更新订单", zap.Int("数量", len(updateOrders)))
	}

	return nil
}

// 将API订单数据转换为模型订单
func convertToPddOrder(orderData model.PddOrderData) model.PddOrder {
	// 解析CustomParameters获取AppID、ParentAppID和AppUserID
	var parentAppID, appID, appUserID int

	// CustomParameters格式可能是"parentAppID_appID_appUserID"或其他格式
	if orderData.CustomParameters != "" {
		parts := strings.Split(orderData.CustomParameters, "_")
		if len(parts) >= 3 {
			parentAppID, _ = strconv.Atoi(parts[0])
			appID, _ = strconv.Atoi(parts[1])
			appUserID, _ = strconv.Atoi(parts[2])
		} else if len(parts) == 2 {
			// 如果只有两部分，假设是"appID_appUserID"
			appID, _ = strconv.Atoi(parts[0])
			appUserID, _ = strconv.Atoi(parts[1])
		} else if len(parts) == 1 {
			// 如果只有一部分，尝试解析为appID
			appID, _ = strconv.Atoi(parts[0])
		}
	}

	return model.PddOrder{
		OrderSn:               orderData.OrderSn,
		GoodsId:               orderData.GoodsId,
		GoodsName:             orderData.GoodsName,
		GoodsThumbnailUrl:     orderData.GoodsThumbnailUrl,
		GoodsPrice:            orderData.GoodsPrice,
		GoodsQuantity:         orderData.GoodsQuantity,
		OrderAmount:           orderData.OrderAmount,
		OrderCreateTime:       orderData.OrderCreateTime,
		OrderPayTime:          orderData.OrderPayTime,
		OrderGroupSuccessTime: orderData.OrderGroupSuccessTime,
		OrderVerifyTime:       orderData.OrderVerifyTime,
		OrderSettleTime:       orderData.OrderSettleTime,
		OrderModifyAt:         orderData.OrderModifyAt,
		OrderStatus:           orderData.OrderStatus,
		OrderStatusDesc:       orderData.OrderStatusDesc,
		PromotionRate:         orderData.PromotionRate,
		PromotionAmount:       orderData.PromotionAmount,
		PId:                   orderData.PId,
		CustomParameters:      orderData.CustomParameters,
		Type:                  orderData.Type,
		GroupId:               orderData.GroupId,
		AuthDuoId:             orderData.AuthDuoId,
		ZsDuoId:               orderData.ZsDuoId,
		CpaNew:                orderData.CpaNew,
		FailReason:            orderData.FailReason,
		MatchChannel:          orderData.MatchChannel,
		OrderReceiveTime:      orderData.OrderReceiveTime,
		BatchNo:               orderData.BatchNo,
		DuoCouponAmount:       orderData.DuoCouponAmount,
		SceneAtMarketFee:      orderData.SceneAtMarketFee,
		// 设置解析出的ID
		ParentAppID:   parentAppID,
		AppID:         appID,
		AppUserID:     appUserID,
		SyncTime:      time.Now(),
		ProcessStatus: 0,
	}
}
