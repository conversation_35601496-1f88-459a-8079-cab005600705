package service

import (
	"errors"
	"fmt"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/admin/model/request"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"gorm.io/gorm"
	"reflect"
	"strconv"
	"yz-go/source"
	"yz-go/utils"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: getMenuTreeMap
//@description: 获取路由总树map
//@param: authorityId string
//@return: err error, treeMap map[string][]model.SysMenu

func getMenuTreeMap(authorityId string) (err error, treeMap map[string][]model.SysMenu) {
	var allMenus []model.SysMenu
	var ids []int64
	treeMap = make(map[string][]model.SysMenu)
	source.DB().Debug().Table("sys_authority_menus").Where("sys_authority_authority_id = ?", authorityId).Pluck("sys_base_menu_id", &ids)
	var authority model.SysAuthority
	source.DB().Where("authority_id=?", authorityId).First(&authority)
	if authorityId == "110" || authority.ParentId == "110" {
		allMenus = model.GVA_SUPPLY_MENUS
	} else if authorityId == "115" || authority.ParentId == "115" { //净水机后台
		allMenus = model.GVA_WATER_MENUS

	} else if authorityId == "111" || authority.ParentId == "111" {
		allMenus = model.GVA_LOCAL_LIFE_MENUS
	} else {
		allMenus = model.GVA_MENUS
	}

	for _, v := range allMenus {
		if authorityId == "888" || authorityId == "110" || authorityId == "111" || authorityId == "115" {
			if collection.Collect(gva.GlobalAuth.Menu).Contains(v.ID) == true {
				continue
			}
			if v.MenuId == "388" {
				if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(13) == false && utils.LocalEnv() == true {
					continue
				}
			}
			if v.MenuId == "398" {
				if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(12) == false && utils.LocalEnv() == true {
					continue
				}
			}
			treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
		} else {
			id, _ := strconv.Atoi(v.MenuId)
			if collection.Collect(ids).Contains(id) == true {
				if v.MenuId == "388" || v.MenuId == "398" {
					if v.MenuId == "388" {
						if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(13) == false && utils.LocalEnv() == true {
							continue
						}
					}
					if v.MenuId == "398" {
						if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(12) == false && utils.LocalEnv() == true {
							continue
						}
					}
				}
				treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
			}
		}

	}
	return err, treeMap
}

func getMenuTreeMapBase() (err error, treeMap map[string][]model.SysMenu) {
	var allMenus []model.SysMenu

	treeMap = make(map[string][]model.SysMenu)

	allMenus = append(allMenus, model.GVA_MENUS...)
	//allMenus =append(allMenus,model.GVA_SUPPLY_MENUS...)
	for _, v := range allMenus {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return err, treeMap
}

func Exis(ids []int64, menuID string) bool {
	for _, b := range ids {
		i, _ := strconv.ParseInt(menuID, 10, 64)
		if b == i {
			return true
		}
	}
	return false
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetMenuTree
//@description: 获取动态菜单树
//@param: authorityId string
//@return: err error, menus []model.SysMenu

func GetMenuTree(authorityId string) (err error, menus []model.SysMenu) {
	err, menuTree := getMenuTreeMap(authorityId)
	menus = menuTree["0"]
	for i := 0; i < len(menus); i++ {
		err = getChildrenList(&menus[i], menuTree)
	}
	return err, menus
}

func GetMenuTreeBase() (err error, menus []model.SysMenu) {
	err, menuTree := getMenuTreeMapBase()
	menus = menuTree["0"]
	for i := 0; i < len(menus); i++ {
		err = getChildrenList(&menus[i], menuTree)
	}
	return err, menus
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: getChildrenList
//@description: 获取子菜单
//@param: menu *model.SysMenu, treeMap map[string][]model.SysMenu
//@return: err error

func getChildrenList(menu *model.SysMenu, treeMap map[string][]model.SysMenu) (err error) {
	menu.Children = treeMap[menu.MenuId]
	for i := 0; i < len(menu.Children); i++ {
		err = getChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetInfoList
//@description: 获取路由分页
//@return: err error, list interface{}, total int64

func GetInfoList() (err error, list interface{}, total int64) {
	var menuList []model.SysBaseMenu
	err, treeMap := getBaseMenuTreeMap()
	menuList = treeMap["0"]
	for i := 0; i < len(menuList); i++ {
		err = getBaseChildrenList(&menuList[i], treeMap)
	}
	return err, menuList, total
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: getBaseChildrenList
//@description: 获取菜单的子菜单
//@param: menu *model.SysBaseMenu, treeMap map[string][]model.SysBaseMenu
//@return: err error

func getBaseChildrenList(menu *model.SysBaseMenu, treeMap map[string][]model.SysBaseMenu) (err error) {
	menu.Children = treeMap[strconv.Itoa(int(menu.ID))]
	for i := 0; i < len(menu.Children); i++ {
		err = getBaseChildrenList(&menu.Children[i], treeMap)
	}
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: AddBaseMenu
//@description: 添加基础路由
//@param: menu model.SysBaseMenu
//@return: err error

func AddBaseMenu(menu model.SysBaseMenu) error {
	if !errors.Is(source.DB().Where("name = ?", menu.Name).First(&model.SysBaseMenu{}).Error, gorm.ErrRecordNotFound) {
		return errors.New("存在重复name，请修改name")
	}
	return source.DB().Create(&menu).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: getBaseMenuTreeMap
//@description: 获取路由总树map
//@return: err error, treeMap map[string][]model.SysBaseMenu

func getBaseMenuTreeMap() (err error, treeMap map[string][]model.SysBaseMenu) {
	var allMenus []model.SysBaseMenu
	treeMap = make(map[string][]model.SysBaseMenu)
	err = source.DB().Order("sort").Preload("Parameters").Not("id >= ? and id < ?", 350, 400).Find(&allMenus).Error
	for _, v := range allMenus {
		treeMap[v.ParentId] = append(treeMap[v.ParentId], v)
	}
	return err, treeMap
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetBaseMenuTree
//@description: 获取基础路由树
//@return: err error, menus []model.SysBaseMenu

func GetBaseMenuTree() (err error, menus []model.SysBaseMenu) {
	err, treeMap := getBaseMenuTreeMap()
	menus = treeMap["0"]
	for i := 0; i < len(menus); i++ {
		err = getBaseChildrenList(&menus[i], treeMap)
	}
	return err, menus
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: AddMenuAuthorityTree
//@description: 为角色增加menu树
//@param: menus []model.SysBaseMenu, authorityId string
//@return: err error

func AddMenuAuthorityTree(menus []model.SysBaseMenu, authorityId string) (err error) {
	var auth model.SysAuthority
	auth.AuthorityId = authorityId
	auth.SysBaseMenus = menus
	err = SetMenuAuthorityTree(&auth)
	return err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetMenuAuthority
// @description: 查看当前角色树
// @param: info *request.GetAuthorityId
// @return: err error, menus []model.SysMenu
func in(target string, str_array []model.AuthorityMenus) bool {

	for _, element := range str_array {
		BaseMenuId := strconv.Itoa(int(element.BaseMenuId))
		if target == BaseMenuId {

			return true

		}

	}

	return false

}

func StrctToSlice(f []model.AuthorityMenus) []string {
	v := reflect.ValueOf(f)
	ss := make([]string, v.NumField())
	for i := range ss {
		ss[i] = fmt.Sprintf("%v", v.Field(i))
	}
	return ss
}

func GetMenuAuthority(info *request.GetAuthorityId) (err error, menus []model.SysMenu) {
	//err = source.DB().Debug().Where("authority_id = ? ", info.AuthorityId).Order("sort").Find(&menus).Error

	var ids = []model.AuthorityMenus{}
	source.DB().Table("sys_authority_menus").Debug().Select("sys_base_menu_id").Where("sys_authority_authority_id = ? ", info.AuthorityId).Find(&ids)
	var allMenus []model.SysMenu
	allMenus = model.GVA_MENUS
	for _, v := range allMenus {
		sid := strconv.Itoa(int(v.ID))
		if in(sid, ids) {
			menus = append(menus, v)
		}
	}
	return err, menus
}
