import service from '@/utils/request';

/* *@Summary 获取饮水机设置
 *@Router  /water-machine/setting
 *@Method  get *@Date    2023-09-25*/
export const getWaterMachineSettings = (params) => {
    return service({
        url: '/water-machine/setting',
        method: 'get',
        params,
    });
};

/* *@Summary 更新饮水机设置
 *@Router  /water-machine/setting
 *@Method  put
 *@Date    2023-09-25*/
export const updateWaterMachineSettings = (data) => {
    return service({
        url: '/water-machine/setting',
        method: 'put',
        data,
    });
};

/* *@Summary 切换饮水机开关状态
 *@Router  /water-machine/setting/toggle
 *@Method  post
 *@Date    2023-09-25*/
export const toggleWaterMachine = (data) => {
    return service({
        url: '/water-machine/setting/toggle',
        method: 'post',
        data,
    });
};
