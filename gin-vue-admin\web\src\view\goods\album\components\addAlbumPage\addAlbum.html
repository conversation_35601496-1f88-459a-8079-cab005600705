<el-drawer
        :title="`${title}商品专辑`"
        size="calc(100% - 220px)"
        :visible="isShow"
        :close-on-press-escape="false"
        :wrapperClosable="false"
        :before-close="handleClose"
>
    <el-form :model="formData" ref="form" label-width="90px">
        <el-row class="noPadding">
            <el-col :span="16">
                <el-form-item prop="title">
                    <span slot="label"><span class="color-red">*</span> 专辑名称:</span>
                    <el-input v-model="formData.title" placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="16">
                <el-form-item prop="desc">
                    <span slot="label"><span class="color-red">*</span> 专辑简介:</span>
                    <el-input v-model="formData.desc" placeholder="请输入"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="16">
                <el-form-item prop="cover">
                    <span slot="label"><span class="color-red">*</span> 专辑封面:</span>
                    <el-upload
                            v-loading="uploadLoading"
                            class="avatar-uploader"
                            :show-file-list="false"
                            :action="path + '/fileUploadAndDownload/upload'"
                            :headers="{ 'x-token': token }"
                            :on-success="handleMainImgSuccess"
                            :on-error="handleCoverError"
                            :before-upload="$fn.beforeAvatarUpload "
                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                    >
                        <img v-if="formData.cover" :src="formData.cover" class="avatar"/>
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </el-form-item>
            </el-col>
            <el-col :span="16">
                <el-form-item prop="type">
                    <span slot="label"><span class="color-red">*</span> 商品数据:</span>
                    <el-radio-group v-model="formData.type">
                        <el-radio :label="1">单个选择</el-radio>
                        <el-radio :label="2">指定分类</el-radio>
                        <el-radio :label="3">营销属性</el-radio>
                        <el-radio :label="4">商品数据</el-radio>
                    </el-radio-group>
                </el-form-item>
                <!-- 指定分类 -->
                <template v-if="formData.type === 2">
                    <el-form-item>
                        <div class="f">
                            <p><span class="color-red">* </span>显示商品数量</p>
                            <m-num-input
                                    style="width: 200px; margin-left: 5px; margin-right: 5px"
                                    v-model="formData.filter.category_num"
                                    :min="0"
                            ></m-num-input>
                            <p>个</p>
                        </div>
                    </el-form-item>


                    <el-form-item v-for="(item,index) in categoryList" :key="item.id">
                        <el-select v-model="item.category_1_id" class="mr10" clearable filterable placeholder="请选择一级分类"
                                   @change="getCategory(item.category_1_id,2,index)">
                            <el-option v-for="c1 in category_1List" :key="c1.id" :label="c1.name"
                                       :value="c1.id"></el-option>
                        </el-select>
                        <el-select v-model="item.category_2_id" class="mr10" clearable filterable placeholder="请选择二级分类"
                                   @change="getCategory(item.category_2_id,3,index)">
                            <el-option v-for="c2 in item.category_2List" :key="c2.id" :label="c2.name"
                                       :value="c2.id"></el-option>
                        </el-select>
                        <el-select v-model="item.category_3_id" class="mr10" clearable placeholder="请选择三级分类">
                            <el-option v-for="c3 in item.category_3List" :key="c3.id" :label="c3.name"
                                       :value="c3.id"></el-option>
                        </el-select>
                        <el-button v-if="index > 0" type="danger" size="medium" @click="delCategory(index)">删除
                        </el-button>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="pushCategory">添加分类</el-button>
                    </el-form-item>
                </template>
                <!-- 营销属性 -->
                <template v-if="formData.type === 3">
                    <el-form-item>
                        <div class="f">
                            <p><span class="color-red">* </span>显示商品数量</p>
                            <m-num-input
                                    style="width: 200px; margin-left: 5px; margin-right: 5px"
                                    v-model="formData.filter.sale_num"
                                    :min="0"
                            ></m-num-input>
                            <p>个</p>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <el-radio-group v-model="formData.filter.attribute_type">
                            <el-radio :label="1">热卖</el-radio>
                            <el-radio :label="3">新品</el-radio>
                            <el-radio :label="4">促销</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </template>
                <!-- 商品数据 -->
                <template v-if="formData.type === 4">
                    <el-form-item>
                        <div class="f">
                            <p><span class="color-red">* </span>显示商品数量</p>
                            <m-num-input
                                    style="width: 200px; margin-left: 5px; margin-right: 5px"
                                    v-model="formData.filter.statistic_num"
                                    :min="0"
                            ></m-num-input>
                            <p>个</p>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <el-radio-group v-model="formData.filter.statistic_type">
                            <el-radio :label="1">销量</el-radio>
                            <el-radio :label="2">时间</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="formData.filter.statistic_type === 1">
                        <div class="f fac">
                            <div class="mr_20">数据周期</div>
                            <el-radio-group v-model="formData.filter.statistic_time">
                                <el-radio :label="0">全部</el-radio>
                                <el-radio :label="1">昨天</el-radio>
                                <el-radio :label="2">上周</el-radio>
                                <el-radio :label="3">上个月</el-radio>
                                <el-radio :label="4">过去7天</el-radio>
                                <el-radio :label="5">过去30天</el-radio>
                                <el-radio :label="6">本年度</el-radio>
                            </el-radio-group>
                        </div>
                    </el-form-item>
                </template>
            </el-col>

            <el-col>
                <el-form-item>
                    <el-button type="primary" @click="confirm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</el-drawer>
