import {mapGetters} from "vuex";
import {
    createCollection,
    findCollectionById,
    updateCollection,
} from "@/api/collection";
import GoodsDialog from "@/components/goodsDialog/index";
import MNumInput from "@/components/mNumInput";
import {confirm} from "@/decorators/decorators";
import {getCategoryListWithParentId, categoryTree, findCategory} from "@/api/category";

export default {
    components: {GoodsDialog, MNumInput},
    data() {
        return {
            // 分类数据
            category_1List: [],
            categoryList: [
                {
                    category_1_id: "",
                    category_2_id: "",
                    category_3_id: "",
                    category_2List: [],
                    category_3List: [],
                },
            ],
            uploadLoading: false,
            isShow: false,
            title: "新建",
            imageUrl: "",
            path: this.$path,
            formData: {
                id: 0,
                // 标题
                title: "",
                // 简介
                desc: "",
                // 封面
                cover: "",
                type: 1, // 1单个选择 2指定分类 3营销属性 4商品数据
                filter: {
                    category_1_id: [],
                    category_2_id: [],
                    category_3_id: [],
                    category_num: 0, // 分类  商品显示数量
                    attribute_type: 1, // 营销属性 1热卖 3新品 4促销
                    sale_num: 0, // 营销属性  商品显示数量
                    statistic_type: 1, // 商品数据 1销量 2时间
                    statistic_num: 0, // 商品数据 商品显示数量
                    statistic_time: 1, // 数据周期
                },
                // 选中商品
                products: [],
            },
            batchRemoveProducts: [],
        };
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),
    },
    methods: {
        /**
         * 获取分类
         * @param pid - 父pid
         * @param level - 级别
         * @param index - 下标
         */
        async getCategory(pid = 0, level = 1, index = 0) {
            switch (level) {
                case 1:
                    this.categoryList[index].category_2List = []
                    this.categoryList[index].category_3List = []
                    this.categoryList[index].category_2_id = ""
                    this.categoryList[index].category_3_id = ""
                    break;
                case 2:
                    this.categoryList[index].category_2_id = ""
                    this.categoryList[index].category_2List = []
                    this.categoryList[index].category_3List = []
                    this.categoryList[index].category_3_id = ""
                    break;
                case 3:
                    this.categoryList[index].category_3_id = ""
                    this.categoryList[index].category_3List = []
                    break;
            }
            let res = {
                data: {
                    list: []
                }
            }
            if (pid !== "") {
                res = await getCategoryListWithParentId({parent_id: pid})
            }
            let list = res.data.list || []
            if (level === 1) {
                this.category_1List = list
            } else {
                this.categoryList[index][`category_${level}List`] = list
            }
        },
        // 添加分类
        pushCategory() {
            this.categoryList.push({
                category_1_id: "",
                category_2_id: "",
                category_3_id: "",
                category_2List: [],
                category_3List: [],
            });
        },
        // 删除分类
        @confirm("提示", "确定删除?")
        delCategory(index) {
            this.categoryList.splice(index, 1)
        },
        handleSelectionChange(val) {
            this.batchRemoveProducts = val;
        },

        //新建处理
        addAlbumPara() {
            this.isShow = true;
            this.title = "新建";
            this.getCategory();
            this.initAlbumInfo();
        },

        //编辑处理
        editAlbumPara(id) {
            this.isShow = true;
            this.title = "编辑";
            this.getAlbumInfoById(id);
        },

        // 获取选中的商品数据
        getDialogGiidsList(data) {
            this.formData.products = this.formData.products
                ? this.formData.products.concat(data)
                : data;
            this.formData.products = this.unique(this.formData.products);
        },

        // 商品去重
        unique(arr) {
            const res = new Map();
            return arr.filter((arr) => !res.has(arr.id) && res.set(arr.id, 1));
        },

        // 提交
        confirm() {
            if (!this.formData.title) {
                this.$message.error("请输入专辑名称");
                return;
            }
            if (!this.formData.desc) {
                this.$message.error("请输入专辑简介");
                return;
            }
            if (!this.formData.cover) {
                this.$message.error("请上传专辑封面");
                return;
            }
            // 处理分类
            if (this.formData.type === 2) {
                this.formData.filter.category_1_id = []
                this.formData.filter.category_2_id = []
                this.formData.filter.category_3_id = []
                for (let i = 0; i < this.categoryList.length; i++) {
                    if (this.categoryList[i].category_1_id==="" ) {
                        this.$message.error("请选择一级分类")
                        return;
                    }else if(this.categoryList[i].category_2_id===""){
                        this.$message.error("请选择二级分类")
                        return;
                    }else if(this.categoryList[i].category_3_id==="" ){
                        this.$message.error("请选择三级分类")
                        return;
                    }
                    if (this.categoryList[i].category_1_id) {
                        this.formData.filter.category_1_id.push(this.categoryList[i].category_1_id)
                    }
                    if (this.categoryList[i].category_2_id) {
                        this.formData.filter.category_2_id.push(this.categoryList[i].category_2_id)
                    }
                    if (this.categoryList[i].category_3_id) {
                        this.formData.filter.category_3_id.push(this.categoryList[i].category_3_id)
                    }
                }
            }

            if (this.formData.id == 0) {
                this.createAlbum();
            } else {
                this.editAlbum(true);
            }
        },

        //创建专辑
        createAlbum() {
            createCollection(this.formData).then((res) => {
                if (res.code == 0) {
                    this.isShow = false;
                    this.$message.success(res.msg);
                    this.$emit("callBackList");
                } else {
                    this.$message.error(res.msg);
                }
            });
        },

        //编辑专辑
        editAlbum(isCallBack) {
            if (this.formData.id == 0) {
                return;
            }
            updateCollection(this.formData).then((res) => {
                if (res.code == 0) {
                    this.$message.success(res.msg);
                    if (isCallBack) {
                        this.isShow = false;
                        this.$emit("callBackList");
                    }
                } else {
                    this.$message.error(res.msg);
                }
            });
        },

        // 打开商品dialog
        openGoodsDialog() {
            this.$refs.goodsDialog.isShow = true;
            this.$refs.goodsDialog.appendToBody = true;
            this.$refs.goodsDialog.fetch();
        },

        handleClose() {
            this.isShow = false;
            this.page = 1;
            this.formData.products = [];
            this.categoryList = [
                {
                    category_1_id: "",
                    category_2_id: "",
                    category_3_id: "",
                    category_2List: [],
                    category_3List: [],
                },
            ]
            this.$refs.form.resetFields();
        },

        // 上传图片
        handleMainImgSuccess(res) {
            this.uploadLoading = false;
            this.formData.cover = res.data.file.url;
        },

        handleCoverError(err, file, fileList) {
            this.uploadLoading = false;
        },

        beforeAvatarUpload(file) {
            this.uploadLoading = true;
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },

        //获取专辑详情
        getAlbumInfoById(id) {
            this.formData.id = id;
            let para = {
                id: id,
            };
            findCollectionById(para).then((res) => {
                if (res.code == 0) {
                    this.initAlbumInfo(res.data.recollection);
                }
            });
        },
        // 分类回显
        async categoryEcho(filter) {
            let res = await categoryTree()
            const tree = res.data.categories
            this.category_1List = this.$fn.deepClone(tree)
            this.categoryList = []
            filter.category_1_id.forEach((id, index) => {
                let obj = this.category_1List.find(item => item.id === id)
                this.categoryList.push({
                    category_1_id: id,
                    category_2_id: "",
                    category_3_id: "",
                    category_2List: obj.children,
                    category_3List: [],
                })
            })
            // 处理二级选中id
            filter.category_2_id.forEach((id,_index) => {
                findCategory({id}).then(res => {
                    // let _index = this.categoryList.indexOfJSON("category_1_id", res.data.recategory.parent_id)
                    this.categoryList.forEach
                    this.categoryList[_index].category_2_id = id
                    // 处理三级list
                    let obj = this.categoryList[_index].category_2List.find(item => item.id === id)
                    this.categoryList[_index].category_3List = obj.children

                    // 处理三级选中id
                    filter.category_3_id.forEach((id3,_index3) => {
                        findCategory({id: id3}).then(res3 => {
                            // let _index = this.categoryList.indexOfJSON("category_2_id", res3.data.recategory.parent_id)
                            this.categoryList[_index3].category_3_id = id3
                        })
                    })
                })
            })
        },
        //初始化数据
        initAlbumInfo(data) {
            if (data) {
                if (data.type === 2) {
                    this.categoryEcho(data.filter)
                }else{
                    this.getCategory()
                }
                data.type = data.type ? data.type : 1
                this.formData = {...data};
            } else {
                this.categoryList = [
                    {
                        category_1_id: "",
                        category_2_id: "",
                        category_3_id: "",
                        category_2List: [],
                        category_3List: [],
                    },
                ]
                this.category_1List = []
                this.formData = {
                    id: 0,
                    products: [],
                    title: "",
                    desc: "",
                    cover: "",
                    type: 1,
                    filter: {
                        category_1_id: [],
                        category_2_id: [],
                        category_3_id: [],
                        category_num: 0, // 分类  商品显示数量
                        attribute_type: 1, // 营销属性 1热卖 3新品 4促销
                        sale_num: 0, // 营销属性  商品显示数量
                        statistic_type: 1, // 商品数据 1销量 2时间
                        statistic_num: 0, // 商品数据 商品显示数量
                        statistic_time: 1, // 数据周期
                    },
                };
            }
        },

        //移除item
        removeItem(index) {
            let products = this.formData.products;
            products.splice(index, 1);
            this.formData.products = products;
            this.editAlbum(false);
        },

        //批量移除item
        batchRemoveItem() {
            if (this.batchRemoveProducts.length == 0) {
                this.$message.error("请选择要移除的商品");
                return;
            }
            let products = this.formData.products;
            this.batchRemoveProducts.forEach((element) => {
                for (let i = 0; i < products.length; i++) {
                    if (products[0].id == element.id) {
                        products.splice(i, 1);
                    }
                }
            });
            this.formData.products = products;
            this.editAlbum(false);
        },

        //上移item
        up(index) {
            if (index == 0) {
                return;
            }
            this.swapItems(this.formData.products, index, index - 1);
        },

        //下移item
        down(index) {
            if (index == this.formData.products.length - 1) {
                return;
            }
            this.swapItems(this.formData.products, index, index + 1);
        },

        //上移下移
        swapItems(arr, index1, index2) {
            arr[index1] = arr.splice(index2, 1, arr[index1])[0];
            return arr;
        },
    },
};
