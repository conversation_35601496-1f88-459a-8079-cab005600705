<template>
    <m-card>
        <el-form :model="settings" label-width="90px">
            <el-tabs v-model="activeTab" type="card">
                <!-- 基础设置 -->
                <el-tab-pane label="基础设置" name="basic">
                    <m-card>
                        <el-form-item label="开启打水机">
                            <el-radio-group
                                v-model="settings.is_enabled"
                                @change="handleBasicChange"
                            >
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </m-card>
                </el-tab-pane>

                <!-- 通知设置 -->
                <el-tab-pane label="通知设置" name="notification">
                    <m-card>
                        <el-form-item label="报修通知">
                            <el-select
                                v-model="settings.notify_type"
                                placeholder="请选择通知方式"
                                @change="handleNotificationChange"
                            >
                                <el-option label="短信" :value="1"></el-option>
                                <el-option
                                    label="公众号"
                                    :value="2"
                                ></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="报修短信通知">
                            <el-input
                                v-model="settings.template_id"
                                placeholder="请输入模板ID"
                                @blur="handleNotificationChange"
                            ></el-input>
                            <div class="template-hint">
                                阿里云模板内容：${name}您好！您关联机器申请报修，机器id：XXXX，机器名称：XXXXX，商城：XXXXXX，报修时间：XXXXXXX，请尽快处理！
                            </div>
                        </el-form-item>
                    </m-card>
                </el-tab-pane>
            </el-tabs>

            <!-- 保存按钮 -->
            <div class="save-actions">
                <el-button
                    type="primary"
                    @click="saveSettings"
                    :loading="saving"
                    >保存设置</el-button
                >
            </div>
        </el-form>
    </m-card>
</template>

<script>
import {
    getWaterMachineSettings,
    updateWaterMachineSettings,
} from '@/api/waterMachine';

export default {
    name: 'waterMachineBase',
    data() {
        return {
            activeTab: 'basic',
            saving: false,
            settings: {
                is_enabled: 0,
                notify_type: 1,
                template_id: '',
            },
        };
    },
    mounted() {
        this.loadSettings();
    },
    methods: {
        // 加载设置
        async loadSettings() {
            try {
                const res = await getWaterMachineSettings();
                if (res.code === 0) {
                    this.settings = { ...res.data.setting };
                } else {
                    this.$message.error(res.msg || '获取设置失败');
                }
            } catch (error) {
                console.error('获取设置失败:', error);
                this.$message.error('获取设置失败');
            }
        },

        // 基础设置变化处理
        handleBasicChange() {
            // 可以在这里添加实时保存逻辑
            console.log('基础设置变化:', this.settings.is_enabled);
        },

        // 通知设置变化处理
        handleNotificationChange() {
            // 可以在这里添加实时保存逻辑
            console.log('通知设置变化:', {
                notify_type: this.settings.notify_type,
                template_id: this.settings.template_id,
            });
        },

        // 保存设置
        async saveSettings() {
            this.saving = true;
            try {
                const res = await updateWaterMachineSettings(this.settings);
                if (res.code === 0) {
                    this.$message.success('保存设置成功');
                } else {
                    this.$message.error(res.msg || '保存设置失败');
                }
            } catch (error) {
                console.error('保存设置失败:', error);
                this.$message.error('保存设置失败');
            } finally {
                this.saving = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>
