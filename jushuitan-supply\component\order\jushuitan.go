package order

import (
	afterSalesModel "after-sales/model"
	"encoding/json"
	"errors"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	model3 "jushuitan-supply/model"
	"jushuitan/common"
	"product/model"
	callback2 "public-supply/callback"
	"public-supply/request"
	"public-supply/response"
	"public-supply/setting"
	"strconv"
	"strings"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
)

type Jushuitan struct {
}

func (hehe *Jushuitan) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (hehe *Jushuitan) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (hehe *Jushuitan) GetAllAddress() (err error, data interface{}) {
	return
}
func (y *Jushuitan) InitSetting(gatherSupplyID uint) (err error) {

	var sysSetting model2.SysSetting
	err, sysSetting = setting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(sysSetting.Value), &JushuitanData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if JushuitanData.BaseInfo.AppKey == "" || JushuitanData.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	return
}
func (s *Jushuitan) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (s *Jushuitan) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	return
}

type BaseInfoData struct {
	AppKey                string `json:"appKey"`
	AppSecret             string `json:"appSecret"`
	AccessToken           string `json:"access_token"`
	AccessTokenExpireTime int    `json:"access_token_expire_time"`
	RefreshToken          string `json:"refresh_token"`
	Weeks                 int    `json:"weeks"`
}

type JushuitanSupplySetting struct {
	BaseInfo   BaseInfoData           `json:"baseInfo"`
	UpdateInfo setting.UpdateInfoData `json:"update"`
	Pricing    setting.PricingData    `json:"pricing"`
	Management setting.Management     `json:"management"`
}

var JushuitanData *JushuitanSupplySetting

var GatherSupplyID uint

func (jushuitan *Jushuitan) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	var localSkuIds []uint
	for _, localSku := range request.LocalSkus {
		localSkuIds = append(localSkuIds, uint(localSku.Sku.Sku))
	}
	var skus []model.Sku
	err = source.DB().Preload("Product").Where("id in ?", localSkuIds).Find(&skus).Error
	if err != nil {
		data.Msg = "获取商品信息失败"
		return
	}
	var SkuSns []string
	var checkSkuStockMap = make(map[string]int)
	var checkSkuTitleMap = make(map[string]string)
	var jushuitanCoId string
	for _, sku := range skus {
		SkuSns = append(SkuSns, sku.Sn)
		checkSkuTitleMap[sku.Sn] = sku.Product.Title
		jushuitanCoId = sku.Product.JushuitanDistributorCoId
		for _, localSku := range request.LocalSkus {
			if uint(localSku.Sku.Sku) == sku.ID {
				checkSkuStockMap[sku.Sn] = localSku.Number
			}
		}
	}
	var shop model3.JushuitanShop
	err = source.DB().Where("shop_id = ?", jushuitanCoId).First(&shop).Error
	if err != nil {
		err = errors.New("上游店铺不存在")
		data.Msg = "上游店铺不存在"
		return
	}
	if shop.IsVirtualStock != 1 {
		//虚拟库存店铺不校验库存
		var requestParams = make(map[string]interface{})
		requestParams["page_num"] = 1
		requestParams["page_size"] = 100
		requestParams["supplier_co_id"] = jushuitanCoId
		requestParams["item_codes"] = strings.Join(SkuSns, ",")
		var requestJson []byte
		requestJson, err = json.Marshal(requestParams)
		if err != nil {
			data.Msg = "请求参数转换失败"
			return
		}
		var result []byte
		err, result = common.RequestJushuitan(requestJson, "https://openapi.jushuitan.com/open/api/goods/inneropen/supplier/goods/querydiserpgoodsdata")
		if err != nil {
			data.Msg = "请求接口失败"
			return
		}

		var responseData JushuitanStockResponse
		err = json.Unmarshal(result, &responseData)
		if err != nil {
			data.Msg = "数据解析失败"
			return
		}
		if responseData.Code != 0 {
			log.Log().Error("聚水潭请求库存出错", zap.Any("data", responseData))
			data.Msg = responseData.Msg
			return
		}
		var jstStockData = make(map[string]int)
		for _, jstSd := range responseData.Data.List {
			jstStockData[jstSd.ItemCode] = jstSd.Stock
		}
		for skuId, stock := range checkSkuStockMap {
			if _, ok := jstStockData[skuId]; ok {
				if jstStockData[skuId] < stock {
					err = errors.New("商品'" + checkSkuTitleMap[skuId] + "'库存不足")
					data.Msg = "商品'" + checkSkuTitleMap[skuId] + "'库存不足"
					return
				}
			} else {
				err = errors.New("商品'" + checkSkuTitleMap[skuId] + "'库存不足")
				data.Msg = "商品'" + checkSkuTitleMap[skuId] + "'库存不足"
				return
			}
		}
	}

	data.Freight = 0
	data.Code = 1
	return
}

type JushuitanStockResponse struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data struct {
		Total int `json:"total"`
		List  []struct {
			ItemCode        string  `json:"item_code"`
			Created         string  `json:"created"`
			StyleCode       string  `json:"style_code"`
			Weight          float64 `json:"weight"`
			ItemName        string  `json:"item_name"`
			BrandName       string  `json:"brand_name"`
			Pic             string  `json:"pic"`
			SupplyPrice     float64 `json:"supply_price"`
			SalePrice       float64 `json:"sale_price"`
			StockStr        string  `json:"stock_str"`
			PropertiesValue string  `json:"properties_value"`
			BasePrice       float64 `json:"base_price"`
			Stock           int     `json:"stock"`
		} `json:"list"`
	} `json:"data"`
	RequestId string `json:"request_id"`
}

func (jushuitan *Jushuitan) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	for _, v := range request.Skus {
		resData.Data.Available = append(resData.Data.Available, uint(v.Sku.Sku))
	}
	return
}

type ConfirmOrderRequest struct {
	GoodsId        int    `json:"goodsId"`
	GoodsNum       int    `json:"goodsNum"`
	GoodsSkuId     string `json:"goodsSkuId"`
	CusOrderNo     string `json:"cus_order_no"`
	IDCard         string `json:"IDCard"`
	UserName       string `json:"user_name"`
	Mode           string `json:"mode"`
	BuyerRemark    string `json:"buyer_remark"`
	Name           string `json:"name"`
	Phone          int64  `json:"phone"`
	ProvinceId     string `json:"province_id"`
	CityId         string `json:"city_id"`
	RegionId       string `json:"region_id"`
	Detail         string `json:"detail"`
	IdCardFrontImg string `json:"id_card_front_img"`
	IdCardBackImg  string `json:"id_card_back_img"`
}

func (jushuitan *Jushuitan) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	return
}

func (jushuitan *Jushuitan) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	return
}

func (jushuitan *Jushuitan) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (jushuitan *Jushuitan) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (jushuitan *Jushuitan) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (jushuitan *Jushuitan) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}
func (jushuitan *Jushuitan) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}
