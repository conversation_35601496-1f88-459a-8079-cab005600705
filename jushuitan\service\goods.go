package service

import (
	"encoding/json"
	"go.uber.org/zap"
	"jushuitan/common"
	"jushuitan/model"
	response2 "jushuitan/response"
	model2 "product/model"
	service2 "product/service"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

func GetJushuitanGoodsModel(productModel service2.ProductSync, sku model2.Sku) (item model.Item) {

	skuSn := strconv.Itoa(int(sku.ID))
	item.IId = skuSn
	item.SkuId = skuSn
	item.SPrice = utils.Decimal(float64(sku.Price) / float64(100))
	item.CPrice = utils.Decimal(float64(sku.CostPrice) / float64(100))
	item.MarketPrice = utils.Decimal(float64(sku.OriginPrice) / float64(100))
	item.Pic = productModel.ImageUrl
	item.SkuPic = sku.ImageUrl
	item.Name = productModel.Title
	//item.PropertiesValue = sku.Title
	item.Weight = float64(sku.Weight)
	item.Enabled = productModel.IsDisplay
	item.SkuCode = sku.Sn
	item.OtherPrice1 = utils.Decimal(float64(sku.GuidePrice) / float64(100))
	item.StockDisabled = true
	item.ShelfLife = 9999
	item.Unit = productModel.Unit
	return
}
func SyncGoods() (err error) {

	go PutJushuitanGoods()

	return
}
func PutJushuitanGoods() (err error) {
	var products []service2.ProductSync
	err = source.DB().Preload("Skus").Where("supplier_id = 0 and gather_supply_id = 0").Find(&products).Error
	if err != nil {
		return
	}
	var uploadProductData model.UploadProductRequest
	url := "https://openapi.jushuitan.com/open/jushuitan/itemsku/upload"
	var i int
	var jsonData []byte
	var skuIds []int
	for _, product := range products {
		for _, sku := range product.Skus {
			i++
			uploadProductData.Items = append(uploadProductData.Items, GetJushuitanGoodsModel(product, sku))
			if i%50 == 0 {
				jsonData, err = json.Marshal(uploadProductData)
				if err != nil {
					return
				}
				var responseByte []byte
				err, responseByte = common.RequestJushuitan(jsonData, url)
				if err != nil {
					return
				}
				var response response2.UploadGoodsResponse
				err = json.Unmarshal(responseByte, &response)
				if err != nil {
					return
				}
				if response.Code == 0 {
					for _, v := range response.Data.Datas {
						if v.IsSuccess == true {
							var skuId int
							skuId, err = strconv.Atoi(v.SkuId)
							skuIds = append(skuIds, skuId)

						}
					}
				} else {
					log.Log().Error("聚水潭批量推送商品错误", zap.Any("err", response))
				}

				uploadProductData.Items = []model.Item{}
			}
		}

	}
	jsonData, err = json.Marshal(uploadProductData)
	if err != nil {
		return
	}
	var responseByte []byte
	err, responseByte = common.RequestJushuitan(jsonData, url)
	if err != nil {
		return
	}
	var response response2.UploadGoodsResponse
	err = json.Unmarshal(responseByte, &response)
	if err != nil {
		return
	}
	if response.Code == 0 {
		for _, v := range response.Data.Datas {
			if v.IsSuccess == true {
				var skuId int
				skuId, err = strconv.Atoi(v.SkuId)
				skuIds = append(skuIds, skuId)
			}
		}
	} else {
		log.Log().Error("聚水潭批量推送商品错误", zap.Any("err", response))
	}
	var productIds []int

	// 分批查询，每次500个ID
	batchSize := 500
	for i := 0; i < len(skuIds); i += batchSize {
		end := i + batchSize
		if end > len(skuIds) {
			end = len(skuIds)
		}

		batch := skuIds[i:end]
		var batchProductIds []int
		err = source.DB().Model(&model2.Sku{}).Where("id in ?", batch).Pluck("product_id", &batchProductIds).Error
		if err != nil {
			log.Log().Error("批量查询SKU对应的product_id失败",
				zap.Any("batch", batch),
				zap.Error(err))
			return
		}
		productIds = append(productIds, batchProductIds...)
	}

	if len(skuIds) > 0 {
		err = UpdateGoodsJushuitanBind(skuIds, "skus")
		err = UpdateGoodsJushuitanBind(productIds, "products")
	}
	return
}
func contains(slice []int, item int) bool {
	set := make(map[int]struct{}, len(slice))
	for _, s := range slice {
		set[s] = struct{}{}
	}

	_, ok := set[item]
	return ok
}
func UpdateGoodsJushuitanBind(skuIds []int, tableName string) (err error) {

	var skuUpdate []map[string]interface{}
	for _, v := range skuIds {
		skuUpdateRow := make(map[string]interface{})
		skuUpdateRow["id"] = v
		skuUpdateRow["jushuitan_bind"] = 1
		skuUpdate = append(skuUpdate, skuUpdateRow)

	}
	err = source.BatchUpdate(skuUpdate, tableName, "")

	return
}
