package service

import (
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"jushuitan/common"
	"jushuitan/model"
	response2 "jushuitan/response"
	"jushuitan/setting"
	model2 "order/model"
	model4 "payment/model"
	"payment/service"
	"strconv"
	"time"
	model3 "user/model"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type Order struct {
	model2.Order
	User    model3.User    `json:"user"`
	PayInfo model4.PayInfo `json:"pay_info" gorm:"foreignKey:PayInfoID"`
}

func GetJushuitanOrderParams(order Order) (err error, orderInfo model.UploadOrderParams) {
	err, jstSetting := setting.GetJstSetting()
	if err != nil {
		return
	}
	if jstSetting.ShopId == 0 || jstSetting.AppKey == "" || jstSetting.AppSecret == "" {
		err = errors.New("聚水潭信息不全，提交失败")
		return
	}
	orderInfo.ShopId = jstSetting.ShopId
	orderInfo.SoId = strconv.Itoa(int(order.OrderSN))
	orderInfo.OrderDate = order.CreatedAt.Format("2006-01-02 15:04:05")
	if order.Status == model2.WaitSend {
		orderInfo.ShopStatus = "WAIT_SELLER_SEND_GOODS"
	}
	if order.Status == model2.Closed {
		if order.PaidAt != nil {
			orderInfo.ShopStatus = "TRADE_CLOSED"
		} else {
			orderInfo.ShopStatus = "TRADE_CLOSED_BY_TAOBAO"
		}
	}
	orderInfo.ShopBuyerId = order.ShippingAddress.Realname
	orderInfo.ReceiverState = order.ShippingAddress.Province
	orderInfo.ReceiverCity = order.ShippingAddress.City
	orderInfo.ReceiverDistrict = order.ShippingAddress.County
	orderInfo.ReceiverAddress = order.ShippingAddress.Detail
	orderInfo.ReceiverName = order.ShippingAddress.Realname
	orderInfo.ReceiverPhone = order.ShippingAddress.Mobile
	orderInfo.PayAmount = utils.Decimal(float64(order.Amount+order.RefundAmount) / 100)
	orderInfo.Freight = utils.Decimal(float64(order.Freight) / 100)
	orderInfo.Remark = order.Note
	orderInfo.BuyerMessage = order.Remark
	orderInfo.ShopModified = time.Now().Format("2006-01-02 15:04:05")

	for _, v := range order.OrderItems {
		var orderItem model.JushuitanOrderItem
		var orderItemRefundNum int
		source.DB().Model(&model2.AfterSales{}).Where("status = ?", model2.CompletedStatus).Where("order_item_id = ?", v.ID).Select("num").Pluck("num", &orderItemRefundNum)
		orderItem.SkuId = strconv.Itoa(int(v.Sku.ID))
		orderItem.ShopSkuId = strconv.Itoa(int(v.Sku.ID))
		orderItem.Amount = utils.Decimal(float64(v.Amount) / 100)
		orderItem.BasePrice = utils.Decimal(float64(v.Amount) / 100)
		orderItem.Qty = int(v.Qty) + orderItemRefundNum
		orderItem.OuterOiId = strconv.Itoa(int(v.ID))
		orderItem.Name = v.Title
		if v.Sku.Product != nil {
			orderInfo.ShopName = v.Sku.Product.JushuitanDistributorSupplierName
			//if orderInfo.ShopName == "杭州水滴供应链管理有限公司" {
			//	err = errors.New("此供应商暂停发货")
			//	return
			//}
		}
		orderInfo.Items = append(orderInfo.Items, orderItem)
	}
	orderInfo.Pay.OuterPayId = strconv.Itoa(int(order.PayInfo.PaySN))
	orderInfo.Pay.Amount = utils.Decimal(float64(order.Amount) / 100)
	if order.PaidAt != nil {
		orderInfo.Pay.PayDate = order.PaidAt.Format("2006-01-02 15:04:05")
	}
	_, payType := service.GetPayType()
	for _, v := range payType {
		if order.PayInfo.PayTypeID == v.Code {
			orderInfo.Pay.Payment = v.Name
		}
	}
	if order.PayInfo.PayTypeID == -1 {
		orderInfo.Pay.Payment = "后台支付"
	}

	if orderInfo.Pay.Payment == "" {
		orderInfo.Pay.Payment = "未知"
	}
	orderInfo.Pay.BuyerAccount = order.User.UUID
	orderInfo.Pay.SellerAccount = order.User.UUID

	return
}
func SyncOrder() (err error) {

	go PutJushuitanOrder()

	return
}
func PutJushuitanOrder() (err error) {
	var orders []Order
	err = source.DB().Preload("User").Preload("PayInfo").Preload("OrderItems.Sku").Preload("ShippingAddress").Where("supplier_id = 0 and gather_supply_id = 0 and status = 1").Find(&orders).Error
	if err != nil {
		return
	}
	var uploadOrderData []model.UploadOrderParams
	url := "https://openapi.jushuitan.com/open/jushuitan/orders/upload"
	var i int
	var jsonData []byte
	var orderSns []int

	for _, orderM := range orders {
		var item model.UploadOrderParams
		err, item = GetJushuitanOrderParams(orderM)
		if err != nil {
			log.Log().Error("聚水潭主动推送订单错误", zap.Any("err", err), zap.Any("data", orderM))
			continue
		}
		uploadOrderData = append(uploadOrderData, item)
		i++
		if i%50 == 0 {
			jsonData, err = json.Marshal(uploadOrderData)
			if err != nil {
				return
			}
			var responseByte []byte
			err, responseByte = common.RequestJushuitan(jsonData, url)
			if err != nil {
				return
			}
			var response response2.UploadOrderResponse
			err = json.Unmarshal(responseByte, &response)
			if err != nil {
				return
			}
			if response.Code == 0 {
				for _, v := range response.Data.Datas {
					if v.Issuccess == true {
						var orderSn int
						orderSn, err = strconv.Atoi(v.SoId)
						orderSns = append(orderSns, orderSn)
					}
				}
			} else {
				log.Log().Error("聚水潭批量推送订单错误", zap.Any("err", response))
			}

			uploadOrderData = []model.UploadOrderParams{}
		}

	}
	jsonData, err = json.Marshal(uploadOrderData)
	if err != nil {
		return
	}
	var responseByte []byte
	err, responseByte = common.RequestJushuitan(jsonData, url)
	if err != nil {
		return
	}
	var response response2.UploadOrderResponse
	err = json.Unmarshal(responseByte, &response)
	if err != nil {
		return
	}
	if response.Code == 0 {
		for _, v := range response.Data.Datas {
			if v.Issuccess == true {
				var orderSn int
				orderSn, err = strconv.Atoi(v.SoId)
				orderSns = append(orderSns, orderSn)
			}
		}
	} else {
		log.Log().Error("聚水潭批量推送订单错误", zap.Any("err", response))
	}
	if len(orderSns) > 0 {
		err = UpdateOrderJushuitanBind(orderSns)
	}
	return
}

func UpdateOrderJushuitanBind(orderSn []int) (err error) {

	var orderUpdate []map[string]interface{}
	for _, v := range orderSn {
		orderUpdateRow := make(map[string]interface{})
		orderUpdateRow["order_sn"] = v
		orderUpdateRow["jushuitan_bind"] = 1
		orderUpdate = append(orderUpdate, orderUpdateRow)

	}
	err = source.BatchUpdate(orderUpdate, "orders", "order_sn")

	return
}
