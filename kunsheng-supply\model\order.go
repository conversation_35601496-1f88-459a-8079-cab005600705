package model

type AfterSales struct {
	ErrorInfo string `json:"error_info"`
	ErrorCode string `json:"error_code"`
}

type ApplyCancel struct {
	ErrorInfo string `json:"error_info"`
	Data      []struct {
		Msg     string `json:"msg"`
		OrderSn string `json:"order_sn"`
		Status  int    `json:"status"`
	} `json:"data"`
	ErrorCode string `json:"error_code"`
}

type OrderRequest struct {
	Area                  string `json:"area"`
	City                  string `json:"city"`
	Logistics             string `json:"logistics"`
	ReceiverName          string `json:"receiver_name"`
	ThirdPartyOrderNumber string `json:"third_party_order_number"`
	ReceiverPhone         string `json:"receiver_phone"`
	ReceiverAddress       string `json:"receiver_address"`
	Goods                 []struct {
		ItemNumber string `json:"item_number"`
		Size       string `json:"size"`
		Quantity   int    `json:"quantity"`
	} `json:"goods"`
}

type OrderResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		OrderNumber           string `json:"order_number"`
		ThirdPartyOrderNumber string `json:"third_party_order_number"`
	} `json:"data"`
}
