package order

import (
	"after-sales/service"
	"errors"
	maiGerPkgAfter "maiger-supply/maiger-pkg/after"
	productModel "product/model"
	publicSupplyCommon "public-supply/common"
	"yz-go/source"
)

func init() {
	service.SetBeforeHandle(func(sales service.OperationAfterSales) (err error) {
		if sales.Order.GatherSupplyType == publicSupplyCommon.SUPPLY_MAIGER {
			if sales.RefundWay == 0 && sales.RefundType != 2 {
				err = errors.New("暂不支持自定义金额退款")
				return
			}

			// 初始化供应链
			m := MaiGerSupply{}

			if err = m.InitSetting(sales.GatherSupplyID); err != nil {
				err = errors.New("售后申请失败，供应链初始化失败")
				return
			}

			// 中台售后类型 -> 迈戈售后类型:售后类型(0-退货/退款; 2-换货； 4-补寄； 6-补偿费用; 7-退款)
			typeMap := map[int]int{
				0: 7,
				1: 0,
				2: 2,
			}

			maiGerRefundType, ok := typeMap[int(sales.RefundType)]
			if !ok {
				err = errors.New("暂不支持该售后方式")
				return
			}

			// 查询产品SKU
			var skuModel productModel.Sku
			if err = source.DB().First(&skuModel, sales.SkuID).Error; err != nil {
				err = errors.New("售后申请失败，商品SKU查询失败")
				return
			}

			// 验证售后方式
			applyParams := maiGerPkgAfter.ApplyParams{
				AccessToken: m.AccessToken,
				Domain:      m.Host,
				OrderSn:     sales.Order.GatherSupplySN,
				SkuId:       skuModel.SourceStrId,
			}

			var applyResult []maiGerPkgAfter.ApplyResult
			if applyResult, err = maiGerPkgAfter.Apply(applyParams); err != nil {
				err = errors.New("售后申请失败，查询售后类型失败" + err.Error())
				return
			}

			for _, item := range applyResult {
				// 如果售后类型存在，支持该售后类型，返回
				if maiGerRefundType == item.ReturnType {
					return
				}
			}

			err = errors.New("暂不支持该售后方式")
		}
		return
	})
}
