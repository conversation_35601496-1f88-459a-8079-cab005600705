package cron

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"time"
	"yz-go/common_data"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"product/model"
	"product/service"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// ProductSyncHandle 商品增量同步定时任务
func ProductSyncHandle() {
	task := cron.Task{
		Key:  "productSync",
		Name: "商品增量同步",
		Spec: "0 */1 * * * *", // 每1分钟执行一次
		Handle: func(task cron.Task) {
			ProductIncrementalSyncCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

// ProductIncrementalSyncCron 商品增量同步主函数
func ProductIncrementalSyncCron() {
	startTime := time.Now()
	log.Log().Info("开始执行商品增量同步任务")

	var lastSyncTime *source.LocalTime
	// 获取上次同步时间
	err, list := service.GetProductListFromEs(elastic.BoolQuery{}, 0, "updated_at", 1)
	if err != nil {
		esError, ok := err.(*elastic.Error)
		if ok {
			if esError.Status == 404 {
				// es中没有数据时，初始化所有订单数据
				log.Log().Info("es中没有商品数据，初始化所有订单数据")
			}
		}
	} else {
		// es中有数据时，从数据库中同步最新的数据
		for _, search := range list {
			lastSyncTime = search.UpdatedAt
			log.Log().Info("es中最新的商品数据", zap.Any("updatedAt", lastSyncTime))
		}
	}

	if lastSyncTime != nil {
		// 1. 处理已删除的商品
		err = syncDeletedProducts(lastSyncTime)
		if err != nil {
			log.Log().Error("同步已删除商品失败", zap.Error(err))
			// 继续执行，不中断整个同步过程
		}
	}
	if lastSyncTime == nil {
		lastSyncTime = &source.LocalTime{Time: time.Unix(0, 0)}
	}
	// 2. 查询需要同步的商品数量
	var updateCount int64
	err = source.DB().Model(&model.Product{}).
		Where("updated_at > ?", lastSyncTime).
		Where("deleted_at IS NULL").
		Count(&updateCount).Error
	if err != nil {
		log.Log().Error("查询更新商品数量失败", zap.Error(err))
		return
	}

	if updateCount == 0 {
		log.Log().Info("没有需要同步的商品")
		return
	}

	log.Log().Info("发现需要同步的商品", zap.Int64("数量", updateCount))

	// 3. 执行增量同步
	err = executeIncrementalSync(lastSyncTime)
	if err != nil {
		log.Log().Error("增量同步执行失败", zap.Error(err))
		return
	}

	duration := time.Since(startTime)
	log.Log().Info("商品增量同步任务完成",
		zap.Int64("同步商品数量", updateCount),
		zap.Duration("耗时", duration))
}

// executeIncrementalSync 执行增量同步
func executeIncrementalSync(lastSyncTime *source.LocalTime) error {
	// 分批处理商品，避免一次性处理过多数据
	const batchSize = 100
	offset := 0

	for {
		var products []service.ProductSync
		err := source.DB().
			Preload("Storages").
			Preload("Skus").
			Preload("Category1").
			Preload("Category2").
			Preload("Category3").
			Preload("Brand").
			Preload("Supplier").
			Preload("GatherSupply").
			Preload("SmallShopProductSale").
			Preload("AlbumRelations").
			Preload("CollectionRelations").
			Preload("OrderItems", func(db *gorm.DB) *gorm.DB {
				return db.Joins("JOIN orders ON orders.id = order_items.order_id").Where("orders.status >= 1")
			}).
			Preload("OrderItems.Order").
			Omit("detail_images").
			Where("updated_at > ?", lastSyncTime).
			Where("deleted_at IS NULL").
			Offset(offset).
			Limit(batchSize).
			Order("updated_at ASC").
			Find(&products).Error

		if err != nil {
			return fmt.Errorf("查询商品失败: %v", err)
		}

		if len(products) == 0 {
			break // 没有更多商品了
		}

		// 处理当前批次的商品
		err = processBatchProducts(products)
		if err != nil {
			log.Log().Error("处理商品批次失败",
				zap.Error(err),
				zap.Int("批次大小", len(products)),
				zap.Int("偏移量", offset))
			// 继续处理下一批，不中断整个同步过程
		} else {
			log.Log().Info("处理商品批次成功",
				zap.Int("批次大小", len(products)),
				zap.Int("偏移量", offset))
		}

		offset += batchSize

		// 如果返回的商品数少于批次大小，说明已经是最后一批
		if len(products) < batchSize {
			break
		}

		// 添加短暂延迟，避免对数据库造成过大压力
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// syncDeletedProducts 同步已删除的商品（从ES中移除）
func syncDeletedProducts(lastSyncTime *source.LocalTime) error {
	// 查询在指定时间后被软删除的商品
	var deletedProducts []model.Product
	err := source.DB().Unscoped().
		Where("deleted_at IS NOT NULL and deleted_at > ?", lastSyncTime).
		Find(&deletedProducts).Error

	if err != nil {
		return fmt.Errorf("查询已删除商品失败: %v", err)
	}

	if len(deletedProducts) == 0 {
		return nil
	}

	log.Log().Info("发现需要删除的商品", zap.Int("数量", len(deletedProducts)))

	// 获取ES客户端
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("获取ES客户端失败: %v", err)
	}

	// 确定ES索引名称
	indexName := "product" + common_data.GetOldProductIndex()

	// 构建批量删除请求
	bulkRequest := es.Bulk()

	for _, product := range deletedProducts {
		deleteReq := elastic.NewBulkDeleteRequest().
			Index(indexName).
			Id(fmt.Sprintf("%d", product.ID))

		bulkRequest = bulkRequest.Add(deleteReq)
	}

	// 执行批量删除
	ctx := context.Background()
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		return fmt.Errorf("批量删除ES文档失败: %v", err)
	}

	// 检查删除结果
	if bulkResponse.Errors {
		var failedItems []string
		for _, item := range bulkResponse.Items {
			for action, result := range item {
				if result.Error != nil && result.Error.Type != "not_found" {
					// 忽略not_found错误，因为文档可能已经不存在
					failedItems = append(failedItems, fmt.Sprintf("%s:%s - %s", action, result.Id, result.Error.Reason))
				}
			}
		}

		if len(failedItems) > 0 {
			log.Log().Error("部分商品删除失败",
				zap.Strings("失败项目", failedItems))
		}
	}

	log.Log().Info("批量删除ES中的商品成功",
		zap.Int("删除数量", len(deletedProducts)),
		zap.String("索引名称", indexName))

	return nil
}

// processBatchProducts 处理商品批次
func processBatchProducts(products []service.ProductSync) error {
	if len(products) == 0 {
		return nil
	}

	// 使用批量同步提高效率
	err := syncProductsBatch(products)
	if err != nil {
		// 如果批量同步失败，尝试逐个同步
		log.Log().Warn("批量同步失败，尝试逐个同步", zap.Error(err))
		return syncProductsIndividually(products)
	}

	return nil
}

// syncProductsBatch 批量同步商品到ES
func syncProductsBatch(products []service.ProductSync) error {
	if len(products) == 0 {
		return nil
	}

	// 获取ES客户端
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("获取ES客户端失败: %v", err)
	}

	// 确定ES索引名称
	indexName := "product" + common_data.GetOldProductIndex()

	// 构建批量请求
	bulkRequest := es.Bulk()

	for _, product := range products {
		// 使用HandleData方法构建商品ES文档
		err, esProduct := service.HandleData(product)
		if err != nil {
			log.Log().Error("构建商品ES文档失败",
				zap.Error(err),
				zap.Uint("商品ID", product.ID))
			continue
		}

		// 添加到批量请求
		indexReq := elastic.NewBulkIndexRequest().
			Index(indexName).
			Id(strconv.Itoa(int(product.ID))).
			Doc(esProduct)

		bulkRequest = bulkRequest.Add(indexReq)
	}

	// 执行批量请求
	ctx := context.Background()
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		return fmt.Errorf("批量同步到ES失败: %v", err)
	}

	// 检查批量操作结果
	if bulkResponse.Errors {
		var failedItems []string
		for _, item := range bulkResponse.Items {
			for action, result := range item {
				if result.Error != nil {
					failedItems = append(failedItems, fmt.Sprintf("%s:%s - %s", action, result.Id, result.Error.Reason))
				}
			}
		}

		if len(failedItems) > 0 {
			log.Log().Error("部分商品同步失败",
				zap.Strings("失败项目", failedItems),
				zap.Int("总数量", len(products)),
				zap.Int("失败数量", len(failedItems)))
		}
	}

	log.Log().Info("批量同步商品到ES成功",
		zap.Int("商品数量", len(products)),
		zap.String("索引名称", indexName),
		zap.Duration("耗时", time.Duration(bulkResponse.Took)*time.Millisecond))

	return nil
}

// syncProductsIndividually 逐个同步商品（批量同步失败时的备用方案）
func syncProductsIndividually(products []service.ProductSync) error {
	var errors []string
	successCount := 0

	for _, product := range products {
		err := syncSingleProduct(product)
		if err != nil {
			errorMsg := fmt.Sprintf("商品ID:%d - %s", product.ID, err.Error())
			errors = append(errors, errorMsg)
			log.Log().Error("同步单个商品失败",
				zap.Error(err),
				zap.Uint("商品ID", product.ID),
				zap.String("商品标题", product.Title))
		} else {
			successCount++
		}
	}

	if len(errors) > 0 {
		log.Log().Error("部分商品逐个同步失败",
			zap.Strings("错误信息", errors),
			zap.Int("成功数量", successCount),
			zap.Int("失败数量", len(errors)))
	}

	log.Log().Info("逐个同步商品完成",
		zap.Int("成功数量", successCount),
		zap.Int("失败数量", len(errors)))

	return nil
}

// syncSingleProduct 同步单个商品到ES
func syncSingleProduct(product service.ProductSync) error {
	// 获取ES客户端
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("获取ES客户端失败: %v", err)
	}

	// 使用HandleData方法构建商品ES文档
	err, esProduct := service.HandleData(product)
	if err != nil {
		return fmt.Errorf("构建商品ES文档失败: %v", err)
	}

	// 确定ES索引名称
	indexName := "product" + common_data.GetOldProductIndex()

	// 同步到ES
	ctx := context.Background()
	_, err = es.Index().
		Index(indexName).
		Id(strconv.Itoa(int(product.ID))).
		BodyJson(esProduct).
		Do(ctx)

	if err != nil {
		return fmt.Errorf("同步商品到ES失败: %v", err)
	}

	log.Log().Debug("同步商品到ES成功",
		zap.Uint("商品ID", product.ID),
		zap.String("商品标题", product.Title),
		zap.String("索引名称", indexName))

	return nil
}

// ProductFullSyncHandle 全量同步任务（保留原有功能作为备用）
func ProductFullSyncHandle() {
	task := cron.Task{
		Key:  "productFullSync",
		Name: "商品全量同步",
		Spec: "0 0 2 * * *", // 每天凌晨2点执行一次全量同步
		Handle: func(task cron.Task) {
			ProductFullSyncCron()
		},
		Status: cron.DISABLED, // 默认禁用，需要时可以启用
	}
	cron.PushTask(task)
}

// ProductFullSyncCron 全量同步定时任务
func ProductFullSyncCron() {
	log.Log().Info("开始执行商品全量同步任务")

	// 调用原有的全量同步逻辑
	err := service.Sync(0) // 0表示全量同步
	if err != nil {
		log.Log().Error("全量同步失败", zap.Error(err))
		return
	}

	log.Log().Info("商品全量同步任务完成")
}
