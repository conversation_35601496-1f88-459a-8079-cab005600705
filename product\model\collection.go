package model

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/source"
)

type CollectionModel struct {
	source.Model
	Title  string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);"`
	Desc   string `json:"desc" form:"desc" gorm:"column:desc;comment:简介;type:text;"`
	Cover  string `json:"cover" form:"cover" gorm:"column:cover;comment:封面;type:varchar(255);"`
	Sort   int    `json:"sort" form:"sort" gorm:"column:sort;comment:排序;type:int(11);"`
	Num    int    `json:"num" form:"num" gorm:"column:num;comment:产品数量;type:int(11);"`
	Type   int    `json:"type" form:"type" gorm:"column:type;default:1;type:int(1)"` //1单个选择 2指定分类 3营销属性 4商品数据
	Filter Filter `json:"filter"`
}

func (CollectionModel) TableName() string {
	return "collections"
}

type Collection struct {
	CollectionModel
	Products []Product `json:"products" gorm:"many2many:collection_products"`
}

type Filter struct {
	Category1ID         CategoryIDList `json:"category_1_id"`
	Category2ID         CategoryIDList `json:"category_2_id"`
	Category3ID         CategoryIDList `json:"category_3_id"`
	CategoryProductNum  int            `json:"category_num"`
	AttributeType       int            `json:"attribute_type"` //1ishot 2isrecommend 3isnew 4ispromotion
	AttributeProductNum int            `json:"sale_num"`
	StatisticType       int            `json:"statistic_type"` // 1销量 2时间
	StatisticTime       int            `json:"statistic_time"` // 0全部（商品销量字段）、1昨日、2上周、3上个月、4过去7天、5过去30天、6本年度
	StatisticProductNum int            `json:"statistic_num"`
}
type CategoryIDList []uint

func (value Filter) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Filter) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}
func (value CategoryIDList) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *CategoryIDList) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type CollectionProductModel struct {
	ID           uint `json:"id" form:"id" gorm:"primarykey"`
	CollectionID uint `json:"collection_id" form:"collection_id"`
	ProductID    uint `json:"product_id" form:"product_id"`
}

func (CollectionProductModel) TableName() string {
	return "collection_products"
}

type CollectionProduct struct {
	CollectionProductModel
	Product Product `json:"product"`
}

type Ad struct {
	source.Model
}
