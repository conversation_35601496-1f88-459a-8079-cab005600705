package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io/ioutil"
	"math"
	"net/http"
	"product/model"
	"product/mq"
	"product/other"
	"strconv"
	"strings"
	"sync"
	"time"
	level2 "user/level"
	"yz-go/common_data"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/service"
	"yz-go/source"
)

const pageSize = 200000

type ProductSync struct {
	model.Product
	Category1            other.Category
	Category2            other.Category
	Category3            other.Category
	Brand                other.Brand
	Supplier             model.Supplier
	Storages             []model.Storage                `json:"storages" gorm:"foreignKey:ProductID"`
	SmallShopProductSale []other.SmallShopProductSale   `json:"small_shop_product_sale" gorm:"foreignKey:ProductID"`
	AlbumRelations       []ProductAlbumRelationProduct  `json:"album_relations" gorm:"foreignKey:ProductID;references:ID"`
	CollectionRelations  []model.CollectionProductModel `json:"collection_relations" gorm:"foreignKey:ProductID;references:ID"`
	OrderItems           model.OrderItems               `json:"order_items" gorm:"foreignKey:ProductID;references:ID"`
	UserLevelID          uint                           `json:"user_level_id" gorm:"-"`
}

type ProductAlbumRelationProduct struct {
	source.Model
	ProductAlbumID uint `json:"product_album_id"`
	ProductID      uint `json:"product_id"`
}

func (ProductSync) TableName() string {
	return "products"
}

func Sync(appID uint) (err error) {
	var statusSync string
	statusSync, err = source.Redis().Get(context.Background(), "productSyncStatus").Result()
	if err != nil {
		err = nil
		statusSync = "StandBy"
	}
	if statusSync == "Running" {
		err = errors.New("商品定时同步中，请稍后再试")
		return
	}
	go Exec(appID)

	return
}

var ewg sync.WaitGroup

func Exec(appID uint) (err error) {
	var app Application
	if appID > 0 {
		err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
		if err != nil || app.ApplicationLevel.ID <= 0 {
			return errors.New("采购等级不存在")
		}
	}

	var total int64
	err = source.DB().Model(&model.Product{}).Where("deleted_at is NULL").Count(&total).Error
	if err != nil {
		return errors.New("产品数量获取失败")
	}
	oldProductIndex := common_data.GetOldProductIndex()
	newProductIndex := common_data.GetNewProductIndex()
	es, err := source.ES()
	if err != nil {
		return errors.New("es实例化失败")
	}

	//创建新库并插入数据
	exists, err := es.IndexExists("product" + newProductIndex).Do(context.Background())
	if err != nil {
		fmt.Println("IndexExists" + err.Error())
		log.Log().Error("IndexExists" + err.Error())
		return err
	}
	if exists {
		_, err = es.DeleteIndex("product" + newProductIndex).Do(context.Background())
		if err != nil {
			log.Log().Error("商品全部删除失败："+err.Error(), zap.Any("err", err))
			return errors.New("同步失败")
		}
		fmt.Printf("product" + newProductIndex + " all delete result %s\n")
	}
	if config.Config().ElasticSearch.Plugin {
		// 重新创建索引
		_, err = es.CreateIndex("product" + newProductIndex).BodyString(fmt.Sprintf(`{
  "settings": {
    "analysis": {
      "analyzer": {
        "default": {
          "type": "%s"
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "title": { 
        "type": "text",
        "analyzer": "%s",
        "search_analyzer": "%s",
		"fields": {
          "keyword": {
            "type": "keyword"
          }
        }
      }
    }
  }
}`, "ik_max_word", "ik_max_word", "ik_max_word")).Do(context.Background())
		if err != nil {
			log.Log().Error("Error creating the index: "+err.Error(), zap.Any("err", err))
			return errors.New("同步失败")
		}
	}

	//var forCount = float64(total) / float64(pageSize)
	//var counts = int(math.Ceil(forCount))
	//
	//for i := 1; i <= counts; i++ {
	//	//ewg.Add(1)
	//	if i > 5 {
	//		break
	//	}
	//	err = RunProductSyncEs(&ewg, i, appID)
	//}
	////ewg.Wait()
	//if err != nil {
	//	return
	//}
	err = RunProductSyncEsV2(&source.LocalTime{time.Unix(0, 0)}, 0, newProductIndex)
	if err != nil {
		return
	}
	err = service.PublishNotify("商品同步完成", "应用（"+strconv.Itoa(int(appID))+"）同步了共"+strconv.Itoa(int(total))+"件商品")
	if err != nil {
		fmt.Println("插入系统消息出错："+err.Error(), zap.Any("err", err))
		log.Log().Error("插入系统消息出错："+err.Error(), zap.Any("err", err))
	}
	//替换成为新的库
	common_data.SetProductIndex(newProductIndex)
	fmt.Println("application" + strconv.Itoa(int(appID)) + "商品同步完成")
	//开始删除旧库
	exists, err = es.IndexExists("product" + oldProductIndex).Do(context.Background())
	if err != nil {
		fmt.Println("IndexExists" + err.Error())
		log.Log().Error("IndexExists" + err.Error())
		return err
	}
	if exists {
		_, err = es.DeleteIndex("product" + oldProductIndex).Do(context.Background())
		if err != nil {
			log.Log().Error("商品全部删除失败："+err.Error(), zap.Any("err", err))
			return errors.New("同步失败")
		}
		fmt.Printf("product" + oldProductIndex + " all delete result %s\n")
	}

	//err = sendMessageToTail()
	//if err != nil {
	//	return
	//}
	//
	//更新销量
	//var productsDwd []uint
	//err = source.DB().Model(&model.Product{}).Where("gather_supply_id = 9").Where("deleted_at is null").Pluck("id", &productsDwd).Error
	//if err != nil {
	//	return
	//}
	//var ordersId []uint
	//err = source.DB().Model(&Order{}).Where("gather_supply_id = 9").Where("status > 1").Pluck("id", &ordersId).Error
	//if err != nil {
	//	return
	//}
	//var orderItems []OrderItem
	//err = source.DB().Model(&OrderItem{}).Select("SUM(qty) as total_qty, product_id").Where("order_id in ?", ordersId).Where("product_id in ?", productsDwd).Group("product_id").Find(&orderItems).Error
	//if err != nil {
	//	return
	//}
	//var productSales []map[string]interface{}
	//for _, orderItem := range orderItems {
	//	var productSale = make(map[string]interface{})
	//	productSale["id"] = orderItem.ProductID
	//	productSale["sales"] = orderItem.TotalQty
	//	productSales = append(productSales, productSale)
	//}
	//err = source.BatchUpdate(productSales, "products", "id")
	//if err != nil {
	//	return
	//}

	//删除默认分类
	//var categorys []other.Category
	//err = source.DB().Where("name = ?", "默认").Where("level = 3").Find(&categorys).Error
	//for _, category := range categorys {
	//	var productCount int64
	//	err = source.DB().Model(&Product{}).Where("category3_id = ?", category.ID).Where("deleted_at is null").Count(&productCount).Error
	//	if err != nil {
	//		return
	//	}
	//	if productCount == 0 {
	//		err = source.DB().Delete(&other.Category{}, "id = ?", category.ID).Error
	//		if err != nil {
	//			return
	//		}
	//	}
	//}
	return
}

//type Order struct {
//	ID uint
//}
//
//type OrderItem struct {
//	ProductID uint `json:"product_id"`
//	TotalQty  uint `json:"total_qty"`
//}

func sendMessageToTail() (err error) {
	var applications []Application
	err = source.DB().Preload("ApplicationLevel").Find(&applications).Error
	if err != nil {
		return
	}
	for _, v := range applications {
		if v.CallBackLinkValidity != 0 && v.AppLevelID != 0 && v.MemberId != 0 {
			var productIds []uint
			err = source.DB().Model(&model.Storage{}).Where("app_id = ?", v.ID).Pluck("product_id", &productIds).Error
			if err != nil {
				log.Log().Info("appid为" + strconv.Itoa(int(v.ID)) + "的采购端storages查询失败")
				err = nil
				continue
			}
			var products []Product
			err = source.DB().Where("id in ?", productIds).Find(&products).Error
			if err != nil {
				log.Log().Info("appid为" + strconv.Itoa(int(v.ID)) + "的products查询失败")
				err = nil
				continue
			}
			for _, product := range products {
				var messageType mq.ProductMessageType
				if product.IsDisplay == 1 {
					messageType = mq.OnSale
				} else {
					messageType = mq.Undercarriage
				}
				err = Connection(mq.ProductMessage{ProductID: product.ID, MessageType: messageType, MemberSign: v.AppSecret, Level: 0}, v.CallBackLink)
				if err != nil {
					err = nil
					continue
				}
			}

		}
	}
	return
}

func Connection(product mq.ProductMessage, url string) (err error) {
	var shopProduct model.Product
	if product.MessageType == mq.Delete {
		err = source.DB().Unscoped().Where("id = ?", product.ProductID).First(&shopProduct).Error
	} else {
		err = source.DB().Where("id = ?", product.ProductID).First(&shopProduct).Error
	}
	if err != nil {
		return
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	var messageId string
	messageId = "product" + strconv.Itoa(int(product.ProductID)) + string(product.MessageType) + strconv.Itoa(int(time.Now().Unix()))
	product.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	var respon Resp
	err, respon = post(url, product, header)

	if err != nil {
		fmt.Println("与商城通信失败：", err)
		//log.Log().Info("与商城通信失败3!", zap.Any("err", err))
	} else {
		if respon.Code == 0 || respon.Result == 1 {
			fmt.Println("与商城通信成功：", product)
			return
		} else {
			fmt.Println("推送消息失败："+url, respon)
			//log.Log().Info("推送消息失败!"+url, zap.Any("err", respon))
		}
	}

	return
}

type Resp struct {
	Code   int `json:"code"`
	Result int `json:"result"`
}

func post(url string, data interface{}, header map[string]string) (error, Resp) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	var respon Resp
	err = json.Unmarshal(result, &respon)
	return err, respon
}
func RunProductSyncEs(wg *sync.WaitGroup, i int, appID uint) (err error) {
	//defer wg.Done()
	//分页size
	es, err := source.ES()
	if err != nil {
		return
	}
	slicePageSize := 5000
	var productData ProductElasticSearch
	for k := 1; k <= pageSize/slicePageSize; k++ {
		var products []ProductSync
		err = source.DB().
			Preload("Storages").
			Preload("Skus").
			Preload("Category1").
			Preload("Category2").
			Preload("Category3").
			Preload("Brand").
			Preload("Supplier").
			Preload("GatherSupply").
			Preload("SmallShopProductSale").
			Preload("AlbumRelations").
			Preload("CollectionRelations").
			Preload("OrderItems", func(db *gorm.DB) *gorm.DB {
				return db.Joins("JOIN orders ON orders.id = order_items.order_id").Where("orders.status >= 1")
			}).
			Preload("OrderItems.Order").
			Omit("detail_images").
			Where("deleted_at is null").Limit(slicePageSize).Offset((i-1)*pageSize + ((k - 1) * slicePageSize)).Order("id desc").Find(&products).Error
		if err != nil {
			fmt.Println("查询商品出错："+err.Error(), zap.Any("err", err))
			log.Log().Error("查询商品出错："+err.Error(), zap.Any("err", err))
			continue
		}
		bulkRequest := es.Bulk().Index("product" + common_data.GetOldProductIndex())
		for _, product := range products {
			//批量将商品信息插入es容器中
			err, productData = HandleData(product)
			//str := strconv.FormatFloat(productData.PromotionRate, 'g', 5, 32)
			//str2 := strconv.FormatFloat(productData.ActivityRate, 'g', 5, 32)
			//str3 := strconv.FormatFloat(productData.ProfitRate, 'g', 5, 32)
			//if len(str) > 5 || len(str2) > 5 || len(str3) > 5 {
			//	fmt.Println("查询商品出错："+err.Error(), zap.Any("err", err))
			//
			//}
			if err != nil {
				fmt.Println("处理商品"+strconv.Itoa(int(product.ID))+"数据出错："+err.Error(), zap.Any("err", err))
				log.Log().Error("处理商品"+strconv.Itoa(int(product.ID))+"数据出错："+err.Error(), zap.Any("err", err))
				continue
			}
			doc := elastic.NewBulkIndexRequest().Id(strconv.Itoa(int(product.ID))).Doc(productData)
			bulkRequest = bulkRequest.Add(doc)

			//if err != nil {
			//	fmt.Println("商品"+strconv.Itoa(int(product.ID))+"插入es操作出错："+err.Error(), productData)
			//	log.Log().Error("商品"+strconv.Itoa(int(product.ID))+"插入es操作出错："+err.Error(), zap.Any("err", err))
			//}
		}

		//执行es新建
		if len(products) > 0 {
			_, err = bulkRequest.Do(context.Background())
			if err != nil {
				fmt.Println("执行es操作出错："+err.Error(), zap.Any("err", err))
				log.Log().Error("执行es操作出错："+err.Error(), zap.Any("err", err))
			}
		}

		//
		//fmt.Println(res)

		//补上差的前100条
		var checkProducts []ProductSync
		err = source.DB().Preload("Storages").Preload("Skus").Preload("Category1").Preload("Category2").Preload("Category3").Preload("Brand").Preload("Supplier").Preload("GatherSupply").Preload("SmallShopProductSale").Omit("detail_images").Where("deleted_at is NULL").Limit(100).Offset((i-1)*pageSize + ((k - 1) * slicePageSize)).Order("id desc").Find(&checkProducts).Error
		if err != nil {
			fmt.Println("查询商品出错："+err.Error(), zap.Any("err", err))
			log.Log().Error("查询商品出错："+err.Error(), zap.Any("err", err))
			continue
		}
		bulkRequest = es.Bulk().Index("product" + common_data.GetOldProductIndex())
		for _, checkProduct := range checkProducts {
			//批量将商品信息插入es容器中
			err, productData = HandleData(checkProduct)
			if err != nil {
				fmt.Println("处理商品"+strconv.Itoa(int(checkProduct.ID))+"数据出错："+err.Error(), zap.Any("err", err))
				log.Log().Error("处理商品"+strconv.Itoa(int(checkProduct.ID))+"数据出错："+err.Error(), zap.Any("err", err))
				continue
			}
			doc := elastic.NewBulkIndexRequest().Id(strconv.Itoa(int(checkProduct.ID))).Doc(productData)
			bulkRequest = bulkRequest.Add(doc)

			//if err != nil {
			//	fmt.Println("商品"+strconv.Itoa(int(product.ID))+"插入es操作出错："+err.Error(), productData)
			//	log.Log().Error("商品"+strconv.Itoa(int(product.ID))+"插入es操作出错："+err.Error(), zap.Any("err", err))
			//}
		}

		//执行es新建
		if len(checkProducts) > 0 {
			var res *elastic.BulkResponse
			res, err = bulkRequest.Do(context.Background())
			log.Log().Info("打印存es数据", zap.Any("info", res))
			if err != nil {
				fmt.Println("执行es操作出错："+err.Error(), zap.Any("err", err))
				log.Log().Error("执行es操作出错："+err.Error(), zap.Any("err", err))
			}
		}

		//
		//fmt.Println(res)
	}

	return
}

type UserLevel struct {
	source.Model
	Level    int `json:"level" form:"level"`
	Discount int `json:"discount" form:"discount"`
}

var inttt = 0
var ProductNum *int = &inttt

func HandleData(product ProductSync) (err error, productElasticSearch ProductElasticSearch) {
	err, level := level2.GetLevels()
	if err != nil {
		return
	}
	productElasticSearch.ID = product.ID
	productElasticSearch.Title = product.Title
	productElasticSearch.SearchTitle = product.Title
	for _, sku := range product.Skus {
		if sku.Title != "" {
			productElasticSearch.SearchTitle += "+" + sku.Title
		}
	}
	productElasticSearch.ImageUrl = product.ImageUrl
	productElasticSearch.SupplierID = product.SupplierID
	productElasticSearch.GatherSupplyID = product.GatherSupplyID
	productElasticSearch.SupplierName = product.Supplier.Name
	productElasticSearch.SupplierShopName = product.Supplier.ShopName
	productElasticSearch.Sort = product.Sort
	productElasticSearch.Barcode = product.Barcode
	productElasticSearch.IsPlugin = product.IsPlugin
	productElasticSearch.SingleOption = product.SingleOption
	productElasticSearch.SourceName = product.SourceName
	productElasticSearch.LocationId = product.LocationID
	if product.GatherSupplyID != 0 {
		switch product.GatherSupply.CategoryID {
		case 2: //中台
			productElasticSearch.Source = 101
			break
		case 3: //永源
			productElasticSearch.Source = 99
			break
		case 4: //YZH
			productElasticSearch.Source = 100
			break
		default:
			productElasticSearch.Source = product.Source
			break
		}
	} else {
		productElasticSearch.Source = product.Source
	}

	productElasticSearch.Category1ID = product.Category1ID
	productElasticSearch.Category2ID = product.Category2ID
	productElasticSearch.Category3ID = product.Category3ID
	productElasticSearch.BrandID = product.BrandID
	var stock int
	productElasticSearch.Stock = stock
	productElasticSearch.Sales = int(product.Sales)

	now := time.Now()
	yesterdayStart, yesterdayEnd := GetYesterdayRange(now)
	weekStart, weekEnd := GetLastWeekRange(now)
	monthStart, monthEnd := GetLastMonthRange(now)
	past7DaysStart, past7DaysEnd := GetPast7DaysRange(now)
	past30DaysStart, past30DaysEnd := GetPast30DaysRange(now)
	thisYearStart := GetThisYearStart(now)

	// 跟据product.OrderItems计算昨日、上周、上月、近7天、近30天、本年度销量
	var salesYesterday, salesLastWeek, salesLastMonth, salesLast7d, salesLast30d, salesThisYear int
	for _, item := range product.OrderItems {
		// 昨日销量
		if !item.CreatedAt.Before(yesterdayStart) && !item.CreatedAt.After(yesterdayEnd) {
			salesYesterday += int(item.Qty)
		}
		// 周销量
		if !item.CreatedAt.Before(weekStart) && !item.CreatedAt.After(weekEnd) {
			salesLastWeek += int(item.Qty)
		}
		// 月销量
		if !item.CreatedAt.Before(monthStart) && !item.CreatedAt.After(monthEnd) {
			salesLastMonth += int(item.Qty)
		}
		// 过去7天销量（不含今天）
		if !item.CreatedAt.Before(past7DaysStart) && !item.CreatedAt.After(past7DaysEnd) {
			salesLast7d += int(item.Qty)
		}
		// 过去30天销量（不含今天）
		if !item.CreatedAt.Before(past30DaysStart) && !item.CreatedAt.After(past30DaysEnd) {
			salesLast30d += int(item.Qty)
		}
		// 本年度销量
		if item.CreatedAt.After(thisYearStart) {
			salesThisYear += int(item.Qty)
		}
	}

	// 昨日、上周、上月、近7天、近30天、本年度销量
	productElasticSearch.SalesYesterday = salesYesterday
	productElasticSearch.SalesLastWeek = salesLastWeek
	productElasticSearch.SalesLastMonth = salesLastMonth
	productElasticSearch.SalesLast7d = salesLast7d
	productElasticSearch.SalesLast30d = salesLast30d
	productElasticSearch.SalesThisYear = salesThisYear

	if product.MinPrice > 0 {
		productElasticSearch.AgreementPrice = product.MinPrice //协议价
	} else {
		productElasticSearch.AgreementPrice = product.Price //协议价
	}
	productElasticSearch.CostPrice = product.CostPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
	productElasticSearch.ActivityPrice = product.ActivityPrice
	productElasticSearch.GuidePrice = product.GuidePrice   //指导价
	productElasticSearch.MarketPrice = product.OriginPrice //市场价
	// 等级折扣价格
	var levels = make(map[int]int)
	var levelIds = make(map[int]uint)
	for k, i := range level {
		if k < 10 {
			levels[k] = i.Discount
			levelIds[k] = i.ID
		}
	}
	for i := 0; i < len(levels); i++ {
		var priceUint uint
		var priceInt int
		var calculateRes bool
		if product.UserPriceSwitch == 1 {
			priceUint, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, levelIds[i])
		}
		// 计算失败时，使用备用计算方法
		if !calculateRes {
			err, priceInt = level2.GetLevelDiscountAmountWithInt(product.Price, product.CostPrice, levels[i])
			if err != nil {
				return
			}
			priceUint = uint(priceInt)
		}
		// 赋值到 productElasticSearch, 可以使用反射优化
		switch i {
		case 0:
			productElasticSearch.Level1Price = int(priceUint)
		case 1:
			productElasticSearch.Level2Price = int(priceUint)
		case 2:
			productElasticSearch.Level3Price = int(priceUint)
		case 3:
			productElasticSearch.Level4Price = int(priceUint)
		case 4:
			productElasticSearch.Level5Price = int(priceUint)
		case 5:
			productElasticSearch.Level6Price = int(priceUint)
		case 6:
			productElasticSearch.Level7Price = int(priceUint)
		case 7:
			productElasticSearch.Level8Price = int(priceUint)
		case 8:
			productElasticSearch.Level9Price = int(priceUint)
		case 9:
			productElasticSearch.Level10Price = int(priceUint)
		}
	}
	// 利润
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level1Price && productElasticSearch.Level1Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level1Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level1Price
	} else {
		productElasticSearch.Level1Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level2Price && productElasticSearch.Level2Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level2Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level2Price
	} else {
		productElasticSearch.Level2Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level3Price && productElasticSearch.Level3Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level3Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level3Price
	} else {
		productElasticSearch.Level3Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level4Price && productElasticSearch.Level4Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level4Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level4Price
	} else {
		productElasticSearch.Level4Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level5Price && productElasticSearch.Level5Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level5Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level5Price
	} else {
		productElasticSearch.Level5Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level6Price && productElasticSearch.Level6Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level6Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level6Price
	} else {
		productElasticSearch.Level6Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level7Price && productElasticSearch.Level7Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level7Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level7Price
	} else {
		productElasticSearch.Level7Profit = 0

	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level8Price && productElasticSearch.Level8Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level8Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level8Price
	} else {
		productElasticSearch.Level8Profit = 0

	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level9Price && productElasticSearch.Level9Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level9Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level9Price
	} else {
		productElasticSearch.Level9Profit = 0
	}
	if int(productElasticSearch.MarketPrice) > productElasticSearch.Level10Price && productElasticSearch.Level10Price > 0 && productElasticSearch.MarketPrice > 0 {
		productElasticSearch.Level10Profit = int(productElasticSearch.MarketPrice) - productElasticSearch.Level10Price
	} else {
		productElasticSearch.Level10Profit = 0
	}
	// 毛利率
	if productElasticSearch.Level1Price > 0 && productElasticSearch.Level1Profit > 0 {
		productElasticSearch.Level1ProfitRate = Decimal((float64(productElasticSearch.Level1Profit) / float64(productElasticSearch.Level1Price)) * 100)
		productElasticSearch.Level1GrossRate = Decimal((float64(productElasticSearch.Level1Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level1ProfitRate = 0
		productElasticSearch.Level1GrossRate = 0
	}
	if productElasticSearch.Level2Price > 0 && productElasticSearch.Level2Profit > 0 {
		productElasticSearch.Level2ProfitRate = Decimal((float64(productElasticSearch.Level2Profit) / float64(productElasticSearch.Level2Price)) * 100)
		productElasticSearch.Level2GrossRate = Decimal((float64(productElasticSearch.Level2Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level2ProfitRate = 0
		productElasticSearch.Level2GrossRate = 0
	}
	if productElasticSearch.Level3Price > 0 && productElasticSearch.Level3Profit > 0 {
		productElasticSearch.Level3ProfitRate = Decimal((float64(productElasticSearch.Level3Profit) / float64(productElasticSearch.Level3Price)) * 100)
		productElasticSearch.Level3GrossRate = Decimal((float64(productElasticSearch.Level3Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level3ProfitRate = 0
		productElasticSearch.Level3GrossRate = 0
	}
	if productElasticSearch.Level4Price > 0 && productElasticSearch.Level4Profit > 0 {
		productElasticSearch.Level4ProfitRate = Decimal((float64(productElasticSearch.Level4Profit) / float64(productElasticSearch.Level4Price)) * 100)
		productElasticSearch.Level4GrossRate = Decimal((float64(productElasticSearch.Level4Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level4ProfitRate = 0
		productElasticSearch.Level4GrossRate = 0
	}
	if productElasticSearch.Level5Price > 0 && productElasticSearch.Level5Profit > 0 {
		productElasticSearch.Level5ProfitRate = Decimal((float64(productElasticSearch.Level5Profit) / float64(productElasticSearch.Level5Price)) * 100)
		productElasticSearch.Level5GrossRate = Decimal((float64(productElasticSearch.Level5Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level5ProfitRate = 0
		productElasticSearch.Level5GrossRate = 0
	}
	if productElasticSearch.Level6Price > 0 && productElasticSearch.Level6Profit > 0 {
		productElasticSearch.Level6ProfitRate = Decimal((float64(productElasticSearch.Level6Profit) / float64(productElasticSearch.Level6Price)) * 100)
		productElasticSearch.Level6GrossRate = Decimal((float64(productElasticSearch.Level6Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level6ProfitRate = 0
		productElasticSearch.Level6GrossRate = 0
	}
	if productElasticSearch.Level7Price > 0 && productElasticSearch.Level7Profit > 0 {
		productElasticSearch.Level7ProfitRate = Decimal((float64(productElasticSearch.Level7Profit) / float64(productElasticSearch.Level7Price)) * 100)
		productElasticSearch.Level7GrossRate = Decimal((float64(productElasticSearch.Level7Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level7ProfitRate = 0
		productElasticSearch.Level7GrossRate = 0
	}
	if productElasticSearch.Level8Price > 0 && productElasticSearch.Level8Profit > 0 {
		productElasticSearch.Level8ProfitRate = Decimal((float64(productElasticSearch.Level8Profit) / float64(productElasticSearch.Level8Price)) * 100)
		productElasticSearch.Level8GrossRate = Decimal((float64(productElasticSearch.Level8Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level8ProfitRate = 0
		productElasticSearch.Level8GrossRate = 0
	}
	if productElasticSearch.Level9Price > 0 && productElasticSearch.Level9Profit > 0 {
		productElasticSearch.Level9ProfitRate = Decimal((float64(productElasticSearch.Level9Profit) / float64(productElasticSearch.Level9Price)) * 100)
		productElasticSearch.Level9GrossRate = Decimal((float64(productElasticSearch.Level9Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level9ProfitRate = 0
		productElasticSearch.Level9GrossRate = 0
	}
	if productElasticSearch.Level10Price > 0 && productElasticSearch.Level10Profit > 0 {
		productElasticSearch.Level10ProfitRate = Decimal((float64(productElasticSearch.Level10Profit) / float64(productElasticSearch.Level10Price)) * 100)
		productElasticSearch.Level10GrossRate = Decimal((float64(productElasticSearch.Level10Profit) / float64(productElasticSearch.MarketPrice)) * 100)
	} else {
		productElasticSearch.Level10ProfitRate = 0
		productElasticSearch.Level10GrossRate = 0
	}
	// 利润率(price - cost_price) / cost_price * 100%
	if product.Price > 0 && product.CostPrice > 0 && product.Price > product.CostPrice {
		productElasticSearch.PriceRate = Decimal((float64(product.Price) - float64(product.CostPrice)) / float64(product.CostPrice) * 100)
	} else {
		productElasticSearch.PriceRate = 0
	}
	// 未计算等级折扣的市场利润率和毛利率
	if productElasticSearch.MarketPrice > 0 {
		if productElasticSearch.MarketPrice > productElasticSearch.AgreementPrice && productElasticSearch.MarketPrice > 0 && productElasticSearch.AgreementPrice > 0 {
			productElasticSearch.MarketRate = Decimal((float64(productElasticSearch.MarketPrice) - float64(productElasticSearch.AgreementPrice)) / float64(productElasticSearch.AgreementPrice) * 100)
			productElasticSearch.GrossProfitRate = Decimal((float64(productElasticSearch.MarketPrice) - float64(productElasticSearch.AgreementPrice)) / float64(productElasticSearch.MarketPrice) * 100)
		} else {
			productElasticSearch.MarketRate = 0
			productElasticSearch.GrossProfitRate = 0
		}
		//productElasticSearch.CostPrice = productElasticSearch.ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
	} else {
		//productElasticSearch.CostPrice = productElasticSearch.AgreementPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
	}
	// 未计算等级折扣的营销利润率
	if productElasticSearch.ActivityPrice > productElasticSearch.AgreementPrice && productElasticSearch.ActivityPrice > 0 && productElasticSearch.AgreementPrice > 0 {
		productElasticSearch.ActivityRate = Decimal((float64(productElasticSearch.ActivityPrice) - float64(productElasticSearch.AgreementPrice)) / float64(productElasticSearch.AgreementPrice) * 100)
	} else {
		productElasticSearch.ActivityRate = 0
	}
	//productElasticSearch.CostPrice = productElasticSearch.ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
	productElasticSearch.SalePrice = product.GuidePrice //建议销售价
	if productElasticSearch.AgreementPrice > 0 && productElasticSearch.GuidePrice > 0 && productElasticSearch.GuidePrice > productElasticSearch.AgreementPrice {
		productElasticSearch.PromotionRate = Decimal((float64(productElasticSearch.GuidePrice) - float64(productElasticSearch.AgreementPrice)) / float64(productElasticSearch.AgreementPrice) * 100)

	} else {
		if product.Price <= 0 {
			productElasticSearch.PromotionRate = 1
		}
		if product.GuidePrice <= 0 {
			productElasticSearch.PromotionRate = 0
		}
		if product.GuidePrice < product.Price {
			productElasticSearch.PromotionRate = 0
		}
	}
	var minPromotionRate float64
	var maxPromotionRate float64
	if len(product.Skus) > 1 {
		for _, sku := range product.Skus {
			stock += sku.Stock
			var promotionRate float64
			if sku.GuidePrice > sku.Price && sku.Price > 0 {
				promotionRate = Decimal((float64(sku.GuidePrice) - float64(sku.Price)) / float64(sku.Price) * 100)
				if minPromotionRate == 0 || promotionRate < minPromotionRate {
					minPromotionRate = promotionRate
				}
				if promotionRate > maxPromotionRate {
					maxPromotionRate = promotionRate
				}
			}
		}
	} else {
		minPromotionRate = productElasticSearch.PromotionRate
		maxPromotionRate = productElasticSearch.PromotionRate
	}
	productElasticSearch.MinPromotionRate = minPromotionRate
	productElasticSearch.MaxPromotionRate = maxPromotionRate
	// 未计算等级折扣的利润
	if productElasticSearch.GuidePrice > 0 && productElasticSearch.AgreementPrice > 0 && productElasticSearch.GuidePrice > productElasticSearch.AgreementPrice {
		productElasticSearch.Profit = int(productElasticSearch.GuidePrice) - int(productElasticSearch.AgreementPrice)
		productElasticSearch.ProfitRate = Decimal(float64(productElasticSearch.Profit) / float64(productElasticSearch.GuidePrice) * 100)
	} else {
		productElasticSearch.ProfitRate = 0
	}
	productElasticSearch.IsRecommend = product.IsRecommend
	productElasticSearch.IsDisplay = product.IsDisplay
	productElasticSearch.IsNew = product.IsNew
	productElasticSearch.IsHot = product.IsHot
	productElasticSearch.IsPromotion = product.IsPromotion
	productElasticSearch.CreatedAt = product.CreatedAt
	productElasticSearch.UpdatedAt = product.UpdatedAt
	productElasticSearch.FreightType = product.FreightType
	productElasticSearch.RecommendBrandStr = product.Brand.Name
	productElasticSearch.RecommendCategoryStr = product.Category1.Name
	productElasticSearch.RecommendCategoryStr = productElasticSearch.RecommendCategoryStr + "," + product.Category2.Name
	productElasticSearch.RecommendCategoryStr = productElasticSearch.RecommendCategoryStr + "," + product.Category3.Name
	// 采购端选品库
	var importApps []string
	for _, storage := range product.Storages {
		importApps = append(importApps, strconv.Itoa(int(storage.AppID)))
	}
	productElasticSearch.ImportApps = strings.Join(importApps, " ")
	productElasticSearch.SupplyLine = product.SupplyLine
	productElasticSearch.IsBill = product.IsBill
	productElasticSearch.IsVideoShop = product.IsVideoShop
	// 店主选中的商品
	if len(product.SmallShopProductSale) > 0 {
		// 小商店id
		var smallShopIds []string
		for _, sale := range product.SmallShopProductSale {
			smallShopIds = append(smallShopIds, strconv.Itoa(int(sale.SmallShopID)))
		}
		productElasticSearch.SmallShopIdString = strings.Join(smallShopIds, " ")
	}
	// 共享专辑选中的商品
	productElasticSearch.AlbumIds = make([]uint, 0, len(product.AlbumRelations))
	for _, relation := range product.AlbumRelations {
		productElasticSearch.AlbumIds = append(productElasticSearch.AlbumIds, relation.ProductAlbumID)
	}
	// 商品专辑选中的商品
	productElasticSearch.CollectIds = make([]uint, 0, len(product.CollectionRelations))
	for _, collection := range product.CollectionRelations {
		productElasticSearch.CollectIds = append(productElasticSearch.CollectIds, collection.CollectionID)
	}
	productElasticSearch.TaxRate = product.TaxRate
	productElasticSearch.IsTaxLogo = product.IsTaxLogo
	productElasticSearch.SkuTaxRates = make([]int, 0, len(product.Skus))
	for k, sku := range product.Skus {
		// 任意规格开启含税，增商品开启含税
		if sku.IsTaxLogo == 1 {
			productElasticSearch.IsTaxLogo = 1
		}
		// sku税率
		productElasticSearch.SkuTaxRates = append(productElasticSearch.SkuTaxRates, sku.TaxRate)

		// 最小利润 未计算等级折扣的最大最小利润
		var profit int
		if sku.OriginPrice > sku.Price && sku.OriginPrice > 0 && sku.Price > 0 {
			profit = int(sku.OriginPrice) - int(sku.Price)
		} else {
			profit = 0
		}
		if k == 0 {
			productElasticSearch.MinProfit = profit
		}
		if profit < productElasticSearch.MinProfit {
			productElasticSearch.MinProfit = profit
		}
	}

	// 计算商品会员等级折扣
	productDiscounts := make(map[int][]uint)
	// 计算商品会员等级折扣价格
	skuDiscountPrices := make(map[int][]uint)
	// 计算商品会员等级折扣后的利润
	skuProfitPrices := make(map[int][]uint)
	// 计算商品会员等级折扣后的利润率
	skuProfitRates := make(map[int][]float64)
	// 计算商品会员等级折扣后的毛利率
	skuGrossRates := make(map[int][]float64)
	// 计算商品会员等级折扣 origin_price 建议零售价 price 批发价计算基数
	for _, sku := range product.Skus {
		for i := 0; i < len(level); i++ {
			var priceUint uint
			var priceInt int
			var calculateRes bool
			if product.UserPriceSwitch == 1 {
				priceUint, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, levelIds[i])
			}
			// 计算失败时，使用备用计算方法
			if !calculateRes {
				err, priceInt = level2.GetLevelDiscountAmountWithInt(sku.Price, sku.CostPrice, levels[i])
				if err != nil {
					log.Log().Error("获取会员等级折扣价格失败", zap.Any("err", err), zap.Int("sku_id", int(sku.ID)), zap.Int("level", i))
					err = nil
					priceUint = sku.Price
				}
				priceUint = uint(priceInt)
			}
			if priceUint <= 0 {
				priceUint = sku.Price
			}
			// 确保 skuDiscountPrices[i] 对应的 slice 已初始化
			if _, ok := skuDiscountPrices[i]; !ok {
				skuDiscountPrices[i] = make([]uint, 0)
			}
			skuDiscountPrices[i] = append(skuDiscountPrices[i], priceUint)
			// 计算会员等级的折扣
			if sku.OriginPrice > 0 {
				var dis uint
				if priceUint > 0 {
					dis = uint(Decimal((float64(priceUint) / float64(sku.OriginPrice)) * 1000))
				} else {
					dis = 0
				}
				// 确保 productDiscounts[i] 对应的 slice 已初始化
				if _, ok := productDiscounts[i]; !ok {
					productDiscounts[i] = make([]uint, 0)
				}
				productDiscounts[i] = append(productDiscounts[i], dis)
			}

			// 计算会员等级的利润
			var profit uint
			if sku.OriginPrice > priceUint && priceUint > 0 && sku.OriginPrice > 0 {
				profit = sku.OriginPrice - priceUint
			} else {
				profit = 0
			}
			// 确保 skuProfitPrices[i] 对应的 slice 已初始化
			if _, ok := skuProfitPrices[i]; !ok {
				skuProfitPrices[i] = make([]uint, 0)
			}
			skuProfitPrices[i] = append(skuProfitPrices[i], profit)
			// 计算会员等级的利润率和毛利率
			var profitRate, grossRate float64
			if priceUint > 0 && profit > 0 {
				profitRate = Decimal4Places((float64(profit) / float64(priceUint)) * 100)
				if sku.OriginPrice > 0 {
					grossRate = Decimal4Places((float64(profit) / float64(sku.OriginPrice)) * 100)
				} else {
					grossRate = 0
				}
			} else {
				profitRate = 0
				grossRate = 0
			}
			// 确保 skuProfitRates[i] 对应的 slice 已初始化
			if _, ok := skuProfitRates[i]; !ok {
				skuProfitRates[i] = make([]float64, 0)
			}
			skuProfitRates[i] = append(skuProfitRates[i], profitRate)
			// 确保 skuGrossRates[i] 对应的 slice 已初始化
			if _, ok := skuGrossRates[i]; !ok {
				skuGrossRates[i] = make([]float64, 0)
			}
			skuGrossRates[i] = append(skuGrossRates[i], grossRate)
		}
	}
	// 比较出最低利润和最高利润
	for si, skuProfitPrice := range skuProfitPrices {
		for _, sItem := range skuProfitPrice {
			price := int(sItem)
			switch si {
			case 0:
				if productElasticSearch.Level1MinProfit == 0 || price < productElasticSearch.Level1MinProfit {
					productElasticSearch.Level1MinProfit = price
				}
				if price > productElasticSearch.Level1MaxProfit {
					productElasticSearch.Level1MaxProfit = price
				}
			case 1:
				if productElasticSearch.Level2MinProfit == 0 || price < productElasticSearch.Level2MinProfit {
					productElasticSearch.Level2MinProfit = price
				}
				if price > productElasticSearch.Level2MaxProfit {
					productElasticSearch.Level2MaxProfit = price
				}
			case 2:
				if productElasticSearch.Level3MinProfit == 0 || price < productElasticSearch.Level3MinProfit {
					productElasticSearch.Level3MinProfit = price
				}
				if price > productElasticSearch.Level3MaxProfit {
					productElasticSearch.Level3MaxProfit = price
				}
			case 3:
				if productElasticSearch.Level4MinProfit == 0 || price < productElasticSearch.Level4MinProfit {
					productElasticSearch.Level4MinProfit = price
				}
				if price > productElasticSearch.Level4MaxProfit {
					productElasticSearch.Level4MaxProfit = price
				}
			case 4:
				if productElasticSearch.Level5MinProfit == 0 || price < productElasticSearch.Level5MinProfit {
					productElasticSearch.Level5MinProfit = price
				}
				if price > productElasticSearch.Level5MaxProfit {
					productElasticSearch.Level5MaxProfit = price
				}
			case 5:
				if productElasticSearch.Level6MinProfit == 0 || price < productElasticSearch.Level6MinProfit {
					productElasticSearch.Level6MinProfit = price
				}
				if price > productElasticSearch.Level6MaxProfit {
					productElasticSearch.Level6MaxProfit = price
				}
			case 6:
				if productElasticSearch.Level7MinProfit == 0 || price < productElasticSearch.Level7MinProfit {
					productElasticSearch.Level7MinProfit = price
				}
				if price > productElasticSearch.Level7MaxProfit {
					productElasticSearch.Level7MaxProfit = price
				}
			case 7:
				if productElasticSearch.Level8MinProfit == 0 || price < productElasticSearch.Level8MinProfit {
					productElasticSearch.Level8MinProfit = price
				}
				if price > productElasticSearch.Level8MaxProfit {
					productElasticSearch.Level8MaxProfit = price
				}
			case 8:
				if productElasticSearch.Level9MinProfit == 0 || price < productElasticSearch.Level9MinProfit {
					productElasticSearch.Level9MinProfit = price
				}
				if price > productElasticSearch.Level9MaxProfit {
					productElasticSearch.Level9MaxProfit = price
				}
			case 9:
				if productElasticSearch.Level10MinProfit == 0 || price < productElasticSearch.Level10MinProfit {
					productElasticSearch.Level10MinProfit = price
				}
				if price > productElasticSearch.Level10MaxProfit {
					productElasticSearch.Level10MaxProfit = price
				}
			}
		}
	}
	// 比较出最低利润率和最高利润率
	for si, skuProfitRate := range skuProfitRates {
		for _, sItem := range skuProfitRate {
			rate := sItem
			switch si {
			case 0:
				if productElasticSearch.Level1MinProfitRate == 0 || rate < productElasticSearch.Level1MinProfitRate {
					productElasticSearch.Level1MinProfitRate = rate
				}
				if rate > productElasticSearch.Level1MaxProfitRate {
					productElasticSearch.Level1MaxProfitRate = rate
				}
			case 1:
				if productElasticSearch.Level2MinProfitRate == 0 || rate < productElasticSearch.Level2MinProfitRate {
					productElasticSearch.Level2MinProfitRate = rate
				}
				if rate > productElasticSearch.Level2MaxProfitRate {
					productElasticSearch.Level2MaxProfitRate = rate
				}
			case 2:
				if productElasticSearch.Level3MinProfitRate == 0 || rate < productElasticSearch.Level3MinProfitRate {
					productElasticSearch.Level3MinProfitRate = rate
				}
				if rate > productElasticSearch.Level3MaxProfitRate {
					productElasticSearch.Level3MaxProfitRate = rate
				}
			case 3:
				if productElasticSearch.Level4MinProfitRate == 0 || rate < productElasticSearch.Level4MinProfitRate {
					productElasticSearch.Level4MinProfitRate = rate
				}
				if rate > productElasticSearch.Level4MaxProfitRate {
					productElasticSearch.Level4MaxProfitRate = rate
				}
			case 4:
				if productElasticSearch.Level5MinProfitRate == 0 || rate < productElasticSearch.Level5MinProfitRate {
					productElasticSearch.Level5MinProfitRate = rate
				}
				if rate > productElasticSearch.Level5MaxProfitRate {
					productElasticSearch.Level5MaxProfitRate = rate
				}
			case 5:
				if productElasticSearch.Level6MinProfitRate == 0 || rate < productElasticSearch.Level6MinProfitRate {
					productElasticSearch.Level6MinProfitRate = rate
				}
				if rate > productElasticSearch.Level6MaxProfitRate {
					productElasticSearch.Level6MaxProfitRate = rate
				}
			case 6:
				if productElasticSearch.Level7MinProfitRate == 0 || rate < productElasticSearch.Level7MinProfitRate {
					productElasticSearch.Level7MinProfitRate = rate
				}
				if rate > productElasticSearch.Level7MaxProfitRate {
					productElasticSearch.Level7MaxProfitRate = rate
				}
			case 7:
				if productElasticSearch.Level8MinProfitRate == 0 || rate < productElasticSearch.Level8MinProfitRate {
					productElasticSearch.Level8MinProfitRate = rate
				}
				if rate > productElasticSearch.Level8MaxProfitRate {
					productElasticSearch.Level8MaxProfitRate = rate
				}
			case 8:
				if productElasticSearch.Level9MinProfitRate == 0 || rate < productElasticSearch.Level9MinProfitRate {
					productElasticSearch.Level9MinProfitRate = rate
				}
				if rate > productElasticSearch.Level9MaxProfitRate {
					productElasticSearch.Level9MaxProfitRate = rate
				}
			case 9:
				if productElasticSearch.Level10MinProfitRate == 0 || rate < productElasticSearch.Level10MinProfitRate {
					productElasticSearch.Level10MinProfitRate = rate
				}
				if rate > productElasticSearch.Level10MaxProfitRate {
					productElasticSearch.Level10MaxProfitRate = rate
				}
			}
		}
	}
	// 比较出最低毛利率和最高毛利率
	for si, skuGrossRate := range skuGrossRates {
		for _, sItem := range skuGrossRate {
			rate := sItem
			switch si {
			case 0:
				if productElasticSearch.Level1MinGrossRate == 0 || rate < productElasticSearch.Level1MinGrossRate {
					productElasticSearch.Level1MinGrossRate = rate
				}
				if rate > productElasticSearch.Level1MaxGrossRate {
					productElasticSearch.Level1MaxGrossRate = rate
				}
			case 1:
				if productElasticSearch.Level2MinGrossRate == 0 || rate < productElasticSearch.Level2MinGrossRate {
					productElasticSearch.Level2MinGrossRate = rate
				}
				if rate > productElasticSearch.Level2MaxGrossRate {
					productElasticSearch.Level2MaxGrossRate = rate
				}
			case 2:
				if productElasticSearch.Level3MinGrossRate == 0 || rate < productElasticSearch.Level3MinGrossRate {
					productElasticSearch.Level3MinGrossRate = rate
				}
				if rate > productElasticSearch.Level3MaxGrossRate {
					productElasticSearch.Level3MaxGrossRate = rate
				}
			case 3:
				if productElasticSearch.Level4MinGrossRate == 0 || rate < productElasticSearch.Level4MinGrossRate {
					productElasticSearch.Level4MinGrossRate = rate
				}
				if rate > productElasticSearch.Level4MaxGrossRate {
					productElasticSearch.Level4MaxGrossRate = rate
				}
			case 4:
				if productElasticSearch.Level5MinGrossRate == 0 || rate < productElasticSearch.Level5MinGrossRate {
					productElasticSearch.Level5MinGrossRate = rate
				}
				if rate > productElasticSearch.Level5MaxGrossRate {
					productElasticSearch.Level5MaxGrossRate = rate
				}
			case 5:
				if productElasticSearch.Level6MinGrossRate == 0 || rate < productElasticSearch.Level6MinGrossRate {
					productElasticSearch.Level6MinGrossRate = rate
				}
				if rate > productElasticSearch.Level6MaxGrossRate {
					productElasticSearch.Level6MaxGrossRate = rate
				}
			case 6:
				if productElasticSearch.Level7MinGrossRate == 0 || rate < productElasticSearch.Level7MinGrossRate {
					productElasticSearch.Level7MinGrossRate = rate
				}
				if rate > productElasticSearch.Level7MaxGrossRate {
					productElasticSearch.Level7MaxGrossRate = rate
				}
			case 7:
				if productElasticSearch.Level8MinGrossRate == 0 || rate < productElasticSearch.Level8MinGrossRate {
					productElasticSearch.Level8MinGrossRate = rate
				}
				if rate > productElasticSearch.Level8MaxGrossRate {
					productElasticSearch.Level8MaxGrossRate = rate
				}
			case 8:
				if productElasticSearch.Level9MinGrossRate == 0 || rate < productElasticSearch.Level9MinGrossRate {
					productElasticSearch.Level9MinGrossRate = rate
				}
				if rate > productElasticSearch.Level9MaxGrossRate {
					productElasticSearch.Level9MaxGrossRate = rate
				}
			case 9:
				if productElasticSearch.Level10MinGrossRate == 0 || rate < productElasticSearch.Level10MinGrossRate {
					productElasticSearch.Level10MinGrossRate = rate
				}
				if rate > productElasticSearch.Level10MaxGrossRate {
					productElasticSearch.Level10MaxGrossRate = rate
				}
			}
		}
	}
	// 比较出最低价和最高价
	for si, skuDiscountPrice := range skuDiscountPrices {
		for _, sItem := range skuDiscountPrice {
			price := int(sItem)
			if price <= 0 {
				price = int(productElasticSearch.AgreementPrice)
			}
			switch si {
			case 0:
				if productElasticSearch.Level1MinPrice == 0 || price < productElasticSearch.Level1MinPrice {
					productElasticSearch.Level1MinPrice = price
				}
				if price > productElasticSearch.Level1MaxPrice {
					productElasticSearch.Level1MaxPrice = price
				}
			case 1:
				if productElasticSearch.Level2MinPrice == 0 || price < productElasticSearch.Level2MinPrice {
					productElasticSearch.Level2MinPrice = price
				}
				if price > productElasticSearch.Level2MaxPrice {
					productElasticSearch.Level2MaxPrice = price
				}
			case 2:
				if productElasticSearch.Level3MinPrice == 0 || price < productElasticSearch.Level3MinPrice {
					productElasticSearch.Level3MinPrice = price
				}
				if price > productElasticSearch.Level3MaxPrice {
					productElasticSearch.Level3MaxPrice = price
				}
			case 3:
				if productElasticSearch.Level4MinPrice == 0 || price < productElasticSearch.Level4MinPrice {
					productElasticSearch.Level4MinPrice = price
				}
				if price > productElasticSearch.Level4MaxPrice {
					productElasticSearch.Level4MaxPrice = price
				}
			case 4:
				if productElasticSearch.Level5MinPrice == 0 || price < productElasticSearch.Level5MinPrice {
					productElasticSearch.Level5MinPrice = price
				}
				if price > productElasticSearch.Level5MaxPrice {
					productElasticSearch.Level5MaxPrice = price
				}
			case 5:
				if productElasticSearch.Level6MinPrice == 0 || price < productElasticSearch.Level6MinPrice {
					productElasticSearch.Level6MinPrice = price
				}
				if price > productElasticSearch.Level6MaxPrice {
					productElasticSearch.Level6MaxPrice = price
				}
			case 6:
				if productElasticSearch.Level7MinPrice == 0 || price < productElasticSearch.Level7MinPrice {
					productElasticSearch.Level7MinPrice = price
				}
				if price > productElasticSearch.Level7MaxPrice {
					productElasticSearch.Level7MaxPrice = price
				}
			case 7:
				if productElasticSearch.Level8MinPrice == 0 || price < productElasticSearch.Level8MinPrice {
					productElasticSearch.Level8MinPrice = price
				}
				if price > productElasticSearch.Level8MaxPrice {
					productElasticSearch.Level8MaxPrice = price
				}
			case 8:
				if productElasticSearch.Level9MinPrice == 0 || price < productElasticSearch.Level9MinPrice {
					productElasticSearch.Level9MinPrice = price
				}
				if price > productElasticSearch.Level9MaxPrice {
					productElasticSearch.Level9MaxPrice = price
				}
			case 9:
				if productElasticSearch.Level10MinPrice == 0 || price < productElasticSearch.Level10MinPrice {
					productElasticSearch.Level10MinPrice = price
				}
				if price > productElasticSearch.Level10MaxPrice {
					productElasticSearch.Level10MaxPrice = price
				}
			}
		}
	}
	// 比较出最低折扣和最高折扣
	for l, dis := range productDiscounts {
		for _, disItem := range dis {
			switch l {
			case 0:
				if productElasticSearch.Level1MinDiscount == 0 || disItem < productElasticSearch.Level1MinDiscount {
					productElasticSearch.Level1MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level1MaxDiscount {
					productElasticSearch.Level1MaxDiscount = disItem
				}
			case 1:
				if productElasticSearch.Level2MinDiscount == 0 || disItem < productElasticSearch.Level2MinDiscount {
					productElasticSearch.Level2MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level2MaxDiscount {
					productElasticSearch.Level2MaxDiscount = disItem
				}
			case 2:
				if productElasticSearch.Level3MinDiscount == 0 || disItem < productElasticSearch.Level3MinDiscount {
					productElasticSearch.Level3MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level3MaxDiscount {
					productElasticSearch.Level3MaxDiscount = disItem
				}
			case 3:
				if productElasticSearch.Level4MinDiscount == 0 || disItem < productElasticSearch.Level4MinDiscount {
					productElasticSearch.Level4MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level4MaxDiscount {
					productElasticSearch.Level4MaxDiscount = disItem
				}
			case 4:
				if productElasticSearch.Level5MinDiscount == 0 || disItem < productElasticSearch.Level5MinDiscount {
					productElasticSearch.Level5MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level5MaxDiscount {
					productElasticSearch.Level5MaxDiscount = disItem
				}
			case 5:
				if productElasticSearch.Level6MinDiscount == 0 || disItem < productElasticSearch.Level6MinDiscount {
					productElasticSearch.Level6MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level6MaxDiscount {
					productElasticSearch.Level6MaxDiscount = disItem
				}
			case 6:
				if productElasticSearch.Level7MinDiscount == 0 || disItem < productElasticSearch.Level7MinDiscount {
					productElasticSearch.Level7MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level7MaxDiscount {
					productElasticSearch.Level7MaxDiscount = disItem
				}
			case 7:
				if productElasticSearch.Level8MinDiscount == 0 || disItem < productElasticSearch.Level8MinDiscount {
					productElasticSearch.Level8MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level8MaxDiscount {
					productElasticSearch.Level8MaxDiscount = disItem
				}
			case 8:
				if productElasticSearch.Level9MinDiscount == 0 || disItem < productElasticSearch.Level9MinDiscount {
					productElasticSearch.Level9MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level9MaxDiscount {
					productElasticSearch.Level9MaxDiscount = disItem
				}
			case 9:
				if productElasticSearch.Level10MinDiscount == 0 || disItem < productElasticSearch.Level10MinDiscount {
					productElasticSearch.Level10MinDiscount = disItem
				}
				if disItem > productElasticSearch.Level10MaxDiscount {
					productElasticSearch.Level10MaxDiscount = disItem
				}
			}
		}
	}
	productElasticSearch.ShopName = product.ShopName
	if len(product.DetailImages) == 0 {
		productElasticSearch.DetailImages = 0
	} else {
		productElasticSearch.DetailImages = 1
	}
	if len(product.Gallery) == 0 {
		productElasticSearch.GalleryImages = 0
	} else {
		productElasticSearch.GalleryImages = 1
	}
	productElasticSearch.JushuitanBind = int(product.JushuitanBind)
	productElasticSearch.StatusLock = product.StatusLock
	productElasticSearch.Price = product.Price
	productElasticSearch.Sn = product.Sn
	productElasticSearch.UserPriceSwitch = product.UserPriceSwitch
	return
}

func Decimal(value float64) float64 {
	// 检查是否为无穷大
	if math.IsInf(value, 1) || value > math.MaxFloat64 {
		return 0
	}

	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}

func HandleUpdateData(product ProductSync) (err error, data map[string]interface{}) {
	var productElasticSearch ProductElasticSearch
	err, productElasticSearch = HandleData(product)
	if err != nil {
		return
	}
	var jsonData []byte
	jsonData, err = json.Marshal(productElasticSearch)
	if err != nil {
		return
	}
	err = json.Unmarshal(jsonData, &data)
	return
}

func GetProductListFromEs(boolQ elastic.BoolQuery, offset int, sortBy string, pageSize int) (err error, productSearch []ProductElasticSearch) {
	es, err := source.ES()
	var res *elastic.SearchResult
	res, err = es.Search("product"+common_data.GetOldProductIndex()).From(offset).Size(pageSize).Sort(sortBy, false).Query(&boolQ).Do(context.Background())
	if err != nil {
		return
	}
	//获取es搜索结果
	productSearch, err = GetSearchResult(res)
	return
}

func RunProductSyncEsV2(updatedAt *source.LocalTime, auto int, productIndex string) (err error) {
	defer source.Redis().Set(context.Background(), "productSyncStatus", "StandBy", time.Second*600)

	es, err := source.ES()
	if err != nil {
		return
	}

	var products []ProductSync
	var count int64
	err = source.DB().Model(&products).Where("updated_at >?", updatedAt).Count(&count).Error
	if err != nil {
		return
	}

	source.Redis().Set(context.Background(), "productSyncStatus", "Running", time.Second*600)

	for i := 0; i < int(count); i += 1000 {
		err = source.DB().
			Preload("Storages").
			Preload("Skus").
			Preload("Category1").
			Preload("Category2").
			Preload("Category3").
			Preload("Brand").
			Preload("Supplier").
			Preload("GatherSupply").
			Preload("SmallShopProductSale").
			Preload("AlbumRelations").
			Preload("CollectionRelations").
			//Preload("OrderItems").
			Preload("OrderItems", func(db *gorm.DB) *gorm.DB {
				return db.Joins("JOIN orders ON orders.id = order_items.order_id").Where("orders.status >= 1")
			}).
			Preload("OrderItems.Order").
			Omit("detail_images").
			Where("updated_at >?", updatedAt).Offset(i).Limit(1000).Find(&products).Error
		if err != nil {
			return
		}

		bulkRequest := es.Bulk().Index("product" + productIndex)

		var esProduct ProductElasticSearch
		var bulkCount int
		for _, product := range products {
			//if product.ID == 250348 {
			//	log.Log().Info("商品同步打印", zap.Any("product", product))
			//}
			err, esProduct = HandleData(product)
			//if product.ID == 250348 {
			//	log.Log().Info("商品同步打印1", zap.Any("es", esProduct))
			//}
			if err != nil {
				log.Log().Debug("商品同步失败,数据处理出现错误:"+err.Error(), zap.Any("id", product.ID))
				continue
			}
			doc := elastic.NewBulkIndexRequest().Id(strconv.Itoa(int(product.ID))).Doc(esProduct)
			bulkRequest = bulkRequest.Add(doc)
			bulkCount++
		}

		_, err = bulkRequest.Do(context.Background())
		if err != nil {
			fmt.Println("执行es操作出错："+err.Error(), zap.Any("err", err))
			log.Log().Debug("执行es操作出错："+err.Error(), zap.Any("err", err))
		}
		boolQ := elastic.NewBoolQuery()
		total, err := es.Count("product" + productIndex).Query(boolQ).Do(context.Background())
		if err != nil {
			log.Log().Debug("商品同步失败,es查询总数失败:" + err.Error())
		}
		log.Log().Debug("本次循环同步数量，es总数量:", zap.Any("count", bulkCount), zap.Any("total", total))
		time.Sleep(500 * time.Millisecond)

	}
	boolQ := elastic.NewBoolQuery()
	total1, err := es.Count("product" + productIndex).Query(boolQ).Do(context.Background())
	if err != nil {
		log.Log().Debug("商品同步失败,es查询总数失败1:" + err.Error())
	}
	log.Log().Debug("本次同步商品总数量，es总数量:", zap.Any("count", count), zap.Any("total", total1))

	//if auto == 1 { //删除商品
	//	var deletedCount int64
	//	err = source.DB().Model(&products).Unscoped().Where("deleted_at >?", updatedAt).Count(&deletedCount).Error
	//	if err != nil {
	//		return
	//	}
	//	for i := 0; i < int(deletedCount); i += 1000 {
	//
	//		err = source.DB().Select("id").Unscoped().Where("deleted_at >?", updatedAt).Offset(i).Limit(1000).Find(&products).Error
	//		if err != nil {
	//			return
	//		}
	//
	//		bulkRequest := es.Bulk().Index("product" + productIndex)
	//		for _, product := range products {
	//			doc := elastic.NewBulkDeleteRequest().Id(strconv.Itoa(int(product.ID)))
	//			bulkRequest = bulkRequest.Add(doc)
	//		}
	//
	//		_, err = bulkRequest.Do(context.Background())
	//		if err != nil {
	//			fmt.Println("执行es操作出错："+err.Error(), zap.Any("err", err))
	//			log.Log().Error("执行es操作出错："+err.Error(), zap.Any("err", err))
	//		}
	//	}
	//}
	return
}
