package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/chenhg5/collection"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	"product/model"
	"product/request"
	"product/response"
	"strconv"
	"strings"
	"unicode/utf8"
	levelModel "user/level"
	"user/setting"
	"yz-go/cache"
	"yz-go/common_data"
	"yz-go/component/log"
	model3 "yz-go/model"
	response2 "yz-go/response"
	yzSetting "yz-go/setting"
	"yz-go/source"
)

// GetProductCardList
//
// @function: GetProductCardList
// @description: 按条件分页获取Product Card销售信息列表
// @param: info request.ProductCardListSearch
// @return: err error, list []response.Product, total int64
// level 1-10 按照等级列表权重正序  未登录或者默认传1即可
func GetProductCardList(info request.ProductCardListSearch, level int) (err error, list []response2.ProductNew, total int64) {
	if info.MaxPrice != 0 && info.MaxPrice <= info.MinPrice {
		err = errors.New("最大金额不能小于最小金额")
		return
	}
	var profitString = "profit"         //利润筛选字段
	var priceString = "agreement_price" //批发价筛选字段
	//根据等级赋值筛选字段
	if level != 0 {
		profitString = "level_" + strconv.Itoa(level) + "_profit"
		priceString = "level_" + strconv.Itoa(level) + "_price" //不是筛选超级批发价暂时去掉这里
	}
	if info.PageSize == 0 {
		info.PageSize = 10
	}
	if info.Page == 0 {
		info.Page = 1
	}
	if info.Page > 100 {
		info.Page = 100
	}
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	db := source.DB()
	//db = db.Where("`is_display` = ?", 1)
	db = db.Where("deleted_at is NULL")
	db = db.Where("freeze = 0")

	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Should(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1), elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*")).MinimumShouldMatch("1")
	}

	filterQ := elastic.NewBoolQuery()
	if info.Ids != nil {

		IDs := make([]interface{}, len(*info.Ids))
		for index, value := range *info.Ids {
			IDs[index] = value
		}
		filterQ.Must(elastic.NewTermsQuery("id", IDs...))

	}
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
		db = db.Where("`is_display` = ?", &info.IsDisplay)
	} else {
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		db = db.Where("`is_display` = ?", 1)
	}
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))
	if info.SupplierID != 0 {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))
	}
	if info.Category1ID != 0 {
		//es = es.Query(elastic.NewMatchQuery("category_1_id", info.Category1ID))
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}

	if info.IsRecommend != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", info.IsRecommend))
	}
	if info.IsNew != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_new", info.IsNew))
	}
	if info.IsHot != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_hot", info.IsHot))
	}
	if info.IsPromotion != 0 {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", info.IsPromotion))
	}
	var userSetting *setting.SysSetting
	err, userSetting = levelModel.GetUserSetting()
	if err != nil {
		return
	}
	if info.SearchByLevelPrice == 1 {
		if userSetting.Value.DiscountType == 0 {
			if info.MinPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice))
			}
			if info.MaxPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice))
			}
		} else {
			if info.MinPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("cost_price").Gte(info.MinPrice))
			}
			if info.MaxPrice != 0 {
				filterQ.Must(elastic.NewRangeQuery("cost_price").Lte(info.MaxPrice))
			}
		}
	} else {
		if info.MinPrice != 0 {
			filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(info.MinPrice))
		}
		if info.MaxPrice != 0 {
			filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(info.MaxPrice))
		}
	}

	if info.PriceForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Gte(info.PriceForm))
	}
	if info.PriceTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + priceString + "").Lte(info.PriceTo))
	}

	if info.ProfitForm != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Gte(info.ProfitForm))
	}
	if info.ProfitTo != 0 {
		filterQ.Must(elastic.NewRangeQuery("" + profitString + "").Lte(info.ProfitTo))
	}

	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}

	//排除指定source商品
	filterQ.MustNot(elastic.NewMatchQuery("source", 109))
	// 指定供应链
	if info.GatherSupplyID != nil {
		filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
	} else {
		var specialSupplyIDs []uint
		err, specialSupplyIDs = getSupplyIDs()
		if len(specialSupplyIDs) > 0 {
			supplyIDs := make([]interface{}, len(specialSupplyIDs))
			for index, value := range specialSupplyIDs {
				supplyIDs[index] = value
			}
			filterQ.MustNot(elastic.NewTermsQuery("gather_supplier_id", supplyIDs...))
		}
	}

	boolQ.Filter(filterQ)
	sort := "sort"
	asc := false
	//调整switch与备注一致 增加一些排序兼容H5使用
	// 1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
	if info.SortBy != 0 {
		switch info.SortBy {
		case 1:
			//sort = "id"
			//asc = false
			//db = db.Order("id desc")
		case 2:
			sort = "agreement_price"
			asc = true
		case 3:
			sort = "agreement_price"
			asc = false
		case 4:
			sort = "sales"
			asc = false
		case 5:
			sort = "sales"
			asc = true
		case 6:
			sort = "created_at"
			asc = false
		case 7:
			sort = "created_at"
			asc = true
		case 8:
			sort = profitString
			asc = false
		case 9:
			sort = profitString
			asc = true
			break
		case 10:
			sort = "origin_rate"
			asc = false
			break
		case 11:
			sort = "origin_rate"
			asc = true
			break
		}
	}
	//es执行搜索
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(sort, asc).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	var ids []uint

	var productSearchs []ProductElasticSearch
	//获取es搜索结果
	productSearchs, err = GetSearchResult(res)
	var idsString string
	for _, v := range productSearchs {
		idsString += "," + strconv.Itoa(int(v.ID))
		ids = append(ids, v.ID)
	}
	if len(ids) > 0 {
		//按照es查询出来的id顺序进行排序
		idsString = idsString[2:utf8.RuneCountInString(idsString)]
	}

	db = db.Where("id in ?", ids)

	var productCards []response2.ProductNew
	//因为排序会因为in 导致乱了 所以增加order进行排序
	var percent, isDefault int
	err, percent, isDefault = levelModel.GetLevelDiscountPercent(info.UserLevelID)
	if err != nil {
		return
	}

	err = db.Preload("Skus").Preload("Supplier").Order("FIND_IN_SET(id,'" + idsString + "')").Find(&productCards).Error
	for itemKey, item := range productCards {
		productCards[itemKey].NormalPrice = item.Price
		productCards[itemKey].Level = level
		if productCards[itemKey].UserPriceSwitch == 1 {
			var calculateRes bool
			productCards[itemKey].LevelPrice, _, calculateRes = productCards[itemKey].UserPrice.GetProductLevelDiscountPrice(item.Price, info.UserLevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, productCards[itemKey].LevelPrice = levelModel.GetLevelDiscountAmount(item.Price, uint(item.ExecPrice), percent)
					if err != nil {
						return
					}
					productCards[itemKey].Price = productCards[itemKey].LevelPrice
				} else {
					productCards[itemKey].LevelPrice = item.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, productCards[itemKey].LevelPrice = levelModel.GetLevelDiscountAmount(item.Price, uint(item.ExecPrice), percent)
				if err != nil {
					return
				}
				productCards[itemKey].Price = productCards[itemKey].LevelPrice
			} else {
				productCards[itemKey].LevelPrice = item.Price
			}
		}

		productCards[itemKey].LevelProfit = item.GuidePrice - int(productCards[itemKey].LevelPrice)

		var maxOriginPrice, minOriginPrice, maxGuidePrice, maxActivityPrice, minPrice, maxPrice, minNormalPrice, maxNormalPrice uint
		for skuKey, sku := range item.Skus {
			item.Skus[skuKey].NormalPrice = sku.Price
			if minNormalPrice == 0 || item.Skus[skuKey].NormalPrice <= minNormalPrice {
				minNormalPrice = item.Skus[skuKey].NormalPrice
			}
			if item.Skus[skuKey].NormalPrice > maxNormalPrice {
				maxNormalPrice = item.Skus[skuKey].NormalPrice
			}
			if productCards[itemKey].UserPriceSwitch == 1 {
				var calculateRes bool
				item.Skus[skuKey].Price, _, calculateRes = productCards[itemKey].UserPrice.GetProductLevelDiscountPrice(item.Skus[skuKey].Price, info.UserLevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					if isDefault == 0 {
						err, item.Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(item.Skus[skuKey].Price, item.Skus[skuKey].ExecPrice, percent)
						if err != nil {
							return
						}
					}
				}
			} else {
				if isDefault == 0 {
					err, item.Skus[skuKey].Price = levelModel.GetLevelDiscountAmount(item.Skus[skuKey].Price, item.Skus[skuKey].ExecPrice, percent)
					if err != nil {
						return
					}
				}
			}
			if minPrice == 0 || item.Skus[skuKey].Price <= minPrice {
				minPrice = item.Skus[skuKey].Price
			}
			if item.Skus[skuKey].Price > maxPrice {
				maxPrice = item.Skus[skuKey].Price
			}
			if item.Skus[skuKey].OriginPrice > maxOriginPrice {
				maxOriginPrice = item.Skus[skuKey].OriginPrice
			}
			if item.Skus[skuKey].GuidePrice > maxGuidePrice {
				maxGuidePrice = item.Skus[skuKey].GuidePrice
			}
			if item.Skus[skuKey].ActivityPrice > maxActivityPrice {
				maxActivityPrice = item.Skus[skuKey].ActivityPrice
			}
			if minOriginPrice == 0 || item.Skus[skuKey].OriginPrice <= minOriginPrice {
				minOriginPrice = item.Skus[skuKey].OriginPrice
			}
		}
		productCards[itemKey].MaxOriginPrice = maxOriginPrice
		productCards[itemKey].MinOriginPrice = minOriginPrice
		productCards[itemKey].MaxGuidePrice = maxGuidePrice
		productCards[itemKey].MaxActivityPrice = maxActivityPrice
		productCards[itemKey].MinPrice = int(minPrice)
		productCards[itemKey].MaxPrice = int(maxPrice)
		productCards[itemKey].MaxNormalPrice = maxNormalPrice
		productCards[itemKey].MinNormalPrice = minNormalPrice
	}
	return err, productCards, total
}

func GetProductStorageCenterList(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevel, isApp int) (err error, list interface{}, total int64) {
	log.Log().Info("请求数据", zap.Any("params", info))
	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	// 获取用户等级排序值
	userLevelSort := levelModel.GetLevelSort(userLevel)
	// 初始化默认值
	var levelKey string
	if userLevelSort >= 1 && userLevelSort <= 10 {
		levelKey = fmt.Sprintf("level_%d", userLevelSort)
	}
	// 根据 info.Type 动态设置值
	if info.Type == "" {
		info.Type = "sort"
	} else if info.Type == "discount" {
		// 如果 userLevelSort 在 1 到 10 的范围内，动态生成列名；否则使用默认值 "sort"
		if levelKey != "" {
			info.Type = levelKey + "_max_discount"
		} else {
			info.Type = "sort"
		}
	} else if info.Type == "market_rate" {
		if levelKey != "" {
			info.Type = levelKey + "_max_profit_rate"
		}
	} else if info.Type == "gross_profit_rate" {
		if levelKey != "" {
			info.Type = levelKey + "_max_gross_rate"
		}
	}
	// 创建db
	var es *elastic.Client
	if es, err = source.ES(); err != nil {
		return
	}

	//获取boolQ
	var boolQ *elastic.BoolQuery
	if err, boolQ = GetProductStorageBoolQ(info, appLevelId, PetSupplierID, userID, userLevelSort); err != nil {
		return
	}

	if info.CollectionID != 0 {
		var collection model.Collection
		if err = source.DB().First(&collection, info.CollectionID).Error; err != nil {
			return
		}

		filterQ := elastic.NewBoolQuery()
		filterQ.Must(elastic.NewMatchQuery("is_display", 1))
		if collection.Type == 1 {
			total = int64(collection.Num)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			var relations []uint
			err = source.DB().Model(&model.CollectionProduct{}).Where("collection_id = ?", info.CollectionID).Pluck("product_id", &relations).Error
			if err != nil {
				return
			}
			relationIds := make([]interface{}, len(relations))
			for index, value := range relations {
				relationIds[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("id", relationIds...))

		} else if collection.Type == 2 {
			total = int64(collection.Filter.CategoryProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			category1Ids := make([]interface{}, len([]uint(collection.Filter.Category1ID)))
			for index, value := range []uint(collection.Filter.Category1ID) {
				category1Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_1_id", category1Ids...))

			category2Ids := make([]interface{}, len([]uint(collection.Filter.Category2ID)))
			for index, value := range []uint(collection.Filter.Category2ID) {
				category2Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_2_id", category2Ids...))

			category3Ids := make([]interface{}, len([]uint(collection.Filter.Category3ID)))
			for index, value := range []uint(collection.Filter.Category3ID) {
				category3Ids[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("category_3_id", category3Ids...))

		} else if collection.Type == 3 {
			total = int64(collection.Filter.AttributeProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			switch collection.Filter.AttributeType {
			case 1:
				filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
				break
			case 2:
				filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
				break
			case 3:
				filterQ.Must(elastic.NewMatchQuery("is_new", 1))
				break
			case 4:
				filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
				break
			}

		} else if collection.Type == 4 {
			total = int64(collection.Filter.StatisticProductNum)
			if info.PageSize*info.Page >= int(total) {
				limit = int(total) - offset
			}
			if collection.Filter.StatisticType == 1 {
				switch collection.Filter.StatisticTime {
				case 1:
					info.Type = "sales_yesterday"
				case 2:
					info.Type = "sales_last_week"
				case 3:
					info.Type = "sales_last_month"
				case 4:
					info.Type = "sales_last_7d"
				case 5:
					info.Type = "sales_last_30d"
				case 6:
					info.Type = "sales_this_year"
				default:
					info.Type = "sales"
				}
				info.Sort = false
			} else if collection.Filter.StatisticType == 2 {
				info.Type = "created_at"
				info.Sort = false
			}
		}
		boolQ.Filter(filterQ)

	}
	var realTotal int64
	realTotal, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if info.CollectionID != 0 {
		if realTotal <= total {
			total = realTotal
		}
	} else {
		total = realTotal
	}

	//es执行搜索
	log.Log().Info("导入数据的详情", zap.Any("info", boolQ))
	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	//获取es搜索结果
	var listAll []ProductElasticSearch
	listAll, err = GetSearchResult(res)
	var listCut []ProductElasticSearchCut
	if isApp == 0 {
		var listSearch []ProductElasticSearch
		err, listSearch = GetProductPrice(listAll, userID, appLevelId, info.AppID, 1, userLevelSort)
		return err, listSearch, total
	} else {
		err, listCut = ProductTransAppPrice(listAll, userID, appLevelId, info.AppID, 1)
		return err, listCut, total
	}
}

func Decimal4Places(value float64) float64 {
	if math.IsInf(value, 1) || value > math.MaxFloat64 {
		return 0
	}
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.4f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Error(err))
		return 0
	}
	return value
}

func GetProductPrice(productsElastic []ProductElasticSearch, userID uint, appLevelId uint, appID uint, hidePrice, userLevelSort int) (err error, result []ProductElasticSearch) {
	var user User
	err = source.DB().First(&user, userID).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	var application *model3.Application
	application, err = cache.GetApplicationFromCache(appID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			application.ApplicationLevel.ServerRadio = 0
		} else {
			return
		}
	}
	//整理供应商id一次性查询
	var supplierIds []uint
	for _, item := range productsElastic {
		if item.SupplierID > 0 {
			supplierIds = append(supplierIds, item.SupplierID)
		}
	}
	var suppliers []response.Supplier
	if len(supplierIds) > 0 {
		source.DB().Where("id in ?", supplierIds).Find(&suppliers) //供应商名称
	}
	for key, _ := range productsElastic {
		//对外供应商名称
		if productsElastic[key].SupplierID > 0 {
			for _, supplierItem := range suppliers {
				if supplierItem.ID == productsElastic[key].SupplierID {
					productsElastic[key].SupplierName = supplierItem.ShopName //对外名称
				}
			}
		}
		//对外供应链名称
		if productsElastic[key].GatherSupplyID > 0 {
			_, settingValue := yzSetting.GetSetting("gatherSupply" + strconv.Itoa(int(productsElastic[key].GatherSupplyID)))
			var gettingData GettingData
			_ = json.Unmarshal([]byte(settingValue), &gettingData)
			productsElastic[key].GatherSupplyName = gettingData.BaseInfo.StoreName
		}
		// 如果 userLevelSort 在 1 到 10 的范围内，动态生成列名
		//agreement_price 协议价 ✅
		//market_rate 利润率✅
		//min_profit 最小利润
		//profit 折扣 其实是利润✅
		//gross_profit_rate 毛利率✅
		switch userLevelSort {
		case 1:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level1MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level1MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level1MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level1MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level1MinPrice)
			} else if productsElastic[key].Level1MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level1MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level1MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level1MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level1MaxProfit
		case 2:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level2MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level2MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level2MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level2MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level2MinPrice)
			} else if productsElastic[key].Level2MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level2MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level2MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level2MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level2MaxProfit
		case 3:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level3MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level3MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level3MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level3MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level3MinPrice)
			} else if productsElastic[key].Level3MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level3MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level3MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level3MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level3MaxProfit
		case 4:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level4MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level4MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level4MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level4MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level4MinPrice)
			} else if productsElastic[key].Level4MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level4MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level4MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level4MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level4MaxProfit
		case 5:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level5MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level5MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level5MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level5MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level5MinPrice)
			} else if productsElastic[key].Level5MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level5MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level5MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level5MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level5MaxProfit
		case 6:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level6MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level6MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level6MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level6MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level6MinPrice)
			} else if productsElastic[key].Level6MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level6MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level6MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level6MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level6MaxProfit
		case 7:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level7MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level7MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level7MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level7MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level7MinPrice)
			} else if productsElastic[key].Level7MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level7MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level7MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level7MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level7MaxProfit
		case 8:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level8MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level8MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level8MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level8MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level8MinPrice)
			} else if productsElastic[key].Level8MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level8MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level8MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level8MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level8MaxProfit
		case 9:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level9MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level9MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level9MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level9MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level9MinPrice)
			} else if productsElastic[key].Level9MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level9MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level9MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level9MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level9MaxProfit
		case 10:
			// 利润率
			productsElastic[key].MarketRate = Decimal4Places(productsElastic[key].Level10MaxProfitRate / 100)
			// 利润率
			productsElastic[key].ProfitRate = Decimal4Places(productsElastic[key].Level10MaxProfitRate / 100)
			// 折扣
			productsElastic[key].DiscountRate = Decimal4Places(float64(productsElastic[key].Level10MaxDiscount) / 100)
			// 协议价
			if productsElastic[key].Level10MinPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level10MinPrice)
			} else if productsElastic[key].Level10MaxPrice > 0 {
				productsElastic[key].AgreementPrice = uint(productsElastic[key].Level10MaxPrice)
			}
			// 毛利率
			productsElastic[key].GrossProfitRate = Decimal4Places(productsElastic[key].Level10MaxGrossRate / 100)
			// 利润
			productsElastic[key].Profit = productsElastic[key].Level10MaxProfit
			// 最小利润
			productsElastic[key].MinProfit = productsElastic[key].Level10MaxProfit
		default:
			if float64(productsElastic[key].MarketPrice)-float64(productsElastic[key].AgreementPrice) > 0 && float64(productsElastic[key].AgreementPrice) > 0 {
				productsElastic[key].MarketRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
				productsElastic[key].GrossProfitRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].MarketPrice))
			} else {
				productsElastic[key].MarketRate = 0
				productsElastic[key].GrossProfitRate = 0
			}
			if productsElastic[key].AgreementPrice > 0 && productsElastic[key].GuidePrice > 0 && productsElastic[key].GuidePrice >= productsElastic[key].AgreementPrice {
				productsElastic[key].PromotionRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
				productsElastic[key].ProfitRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].GuidePrice))
				productsElastic[key].Profit = int(productsElastic[key].GuidePrice - productsElastic[key].AgreementPrice)
				productsElastic[key].DiscountRate = Decimal((float64(productsElastic[key].AgreementPrice) / float64(productsElastic[key].MarketPrice)) * 10)
			}
		}
		productsElastic[key].NormalAgreementPrice = productsElastic[key].AgreementPrice
		var importApps []string
		importApps = strings.Split(productsElastic[key].ImportApps, " ")
		if collection.Collect(importApps).Contains(strconv.Itoa(int(appID))) == true {
			productsElastic[key].IsImport = 1
		}
		//技术服务费
		productsElastic[key].AgreementPrice = uint(Decimal(math.Floor(float64(productsElastic[key].AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000))))
		if hidePrice == 1 {
			productsElastic[key].NormalAgreementPrice = productsElastic[key].AgreementPrice
		}
		/*if float64(productsElastic[key].MarketPrice)-float64(productsElastic[key].AgreementPrice) > 0 && float64(productsElastic[key].AgreementPrice) > 0 {
			productsElastic[key].MarketRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
			productsElastic[key].GrossProfitRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].MarketPrice))
		} else {
			productsElastic[key].MarketRate = 0
			productsElastic[key].GrossProfitRate = 0
		}*/

		if float64(productsElastic[key].MarketPrice)-float64(productsElastic[key].NormalAgreementPrice) > 0 && float64(productsElastic[key].NormalAgreementPrice) > 0 {
			productsElastic[key].NormalMarketRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].NormalAgreementPrice))
			productsElastic[key].NormalGrossProfitRate = Decimal((float64(productsElastic[key].MarketPrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].MarketPrice))
		} else {
			productsElastic[key].NormalMarketRate = 0
			productsElastic[key].NormalGrossProfitRate = 0
		}

		if productsElastic[key].AgreementPrice > 0 && productsElastic[key].GuidePrice > 0 && productsElastic[key].GuidePrice >= productsElastic[key].AgreementPrice {
			productsElastic[key].PromotionRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
			/*productsElastic[key].ProfitRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].GuidePrice))
			productsElastic[key].Profit = int(productsElastic[key].GuidePrice - productsElastic[key].AgreementPrice)
			productsElastic[key].DiscountRate = Decimal((float64(productsElastic[key].AgreementPrice) / float64(productsElastic[key].MarketPrice)) * 10)*/

		} else {
			if productsElastic[key].AgreementPrice <= 0 {
				productsElastic[key].PromotionRate = 1
			}
			if productsElastic[key].GuidePrice <= 0 {
				productsElastic[key].PromotionRate = 0
			}
			if productsElastic[key].GuidePrice < productsElastic[key].AgreementPrice {
				productsElastic[key].PromotionRate = 0
			}
		}

		if productsElastic[key].NormalAgreementPrice > 0 && productsElastic[key].GuidePrice > 0 && productsElastic[key].GuidePrice >= productsElastic[key].NormalAgreementPrice {
			productsElastic[key].NormalPromotionRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].NormalAgreementPrice))
			productsElastic[key].NormalProfitRate = Decimal((float64(productsElastic[key].GuidePrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].GuidePrice))
		} else {
			if productsElastic[key].NormalAgreementPrice <= 0 {
				productsElastic[key].NormalPromotionRate = 1
				productsElastic[key].NormalProfitRate = 1
			}
			if productsElastic[key].GuidePrice <= 0 {
				productsElastic[key].NormalPromotionRate = 0
				productsElastic[key].NormalProfitRate = 0
			}
			if productsElastic[key].GuidePrice < productsElastic[key].NormalAgreementPrice {
				productsElastic[key].NormalPromotionRate = 0
				productsElastic[key].NormalProfitRate = 0
			}
		}
		if productsElastic[key].ActivityPrice > 0 {
			if productsElastic[key].ActivityPrice > productsElastic[key].AgreementPrice && productsElastic[key].ActivityPrice > 0 && productsElastic[key].AgreementPrice > 0 {
				productsElastic[key].ActivityRate = Decimal((float64(productsElastic[key].ActivityPrice) - float64(productsElastic[key].AgreementPrice)) / float64(productsElastic[key].AgreementPrice))
			} else {
				productsElastic[key].ActivityRate = 0
			}

			if productsElastic[key].ActivityPrice > productsElastic[key].NormalAgreementPrice && productsElastic[key].ActivityPrice > 0 && productsElastic[key].NormalAgreementPrice > 0 {
				productsElastic[key].NormalActivityRate = Decimal((float64(productsElastic[key].ActivityPrice) - float64(productsElastic[key].NormalAgreementPrice)) / float64(productsElastic[key].NormalAgreementPrice))
			} else {
				productsElastic[key].NormalActivityRate = 0
			}
			productsElastic[key].CostPrice = productsElastic[key].ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		} else {
			productsElastic[key].CostPrice = productsElastic[key].AgreementPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
		}
		productsElastic[key].ImportApps = ""
		result = append(result, productsElastic[key])
	}
	return
}

func GetProductStorageBoolQ(info request.ProductStorageSearch, appLevelId uint, PetSupplierID uint, userID uint, userLevelSort int) (err error, boolQuery *elastic.BoolQuery) {
	log.Log().Info("选品参数", zap.Any("data", info))
	boolQ := elastic.NewBoolQuery()
	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}
	//db := source.DB().Model(&model.Product{})
	//db = db.Where("deleted_at is NULL")
	// 如果有条件搜索 下方会自动创建搜索语句
	//增加这个的原因是客户同时使用商品名称与供应商名称进行筛选时会得到不属于这个供应商的数据，所以增加这个条件
	//屏蔽search_title这个的原因是匹配满足两项刚好 如果三相，容易查询不到
	//单独筛选名称与供应商名称不变
	if info.Title != "" && info.SupplierName != "" {
		titleBool := elastic.NewBoolQuery().Should(
			elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1),
			elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*"),
		).MinimumShouldMatch("1")

		supplierBool := elastic.NewBoolQuery().Should(
			elastic.NewMatchPhraseQuery("supplier_name", info.SupplierName).Slop(1),
			elastic.NewMatchPhraseQuery("supplier_shop_name", info.SupplierName).Slop(1),
		).MinimumShouldMatch("1")

		boolQ.Must(titleBool, supplierBool)
	} else if info.Title != "" {
		boolQ.Should(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(1), elastic.NewWildcardQuery("title.keyword", "*"+info.Title+"*")).MinimumShouldMatch("1")
	} else if info.SupplierName != "" {
		boolQ.Should(
			elastic.NewMatchPhraseQuery("supplier_name", info.SupplierName).Slop(1),
			elastic.NewMatchPhraseQuery("supplier_shop_name", info.SupplierName).Slop(1),
		).MinimumShouldMatch("1")
	}
	if info.IsImport != nil {
		if *info.IsImport == 1 {
			boolQ.Must(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		} else {
			boolQ.MustNot(elastic.NewMatchQuery("import_apps", strconv.Itoa(int(info.AppID))).Analyzer("whitespace"))
		}

	}
	if info.SupplyLineId != "" {
		boolQ.MustNot(elastic.NewWildcardQuery("supply_line.keyword", "*"+info.SupplyLineId+"*"))
		//	boolQ.MustNot(elastic.NewTermQuery("supply_line.keyword", info.SupplyLineId))

	}
	filterQ := elastic.NewBoolQuery()
	if appLevelId > 0 {
		var ids []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", appLevelId).Pluck("gather_supply_id", &ids).Error
		if err != nil {
			return
		}
		var sourceIds []uint
		err = source.DB().Model(&GatherSupplyApplicationLevel{}).Where("application_level_id = ?", appLevelId).Where("gather_supply_id in ?", ids).Pluck("source_id", &sourceIds).Error
		if err != nil {
			return
		}
		ids = append(ids, 0)
		status := make([]interface{}, len(ids))
		for index, value := range ids {
			status[index] = value
		}

		filterQ.Must(elastic.NewTermsQuery("gather_supplier_id", status...))
		//中台和云仓的商品不加限制
		sourceIds = append(sourceIds, 0)
		sourceIds = append(sourceIds, 98)
		statusS := make([]interface{}, len(sourceIds))
		for index, value := range sourceIds {
			statusS[index] = value
		}

		filterQ.Must(elastic.NewTermsQuery("source", statusS...))
		log.Log().Info("GetProductStorageInfoList ", zap.Any("info", sourceIds))
	}
	//千人千价 只查询指定商品
	var user Users
	err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	var thousandsProductIds []uint
	if len(user.ThousandsPrices.Products) > 0 && user.ThousandsPrices.Status == 1 && user.ThousandsPrices.FilterImport == 1 {
		for _, product := range user.ThousandsPrices.Products {
			thousandsProductIds = append(thousandsProductIds, product.ID)
		}
	}
	if len(thousandsProductIds) > 0 {
		thousandsStatus := make([]interface{}, len(thousandsProductIds))
		for indext, valuet := range thousandsProductIds {
			thousandsStatus[indext] = valuet
		}
		filterQ.Must(elastic.NewTermsQuery("id", thousandsStatus...))
	}
	goodsIds := make([]interface{}, len(info.GoodsIds))
	for index, value := range info.GoodsIds {
		goodsIds[index] = value
	}
	if len(info.GoodsIds) > 0 {
		filterQ.Must(elastic.NewTermsQuery("id", goodsIds...))
	}
	filterQ.MustNot(elastic.NewMatchQuery("source", 109))
	var applicationSetting ApplicationSetting
	err, applicationSetting = GetApplicationSetting()
	if err != nil {
		return
	}
	if applicationSetting.Value.MultiPetSupplier == 1 {
		var petSupplierIDs []uint
		err = source.DB().Model(&ApplicationPetSupplier{}).Where("application_id = ?", info.AppID).Pluck("pet_supplier_id", &petSupplierIDs).Error
		if err != nil {
			return
		}
		if len(petSupplierIDs) > 0 {
			petSuppliers := make([]interface{}, len(petSupplierIDs))
			for index, value := range petSupplierIDs {
				petSuppliers[index] = value
			}
			filterQ.Must(elastic.NewTermsQuery("supplier_id", petSuppliers...))

		} else if info.SupplierID != nil {

			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))

		}
	} else {
		if PetSupplierID != 0 {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", PetSupplierID))

		} else if info.SupplierID != nil {

			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.SupplierID))

			//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		}
	}
	var banSupplierIds []uint
	err = source.DB().Model(response.Supplier{}).Where("is_storage = 1").Pluck("id", &banSupplierIds).Error
	if err != nil {
		return
	}
	if len(banSupplierIds) > 0 {
		banSuppliers := make([]interface{}, len(banSupplierIds))
		for index, value := range banSupplierIds {
			banSuppliers[index] = value
		}
		filterQ.MustNot(elastic.NewTermsQuery("supplier_id", banSuppliers...))
	}
	if info.Category1ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_1_id", info.Category1ID))

	}
	if info.Category2ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_2_id", info.Category2ID))
	}
	if info.Category3ID != 0 {
		filterQ.Must(elastic.NewMatchQuery("category_3_id", info.Category3ID))
	}
	if info.Source != nil {
		filterQ.Must(elastic.NewMatchQuery("source", &info.Source))
	} else {
		//filterQ.MustNot(elastic.NewMatchQuery("source", 99))
	}
	if info.IsTaxLogo != nil {
		// 等于3的时候不用查询，没有‘未设置’状态（韦平原型设计的有‘未设置’状态）
		if *info.IsTaxLogo == 1 || *info.IsTaxLogo == 2 {
			filterQ.Must(elastic.NewMatchQuery("is_tax_logo", info.IsTaxLogo))
		}
	}
	if info.TaxRate != nil {
		orQuery := elastic.NewBoolQuery().Should(
			elastic.NewMatchQuery("tax_rate", info.TaxRate),
			elastic.NewTermQuery("sku_tax_rates", info.TaxRate),
		)
		filterQ.Must(orQuery)
	}
	if info.AlbumId != nil {
		filterQ.Must(elastic.NewTermQuery("album_ids", info.AlbumId))
	}
	if info.CollectId != nil {
		// 查询商品专辑
		var collectionModel model.CollectionModel
		source.DB().Where("id = ?", info.CollectId).First(&collectionModel)

		switch collectionModel.Type {
		// 根据商品专辑设置 筛选绑定商品
		case 1:
			filterQ.Must(elastic.NewTermQuery("collect_ids", info.CollectId))
		// 根据商品专辑设置 按分类筛选商品
		case 2:
			// 将 Category1ID 转换为 []interface{}
			category1ID := make([]interface{}, len(collectionModel.Filter.Category1ID))
			for i, id := range collectionModel.Filter.Category1ID {
				category1ID[i] = id
			}
			category2ID := make([]interface{}, len(collectionModel.Filter.Category2ID))
			for i, id := range collectionModel.Filter.Category2ID {
				category2ID[i] = id
			}
			category3ID := make([]interface{}, len(collectionModel.Filter.Category3ID))
			for i, id := range collectionModel.Filter.Category3ID {
				category3ID[i] = id
			}
			filterQ.Must(elastic.NewTermsQuery("category_1_id", category1ID...))
			filterQ.Must(elastic.NewTermsQuery("category_2_id", category2ID...))
			filterQ.Must(elastic.NewTermsQuery("category_3_id", category3ID...))
		// 根据商品专辑设置 按营销属性筛选商品
		case 3:
			switch collectionModel.Filter.AttributeType {
			case 1:
				filterQ.Must(elastic.NewMatchQuery("is_hot", 1))
			//case 2:
			//	filterQ.Must(elastic.NewMatchQuery("is_recommend", 1))
			case 3:
				filterQ.Must(elastic.NewMatchQuery("is_new", 1))
			case 4:
				filterQ.Must(elastic.NewMatchQuery("is_promotion", 1))
			}
		// 根据商品专辑设置 按商品数据筛选商品
		case 4:
		}
	}
	filterQ.Must(elastic.NewMatchQuery("is_plugin", 0))
	// 指定供应链
	if info.GatherSupplyID != nil {
		var zer = uint(0)
		var zero *uint
		zero = &zer
		if info.GatherSupplyID == zero {
			filterQ.Must(elastic.NewMatchQuery("supplier_id", info.GatherSupplyID))
			filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))

		} else {
			filterQ.Must(elastic.NewMatchQuery("gather_supplier_id", info.GatherSupplyID))
		}
	} else {
		if supply.ID != 0 {
			filterQ.MustNot(elastic.NewMatchQuery("gather_supplier_id", supply.ID))
		}
	}
	if info.IsRecommend != nil {
		filterQ.Must(elastic.NewMatchQuery("is_recommend", &info.IsRecommend))
	}
	if info.IsBill != nil {
		filterQ.Must(elastic.NewMatchQuery("is_bill", &info.IsBill))
	}
	if info.IsNew != nil {
		filterQ.Must(elastic.NewMatchQuery("is_new", &info.IsNew))
	}
	if info.IsHot != nil {
		filterQ.Must(elastic.NewMatchQuery("is_hot", &info.IsHot))
	}
	if info.IsPromotion != nil {
		filterQ.Must(elastic.NewMatchQuery("is_promotion", &info.IsPromotion))
	}
	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
	}
	if info.FreightType == 2 {
		filterQ.MustNot(elastic.NewMatchQuery("freight_type", 3))
	}
	if info.FreightType == 1 {
		filterQ.Must(elastic.NewMatchQuery("freight_type", 3))
	}
	if info.AgreementPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Gte(*info.AgreementPrice.From * 100))
	}
	if info.AgreementPrice.To != nil && *info.AgreementPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("agreement_price").Lte(*info.AgreementPrice.To * 100))
	}
	if info.ActivityPrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Gte(*info.ActivityPrice.From * 100))
	}
	if info.ActivityPrice.To != nil && *info.ActivityPrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_price").Lte(*info.ActivityPrice.To * 100))
	}
	if info.GuidePrice.From != nil {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Gte(*info.GuidePrice.From * 100))
	}
	if info.GuidePrice.To != nil && *info.GuidePrice.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("guide_price").Lte(*info.GuidePrice.To * 100))
	}
	// 定义默认值
	grossRateColumn := "gross_profit_rate"
	marketRateColumn := "market_rate"
	levelMinDiscountColumn := "level_1_min_discount"
	levelMaxDiscountColumn := "level_1_max_discount"
	// 如果 userLevelSort 在 1 到 10 的范围内，动态生成列名
	if userLevelSort >= 1 && userLevelSort <= 10 {
		levelKey := fmt.Sprintf("level_%d", userLevelSort)
		grossRateColumn = levelKey + "_gross_rate"
		marketRateColumn = levelKey + "_profit_rate"
		levelMinDiscountColumn = levelKey + "_min_discount"
		levelMaxDiscountColumn = levelKey + "_max_discount"
	}
	// 折扣范围查询
	if info.Discount.From != nil || (info.Discount.To != nil && *info.Discount.To > 0) {
		// 8000/10000 = 0.8 * 1000 = 800
		// 构建两个独立的范围查询（分别检查 min 和 max 是否在目标区间）
		minInRange := buildRangeQueryForDiscount(levelMinDiscountColumn, info.Discount.From, info.Discount.To)
		maxInRange := buildRangeQueryForDiscount(levelMaxDiscountColumn, info.Discount.From, info.Discount.To)
		// 用 OR 关系组合（满足任意一个即可）
		filterQ.Should(minInRange, maxInRange)
		// 显式声明至少满足一个条件（其实 Should 默认就是 OR）
		filterQ.MinimumShouldMatch("1")
	}

	if info.ProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Gte(*info.ProfitRate.From))
	}
	if info.ProfitRate.To != nil && *info.ProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit_rate").Lte(*info.ProfitRate.To))
	}
	if info.GrossProfitRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Gte(*info.GrossProfitRate.From))
	}
	if info.GrossProfitRate.To != nil && *info.GrossProfitRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(grossRateColumn).Lte(*info.GrossProfitRate.To))
	}
	if info.MarketRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Gte(*info.MarketRate.From))
	}
	if info.MarketRate.To != nil && *info.MarketRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery(marketRateColumn).Lte(*info.MarketRate.To))
	}
	if info.Profit.From != nil {
		filterQ.Must(elastic.NewRangeQuery("profit").Gte(*info.Profit.From * 100))
	}
	if info.Profit.To != nil && *info.Profit.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("profit").Lte(*info.Profit.To * 100))
	}
	if info.OriginRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("min_origin_rate").Gte(Decimal(float64(*info.OriginRate.From))))
	}
	if info.OriginRate.To != nil && *info.OriginRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("max_origin_rate").Lte(Decimal(float64(*info.OriginRate.To))))
	}
	if info.ActivityRate.From != nil {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Gte(float64(*info.ActivityRate.From)))
	}
	if info.ActivityRate.To != nil && *info.ActivityRate.To > 0 {
		filterQ.Must(elastic.NewRangeQuery("activity_rate").Lte(float64(*info.ActivityRate.To)))

	}
	boolQ.Filter(filterQ)
	return err, boolQ
}

// buildDiscountRangeQuery 构建范围查询
func buildRangeQueryForDiscount(field string, from, to *float64) *elastic.RangeQuery {
	rangeQ := elastic.NewRangeQuery(field)
	if from != nil {
		rangeQ.Gte(*from * 100)
	}
	if to != nil && *to > 0 {
		rangeQ.Lte(*to * 100)
	}
	return rangeQ
}
