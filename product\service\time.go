package service

import "time"

//时间范围判断应包含结束时间

func InTimeRange(t, start, end time.Time) bool {
	return !t.Before(start) && !t.After(end)
}

func GetYesterdayRange(now time.Time) (time.Time, time.Time) {
	yesterday := now.AddDate(0, 0, -1)

	startOfYesterday := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, now.Location())
	endOfYesterday := startOfYesterday.Add(24*time.Hour - time.Nanosecond)

	return startOfYesterday, endOfYesterday
}

func GetLastWeekRange(now time.Time) (time.Time, time.Time) {
	// 获取当前是星期几（Go: 周日=0，周一=1，…）
	weekday := int(now.Weekday())
	if weekday == 0 {
		weekday = 7 // 调整：把周日当作 7，方便统一处理
	}

	// 计算本周一
	thisMonday := time.Date(now.Year(), now.Month(), now.Day()-weekday+1, 0, 0, 0, 0, now.Location())

	// 上周一 = 本周一 - 7天
	lastMonday := thisMonday.AddDate(0, 0, -7)
	// 上周日 = 本周一 - 1天，设置为当天结束（23:59:59.999）
	lastSunday := thisMonday.AddDate(0, 0, -1).Add(time.Hour*23 + time.Minute*59 + time.Second*59 + time.Millisecond*999)

	return lastMonday, lastSunday
}

func GetLastMonthRange(now time.Time) (time.Time, time.Time) {
	// 获取当前月的第一天
	currentMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 上个月的第一天
	lastMonthStart := currentMonthStart.AddDate(0, -1, 0)

	// 当前月第一天减一纳秒即是上个月的最后时刻
	lastMonthEnd := currentMonthStart.Add(-time.Nanosecond)

	return lastMonthStart, lastMonthEnd
}

// 获取“过去7天”的开始和结束时间（不包含今天）

func GetPast7DaysRange(now time.Time) (time.Time, time.Time) {
	// 今天的 0 点
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// 开始时间：7 天前的 0 点
	start := todayStart.AddDate(0, 0, -7)

	// 结束时间：今天 0 点的前一纳秒（即昨天的 23:59:59.999999999）
	end := todayStart.Add(-time.Nanosecond)

	return start, end
}

// 获取“过去30天”的开始和结束时间（不包含今天）

func GetPast30DaysRange(now time.Time) (time.Time, time.Time) {
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	start := todayStart.AddDate(0, 0, -30)
	end := todayStart.Add(-time.Nanosecond)

	return start, end
}

// 获取当前年的开始时间

func GetThisYearStart(now time.Time) time.Time {
	return time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
}
