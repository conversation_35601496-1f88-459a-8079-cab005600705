import operation from "@/utils/operation";
import PricePopup from '../components/pricePopup.vue'
import PriceBatchPopup from '../components/priceBatchPopup.vue'
import { mapGetters } from 'vuex';
import scrollPaginationMixin from '@/mixin/scrollPaginationMixin.js';
import skeletonGoods from '@/components/mGoods/skeletonGoods.vue';

export default {
  name: "albumDetail",
  mixins: [scrollPaginationMixin],
  components: { 
    PricePopup,
    PriceBatchPopup,
    skeletonGoods
  },
  computed: {
    ...mapGetters('home', ['getFramework']),
  },
  data() {
    return {
      id: '',
      albumData:{
        covers:[
          {src:""}
        ],
      },
      albumList: [],
      //改价
      pricePopupShow: false,
      editPopupShow: false,
      priceBatchShow: false,
      product: {}, // 单条商品数据源
      price: '', // 单规格销售价
      headTitle: '商品改价', // Popup标题
      pushChannelsShow: false,
      checkboxValue: [],
      isTip: 0, // 0-关闭提示窗，1-开启提示窗
      isExist: true,
      ratioValues: {},
    }
  },
  mounted() {
    this.init()
    //this.isLogin()
    this.getPriceInit() 
    this.getExist()
    console.log(this.getFramework,this.$ls.getUserId());
    
  },
  methods: {
    async addSelection() {
      let res = await this.$get("/application/getApplicationApply");
        if (res.code === 0) {
          let data = {
            id: parseInt(this.$route.query.productId)
          }
          let that = this
          this.$post('/productAlbumApi/addAlbumProductStorage', data).then(i => {
            if (i.code == 0) {
              // that.initGoodsInfo()
              that.$message.success('加入成功')
            } else {
              that.$message.error(i.msg)
            }
          }).catch(() => {})
        }else{
            this.$router.push('/personalCenter/apiProcurement')
        }
    },
    /* pagination(val) {
      this.page = val.page
      this.init()
    }, */
    getExist(){
      let params = {
        id: parseInt(this.$route.query.productId),
        page: this.page,
        pageSize: this.pageSize
      }
      this.$post("/smallShop/album/exist", params).then(res=> {
        if (res.code == 0) {
          this.isExist = res.data.exist
        }
      }).catch(function (res) {
          console.log(res);
      });
    },
    async exportAlbum() {
        const {code,data,msg} = await this.$post("/productAlbumApi/exportAlbumProducts", {aid: parseInt(this.$route.query.productId)})
        if(code === 0){
            let link = this.$path + '/' + data.link
            this.$fn.exportTable(link)
        }else{
            this.$message.error(msg)
        }
    },
    loadNextPage() {
      this.page++;
      this.init(this.page, this.pageSize ,true)
  },
    init(page = this.page,pageSize = this.pageSize, addList = false) {
      this.isLoading = true
      let para = {
        id: parseInt(this.$route.query.productId),
        page,
        pageSize
      }
      this.$post("/productAlbumApi/findAlbum",para).then(res=> {
        this.isLoading = false
        if (res.code == 0) {
          this.albumData = res.data.album
          this.albumList = addList === true ? this.albumList.concat(res.data.pageResult.list) : res.data.pageResult.list || []
          // this.albumId = res.data.pageResult.list.id
          // console.log('this.albumId',this.albumId);
          this.total = res.data.pageResult.total
          this.hasMore = res.data.pageResult.total > this.albumList.length;

          this.updateSeo(this.albumData.name,this.albumData.name,true)
        } else {
            this.$message.error(res.msg)
        }
      }).catch(function (res) {
        this.isLoading = false
          console.log(res);
      });
    },
    getPriceInit() {
      const api = '/smallShop/setting/getShopProductSetting'
      this.$get(api).then(res => {
          if (res.code === 0) {
              // this.server_ratio = res.data.server_ratio / 100
              // res.data.setting.value.activity_price_ratio = res.data.setting.value.activity_price_ratio < 100 ? 100 : res.data.setting.value.activity_price_ratio
              // res.data.setting.value.agreement_price_ratio = res.data.setting.value.agreement_price_ratio < 100 ? 100 : res.data.setting.value.agreement_price_ratio
              // res.data.setting.value.guide_price_ratio = res.data.setting.value.guide_price_ratio < 100 ? 100 : res.data.setting.value.guide_price_ratio
              // res.data.setting.value.origin_price_ratio = res.data.setting.value.origin_price_ratio < 100 ? 100 : res.data.setting.value.origin_price_ratio
              // Object.assign(this.formDataPrice, res.data.setting.value)
              this.isTip = res.data.setting.value.tip
              this.ratioValues = res.data.setting.value
              // this.settingId = res.data.setting.id
              // this.takeGroup
              // let value = res.data.setting.value
              // if(value.is_activity_price){
              //     this.takeGroup = 'is_activity_price'
              // }
              // if(value.is_agreement_price){
              //     this.takeGroup = 'is_agreement_price'
              // }
              // if(value.is_guide_price){
              //     this.takeGroup = 'is_guide_price'
              // }
              // if(value.is_origin_price){
              //     this.takeGroup = 'is_origin_price'
              // }
          } else {
              this.$message.error(res.msg)
          }
      }).catch(Error => {
          console.log(Error)
      })
    },
    formatPrice(num){
      switch (num) {
        case 0:
          return 0
        default:
          const str = num.toString()
          const front = str.substring(0, str.length - 2) ? str.substring(0, str.length - 2) : 0
          const end = str.substring(str.length - 2)
          const formatPrice = front + '.' + end
          return formatPrice
      }
    },
    // 利润率 = ( 指导价- (超级)批发价 ) / (超级)批发价
    profitPercent(max,min){
      const differ = operation.accSub(max, min)
      const profit = operation.accDiv(differ, min)
      const percent = (((profit * 10000))/100.00).toFixed(2) + '%'
      return percent
    },
    // 超级批发价
    superPrice(num){
      const price = this.$fn.superTradePrice(num)
      return this.formatPrice(price)
    },
    //是否登录
    // isLogin(){
    //   if (this.$ls.getUserId()){
    //     console.log("已经登录", this.$ls.getUserId())
    //   } else {
    //     console.log("还没登录", this.$ls.getUserId())
    //   }
    // }

    onDeleteAlbum(){
      this.$confirm("该专辑已移出小商店，在专辑列表不再显示，但是原专辑商品已经保留在小店中！", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        let params = {
          album_ids: []
        }
        params.album_ids[0] = this.albumData.id
        this.$del("/smallShop/album/removeAlbum", params).then(res => {
            if (res.code === 0) {
                this.$message.success(res.msg)
                this.init()
                this.getPriceInit() 
                this.getExist()
            } else {
                this.$message.error(res.msg)
            }
        }).catch(function (res) {
            console.log(res);
        })
      }).catch(() => {
      });
    },
    // 以下：多规格改价
    // 打开批量改价popup（商品管理页面，目前在用）
    onOpenPriceBatch() {
      this.priceBatchShow = true
        // if(this.checkboxValue.length === 0) {
        //     this.$message.error('请选择需要批量改价的商品')
        // } else {
        //     this.priceBatchShow = true
        // }
    },
    onOpenPromptBatch() {
      this.priceBatchShow = true
    },
    // 确认批量改价popup（商品管理页面，目前在用）
    onConfirmPriceBatch(p) {
        this.onAddProductBatch(p)
        this.onClosePriceBatch()
        
    },
    // 提交批量改价（商品管理页面，目前在用）
    onAddProductBatch(params) {
        // const api = '/smallShop/product/batchChangePrice'
        const api = '/smallShop/album/importAlbum'
        // this.ids = this.albumList.map(item => item.id)
        let newParams = {
            // product_ids: params.product_ids,
            album_ids: [],
            price_proportion: params.price_proportion,
            price_type: params.price_type,
        }
        newParams.album_ids[0] = this.albumData.id
        this.$post(api, newParams, true)
            .then(res => {
                if (res.code === 0) {
                    // this.getProductServe()
                    this.$message.success(res.msg)
                    this.init()
                    this.getPriceInit() 
                    this.getExist()
                } else {
                    this.$message.error(res.msg)
                }
            })
            .catch(Error => {
                console.log(Error)
            })
    },
    onClosePriceBatch(flg = '') {
      // if (!flg) {
      //     this.pricePopupShow = false
      //     this.editPopupShow = false
      // } else {
      //     this[flg] = false
      // }
      this.priceBatchShow = false
    },

    // 以下：单规格改价
    // 单规格改价popup
    onOpenEditPopup() {
        this.editPopupShow = true
    },
    // 确认商品管理popup
    onConfirmPricePopup(p) {
        this.onAddProduct(p)
        this.onClosePricePopup()
    },
    // 确认批量改价popup
    onConfirmEditPopup(p) {
        this.onChangePrice(p)
        this.onCloseEditPopup()
    },
    // 关闭商品管理popup
    onClosePricePopup(flg = '') {
        if (!flg) {
            this.pricePopupShow = false
            this.editPopupShow = false
        } else {
            this[flg] = false
        }
    },
    // 关闭批量改价popup
    onCloseEditPopup() {
        this.editPopupShow = false
    },
    // 提交选品
    onAddProduct(params) {
      const api = '/smallShop/product/changePrice'
      let newParams = {
          product_id: params.product_ids[0],
          price_proportion: params.price_proportion,
          price_type: params.price_type,
      }
      this.$post(api, newParams, true)
      .then(res => {
      if (res.code === 0) {
          // this.getProductServe()
      } else {
          this.$message.error(res.msg)
      }
      })
      .catch(Error => {
        console.log(Error)
      })
    },
    // 打开商品管理popup
    onOpenPricePopup(item){
        this.pricePopupShow = true      
        this.product = item
        // this.$refs.pricePopup.price_proportion =
        //     item.small_shop_product_sale.price_proportion / 100
        // this.$refs.pricePopup.price_type = item.small_shop_product_sale.price_type
        if(item?.small_shop_product_sale?.price_proportion){
            this.$refs.pricePopup.price_proportion =
                item.small_shop_product_sale.price_proportion / 100
        }
        if(item?.small_shop_product_sale?.price_type){
            this.$refs.pricePopup.price_type = item.small_shop_product_sale.price_type
        }
        this.$nextTick(() => {
            this.$refs.pricePopup.computedSellingPrice()
        })
    },



  },
}