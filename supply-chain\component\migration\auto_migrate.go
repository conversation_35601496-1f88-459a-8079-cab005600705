package migration

import (
	adModel "ad/model"
	afterSalesModel "after-sales/model"
	aliopen "ali-open/model"
	alijx "ali-selected/model"
	applicationModel "application/model"
	areaAgencyModel "area-agency/model"
	bynSupplyModel "byn-supply/model"
	categoryModel "category/model"
	cloudModel "cloud/model"
	commentModel "comment/model"
	curriculum "course-distribution/model"
	jhcps "cps/model"
	dahangErpModel "dahang-erp/model"
	distributorToolModel "distributor-tool/model"
	distributorModel "distributor/model"
	cps "douyin-cps/model"
	model2 "dwd-supply/model"
	model7 "ec-cps-ctrl/model"
	eventDistributionModel "event-distribution/model"
	favoriteModel "favorite/model"
	financeModel "finance/model"
	"fmt"
	fuluSupplyModel "fulu-supply/model"
	"gin-vue-admin/admin/model"
	"go.uber.org/zap"
	gongmall "gongmall/model"
	guangdian "guangdian/model"
	hbsk "hbsk/model"
	heheModel "hehe-supply/model"
	institutionModel "institution/model"
	jd "jd-supply/model"
	model5 "jushuitan-supply/model"
	model3 "jushuitan/model"
	knowledgeBaseModel "knowledge-base/model"
	kunsheng "kunsheng-supply/model"
	leaseModel "lease/model"
	lianlian "lianlian/model"
	localLifeModel "local-life/model"
	material "material-distribute/model"
	meituanModel "meituan-distributor/model"
	merchantModel "merchant/model"
	notificationModel "notification/model"
	operationModel "operation/model"
	orderExportModel "order-export/model"
	orderModel "order/model"
	paymentModel "payment/model"
	pluginModel "plugin/model"
	postModel "poster/model"
	productAlbumModel "product-album/model"
	productModel "product/model"
	supplyModel "public-supply/model"
	purchase "purchase-account/model"
	cinemaTicket "race-cinema-ticket/model"
	regionsMatch "region-match/models"
	regionModel "region/model"
	salesModel "sales/model"
	script "script-distribute/model"
	serviceProviderSystemModel "service-provider-system/model"
	shareliveModel "share-live/model"
	addressModel "shipping/address"
	shippingModel "shipping/model"
	shopModel "shop/model"
	cartModel "shopping-cart/model"
	smallShopVideoModel "small-shop-video/model"
	smallShopModel "small-shop/model"
	"sort"
	supplierModel "supplier/model"
	homeModel "supply-chain/model"
	surfaceSingleModel "surface-single/model"
	"sync"
	"wdt-supply/goods"
	yjf "yijifen/model"

	aggregatedPaymentSplitSettlementSupply "aggregated-payment-split-settlement/model"
	aiasstantModel "ai-assistant/model"
	douyinGroupSupply "douyin-group/model"
	guanaitongModel "guanaitong-supply/model"
	jdVopModel "jd-vop-supply/model"
	maiGerModel "maiger-supply/model"
	shamaSupply "shama-supply/model"
	szbaoSupplyModel "szbao-supply/model"
	model6 "thousands-prices/model"
	tianmaSupply "tianma-supply/model"
	tradeModel "trade/model"
	cakeModel "uncle-cake/model"
	userEquityModel "user-equity/model"
	userPrice "user-price-auth/model"
	userModel "user/model"
	video "video-distribute/model"
	model4 "virtual-stock/model"
	water "water-machine/model"
	wdtModel "wdt-supply/model"
	wechatofficialModel "wechatofficial/model"
	wps "weipinshang/model"
	yyt "yiyatong/model"
	YouxuanModel "youxuan-supply/model"
	yunzhongheSupply "yunzhonghe-supply/model"
	"yz-go/component/log"
	"yz-go/source"
)

func GetMigrates() []func() (err error) {
	migrates := []func() (err error){

		guangdian.Migrate,
		jd.Migrate,
		cakeModel.Migrate,
		curriculum.Migrate,
		yyt.Migrate,
		yjf.Migrate,
		video.Migrate,
		wps.Migrate,
		material.Migrate,
		userPrice.Migrate,
		gongmall.Migrate,
		script.Migrate,
		hbsk.Migrate,
		kunsheng.Migrate,
		productModel.Migrate,
		commentModel.Migrate,
		categoryModel.Migrate,
		lianlian.Migrate,
		afterSalesModel.Migrate,
		applicationModel.Migrate,
		cinemaTicket.Migrate,
		pluginModel.Migrate,
		financeModel.Migrate,
		alijx.Migrate,
		aliopen.Migrate,
		cartModel.Migrate,
		supplierModel.Migrate,
		regionModel.Migrate,
		regionsMatch.Migrate,
		adModel.Migrate,
		salesModel.Migrate,
		favoriteModel.Migrate,
		tradeModel.Migrate,
		homeModel.Migrate,
		notificationModel.Migrate,
		operationModel.Migrate,
		addressModel.Migrate,
		paymentModel.Migrate,
		supplyModel.Migrate,
		shippingModel.Migrate,
		orderModel.Migrate,
		shopModel.Migrate,
		wechatofficialModel.Migrate,
		areaAgencyModel.Migrate,
		productAlbumModel.Migrate,
		institutionModel.Migrate,
		localLifeModel.Migrate,
		postModel.Migrate,
		orderExportModel.Migrate,
		smallShopModel.Migrate,
		smallShopVideoModel.Migrate,
		purchase.Migrate,
		merchantModel.Migrate,
		distributorModel.Migrate,
		distributorToolModel.Migrate,
		surfaceSingleModel.Migrate,
		szbaoSupplyModel.Migrate,
		bynSupplyModel.Migrate,
		fuluSupplyModel.Migrate,
		userEquityModel.Migrate,
		cloudModel.Migrate,
		model2.Migrate,
		heheModel.Migrate,
		model3.Migrate,
		model5.Migrate,
		model4.Migrate,
		water.Migrate,
		knowledgeBaseModel.Migrate,
		cps.Migrate,
		jhcps.Migrate,
		leaseModel.Migrate,
		shareliveModel.Migrate,
		dahangErpModel.Migrate,
		userModel.Migrate,
		serviceProviderSystemModel.Migrate,
		YouxuanModel.Migrate,
		meituanModel.Migrate,
		eventDistributionModel.Migrate,
		model6.Migrate,
		yunzhongheSupply.Migrate,
		tianmaSupply.Migrate,
		shamaSupply.Migrate,
		douyinGroupSupply.Migrate,
		jdVopModel.Migrate,
		maiGerModel.Migrate,
		guanaitongModel.Migrate,
		aiasstantModel.Migrate,
		model7.Migrate,
		aggregatedPaymentSplitSettlementSupply.Migrate,
		wdtModel.Migrate,
		goods.WdtGoodsInit,
	}
	return migrates
}
func ModuleMysqlTables() (errs []error) {
	var err error
	model.MysqlTables(source.DB())
	migrates := GetMigrates()
	var wg sync.WaitGroup
	errChan := make(chan error, len(migrates)) // 创建一个错误通道

	for i, v := range migrates {
		migrate := v
		migrateIndex := i
		wg.Add(1)
		go func(wg *sync.WaitGroup) {
			defer wg.Done()
			err = migrate()
			if err != nil {
				log.Log().Info(fmt.Sprintf("迁移出错%d", migrateIndex), zap.Any("err", err))
				log.Log().Error(fmt.Sprintf("迁移出错%d", migrateIndex), zap.Any("err", err))
				errChan <- err // 发送错误到通道
			}
			log.Log().Info(fmt.Sprintf("迁移完成%d", migrateIndex))
		}(&wg)
	}
	wg.Wait()
	close(errChan) // 关闭通道
	// 收集所有错误
	for aErr := range errChan {
		if aErr != nil {
			errs = append(errs, aErr)
		}
	}

	sort.Sort(sort.Reverse(model.Menu(model.GVA_MENUS)))
	sort.Sort(sort.Reverse(model.Menu(model.GVA_SUPPLY_MENUS)))
	log.Log().Debug("迁移菜单项", zap.Any("GVA_MENUS", model.GVA_MENUS))
	log.Log().Debug("迁移菜单项", zap.Any("GVA_SUPPLY_MENUS", model.GVA_SUPPLY_MENUS))
	log.Log().Info("执行后台按钮排序")

	return
}
