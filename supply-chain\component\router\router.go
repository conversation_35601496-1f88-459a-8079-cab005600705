package router

import (
	adRoute "ad/route"
	afterSalesRouter "after-sales/route"
	afterSalesRoute "after-sales/router"
	route12 "ai-assistant/route"
	aliopen "ali-open/route"
	route6 "ali-selected/route"
	appMiddleware "application/middleware"
	applicationRoute "application/router"
	areaAgencyRouter "area-agency/route"
	bynRoute "byn-supply/route"
	categoryRoute "category/route"
	cloudRoute "cloud/route"
	commentRoute "comment/route"
	course "course-distribution/route"
	jhcps "cps/route"
	distributorToolRouter "distributor-tool/route"
	distributorRouter "distributor/route"
	cps "douyin-cps/router"
	eventDistributionRoute "event-distribution/route"
	favoriteRoute "favorite/route"
	financeRoute "finance/route"
	fuluRoute "fulu-supply/route"
	gatherRoute "gather-supply/route"
	gather_route "gather-supply/route"
	"github.com/gin-gonic/gin"
	gongmall "gongmall/route"
	router5 "guanaitong-supply/route"
	guangdianRoute "guangdian/route"
	hbsk "hbsk/router"
	installRoute "install/route"
	institutionRoute "institution/route"
	jdVopSupplyRoute "jd-vop-supply/route"
	route8 "jushuitan-supply/route"
	route4 "jushuitan/route"
	route3 "knowledge-base/route"
	kunsheng "kunsheng-supply/route"
	lakalarouter "lakala/route"
	leaseRouter "lease/route"
	"lianlian/route"
	localLifeRoute "local-life/route"
	materialDistribute "material-distribute/router"
	router3 "meituan-distributor/router"
	merchantRouter "merchant/route"
	route7 "monitor/route"
	notificationRoute "notification/route"
	operation "operation/route"
	orderExportRoute "order-export/route"
	orderRoute "order/route"
	router4 "payment/router"
	pluginRoute "plugin/router"
	posterRoute "poster/route"
	productAlbumRouter "product-album/route"
	productRoute "product/route"
	promotionRouter "promotion/route"
	purchaseRoute "purchase-account/router"
	cinemaTicketRoute "race-cinema-ticket/route"
	regionRoute "region/route"
	salesRoute "sales/route"
	script "script-distribute/router"
	route2 "self-supply/router"
	serviceProviderSystemRouter "service-provider-system/route"
	shippingRoute "shipping/route"
	shoppingCartRoute "shopping-cart/route"
	smallShopVideoRouter "small-shop-video/route"
	smallShopMiddleware "small-shop/middleware"
	smallShopRouter "small-shop/route"
	smallShopWechatMiniRouter "small-shop/route"
	supplierRoute "supplier/route"
	homeRoute "supply-chain/route"
	surfaceSingle "surface-single/route"
	route10 "tianma-supply/route"
	tradeRoute "trade/route"
	cake "uncle-cake/route"
	userEquityRoute "user-equity/route"
	userAuthRoute "user-price-auth/route"
	userPurchaseRoute "user-purchase/route"
	"user/middleware"
	userRoute "user/route"
	"video-distribute/router"
	video "video-distribute/router"
	route5 "virtual-stock/route"
	route14 "water-machine/route"
	route13 "wdt-supply/route"
	wechatMiniRouter "wechatmini/route"
	wechatofficialRouter "wechatofficial/route"
	route11 "yiyatong/route"
	route9 "youxuan-supply/route"

	aggregatedPaymentSplitSettlementRouter "aggregated-payment-split-settlement/route"
	dahangErpRoute "dahang-erp/route"
	douyinGroupRouter "douyin-group/router"
	ecCpsRoute "ec-cps-ctrl/router"
	shamaRoute "shama-supply/route"
	shareLiveRoute "share-live/route"
	thousandsPricesRoute "thousands-prices/route"
	yzMiddleware "yz-go/component/middleware"

	"yz-go/config"
)

// 后台公共接口
func initAdminPublicRouter(Router *gin.RouterGroup) {
	installRoute.InitInstall(Router)
	//supplierRoute.InitUserPublicRouter(Router)
	userRoute.InitAdminPublicRouter(Router)
	applicationRoute.InitAdminPublicRouter(Router)
	shippingRoute.InitAdminPublicRouter(Router)
	supplierRoute.InitAdminPublicRouter(Router)
	homeRoute.InitAdminPublicRouter(Router)
	tradeRoute.InitAdminPublicRouter(Router)
	orderRoute.InitAdminPublicRouter(Router)
	jhcps.InitAdminPublicRouter(Router)
	productRoute.InitAdminPublicRouter(Router)
	regionRoute.InitAdminPublicRouter(Router)
	shareLiveRoute.InitAdminPublicRouter(Router)
	serviceProviderSystemRouter.InitAdminPublicRouter(Router)
	dahangErpRoute.InitAdminPublicRouter(Router)
	fuluRoute.InitAdminPublicRouter(Router)
	institutionRoute.InitAdminPublicRouter(Router)
	distributorRouter.InitAdminPublicRouter(Router)
	smallShopRouter.InitAdminPublicRouter(Router)
	douyinGroupRouter.InitAdminPublicRouter(Router)
	ecCpsRoute.InitAdminPublicRouter(Router)

}

// 后台私有接口
func initAdminPrivateRouter(Router *gin.RouterGroup) {
	jdVopSupplyRoute.InitAdminPrivateRouter(Router)
	financeRoute.InitAccountApplyRouter(Router)
	course.InitAdminPrivateRouter(Router)
	gather_route.InitGatherSupplyRouter(Router)
	bynRoute.InitBynSupplyRouter(Router)
	route6.InitAliPrivateRoute(Router)
	lakalarouter.InitLakalaSuperRouter(Router)
	route8.InitJushuitanPrivateRoute(Router)
	route13.InitWdtPrivateRoute(Router)
	route9.InitYouxuanPrivateRoute(Router)
	gongmall.InitAdminPrivateRouter(Router)
	cinemaTicketRoute.InitAdminPrivateRouter(Router)
	productRoute.InitAdminPrivateRouter(Router)
	purchaseRoute.InitAdminPrivateRouter(Router)
	commentRoute.InitAdminCommentPrivateRouter(Router)
	productRoute.InitAdminCollectionPrivateRouter(Router)
	route2.InitAdminCollectionPrivateRouter(Router)
	categoryRoute.InitAdminPrivateRouter(Router)
	supplierRoute.InitSupplierRouter(Router)
	applicationRoute.InitApplicationRouter(Router)
	pluginRoute.InitPluginRouter(Router)
	adRoute.InitAdminPrivateRouter(Router)
	router.InitAdminPrivateRouter(Router)
	materialDistribute.InitAdminPrivateRouter(Router)
	router4.InitPaymentSuperRoute(Router)
	hbsk.InitHbskAdminPrivateRouter(Router)
	cake.InitAdminPrivateRouter(Router)
	route10.InitTianMaRouter(Router)
	kunsheng.InitKunShengMaRouter(Router)
	salesRoute.InitAdminPrivateRouter(Router)
	userRoute.InitAdminPrivateRouter(Router)
	homeRoute.InitAdminPrivateRouter(Router)
	homeRoute.InitExaFileGroupRouter(Router)
	homeRoute.InitDashboardRouter(Router)
	tradeRoute.InitAdminPrivateRouter(Router)
	orderRoute.InitAdminPrivateRouter(Router)
	route7.InitMonitorAdminPrivateRouter(Router)
	orderRoute.InitBillAdminPrivateRouter(Router)
	orderExportRoute.InitAdminPrivateRouter(Router)
	shippingRoute.InitAdminPrivateRouter(Router)
	notificationRoute.InitAdminPrivateRouter(Router)
	wechatofficialRouter.InitWechatofficialPrivateRouter(Router)
	wechatMiniRouter.InitWechatMiniPrivateRouter(Router)
	afterSalesRouter.InitAdminPrivateRouter(Router)
	aliopen.InitAdminPrivateRouter(Router)
	script.InitAdminPrivateRouter(Router)
	fuluRoute.InitAdminPrivateRouter(Router)
	userEquityRoute.InitAdminPrivateRouter(Router)
	userAuthRoute.InitAdminPrivateRouter(Router)
	merchantRouter.InitAdminPrivateRouter(Router)
	areaAgencyRouter.InitAdminPrivateRouter(Router)
	productAlbumRouter.InitAdminPrivateRouter(Router)
	posterRoute.InitAdminPrivateRouter(Router)
	institutionRoute.InitAdminPrivateRouter(Router)
	localLifeRoute.InitAdminPrivateRouter(Router)
	localLifeRoute.InitBrandPrivateRouter(Router)
	smallShopRouter.InitAdminPrivateRouter(Router)
	smallShopVideoRouter.InitAdminPrivateRouter(Router)
	distributorRouter.InitAdminPrivateRouter(Router)
	distributorToolRouter.InitAdminPrivateRouter(Router)
	surfaceSingle.InitAdminPrivateRouter(Router)
	route3.InitAdminArticlePrivateRouter(Router)
	operation.InitAdminPrivateRouter(Router)
	cloudRoute.InitCloudRouter(Router)
	route.InitAdminPrivateRouter(Router)
	guangdianRoute.InitAdminPrivateRouter(Router)
	cps.InitAdminPrivateRouter(Router)
	jhcps.InitAdminPrivateRouter(Router)
	router3.InitAdminPrivateRouter(Router)
	route4.InitAdminPrivateRouter(Router)
	leaseRouter.InitAdminPrivateRouter(Router)
	route5.InitAdminPrivateRouter(Router)

	route14.InitWaterMachineRouter(Router)
	shareLiveRoute.InitAdminPrivateRouter(Router)
	serviceProviderSystemRouter.InitAdminPrivateRouter(Router)
	dahangErpRoute.InitAdminPrivateRouter(Router)
	eventDistributionRoute.InitAdminPrivateRouter(Router)
	thousandsPricesRoute.InitAdminPrivateRouter(Router)
	shamaRoute.InitAdminPrivateRouter(Router)
	douyinGroupRouter.InitAdminPrivateRouter(Router)
	promotionRouter.InitAdminPrivateRouter(Router)
	router5.InitAdminPrivateRouter(Router)
	route12.InitAdminPrivateRouter(Router)
	ecCpsRoute.InitAdminPrivateRouter(Router)
	aggregatedPaymentSplitSettlementRouter.InitAdminPrivateRouter(Router)

}

// 对外交互接口
func initAppPrivateRouter(Router *gin.RouterGroup) {
	Router.Use(appMiddleware.JWTAuth())
	Router.Use(appMiddleware.RequestSign())
	{
		course.InitPublicCurriculumRouter(Router)
		cake.InitCakeAppRouter(Router)
		guangdianRoute.InitPublicRouter(Router)
		route.InitLianLianAppRouter(Router) //lianlian router
		video.InitPublicAppRouter(Router)
		materialDistribute.InitPublicAppRouter(Router)
		script.InitPublicAppRouter(Router)
		cinemaTicketRoute.InitCinemaPrivateRouter(Router) //电影票接口
		fuluRoute.InitAppPrivateRouter(Router)
		userEquityRoute.InitAppPrivateRouter(Router)
		productAlbumRouter.InitAppPrivateRouter(Router)
		bynRoute.InitAppPrivateRouter(Router)
		productRoute.InitAppPrivateRouter(Router)
		productRoute.InitAppCollectionPrivateRouter(Router)
		distributorToolRouter.InitAppPrivateRouter(Router)
		financeRoute.InitAppPrivateRouter(Router)
		tradeRoute.InitAppPrivateRouter(Router)
		categoryRoute.InitAppPrivateRouter(Router)
		applicationRoute.InitAppPrivateRouter(Router)
		cps.InitAppPrivateRouter(Router)
		shippingRoute.InitAppPrivateRouter(Router)
		afterSalesRoute.InitAppPrivateRouter(Router)
		regionRoute.InitAppPrivateRouter(Router)
		afterSalesRouter.InitAfterSalesAppPrivateRouter(Router)
		supplierRoute.InitAppPrivateRouter(Router)
		route3.InitAppArticleRouter(Router)
		gather_route.InitAppPrivateRouter(Router)
		jhcps.InitAppPrivateRouter(Router)
		router3.InitAppPrivateRouter(Router)
		leaseRouter.InitAppPrivateRouter(Router)
		route5.InitAppPrivateRouter(Router)
		shareLiveRoute.InitAppPrivateRouter(Router)         //共享直播采购端API
		eventDistributionRoute.InitAppPrivateRouter(Router) //团购活动API
		douyinGroupRouter.InitAppPrivateRouter(Router)      //抖音团购API
		localLifeRoute.InitAppPrivateRouter(Router)         //本地生活采购端API
		ecCpsRoute.InitAppPrivateRouter(Router)
	}
	// 采购权限中间件拦截
	Router.Use(appMiddleware.RequestSign()).Use(middleware.VerifyPurchase())
	{
		gather_route.PermInitAppPrivateRouter(Router)
		//tradeRoute.PermInitAppPrivateRouter(Router)
		fuluRoute.PermInitAppPrivateRouter(Router)
		userEquityRoute.PermInitAppPrivateRouter(Router)
	}
	// 采购端黑名单和会员等级采购权限中间件拦截下单
	Router.Use(appMiddleware.RequestSign()).Use(middleware.VerifyPurchase()).Use(appMiddleware.Banlist())
	{
		tradeRoute.PermInitAppPrivateRouter(Router)
	}
	// 采购端黑名单中间件拦截商品选品
	Router.Use(appMiddleware.RequestSign()).Use(appMiddleware.Banlist())
	{
		productRoute.PermInitAppPrivateRouter(Router)
	}
}

// 对外交互接口
func initAppPublicRouter(Router *gin.RouterGroup) {

	applicationRoute.InitAppPublicRouter(Router)
	route4.InitAppPublicRouter(Router)

}

// 前端公共接口
func initUserPublicRouter(Router *gin.RouterGroup) {
	if config.Config().System.ApiCache {
		Router.Use(yzMiddleware.GetCache())
		Router.Use(yzMiddleware.SetCache())
	}
	Router = Router.Group("api")
	{
		hbsk.InitHbskPublicRouter(Router)
		gongmall.InitPublicGongMallRouter(Router)
		userAuthRoute.InitApiPrivateRouter(Router)
		pluginRoute.InitPluginPublicRouter(Router)
		course.InitPublicCurriculumRouter(Router)
		productRoute.InitUserPublicRouter(Router)
		applicationRoute.InitUserPublicRouter(Router)
		commentRoute.InitUserPublicCommentRouter(Router)
		homeRoute.InitUserPublicRouter(Router)
		homeRoute.InitCommonPublicRouter(Router)
		userRoute.InitUserPublicRouter(Router)
		userPurchaseRoute.InitUserPublicRouter(Router)
		categoryRoute.InitUserPublicRouter(Router)
		regionRoute.InitUserPublicRouter(Router)
		favoriteRoute.InitUserPublicRouter(Router)
		salesRoute.InitUserPublicRouter(Router)
		financeRoute.InitFinancePublicRouter(Router)
		shippingRoute.InitUserPublicRouter(Router)
		gatherRoute.InitGatherSupplyPublicRouter(Router)
		localLifeRoute.InitCallBackRouter(Router)
		smallShopVideoRouter.InitPublicRouter(Router)
		route.InitLianLianPublicRouter(Router)
		route11.InitYytPublicRouter(Router)
		router4.InitPublicNotifyRoute(Router)
		cinemaTicketRoute.InitCinemaTicketPublicRouter(Router) //电影票公共回调
		route.InitLianLianAppRouter(Router)
		cake.InitCakePublicRouter(Router)
		ecCpsRoute.InitUserPublicRouter(Router)

		//course.InitPublicCurriculum(Router)

		wechatofficialRouter.InitWechatofficialPublicRouter(Router)
		supplierRoute.InitSupplierPublicouter(Router)
		wechatMiniRouter.InitWechatMiniPublicRouter(Router) //微信小程序公有API
		smallShopWechatMiniRouter.InitWechatMiniPublicRouter(Router)
		leaseRouter.InitUserPublicRouter(Router)

		route6.InitAliPublicRouter(Router)
		aliopen.InitAliPublicRouter(Router)

		video.InitPublicRouter(Router)
		materialDistribute.InitPublicRouter(Router)
		productAlbumRouter.InitUserPublicRouter(Router)

		shareLiveRoute.InitUserPublicRouter(Router)

		posterRoute.InitUserPublicRouter(Router)

	}
}

func initShopkeeperPrivateRouter(Router *gin.RouterGroup) {
	Router = Router.Group("api")
	Router.Use(middleware.JWTAuth()).Use(smallShopMiddleware.VerifyShopkeeper())
	{
		smallShopRouter.InitShopkeeperPrivateRouter(Router)
	}
}

// 前端私有接口
func initUserPrivateRouter(Router *gin.RouterGroup) {
	Router = Router.Group("api")
	Router.Use(middleware.JWTAuth())
	{

		course.InitFrontPrivateCurriculumRouter(Router)
		gatherRoute.InitGatherSupplyPrivateRouter(Router)
		commentRoute.InitUserPrivateCommentRouter(Router)
		shoppingCartRoute.InitUserPrivateRouter(Router)
		userRoute.InitUserPrivateRouter(Router)
		financeRoute.InitFinancePrivateRouter(Router)
		supplierRoute.InitUserPrivateRouter(Router)
		gongmall.InitPrivateRouter(Router)
		favoriteRoute.InitUserPrivateRouter(Router)
		tradeRoute.InitUserPrivateRouter(Router)
		userPurchaseRoute.InitUserPrivateRouter(Router)
		orderRoute.InitUserPrivateRouter(Router)
		fuluRoute.InitUserPrivateRouter(Router)
		orderRoute.InitBillUserPrivateRouter(Router)
		homeRoute.InitUserPrivateRouter(Router)
		afterSalesRouter.InitUserPrivateRouter(Router)
		router4.InitPaymentRoute(Router)
		areaAgencyRouter.InitUserPrivateRouter(Router)
		productAlbumRouter.InitUserPrivateRouter(Router)
		posterRoute.InitUserPrivateRouter(Router)
		promotionRouter.InitUserPrivateRouter(Router)
		institutionRoute.InitUserPrivateRouter(Router)
		applicationRoute.InitUserPrivateRouter(Router)
		merchantRouter.InitUserPrivateRouter(Router)
		distributorRouter.InitUserPrivateRouter(Router)
		productRoute.InitUserPrivateRouter(Router)
		purchaseRoute.InitUserPrivateRouter(Router)
		route3.InitUserPrivateArticleRouter(Router)
		smallShopRouter.InitSmallShopKeeperPrivateRouter(Router)
		smallShopVideoRouter.InitPrivateRouter(Router)
		cake.InitUserPrivateRouter(Router)
		cinemaTicketRoute.InitUserPrivateRouter(Router)
		cps.InitUserPrivateRouter(Router)
		leaseRouter.InitUserPrivateRouter(Router)
		shareLiveRoute.InitUserPrivateRouter(Router)
		wechatMiniRouter.InitWechatMiniPrivate(Router)
		localLifeRoute.InitFrontPrivateRouter(Router)
		materialDistribute.InitUserPrivateRouter(Router)
		video.InitUserPrivateRouter(Router)
		homeRoute.InitUserDashboardRouter(Router)
		route12.InitUserPrivateRouter(Router)

	}
	Router.Use(middleware.JWTAuth()).Use(middleware.VerifyApi())
	{
		tradeRoute.PermInitUserPrivateRouter(Router)
	}
}

// 小商店前端私有接口
func InitSmallShopUserPrivateRouter(Router *gin.RouterGroup) {
	Router = Router.Group("smallShop")
	Router.Use(smallShopMiddleware.JWTAuth())
	{
		smallShopRouter.InitSmallShopUserPrivateRouter(Router)
		video.InitSmallShopPrivateRouter(Router)
		materialDistribute.InitSmallShopPrivateRouter(Router) //素材分发

		shareLiveRoute.InitSmallShopUserPublicRouter(Router)

		//course.InitSmallShopPrivateCurriculumRouter(Router) //小商店API

	}
}

// 小商店前端公共接口
func InitSmallShopUserPublicRouter(Router *gin.RouterGroup) {
	Router = Router.Group("smallShop")
	{
		smallShopRouter.InitSmallShopUserPublicRouter(Router)
	}
}
