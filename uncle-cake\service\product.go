package service

import (
	"context"
	"crypto/sha1"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/chenhg5/collection"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	"math"
	url2 "net/url"
	order2 "order/order"
	"os"
	model3 "product/model"
	"product/mq"
	request2 "product/request"
	"product/service"
	"public-supply/common"
	model2 "public-supply/model"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"time"
	"uncle-cake/model"
	"uncle-cake/request"
	"user/level"
	"yz-go/cache"
	"yz-go/common_data"
	"yz-go/component/log"
	"yz-go/config"
	model4 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type ModelData struct {
	Key        string                  `json:"key"`
	Domain     string                  `json:"domain"`
	ChannelId  string                  `json:"channel_id"`
	UserID     string                  `json:"user_id"`
	UpdateInfo gsetting.UpdateInfoData `json:"update"`
	Cake       []int64                 `json:"cake"`
	GatherId   uint                    `json:"gather_id"`
}

var GatherSupplyID uint

func (l *ModelData) InitSetting() (err error) {

	var gather model2.GatherSupply
	err = source.DB().Where("category_id=9").First(&gather).Error
	if err != nil {
		return
	}

	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gather.ID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	var CakeSupplySetting model2.SupplySetting
	//var setting model3.SysSetting
	//
	//redisData, _ := source.Redis().Get(context.Background(), "UncleCakeSetting").Result()
	//if redisData != "" {
	//	log.Log().Info("UncleCakeSetting 获取redis setting ", zap.Any("info", redisData))
	err = json.Unmarshal([]byte(setting.Value), &CakeSupplySetting)

	//setting.

	GatherSupplyID = gather.ID
	l.Domain = CakeSupplySetting.BaseInfo.ApiUrl
	l.GatherId = gather.ID
	//l.ChannelId = "goushihui"
	l.ChannelId = CakeSupplySetting.BaseInfo.AppSecret
	//l.Key = "0rbllDGc9qXfTyokjKiPDV9EYaTvUcxt"
	l.Key = CakeSupplySetting.BaseInfo.AppKey

	l.UserID = CakeSupplySetting.BaseInfo.UserId
	l.UpdateInfo = CakeSupplySetting.UpdateInfo
	l.Cake = CakeSupplySetting.Cake
	return

	//var supplySetting model2.SupplySetting
	//var setting model3.SysSetting
	//
	//redisData, _ := source.Redis().Get(context.Background(), "UncleCakeSetting").Result()
	//if redisData != "" {
	//	log.Log().Info("UncleCakeSetting 获取redis setting ", zap.Any("info", redisData))
	//	err = json.Unmarshal([]byte(redisData), &supplySetting)
	//	if err != nil {
	//		log.Log().Error("UncleCakeSetting InitSetting解析失败", zap.Any("err", err))
	//	}
	//	l.Key = supplySetting.BaseInfo.AppKey
	//	l.Domain = supplySetting.BaseInfo.ApiUrl
	//	l.ChannelId = supplySetting.BaseInfo.AppSecret
	//	return
	//}

	//var gather model2.GatherSupply
	//source.DB().Where("category_id=9").First(&gather)
	//
	//err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gather.ID)))
	//if err != nil {
	//	fmt.Println("获取供应链key设置失败")
	//	return
	//}

	return
	source.Redis().Set(context.Background(), "UncleCakeSetting", setting.Value, 0)
	err = json.Unmarshal([]byte(setting.Value), &supplySetting)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if supplySetting.BaseInfo.AppKey == "" && supplySetting.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	l.Key = supplySetting.BaseInfo.AppKey
	l.Domain = supplySetting.BaseInfo.ApiUrl
	l.ChannelId = supplySetting.BaseInfo.AppSecret

	return

}

func Sha1(data string) string {
	t := sha1.New()
	io.WriteString(t, data)
	return fmt.Sprintf("%x", t.Sum(nil))
}

// 同步城市
func (l *ModelData) SyncCity() (err error) {
	var count int64
	source.DB().Model(model.CakeCity{}).Count(&count)
	if count > 1 {
		return
	}
	err, cityLists := l.GetCitiesList()
	if err != nil {
		return
	}

	List := cityLists.Data.CityList
	var CityList []model.CakeCity
	for _, item := range List {
		for _, citem := range item {
			CityList = append(CityList, model.CakeCity{
				Id:          citem.Id,
				Name:        citem.Name,
				FirstLetter: citem.FirstLetter,
			})
		}
	}

	err = source.DB().Create(&CityList).Error

	return
}

//type ArrMap map[string]string
//type ChildrenCity map[string]map[string][]ArrMap

func OrderStatusCheck() (err error) {
	var data ModelData

	data.InitSetting()
	var order []order2.Order
	log.Log().Info("蛋糕订单状态检测查询开始", zap.Any("info", GatherSupplyID))

	err = source.DB().Where("gather_supply_type=9 and gather_supply_sn!='' and `status` =1 and gather_supply_id=?", GatherSupplyID).Find(&order).Error
	if err != nil {
		log.Log().Error("蛋糕订单状态检测查询错误", zap.Any("err", err))
		return
	}
	if len(order) > 0 {

		for _, item := range order {
			data.OrderStatusCheck(item.GatherSupplySN)

		}

	}

	return
}

func (l *ModelData) OrderStatusCheck(orderSn string) (err error, cityLists model.CityLists) {

	fmt.Println(orderSn)
	url := l.Domain + "dsapi/order/order_detail"
	//var reqMap = make(map[string]string)
	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	reqValuesData := url2.Values{}

	//reqMap["channel_no"] = l.ChannelId
	//reqMap["timestamp"] = timeUnixString
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	//reqMap["sign"] = Md5Sing
	//reqJsonData, err := json.Marshal(reqMap)
	//fmt.Println("请求数据：", string(reqJsonData))
	//resData := utils.HttpPostJson(reqJsonData, url)
	var headerMap = make(map[string]string)

	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("order_no", orderSn)
	//reqValuesData.Set("city_id", cityId)
	//reqValuesData.Set("brand_id", brandId)
	reqJsonData, err := json.Marshal(reqValuesData)
	log.Log().Info("蛋糕订单详情请求数据", zap.Any("err", string(reqJsonData)))

	//fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	log.Log().Info("蛋糕订单详情返回数据", zap.Any("err", string(resData)))

	var cakeOrderStatusDetail model.CakeOrderStatusDetail
	//json.Unmarshal(resData, &cityLists)
	err = json.Unmarshal(resData, &cakeOrderStatusDetail)
	if err != nil {
		log.Log().Error("蛋糕订单详情物流解析失败", zap.Any("err", err), zap.Any("err", resData))
		return
	}

	if cakeOrderStatusDetail.Code == 200 && cakeOrderStatusDetail.Msg == "success" {

		if cakeOrderStatusDetail.Data.ShipType == "same" {
			err = OrderStatusSame(cakeOrderStatusDetail)
			if err != nil {
				log.Log().Error("蛋糕订单详情Same OrderStatusSame物流发货失败", zap.Any("err", err))
			}
		}
		if cakeOrderStatusDetail.Data.ShipType == "delivery" {
			err = OrderStatusDelivery(cakeOrderStatusDetail)
			if err != nil {
				log.Log().Error("蛋糕订单详情delivery物流 OrderStatusDelivery发货失败", zap.Any("err", err))
			}

		}
		if cakeOrderStatusDetail.Data.ShipType == "shop" {
			log.Log().Info("蛋糕订单自提处理", zap.Any("info", cakeOrderStatusDetail))

			err = OrderStatusShop(cakeOrderStatusDetail)
			if err != nil {
				log.Log().Error("蛋糕订单详情delivery OrderStatusShop 物流发货失败", zap.Any("err", err))

			}
		}

	} else {
		log.Log().Error("蛋糕订单详情物流检测失败", zap.Any("err", cakeOrderStatusDetail))
	}

	return

}

func OrderStatusShop(cakeOrderStatusDetail model.CakeOrderStatusDetail) (err error) {
	var order order2.Order
	err = source.DB().Where("order_sn=?", cakeOrderStatusDetail.Data.OutOrderNo).First(&order).Error
	if err != nil {
		log.Log().Error("OrderStatusDeliveryOrderStatusShop蛋糕查询失败", zap.Any("err", err), zap.Any("err", cakeOrderStatusDetail.Data.OutOrderNo))
		return
	}
	if cakeOrderStatusDetail.Data.Status == "2" {
		err = order2.Send(order.ID)
		if err != nil {
			log.Log().Error("OrderStatusDelivery OrderStatusShop蛋糕商家配送发货失败", zap.Any("err", err))

		}

	}
	return
}

func OrderStatusDelivery(cakeOrderStatusDetail model.CakeOrderStatusDetail) (err error) {
	var order order2.Order
	err = source.DB().Where("order_sn=?", cakeOrderStatusDetail.Data.OutOrderNo).First(&order).Error
	if err != nil {
		log.Log().Error("OrderStatusDelivery蛋糕查询失败", zap.Any("err", err), zap.Any("err", cakeOrderStatusDetail.Data.OutOrderNo))
		return
	}
	var cakeOrder model.CakeOrder
	//cakeOrder.OrderSn = cakeOrderStatusDetail.Data.OrderNo       //蛋叔订单
	//cakeOrder.OutOrderNo = cakeOrderStatusDetail.Data.OutOrderNo //中台订单
	cakeOrder.Status = cakeOrderStatusDetail.Data.Status
	var StatusName string
	if cakeOrder.Status == "0" {
		StatusName = "待分配到商家"
	} else if cakeOrder.Status == "1" {
		StatusName = "已分配到商家"
	} else if cakeOrder.Status == "2" {
		StatusName = "已配送完成"
	} else if cakeOrder.Status == "4" {
		StatusName = "订单退款且已取消"
	}
	cakeOrder.StatusName = StatusName
	err = source.DB().Where("order_sn=?", cakeOrderStatusDetail.Data.OrderNo).Updates(&cakeOrder).Error
	if err != nil {
		log.Log().Error("OrderStatusDelivery蛋糕状态变更失败", zap.Any("err", err))
		return
	}

	if cakeOrder.Status == "2" {
		log.Log().Info("OrderStatusDelivery蛋糕商家配送完成", zap.Any("err", cakeOrder))
		err = order2.Send(order.ID)
		if err != nil {
			log.Log().Error("OrderStatusDelivery蛋糕商家配送发货失败", zap.Any("err", err))

		}
		//
		//err = order2.Received(order.ID)
		//
		//if err != nil {
		//	log.Log().Error("OrderStatusDelivery蛋糕商家配送收货失败", zap.Any("err", err))
		//
		//}

	}

	return
}

func OrderStatusSame(cakeOrderStatusDetail model.CakeOrderStatusDetail) (err error) {
	var expressData ExpressData

	expressData.ExpressName = cakeOrderStatusDetail.Data.ExpressCp
	expressData.ExpressNo = cakeOrderStatusDetail.Data.ExpressNo

	if expressData.ExpressNo == "" {
		err = errors.New("快递号为空")
		return
	}

	if cakeOrderStatusDetail.Data.OutOrderNo == "" {
		err = errors.New("中台单号空")
		return
	}
	orderSn, _ := strconv.Atoi(cakeOrderStatusDetail.Data.OutOrderNo)
	log.Log().Info("蛋糕物流发货查询订单号", zap.Any("info", orderSn))

	var order order2.Order
	err = source.DB().Where("order_sn=?", orderSn).First(&order).Error
	if err != nil {
		log.Log().Error("蛋糕回调发货查询订单失败~", zap.Any("err", err))
		return
	}
	err = Send(order.ID, expressData)
	if err != nil {
		log.Log().Error("蛋糕回调发货失败Send", zap.Any("err", err))
		return
	}

	return
}

func (l *ModelData) GetCitiesList() (err error, cityLists model.CityLists) {

	url := l.Domain + "dsapi/city/get_cities_list"
	var reqMap = make(map[string]string)
	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	reqValuesData := url2.Values{}

	reqMap["channel_no"] = l.ChannelId
	reqMap["timestamp"] = timeUnixString
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	//reqMap["sign"] = Md5Sing
	//reqJsonData, err := json.Marshal(reqMap)
	//fmt.Println("请求数据：", string(reqJsonData))
	//resData := utils.HttpPostJson(reqJsonData, url)
	var headerMap = make(map[string]string)

	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	//reqValuesData.Set("city_id", cityId)
	//reqValuesData.Set("brand_id", brandId)
	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)

	//json.Unmarshal(resData, &cityLists)
	json.Unmarshal(resData, &cityLists)

	return

}

func (l *ModelData) GetCityId(CityName string) (err error, getCityId model.GetCityId) {

	url := l.Domain + "dsapi/city/get_city_id"
	reqValuesData := url2.Values{}
	var headerMap = make(map[string]string)
	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sign := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sign)
	reqValuesData.Set("city_name", CityName)
	reqJsonData, err := json.Marshal(reqValuesData)

	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)

	json.Unmarshal(resData, &getCityId)
	fmt.Println(resData)

	//fmt.Println(resData)
	return

}

func (l *ModelData) UserLogin(uid string) (err error, data model.UserInfo) {

	url := l.Domain + "dsapi/user/user_login"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("uid", uid)
	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 是否可配送
func (l *ModelData) GetShipStatus(uid string) (err error, data model.ShipStatus) {

	url := l.Domain + "dsapi/addr/get_ship_status"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("id", "1528190")
	reqValuesData.Set("spec_ids", "1183486")
	reqValuesData.Set("brand_id", "47")
	reqValuesData.Set("product_amount", "188.00")

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	log.Log().Info("请求", zap.Any("info", string(reqJsonData)))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)
	log.Log().Info("返回", zap.Any("info", string(resData)))

	fmt.Println(data)

	return

}

func (l *ModelData) OprateAddr(reqData model.CreateAddress, uid string) (err error, data model.AddressInfo) {

	url := l.Domain + "dsapi/addr/oprate_addr"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("user_id", uid)
	reqValuesData.Set("city_name", reqData.CityName) //北京市 双井富力城小区三期10-08号
	reqValuesData.Set("area", reqData.Area)
	reqValuesData.Set("addr", reqData.Addr)
	reqValuesData.Set("name", reqData.Name)
	reqValuesData.Set("phone", reqData.Phone)
	reqJsonData, err := json.Marshal(reqValuesData)

	log.Log().Info("蛋糕地址创建请求信息", zap.Any("info", string(reqJsonData)))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	log.Log().Info("蛋糕地址创建请求信息返回", zap.Any("info", resData))

	json.Unmarshal(resData, &data)

	return

}

func (l *ModelData) GetCityArea(cityId string) (err error, cityAreaList model.CityAreaList) {

	url := l.Domain + "dsapi/city/get_city_area"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("city_id", cityId)
	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &cityAreaList)

	return

}

func (l *ModelData) GetLocalBrandList(info model.SearchBrand) (err error, BrandList []model.CakeBrand, total int64) {

	var List []model.CakeBrand
	db := source.DB().Model(model.CakeBrand{})
	info.Page = info.Page
	info.PageSize = info.PageSize
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	if info.PageInfo.Page == 0 {
		db.Where("status=?", 1)

	}
	if info.BrandName != "" {
		db = db.Where("name like ?", "%"+info.BrandName+"%")
	}
	if info.CateId != "" {
		db.Where("cat_id =?", info.CateId)
	}
	if info.CityId != "" {
		//db.Where("city_id in (?)", info.CityId)
		db.Where("FIND_IN_SET(?,city_id) >0", info.CityId)
	}
	if info.BrandId != "" {
		db.Where("id =?", info.BrandId)
	}

	//if joinWhere != "" {
	//	db.Joins(joinWhere)
	//}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&List).Error

	//var BrandList []model.CakeBrand

	for _, item := range List {
		var counts int64
		source.DB().Model(model.CakeShop{}).Where("brand_id=?", item.ID).Count(&counts)
		item.ShopCount = counts
		item.CityCount = len(strings.Split(item.CityId, ","))
		BrandList = append(BrandList, item)
		//var counts int64
		//source.DB().Model(model.CakeShop{}).Where("brand_id=?", item.ID).Count(&counts)
		//item.ShopCount = 3

	}

	return

}

func (l *ModelData) GetLocalBrandListNoCount(info model.SearchBrand) (err error, BrandList []model.CakeBrand, total int64) {

	//var List []model.CakeBrand
	db := source.DB().Model(model.CakeBrand{})
	info.Page = info.Page
	info.PageSize = info.PageSize
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	if info.PageInfo.Page == 0 {
		db.Where("status=?", 1)

	}
	if info.BrandName != "" {
		db = db.Where("name like ?", "%"+info.BrandName+"%")
	}
	if info.CateId != "" {
		db.Where("cat_id =?", info.CateId)
	}
	if info.CityId != "" {
		db.Where("city_id in (?)", info.CityId)
	}
	if info.BrandId != "" {
		db.Where("id =?", info.BrandId)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&BrandList).Error

	return

}

func (l *ModelData) GetBrand(info model.SearchBrand) (err error, BrandList []model.CakeBrand, total int64) {

	var List []model.CakeBrand
	db := source.DB().Model(model.CakeBrand{})
	info.Page = info.Page
	info.PageSize = info.PageSize
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	if info.PageInfo.Page == 0 {
		db.Where("status=?", 1)

	}
	if info.BrandName != "" {
		db = db.Where("name like ?", "%"+info.BrandName+"%")
	}
	if info.CateId != "" {
		db.Where("cat_id =?", info.CateId)
	}
	if info.CityId != "" {
		db.Where("city_id in (?)", info.CityId)
	}
	if info.BrandId != "" {
		db.Where("id =?", info.BrandId)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&List).Error
	BrandList = List

	return

}

func (l *ModelData) GetLocalShopList(info model.SearchShop) (err error, List []model.CakeShop, total int64) {

	db := source.DB().Model(model.CakeShop{})
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	if info.PageInfo.Page == 0 {
		db.Where("status=?", 1)

	}

	if info.ShopId != "" {
		db.Where("shop_id=?", info.ShopId)
	}
	if info.BrandName != "" {
		db.Where("brand_name=?", info.BrandName)
	}
	if info.ShopName != "" {
		db.Where("shop_name=?", info.ShopName)
	}
	if info.CityId != "" {
		db.Where("city_id=?", info.CityId)
	}

	//if joinWhere != "" {
	//	db.Joins(joinWhere)
	//}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&List).Error

	return

}
func (l *ModelData) GetLocalCityList(info model.SearchCity) (err error, List []model.CakeCity, total int64) {

	db := source.DB().Model(model.CakeCity{})
	//limit := info.PageSize
	//offset := info.PageSize * (info.Page - 1)

	//if joinWhere != "" {
	//	db.Joins(joinWhere)
	//}
	//err = db.Count(&total).Error
	err = db.Find(&List).Error

	return

}
func (l *ModelData) GetLocalCakeProduct(info model.SearchCakeProduct) (err error, List []model.CakeProductModel, total int64) {

	db := source.DB().Model(model.CakeProductModel{})
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db.Where("cat_id=?", info.CateId)
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&List).Error

	return

}

func (l *ModelData) GetBrandList(cityId string) (err error, brandList model.BrandList) {

	url := l.Domain + "dsapi/brand/brand_city_lists"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	//reqValuesData.Set("city_id", cityId)
	//reqJsonData, err := json.Marshal(reqValuesData)

	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	err = json.Unmarshal(resData, &brandList)
	if err != nil {
		fmt.Println(err.Error())
	}

	//101720
	return

}
func (l *ModelData) GetBrandInfo(brandId string) (err error, data model.BrandInfo) {

	url := l.Domain + "dsapi/brand/get_brand_info"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("brand_id", brandId)
	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	//101720
	return

}

// 同步店铺
func (l *ModelData) SyncShop() (err error) {

	source.DB().Exec("truncate table cake_shops")
	if len(l.Cake) == 0 {
		return
	}
	var cakeCity []model.CakeCity
	err = source.DB().Find(&cakeCity).Error
	if err != nil {
		return
	}
	if len(cakeCity) <= 0 {
		l.SyncCity()
		source.DB().Find(&cakeCity)
	}
	errBrand, list := l.GetBrandList("")

	if errBrand != nil {
		return
	}
	if list.Code != 200 {
		return
	}
	//if len(cakeCity) > 0 {
	//
	//	for _, item := range cakeCity {
	//
	var cakeBrandData []model.CakeBrand
	cakeBrandData = list.Data

	for _, brandItem := range cakeBrandData {

		//if brandItem.ID == "1001-103666" && item.Id == "1116" {
		//	fmt.Println("111")
		//}
		catId, _ := strconv.Atoi(brandItem.CatId)
		if collection.Collect(l.Cake).Contains(catId) != true {
			continue

		}

		cityList := strings.Split(brandItem.CityId, ",")
		for _, item := range cityList {

			errShop, shopList := l.GetShopList(item, brandItem.ID)

			if errShop != nil {
				continue
			}

			for _, itemShop := range shopList.Data.Shops {
				itemShop.BrandId = brandItem.ID
				itemShop.BrandName = brandItem.Name
				itemShop.CityId = item
				//itemShop.CityName = item.Name
				itemShop.CatId = brandItem.CatId
				source.DB().Where("shop_id=?", itemShop.ShopId).FirstOrCreate(&itemShop)

			}

		}

		//source.DB().Where("id=?", branItem.ID).FirstOrCreate(&branItem)
	}

	//	}
	//
	//}

	return
}

func (l *ModelData) GetShopList(cityId, brandId string) (err error, shopList model.ShopList) {

	url := l.Domain + "dsapi/brand/get_shop_lists"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("city_id", cityId)
	reqValuesData.Set("brand_id", brandId)

	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	err = json.Unmarshal(resData, &shopList)
	if err != nil {
		fmt.Println(err)
	}

	return

}

func (l *ModelData) DelCakeData(cake []int64) (err error) {

	if len(cake) == 0 {
		err = source.DB().Exec("truncate table cake_brands").Error
		err = source.DB().Exec("truncate table cake_shops").Error
		err = source.DB().Exec("truncate table cake_products").Error
		var product []model3.Product
		source.DB().Where("gather_supply_id=?", l.GatherId).Where("source=?", 110).Find(&product)

		for _, item := range product {
			service.DeleteProduct(item)
		}
		if err != nil {
			return
		}
	} else {
		err = source.DB().Unscoped().Delete(&model.CakeBrand{}, "cat_id  not in (?)", cake).Error
		if err != nil {
			return
		}

		err = source.DB().Unscoped().Delete(&model.CakeShop{}, "cat_id  not in (?)", cake).Error
		if err != nil {
			return
		}
		err = source.DB().Unscoped().Delete(&model.CakeProduct{}, "cat_id  not in (?)", cake).Error
		if err != nil {
			return
		}

		var product []model3.Product
		source.DB().Where("gather_supply_id=?", l.GatherId).Where("location_id not in (?)", cake).Where("source=?", 110).Find(&product)

		for _, item := range product {
			service.DeleteProduct(item)
		}

	}

	return
}

// 同步品牌列表数据
func (l *ModelData) SyncBrand() (err error) {

	source.DB().Exec("truncate table cake_brands")
	//
	//var cakeCity []model.CakeCity
	//err = source.DB().Find(&cakeCity).Error
	//if err != nil {
	//	return
	//}
	//if len(cakeCity) <= 0 {
	//	l.SyncCity()
	//	source.DB().Find(&cakeCity)
	//
	//}
	//
	//if len(cakeCity) > 0 {
	//
	//	for _, item := range cakeCity {
	errBrand, list := l.BrandCityList()

	if errBrand != nil {
		return
	}
	//if list.Code != 200 {
	//	continue
	//}
	var cakeBrandData []model.CakeBrand
	cakeBrandData = list.Data

	for _, branItem := range cakeBrandData {
		catId, _ := strconv.Atoi(branItem.CatId)
		if len(l.Cake) == 0 {
			return
		}
		if collection.Collect(l.Cake).Contains(catId) != true {
			continue

		}
		var catName string
		if branItem.CatId == "1" {
			catName = "蛋糕"
		}
		if branItem.CatId == "5" {
			catName = "零食小吃"
		}
		if branItem.CatId == "8" {
			catName = "鲜花"
		}
		branItem.CatName = catName
		branItem.CityId = branItem.CityId

		_, brandInfo := l.GetBrandInfo(branItem.ID)

		branItem.Description = brandInfo.Data.Description
		branItem.ShortDescription = brandInfo.Data.ShortDescription
		source.DB().Where("id=?", branItem.ID).FirstOrCreate(&branItem)
	}

	//	}
	//
	//}

	return
}

func (l *ModelData) BrandCityList() (err error, data model.BrandCityList) {

	url := l.Domain + "dsapi/brand/brand_city_lists"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取商品列表
func (l *ModelData) GetProductList(page int, catId string) (err error, data model.ResProductList) {

	url := l.Domain + "dsapi/product/get_product_hot_lists"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("sort_price_type", "1")

	//1-蛋糕，5-零食小吃，8-鲜花   详情的cat_id
	//product_type  	商品类型，1是蛋糕，2是零食，3是鲜花  搜索条件的id
	productType := "1"
	if catId == "5" {
		productType = "2"
	}
	if catId == "8" {
		productType = "3"
	}
	reqValuesData.Set("product_type", productType)
	pages := strconv.Itoa(page)
	reqValuesData.Set("page", pages)
	reqValuesData.Set("size", "50")

	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取商品详情
func (l *ModelData) GetProductDetail(productId, cityId string) (err error, data model.ProductDetail) {

	url := l.Domain + "dsapi/product/get_product_details"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	//reqValuesData.Set("product_id", "1323189")
	//reqValuesData.Set("city_id", "1")
	reqValuesData.Set("product_id", productId)
	reqValuesData.Set("city_id", cityId)
	//reqValuesData.Set("size", "50")

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取规格状态
func (l *ModelData) GetProductSpecsStatus(skuId string) (err error, data model.SpecsStatus) {

	url := l.Domain + "dsapi/product/get_product_specs_status"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("spec_id", skuId)

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取商品状态
func (l *ModelData) GetProductStatus(productId string) (err error, data model.SpecsStatus) {

	url := l.Domain + "dsapi/product/get_product_status"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("product_id", productId)

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取商品可售卖城市
func (l *ModelData) GetProductCities(productId, brandId string) (err error, data model.ProductCities) {

	url := l.Domain + "/dsapi/product/get_product_cities"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("product_id", productId)
	reqValuesData.Set("brand_id", brandId)

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取品牌在城市售卖所有商品
func (l *ModelData) GetSaleProducts(cityId, brandId string) (err error, data model.ProductSales) {

	url := l.Domain + "dsapi/product/onSaleProducts"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("city_id", cityId)
	reqValuesData.Set("brand_id", brandId)

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 获取近期下架的商品
func (l *ModelData) GetOffProduct(reqParam model.ReqOffProduct) (err error, data model.OffProduct) {

	url := l.Domain + "dsapi/product/get_off_product"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	if reqParam.BrandId != "" {
		reqValuesData.Set("brand_id", reqParam.BrandId)
	}
	reqValuesData.Set("start", reqParam.Start)
	reqValuesData.Set("end", reqParam.End)

	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

func (l *ModelData) UpdateShopStatus(status, id uint) (err error) {

	//err = source.DB().Where("id=?", id).Updates(model.CakeShop{Status: status}).Error

	err = source.DB().Transaction(func(tx *gorm.DB) (err error) {
		err = tx.Where("id=?", id).Updates(model.CakeShop{Status: status}).Error
		//err = tx.Where("shop_id=?", id).Updates(model.CakeProduct{IsDisplay: status}).Error
		return
	})

	return
}

func (l *ModelData) UpdateBrandStatus(status, id uint) (err error) {

	err = source.DB().Transaction(func(tx *gorm.DB) (err error) {
		err = tx.Where("id=?", id).Updates(model.CakeBrand{Status: status}).Error
		err = tx.Where("brand_id=?", id).Updates(model.CakeShop{Status: status}).Error
		err = tx.Where("brand_id=?", id).Updates(model.CakeProduct{IsDisplay: status}).Error

		var ids, productIds []uint
		source.DB().Model(model.CakeProduct{}).Where("brand_id=?", id).Pluck("id", &ids)

		if len(ids) == 0 {
			return
		}

		source.DB().Model(model3.Product{}).Where("source_goods_id in (?)", ids).Where("source=?", common.CAKE_SOURCE).Pluck("id", &productIds)
		source.DB().Model(model3.Product{}).Where("source_goods_id in (?)", ids).Where("source=?", common.CAKE_SOURCE).UpdateColumn("is_display", "0")

		var productMessageType mq.ProductMessageType //队列消息类型

		if status == 2 {
			productMessageType = mq.Undercarriage
		} else if status == 1 {
			productMessageType = mq.OnSale
		}

		for _, Item := range productIds {
			mq.PublishMessage(Item, productMessageType, 0)
		}
		return
	})

	return
}

func GetCityList(ids string) (err error, cakeCity []model.CakeCity) {
	var ArrIds []uint
	stringIds := strings.Split(ids, ",")
	for _, item := range stringIds {
		id, _ := strconv.Atoi(item)
		ArrIds = append(ArrIds, uint(id))
	}

	err = source.DB().Where("id in (?)", ArrIds).Find(&cakeCity).Error

	return

}

// 获取配送范围
func (l *ModelData) GetRule(reqParam model.ReqRule) (err error, data model.Rule) {

	url := l.Domain + "dsapi/city/get_rules"
	var headerMap = make(map[string]string)
	reqValuesData := url2.Values{}

	var product model3.Product

	err = source.DB().Where("id=?", reqParam.ProductId).First(&product).Error
	if err != nil {
		return
	}
	if product.ID <= 0 {
		err = errors.New("商品不存在")
		return
	}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	if reqParam.BrandId != "" {
		reqValuesData.Set("brand_id", reqParam.BrandId)
	}
	//reqValuesData.Set("product_id", reqParam.ProductId)
	reqValuesData.Set("city_id", reqParam.CityId)
	productId := strconv.Itoa(int(product.SourceGoodsID))
	reqValuesData.Set("product_id", productId)

	reqJsonData, err := json.Marshal(reqValuesData)
	//fmt.Println("请求数据：", string(reqJsonData))
	log.Log().Info("(l *ModelData) GetRule 请求 ", zap.Any("info", reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	log.Log().Info("(l *ModelData) GetRule 返回", zap.Any("info", string(resData)))

	json.Unmarshal(resData, &data)

	return

}

// 获取订单详情
func (l *ModelData) GetOrder(reqParam model.ReqOrderData) (err error, cakeOrderStatusDetail interface{}) {

	var order Order

	err = source.DB().Where("third_order_sn=?", reqParam.ThirdOrderSN).First(&order).Error
	if err != nil {
		return
	}

	url := l.Domain + "dsapi/order/order_detail"

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	reqValuesData := url2.Values{}

	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))

	var headerMap = make(map[string]string)

	orderSN := strconv.Itoa(int(order.OrderSN))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)
	reqValuesData.Set("out_order_no", orderSN)

	reqJsonData, err := json.Marshal(reqValuesData)
	log.Log().Info("蛋糕订单详情请求数据", zap.Any("err", string(reqJsonData)))

	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	log.Log().Info("蛋糕订单详情返回数据", zap.Any("err", string(resData)))

	err = json.Unmarshal(resData, &cakeOrderStatusDetail)
	if err != nil {
		log.Log().Error("蛋糕订单详情物流解析失败", zap.Any("err", err), zap.Any("err", resData))
		return
	}
	return

}

// 批量获取配送范围
func (l *ModelData) GetRuleIds(reqParam model.ReqRuleIds) (err error, data model.Rules) {

	url := l.Domain + "dsapi/product/get_rule_ids"
	var headerMap = make(map[string]string)

	reqValuesData := url2.Values{}

	timeUnix := time.Now().Unix()
	timeUnixString := strconv.Itoa(int(timeUnix))
	signString := "channel_no" + l.ChannelId + "timestamp" + timeUnixString + l.Key
	sha1String := Sha1(signString)
	Md5Sing := utils.MD5V([]byte(sha1String))
	reqValuesData.Set("channel_no", l.ChannelId)
	reqValuesData.Set("timestamp", timeUnixString)
	reqValuesData.Set("sign", Md5Sing)

	reqByteData, MarshalErr := json.Marshal(&reqParam)
	if MarshalErr != nil {
		log.Log().Error("GetRuleIdsjson错误", zap.Any("err", MarshalErr))
		return
	}
	reqValuesData.Set("product", string(reqByteData))
	reqJsonData, err := json.Marshal(reqValuesData)
	fmt.Println("请求数据：", string(reqJsonData))
	err, resData := utils.PostForm(url, reqValuesData, headerMap)
	json.Unmarshal(resData, &data)

	return

}

// 导出蛋糕上架中的商品到excel
func (l *ModelData) ExportCakeProductToExcel(info request.ProductSearch) (err error, link string) {

	var cakeList []model.CakeProduct
	db := source.DB().Model(&model.CakeProduct{})
	db = db.Where("deleted_at is NULL")
	//db = db.Where("source =?", common2.CAKE_SOURCE)
	if info.IsYushou != "" {
		db.Where("is_yushou=?", info.IsYushou)
	}
	if info.ID > 0 {

		var ids []int64
		source.DB().Model(model3.Product{}).Where("id=?", info.ID).Pluck("source_goods_id", &ids)

		db.Where("id in (?)", ids)
	}

	if info.Title != "" {
		db.Where("title like ?", "%"+info.Title+"%")
	}

	if info.GoodsStatus != "" {
		db.Where("status=?", info.GoodsStatus)
	}

	if info.CateId > 0 {
		db.Where("cat_id=?", info.CateId)
	}
	if info.BrandID > 0 {
		db.Where("brand_id=?", info.BrandID)
	}

	//err = db.Count(&total).Error
	err = db.Preload("LocalProduct").Preload("LocalProduct.Skus").Order("created_at desc").Find(&cakeList).Error

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "商品标题")
	f.SetCellValue("Sheet1", "B1", "商品ID")
	f.SetCellValue("Sheet1", "C1", "成本价")
	f.SetCellValue("Sheet1", "D1", "供货价")
	f.SetCellValue("Sheet1", "E1", "市场价")
	f.SetCellValue("Sheet1", "F1", "品牌")
	f.SetCellValue("Sheet1", "G1", "主图")

	i := 2
	for _, v := range cakeList {

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.Title)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.Id)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), float64(v.LocalProduct.CostPrice)/100)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), float64(v.LocalProduct.Price)/100)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), float64(v.LocalProduct.OriginPrice)/100)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.BrandName)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.LocalProduct.ImageUrl)
		//f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Id)

		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_cake_product"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "蛋糕商品列表.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link

}

func (l *ModelData) GetProductInfoList(info request.ProductSearchNew) (err error, list []model.CakeProduct, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.CakeProduct{})
	db = db.Where("deleted_at is NULL")
	//db = db.Where("source =?", common2.CAKE_SOURCE)
	if info.IsYushou != "" {
		db.Where("is_yushou=?", info.IsYushou)
	}
	if info.ID > 0 {

		var ids []int64
		source.DB().Model(model3.Product{}).Where("id=?", info.ID).Pluck("source_goods_id", &ids)

		db.Where("id in (?)", ids)
	}

	if info.Title != "" {
		db.Where("title like ?", "%"+info.Title+"%")
	}

	if info.GoodsStatus != "" {
		db.Where("status=?", info.GoodsStatus)
	}

	if info.CateId > 0 {
		db.Where("cat_id=?", info.CateId)
	}
	if info.BrandID != "" {
		db.Where("brand_id=?", info.BrandID)
	}

	err = db.Count(&total).Error
	err = db.Preload("LocalProduct").Preload("LocalProduct.Skus").Order("created_at desc").Limit(limit).Offset(offset).Find(&list).Error

	//err = db.Count(&total).Error

	return
}

func GetProductList(info request2.ProductStorageSearch, appLevelId uint) (err error, list []model3.Product, total int64) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&list).Where("source=?", common.CAKE_SOURCE).Select("id")

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&list).Error

	return err, list, total
}

func GetProductDetail(id uint, appID uint) (err error, product model.CakeProductDetail) {

	var application *model4.Application
	application, err = cache.GetApplicationFromCache(appID)
	if err != nil {
		return
	}
	var user *model4.User
	user, err = cache.GetUserFromCache(application.MemberId)
	if err != nil {
		return
	}

	err = source.DB().Preload("CakeProduct").Preload("Skus").First(&product, "id=?", id).Error

	var percent, isDefault int
	err, percent, isDefault = level.GetLevelDiscountPercent(user.LevelID)
	if err != nil {
		return
	}
	if product.UserPriceSwitch == 1 {
		var calculateRes bool
		product.AgreementPrice, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, user.LevelID)
		// 商品没有设置该等级，使用默认折扣
		if calculateRes == false {
			if isDefault == 0 {
				err, product.AgreementPrice = level.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
				if err != nil {
					return
				}
			} else {
				product.AgreementPrice = product.Price
			}
		}
	} else {
		if isDefault == 0 {
			err, product.AgreementPrice = level.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
			if err != nil {
				return
			}
		} else {
			product.AgreementPrice = product.Price
		}
	}

	product.AgreementPrice = uint(math.Floor(float64(product.AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))

	for i, sku := range product.Skus {
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			product.Skus[i].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				err, product.Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
				if err != nil {
					return
				}
			}
		} else {
			err, product.Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
			if err != nil {
				return
			}
		}
		product.Skus[i].Price = uint(math.Floor(float64(product.Skus[i].Price) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
		product.Skus[i].CostPrice = product.Skus[i].Price
	}
	//productList = append(productList, product)

	return

}

func GetSupplyProductDetail(id uint, appID uint) (err error, product model.SupplyCakeProductDetail) {

	var application service.Application
	err = source.DB().Preload("ApplicationLevel").Preload("User.UserLevel").Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}

	err = source.DB().Preload("CakeProduct").Preload("Skus").First(&product, "id=?", id).Error

	var percent, isDefault int
	err, percent, isDefault = level.GetLevelDiscountPercent(application.User.LevelID)
	if err != nil {
		return
	}
	if product.UserPriceSwitch == 1 {
		var calculateRes bool
		product.AgreementPrice, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(product.Price, application.User.LevelID)
		// 商品没有设置该等级，使用默认折扣
		if calculateRes == false {
			if isDefault == 0 {
				err, product.AgreementPrice = level.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
				if err != nil {
					return
				}
			} else {
				product.AgreementPrice = product.Price
			}
		}
	} else {
		if isDefault == 0 {
			err, product.AgreementPrice = level.GetLevelDiscountAmount(product.Price, product.CostPrice, percent)
			if err != nil {
				return
			}
		} else {
			product.AgreementPrice = product.Price
		}
	}

	product.AgreementPrice = uint(math.Floor(float64(product.AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))

	for i, sku := range product.Skus {
		if product.UserPriceSwitch == 1 {
			var calculateRes bool
			product.Skus[i].Price, _, calculateRes = product.UserPrice.GetProductLevelDiscountPrice(sku.Price, application.User.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				err, product.Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
				if err != nil {
					return
				}
			}
		} else {
			err, product.Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
			if err != nil {
				return
			}
		}
		product.Skus[i].Price = uint(math.Floor(float64(product.Skus[i].Price) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
		product.Skus[i].CostPrice = product.Skus[i].Price
	}
	//productList = append(productList, product)

	return

}
func GetBatchProductDetail(id []uint, appID uint) (err error, product []model.CakeProductDetail) {

	var application service.Application
	err = source.DB().Preload("ApplicationLevel").Preload("User.UserLevel").Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}

	err = source.DB().Preload("CakeProduct").Where("id in (?)", id).Preload("Skus").Find(&product).Error

	var percent, isDefault int
	err, percent, isDefault = level.GetLevelDiscountPercent(application.User.LevelID)
	if err != nil {
		return
	}

	for index, detailItem := range product {
		if detailItem.UserPriceSwitch == 1 {
			var calculateRes bool
			product[index].AgreementPrice, _, calculateRes = detailItem.UserPrice.GetProductLevelDiscountPrice(detailItem.Price, application.User.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, product[index].AgreementPrice = level.GetLevelDiscountAmount(detailItem.Price, detailItem.CostPrice, percent)
					if err != nil {
						return
					}
				} else {
					product[index].AgreementPrice = detailItem.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, product[index].AgreementPrice = level.GetLevelDiscountAmount(detailItem.Price, detailItem.CostPrice, percent)
				if err != nil {
					return
				}
			} else {
				product[index].AgreementPrice = detailItem.Price
			}
		}

		product[index].AgreementPrice = uint(math.Floor(float64(product[index].AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))

		for i, sku := range product[index].Skus {
			if detailItem.UserPriceSwitch == 1 {
				var calculateRes bool
				product[index].Skus[i].Price, _, calculateRes = detailItem.UserPrice.GetProductLevelDiscountPrice(sku.Price, application.User.LevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					err, product[index].Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
					if err != nil {
						return
					}
				}
			} else {
				err, product[index].Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
				if err != nil {
					return
				}
			}
			product[index].Skus[i].Price = uint(math.Floor(float64(product[index].Skus[i].Price) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
			product[index].Skus[i].CostPrice = product[index].Skus[i].Price
		}

	}

	//productList = append(productList, product)

	return

}

func GetSupplyBatchProductDetail(id []uint, appID uint) (err error, product []model.SupplyCakeProductDetail) {

	var application service.Application
	err = source.DB().Preload("ApplicationLevel").Preload("User.UserLevel").Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}

	err = source.DB().Preload("CakeProduct").Where("id in (?)", id).Preload("Skus").Find(&product).Error

	var percent, isDefault int
	err, percent, isDefault = level.GetLevelDiscountPercent(application.User.LevelID)
	if err != nil {
		return
	}

	for index, detailItem := range product {
		if detailItem.UserPriceSwitch == 1 {
			var calculateRes bool
			product[index].AgreementPrice, _, calculateRes = detailItem.UserPrice.GetProductLevelDiscountPrice(detailItem.Price, application.User.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				if isDefault == 0 {
					err, product[index].AgreementPrice = level.GetLevelDiscountAmount(detailItem.Price, detailItem.CostPrice, percent)
					if err != nil {
						return
					}
				} else {
					product[index].AgreementPrice = detailItem.Price
				}
			}
		} else {
			if isDefault == 0 {
				err, product[index].AgreementPrice = level.GetLevelDiscountAmount(detailItem.Price, detailItem.CostPrice, percent)
				if err != nil {
					return
				}
			} else {
				product[index].AgreementPrice = detailItem.Price
			}
		}

		product[index].AgreementPrice = uint(math.Floor(float64(product[index].AgreementPrice) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))

		for i, sku := range product[index].Skus {
			if detailItem.UserPriceSwitch == 1 {
				var calculateRes bool
				product[index].Skus[i].Price, _, calculateRes = detailItem.UserPrice.GetProductLevelDiscountPrice(sku.Price, application.User.LevelID)
				// 商品没有设置该等级，使用默认折扣
				if calculateRes == false {
					err, product[index].Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
					if err != nil {
						return
					}
				}
			} else {
				err, product[index].Skus[i].Price = level.GetLevelDiscountAmount(sku.Price, sku.CostPrice, percent)
				if err != nil {
					return
				}
			}
			product[index].Skus[i].Price = uint(math.Floor(float64(product[index].Skus[i].Price) * (1 + float64(application.ApplicationLevel.ServerRadio)/10000)))
			product[index].Skus[i].CostPrice = product[index].Skus[i].Price
		}

	}

	//productList = append(productList, product)

	return

}

// GetProductInfoList
// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetProductInfoList
// @description: 分页获取Product完整记录列表
// @param: info request.ProductSearch
// @return: err error, list []model.Product, total int64
func GetProductStorageInfoList(info request2.ProductStorageSearch, appLevelId uint) (err error, list []service.ProductElasticSearch, total int64) {

	limit := info.PageSize
	if limit > 100 {
		limit = 100
	}
	if info.Page > 100 {
		info.Page = 100
	}
	offset := info.PageSize * (info.Page - 1)
	if info.Type == "" {
		info.Type = "sort"
	}

	// 创建db
	es, err := source.ES()
	if err != nil {
		return
	}
	//db := source.DB().Model(&model.Product{})
	//db = db.Where("deleted_at is NULL")
	// 如果有条件搜索 下方会自动创建搜索语句
	boolQ := elastic.NewBoolQuery()
	if info.Title != "" {
		//es = es.Query(elastic.NewMatchPhraseQuery("title", info.Title))
		boolQ.Must(elastic.NewMatchPhraseQuery("search_title", info.Title).Slop(2))
	}

	filterQ := elastic.NewBoolQuery()

	filterQ.Must(elastic.NewMatchQuery("source", 110))

	if info.IsDisplay != nil {
		filterQ.Must(elastic.NewMatchQuery("is_display", &info.IsDisplay))
	}

	boolQ.Filter(filterQ)
	total, err = es.Count("product" + common_data.GetOldProductIndex()).Query(boolQ).Do(context.Background())
	if err != nil {
		return
	}
	if total > int64(info.PageSize*100) {
		total = int64(info.PageSize * 100)
	}
	//es执行搜索
	log.Log().Info("导入数据的详情", zap.Any("info", boolQ))

	res, err := es.Search("product"+common_data.GetOldProductIndex()).Size(limit).From(offset).Sort(info.Type, info.Sort).Query(boolQ).Do(context.Background())
	//log.Log().Info("导入数据的详情", zap.Any("info", res))
	if err != nil {
		return
	}
	//var ids []uint
	//var productSearchs []ProductElasticSearch
	//获取es搜索结果
	list, err = service.GetSearchResult(res)
	//for _,v := range productSearchs {
	//	ids = append(ids, v.ID)
	//}
	//db = db.Where("id in ?", ids)
	//err = db.Find(&list).Error
	//for k,product := range list {
	//	var productNewData ProductElasticSearch
	//	err,productNewData = HandleData(product)
	//}
	return err, list, total
}
