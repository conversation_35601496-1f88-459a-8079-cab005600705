# 饮水机控制模块 (Water Machine)

## 功能概述

饮水机控制模块提供了基础的设置管理功能，包括开关控制、温度设置、自动关机等功能。

## 功能特性

### 基础设置功能
- ✅ **开关控制**: 启用/禁用饮水机模块
- ✅ **设备信息**: 设置饮水机名称、位置、描述
- ✅ **温度控制**: 设置最高/最低温度范围
- ✅ **自动关机**: 设置自动关机功能和时间
- ✅ **快速切换**: 一键切换开关状态

---

# 主要模块 API 接口说明

## 1. 设备类型管理
| 接口 | 方法 | 参数 | 说明 |
|------|------|------|------|
| /deviceType | POST | name | 新增设备类型 |
| /deviceType | PUT | id, name | 修改设备类型 |
| /deviceType | DELETE | id | 删除设备类型 |
| /deviceType/list | GET |  | 查询设备类型列表 |

## 2. 厂家管理
| 接口 | 方法 | 参数 | 说明 |
|------|------|------|------|
| /manufacturer | POST | name, brand | 新增厂家 |
| /manufacturer | PUT | id, name, brand | 修改厂家 |
| /manufacturer | DELETE | id | 删除厂家 |
| /manufacturer/list | GET |  | 查询厂家列表 |

## 3. 设备管理
| 接口 | 方法 | 参数 | 说明 |
|------|------|------|------|
| /device | POST | name, type_id, device_model, manufacturer_id | 新增设备 |
| /device | PUT | id, name, type_id, device_model, manufacturer_id | 修改设备 |
| /device | DELETE | id | 删除设备 |
| /device/list | GET |  | 查询设备列表 |

## 4. 机器管理
| 接口 | 方法 | 参数 | 说明 |
|------|------|------|------|
| /machine | POST | name, device_id, purchase_side, mall, maintainers(JSON数组), province, city, district, street, address, longitude, latitude | 新增机器 |
| /machine | PUT | id, ...同上 | 修改机器 |
| /machine | DELETE | id | 删除机器 |
| /machine/list | GET |  | 查询机器列表 |
| /machine/import | POST | file (Excel) | 批量导入机器（Excel） |

**说明：**
- maintainers 字段为运维人员ID数组，需传JSON字符串，如 `[1,2,3]`
- /machine/import 需使用 multipart/form-data 上传Excel文件，表头顺序需与字段一致

## 5. 运营中心管理
| 接口 | 方法 | 参数 | 说明 |
|------|------|------|------|
| /operation-center | POST | name, member_ids(JSON数组), data_perm(JSON), admin_account, admin_pwd | 新增运营中心 |
| /operation-center | PUT | id, ...同上 | 修改运营中心 |
| /operation-center | DELETE | id | 删除运营中心 |
| /operation-center/list | GET |  | 查询运营中心列表 |

**说明：**
- member_ids 为会员ID数组，如 `[1,2,3]`
- data_perm 为权限JSON，如 `{"machine":true,"consume":true,"maintainer":false,"repair":true}`
- 返回数据包含 machine_count, maintainer_count 两个统计字段，仅用于显示

## 6. 会员卡管理
| 接口 | 方法 | 参数 | 说明 |
|------|------|------|------|
| /member-card | POST | card_no, status, purchase_side, mall, balance | 新增会员卡 |
| /member-card | PUT | id, ...同上 | 修改会员卡 |
| /member-card | DELETE | id | 删除会员卡 |
| /member-card/list | GET |  | 查询会员卡列表 |
| /member-card/batch-generate | POST | count, status, purchase_side, mall | 批量生成会员卡 |

**说明：**
- card_no 为11位数字字符串，唯一
- status 可选值：正常、停用、挂失
- balance 新增和批量生成时默认100
- 批量生成接口参数通过表单提交（Content-Type: application/x-www-form-urlencoded）

---

## 典型请求示例

### 新增设备类型
```http
POST /deviceType
{
  "name": "饮水机"
}
```

### 批量生成会员卡
```http
POST /member-card/batch-generate
Content-Type: application/x-www-form-urlencoded

count=10&status=正常&purchase_side=采购端A&mall=商城A
```

### 机器Excel导入
```bash
curl -F "file=@/path/to/machine.xlsx" http://localhost:8080/machine/import
```

---

## 其他说明
- 所有POST/PUT/DELETE接口参数均为JSON格式，除批量生成会员卡和机器导入外。
- 返回格式统一为：
  ```json
  {
    "code": 200,
    "msg": "操作成功",
    "data": {...}
  }
  ```
- 列表接口返回字段与模型一致，部分统计字段仅用于显示。

---

## 项目结构

```
water-machine/
├── api/v1/
│   ├── setting.go          # 设置API接口
│   ├── device_type.go      # 设备类型API
│   ├── manufacturer.go     # 厂家API
│   ├── device.go           # 设备API
│   ├── machine.go          # 机器API
│   ├── operation_center.go # 运营中心API
│   ├── member_card.go      # 会员卡API
│   └── ...
├── model/
│   ├── setting.go          # 数据模型
│   ├── device_type.go      # 设备类型模型
│   ├── manufacturer.go     # 厂家模型
│   ├── device.go           # 设备模型
│   ├── machine.go          # 机器模型
│   ├── operation_center.go # 运营中心模型
│   ├── member_card.go      # 会员卡模型
│   └── ...
├── service/
│   └── ...                # 业务逻辑
├── route/
│   └── ...                # 路由注册
└── README.md               # 说明文档
```

---

如需详细字段说明、返回示例或导入模板，请随时告知！ 