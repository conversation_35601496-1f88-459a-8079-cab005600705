package v1

import (
	"water-machine/service"
	"yz-go/source"

	"water-machine/model"

	"github.com/gin-gonic/gin"
)

type AuthorizationListReq struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

// 授权记录分页列表接口
func GetAuthorizationListApi(c *gin.Context) {
	var req AuthorizationListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "参数错误"})
		return
	}
	res, err := service.GetAuthorizationList(source.DB(), req.Page, req.PageSize)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{"list": res.List, "total": res.Total, "page": req.Page, "page_size": req.PageSize})
}

// 创建授权接口
func CreateAuthorizationApi(c *gin.Context) {
	type Req struct {
		InitiatorID uint `json:"initiator_id"`
		TargetID    uint `json:"target_id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.InitiatorID == 0 || req.TargetID == 0 {
		c.JSON(400, gin.H{"error": "参数错误"})
		return
	}
	err := service.CreateAuthorization(source.DB(), req.InitiatorID, req.TargetID)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{"msg": "创建成功"})
}

// 审核授权接口
func ReviewAuthorizationApi(c *gin.Context) {
	type Req struct {
		ID     uint `json:"id"`
		Status int  `json:"status"` // 1授权成功 2拒绝授权
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 || (req.Status != 1 && req.Status != 2) {
		c.JSON(400, gin.H{"error": "参数错误"})
		return
	}
	err := service.ReviewAuthorization(source.DB(), req.ID, model.AuthorizationStatus(req.Status))
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{"msg": "审核成功"})
}
