package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupAuthorizationRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/authorization/list", GetAuthorizationListApi)
	r.POST("/authorization/create", CreateAuthorizationApi)
	r.POST("/authorization/review", ReviewAuthorizationApi)
	return r
}

func TestGetAuthorizationListApi(t *testing.T) {
	r := setupAuthorizationRouter()
	body := map[string]interface{}{"page": 1, "page_size": 10}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/authorization/list", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.Serve<PERSON>TP(w, req)
	assert.Equal(t, 200, w.Code)
	// 输出返回内容
	println("返回内容：", w.Body.String())
}

func TestCreateAuthorizationApi(t *testing.T) {
	r := setupAuthorizationRouter()
	body := map[string]interface{}{"initiator_id": 1, "target_id": 2}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/authorization/create", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)
	println("创建授权返回：", w.Body.String())
}

func TestReviewAuthorizationApi(t *testing.T) {
	r := setupAuthorizationRouter()
	body := map[string]interface{}{"id": 1, "status": 1} // 审核通过
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/authorization/review", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)
	println("审核授权返回：", w.Body.String())
}
