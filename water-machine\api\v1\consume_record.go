package v1

import (
	"water-machine/service"
	"yz-go/source"

	"github.com/gin-gonic/gin"
)

type CreateConsumeRecordReq struct {
	DeviceID    uint   `json:"device_id"`
	CardNo      string `json:"card_no"`
	PurchaseID  uint   `json:"purchase_id"`
	MallID      uint   `json:"mall_id"`
	Amount      int    `json:"amount"`
	WaterVolume int    `json:"water_volume"`
}

// 创建消费记录接口
func CreateConsumeRecordApi(c *gin.Context) {
	var req CreateConsumeRecordReq
	if err := c.ShouldBindJSON(&req); err != nil || req.DeviceID == 0 || req.CardNo == "" || req.PurchaseID == 0 || req.MallID == 0 || req.Amount <= 0 || req.WaterVolume <= 0 {
		c.JSON(400, gin.H{"error": "参数错误"})
		return
	}
	record, err := service.CreateConsumeRecord(source.DB(), req.DeviceID, req.CardNo, req.PurchaseID, req.MallID, req.Amount, req.WaterVolume)
	if err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{"msg": "消费成功", "card_balance": record.CardBalance, "record": record})
}

type ConsumeRecordListReq struct {
	DeviceID   uint   `json:"device_id"`
	CardNo     string `json:"card_no"`
	PurchaseID uint   `json:"purchase_id"`
	MallID     uint   `json:"mall_id"`
	Page       int    `json:"page"`
	PageSize   int    `json:"page_size"`
}

// 消费记录分页列表接口
func GetConsumeRecordListApi(c *gin.Context) {
	var req ConsumeRecordListReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "参数错误"})
		return
	}
	cond := map[string]interface{}{}
	if req.DeviceID != 0 {
		cond["device_id"] = req.DeviceID
	}
	if req.CardNo != "" {
		cond["card_no"] = req.CardNo
	}
	if req.PurchaseID != 0 {
		cond["purchase_id"] = req.PurchaseID
	}
	if req.MallID != 0 {
		cond["mall_id"] = req.MallID
	}
	list, total, err := service.GetConsumeRecordList(source.DB(), cond, req.Page, req.PageSize)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{"list": list, "total": total, "page": req.Page, "page_size": req.PageSize})
}
