package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupConsumeRecordRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/consume_record/create", CreateConsumeRecordApi)
	r.POST("/consume_record/list", GetConsumeRecordListApi)
	return r
}

func TestCreateConsumeRecordApi(t *testing.T) {
	r := setupConsumeRecordRouter()
	body := map[string]interface{}{
		"device_id":    1,
		"card_no":      "12345678901",
		"purchase_id":  1,
		"mall_id":      1,
		"amount":       100,
		"water_volume": 500,
	}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/consume_record/create", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)
	println("消费记录返回：", w.Body.String())
}

func TestGetConsumeRecordListApi(t *testing.T) {
	r := setupConsumeRecordRouter()
	body := map[string]interface{}{
		"page":      1,
		"page_size": 10,
	}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/consume_record/list", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)
	println("消费记录列表返回：", w.Body.String())
}
