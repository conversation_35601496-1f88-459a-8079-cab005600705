package v1

import (
	"github.com/gin-gonic/gin"
	"water-machine/model"
	"water-machine/service"
	yzResponse "yz-go/response"
)

// 新增设备
func CreateDevice(c *gin.Context) {
	var d model.WaterDevice
	if err := c.ShouldBindJSON(&d); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if d.Name == "" || d.TypeID == 0 || d.DeviceModel == "" || d.ManufacturerID == 0 {
		yzResponse.FailWithMessage("设备名称、设备类型、型号、所属厂家不能为空", c)
		return
	}
	if err := service.CreateDevice(&d); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改设备
func UpdateDevice(c *gin.Context) {
	var d model.WaterDevice
	if err := c.ShouldBindJSON(&d); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if d.ID == 0 || d.Name == "" || d.TypeID == 0 || d.DeviceModel == "" || d.ManufacturerID == 0 {
		yzResponse.FailWithMessage("ID、设备名称、设备类型、型号、所属厂家不能为空", c)
		return
	}
	if err := service.UpdateDevice(&d); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除设备
func DeleteDevice(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteDevice(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询设备列表
func GetDeviceList(c *gin.Context) {
	list, err := service.GetDeviceList()
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"list": list}, c)
}
