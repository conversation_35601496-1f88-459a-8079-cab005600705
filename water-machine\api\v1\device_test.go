package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupDeviceRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/device", CreateDevice)
	r.PUT("/device", UpdateDevice)
	r.DELETE("/device", DeleteDevice)
	r.GET("/device/list", GetDeviceList)
	return r
}

func TestDeviceCRUD(t *testing.T) {
	r := setupDeviceRouter()

	// 1. 新增
	dev := model.WaterDevice{Name: "设备A", TypeID: 1, DeviceModel: "X100", ManufacturerID: 1}
	jsonData, _ := json.Marshal(dev)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/device", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.Serve<PERSON>TP(w, req)
	assert.Equal(t, 200, w.Code)

	// 2. 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/device/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")

	// 3. 修改
	// 假设ID为1（实际应从查询结果中获取）
	devUpdate := model.WaterDevice{Model: model.WaterDevice{}.Model, Name: "设备A-修改", TypeID: 1, DeviceModel: "X200", ManufacturerID: 1}
	devUpdate.ID = 4
	jsonDataUp, _ := json.Marshal(devUpdate)
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("PUT", "/device", bytes.NewBuffer(jsonDataUp))
	req3.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, req3)
	assert.Equal(t, 200, w3.Code)

	// 4. 删除
	deleteBody := map[string]interface{}{"id": 4}
	jsonDel, _ := json.Marshal(deleteBody)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest("DELETE", "/device", bytes.NewBuffer(jsonDel))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	assert.Equal(t, 200, w4.Code)
}
