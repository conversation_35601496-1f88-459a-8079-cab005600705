package v1

import (
	"github.com/gin-gonic/gin"
	"water-machine/model"
	"water-machine/service"
	yzResponse "yz-go/response"
)

// 新增设备类型
func CreateDeviceType(c *gin.Context) {
	var deviceType model.WaterDeviceType
	if err := c.ShouldBindJSON(&deviceType); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if deviceType.Name == "" {
		yzResponse.FailWithMessage("名称不能为空", c)
		return
	}
	if err := service.CreateDeviceType(&deviceType); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改设备类型
func UpdateDeviceType(c *gin.Context) {
	var deviceType model.WaterDeviceType
	if err := c.ShouldBind<PERSON>(&deviceType); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if deviceType.ID == 0 || deviceType.Name == "" {
		yzResponse.FailWithMessage("ID和名称不能为空", c)
		return
	}
	if err := service.UpdateDeviceType(&deviceType); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除设备类型
func DeleteDeviceType(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteDeviceType(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询设备类型列表
func GetDeviceTypeList(c *gin.Context) {
	list, err := service.GetDeviceTypeList()
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"list": list}, c)
}
