package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"
	"yz-go/source"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupDeviceTypeRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/deviceType", CreateDeviceType)
	r.PUT("/deviceType", UpdateDeviceType)
	r.DELETE("/deviceType", DeleteDeviceType)
	r.GET("/deviceType/list", GetDeviceTypeList)
	return r
}

func TestDeviceTypeCRUD(t *testing.T) {
	r := setupDeviceTypeRouter()

	// 1. 新增
	dt := model.WaterDeviceType{Name: "测试类型A"}
	jsonData, _ := json.Marshal(dt)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/deviceType", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 2. 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/deviceType/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")

	// 3. 修改
	// 假设ID为1（实际应从查询结果中获取）
	//dtUpdate := model.WaterDeviceType{ID: 1, Name: "测试类型A-修改"}

	dtUpdate := model.WaterDeviceType{
		Model: source.Model{ID: 1},
		Name:  "测试类型A-修改",
	}
	
	jsonDataUp, _ := json.Marshal(dtUpdate)
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("PUT", "/deviceType", bytes.NewBuffer(jsonDataUp))
	req3.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, req3)
	assert.Equal(t, 200, w3.Code)

	// 4. 删除
	deleteBody := map[string]interface{}{"id": 1}
	jsonDel, _ := json.Marshal(deleteBody)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest("DELETE", "/deviceType", bytes.NewBuffer(jsonDel))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	assert.Equal(t, 200, w4.Code)
}
