package v1

import (
	"encoding/json"
	"fmt"
	"strconv"
	"water-machine/model"
	"water-machine/service"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

// 新增机器
func CreateMachine(c *gin.Context) {
	var m model.WaterMachine
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.Name == "" || m.DeviceID == 0 || m.PurchaseID == 0 || m.MallID == 0 || len(m.Maintainers) == 0 ||
		m.Province == "" || m.City == "" || m.District == "" || m.Street == "" || m.Address == "" {
		yzResponse.FailWithMessage("必填项不能为空", c)
		return
	}
	if err := service.CreateMachine(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改机器
func UpdateMachine(c *gin.Context) {
	var m model.WaterMachine
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 {
		yzResponse.FailWithMessage("ID不能为空", c)
		return
	}
	if err := service.UpdateMachine(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除机器
func DeleteMachine(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteMachine(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询机器列表
func GetMachineList(c *gin.Context) {
	list, err := service.GetMachineList()
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	// 反序列化Maintainers为数组
	for i := range list {
		var maintainers []uint
		_ = json.Unmarshal(list[i].Maintainers, &maintainers)
		// 你可以在返回时加到map里
	}
	yzResponse.OkWithData(gin.H{"list": list}, c)
}

// 导入机器信息
func ImportMachineExcel(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		yzResponse.FailWithMessage("文件获取失败", c)
		return
	}
	src, err := file.Open()
	if err != nil {
		yzResponse.FailWithMessage("文件打开失败", c)
		return
	}
	defer src.Close()

	f, err := excelize.OpenReader(src)
	if err != nil {
		yzResponse.FailWithMessage("Excel解析失败", c)
		return
	}

	importCount, err := service.ImportMachineExcelFromFile(f)
	if err != nil {
		yzResponse.FailWithMessage("导入失败: "+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("成功导入"+fmt.Sprint(importCount)+"条数据", c)
}

func parseUint(s string) uint {
	v, _ := strconv.ParseUint(s, 10, 64)
	return uint(v)
}
func parseFloat(s string) float64 {
	v, _ := strconv.ParseFloat(s, 64)
	return v
}
