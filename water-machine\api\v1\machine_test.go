package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupMachineRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/machine", CreateMachine)
	r.PUT("/machine", UpdateMachine)
	r.DELETE("/machine", DeleteMachine)
	r.GET("/machine/list", GetMachineList)
	return r
}

func TestMachineCRUD(t *testing.T) {
	r := setupMachineRouter()

	// 1. 新增
	maintainers, _ := json.Marshal([]uint{1, 2})
	m := model.WaterMachine{
		Name:        "机器A",
		DeviceID:    1,
		PurchaseID:  1, // 原 PurchaseSide
		MallID:      1, // 原 Mall
		Maintainers: maintainers,
		Province:    "省A",
		City:        "市A",
		District:    "区A",
		Street:      "街道A",
		Address:     "详细地址A",
		Longitude:   120.123456,
		Latitude:    30.654321,
	}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/machine", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 2. 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/machine/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")

	// 3. 修改
	mUpdate := m
	mUpdate.Name = "机器A-修改"
	mUpdate.ID = 1
	jsonDataUp, _ := json.Marshal(mUpdate)
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("PUT", "/machine", bytes.NewBuffer(jsonDataUp))
	req3.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, req3)
	assert.Equal(t, 200, w3.Code)

	// 4. 删除
	deleteBody := map[string]interface{}{"id": 1}
	jsonDel, _ := json.Marshal(deleteBody)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest("DELETE", "/machine", bytes.NewBuffer(jsonDel))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	assert.Equal(t, 200, w4.Code)
}
