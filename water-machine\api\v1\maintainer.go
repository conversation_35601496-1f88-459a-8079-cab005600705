package v1

import (
	"water-machine/model"
	"water-machine/request"
	"water-machine/service"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
)

// 新增运维人员
func CreateMaintainer(c *gin.Context) {
	var m model.WaterMaintainer
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.Name == "" || m.MemberID == 0 || m.OperationCenterID == 0 || len(m.DataPerm) == 0 {
		yzResponse.FailWithMessage("必填项不能为空", c)
		return
	}
	if err := service.CreateMaintainer(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改运维人员
func UpdateMaintainer(c *gin.Context) {
	var m model.WaterMaintainer
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 || m.Name == "" || m.MemberID == 0 || m.OperationCenterID == 0 || len(m.DataPerm) == 0 {
		yzResponse.FailWithMessage("ID和必填项不能为空", c)
		return
	}
	if err := service.UpdateMaintainer(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除运维人员
func DeleteMaintainer(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteMaintainer(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询运维人员列表（分页+条件）
func GetMaintainerList(c *gin.Context) {
	var req request.MaintainerSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	list, total, err := service.GetMaintainerListWithPage(req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, c)
}
