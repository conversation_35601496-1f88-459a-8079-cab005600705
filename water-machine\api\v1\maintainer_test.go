package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupMaintainerRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/maintainer", CreateMaintainer)
	r.PUT("/maintainer", UpdateMaintainer)
	r.DELETE("/maintainer", DeleteMaintainer)
	r.GET("/maintainer/list", GetMaintainerList)
	return r
}

func TestMaintainerCRUD(t *testing.T) {
	r := setupMaintainerRouter()

	// 1. 新增
	dataPerm, _ := json.Marshal(map[string]bool{

		"machine": true,
		"repair":  true,
	})

	m := model.WaterMaintainer{
		Name:              "张三",
		MemberID:          1,
		OperationCenterID: 1,
		DataPerm:          dataPerm,
	}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/maintainer", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 2. 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/maintainer/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")

	// 3. 修改
	mUpdate := m
	mUpdate.Name = "李四"
	mUpdate.ID = 1
	jsonDataUp, _ := json.Marshal(mUpdate)
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("PUT", "/maintainer", bytes.NewBuffer(jsonDataUp))
	req3.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, req3)
	assert.Equal(t, 200, w3.Code)

	// 4. 删除
	deleteBody := map[string]interface{}{"id": 1}
	jsonDel, _ := json.Marshal(deleteBody)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest("DELETE", "/maintainer", bytes.NewBuffer(jsonDel))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	assert.Equal(t, 200, w4.Code)
}
