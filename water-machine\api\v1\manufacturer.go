package v1

import (
	"github.com/gin-gonic/gin"
	"water-machine/model"
	"water-machine/service"
	yzResponse "yz-go/response"
)

// 新增厂家
func CreateManufacturer(c *gin.Context) {
	var m model.WaterManufacturer
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.Name == "" || m.Brand == "" {
		yzResponse.FailWithMessage("厂家名称和品牌不能为空", c)
		return
	}
	if err := service.CreateManufacturer(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改厂家
func UpdateManufacturer(c *gin.Context) {
	var m model.WaterManufacturer
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 || m.Name == "" || m.Brand == "" {
		yzResponse.FailWithMessage("ID、厂家名称和品牌不能为空", c)
		return
	}
	if err := service.UpdateManufacturer(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除厂家
func DeleteManufacturer(c *gin.Context) {
	type Req struct{ ID uint `json:"id"` }
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteManufacturer(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询厂家列表
func GetManufacturerList(c *gin.Context) {
	list, err := service.GetManufacturerList()
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"list": list}, c)
} 