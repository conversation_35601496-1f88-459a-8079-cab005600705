package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupManufacturerRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/manufacturer", CreateManufacturer)
	r.PUT("/manufacturer", UpdateManufacturer)
	r.DELETE("/manufacturer", DeleteManufacturer)
	r.GET("/manufacturer/list", GetManufacturerList)
	return r
}

func TestManufacturerCRUD(t *testing.T) {
	r := setupManufacturerRouter()

	// 1. 新增
	m := model.WaterManufacturer{Name: "厂家A", Brand: "品牌A"}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/manufacturer", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 2. 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/manufacturer/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")

	// 3. 修改
	// 假设ID为1（实际应从查询结果中获取）
	mUpdate := model.WaterManufacturer{Model: model.WaterManufacturer{}.Model, Name: "厂家A-修改", Brand: "品牌A-修改"}
	mUpdate.ID = 1
	jsonDataUp, _ := json.Marshal(mUpdate)
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("PUT", "/manufacturer", bytes.NewBuffer(jsonDataUp))
	req3.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, req3)
	assert.Equal(t, 200, w3.Code)

	// 4. 删除
	deleteBody := map[string]interface{}{ "id": 1 }
	jsonDel, _ := json.Marshal(deleteBody)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest("DELETE", "/manufacturer", bytes.NewBuffer(jsonDel))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	assert.Equal(t, 200, w4.Code)
} 