package v1

import (
	"water-machine/model"
	"water-machine/request"
	"water-machine/service"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
)

// 新增会员卡
func CreateMemberCard(c *gin.Context) {
	var m model.WaterMemberCard
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.CardNo == "" || m.Status == "" || m.PurchaseSideID == 0 || m.MallID == 0 {
		yzResponse.FailWithMessage("必填项不能为空", c)
		return
	}
	if err := service.CreateMemberCard(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改会员卡
func UpdateMemberCard(c *gin.Context) {
	var m model.WaterMemberCard
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 || m.CardNo == "" || m.Status == "" || m.PurchaseSideID == 0 || m.MallID == 0 {
		yzResponse.FailWithMessage("ID和必填项不能为空", c)
		return
	}
	if err := service.UpdateMemberCard(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除会员卡
func DeleteMemberCard(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteMemberCard(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询会员卡列表（分页+条件）
func GetMemberCardList(c *gin.Context) {
	var req request.MemberCardSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	list, total, err := service.GetMemberCardListWithPage(req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, c)
}

// 批量生成会员卡
func BatchGenerateMemberCards(c *gin.Context) {
	type Req struct {
		Count          int    `json:"count"`
		Status         string `json:"status"`
		PurchaseSideID uint   `json:"purchase_side_id"`
		MallID         uint   `json:"mall_id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.Count <= 0 || req.PurchaseSideID == 0 || req.MallID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	cards, err := service.BatchGenerateMemberCards(req.Count, req.Status, req.PurchaseSideID, req.MallID)
	if err != nil {
		yzResponse.FailWithMessage("批量生成失败: "+err.Error(), c)
		return
	}

	yzResponse.OkWithData(cards, c)
}

// 会员卡充值余额
func RechargeMemberCardBalanceApi(c *gin.Context) {
	type Req struct {
		ID     uint    `json:"id"`
		Amount float64 `json:"amount"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 || req.Amount <= 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.RechargeMemberCardBalance(req.ID, req.Amount); err != nil {
		yzResponse.FailWithMessage("充值失败: "+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("充值成功", c)
}

// 会员卡换绑接口，支持批量
func RebindMemberCardApi(c *gin.Context) {
	type Req struct {
		IDs             []uint `json:"ids"` // 支持批量
		NewPurchaseSide uint   `json:"new_purchase_side_id"`
		NewMall         uint   `json:"new_mall_id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || len(req.IDs) == 0 || req.NewPurchaseSide == 0 || req.NewMall == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.RebindMemberCard(req.IDs, req.NewPurchaseSide, req.NewMall); err != nil {
		yzResponse.FailWithMessage("换绑失败: "+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("换绑成功", c)
}

// 查询会员卡换绑记录（分页+条件）
func GetMemberCardRebindListApi(c *gin.Context) {
	var req request.MemberCardRebindSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	list, total, err := service.GetMemberCardRebindList(req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, c)
}
