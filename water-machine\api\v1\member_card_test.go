package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupMemberCardRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/member-card", CreateMemberCard)
	r.PUT("/member-card", UpdateMemberCard)
	r.DELETE("/member-card", DeleteMemberCard)
	r.GET("/member-card/list", GetMemberCardList)
	r.POST("/member-card/batch-generate", BatchGenerateMemberCards)
	return r
}

func TestMemberCardCRUD(t *testing.T) {
	r := setupMemberCardRouter()

	// 1. 新增
	m := model.WaterMemberCard{
		CardNo:       "12345678901",
		Status:       "正常",
		PurchaseSide: model.ApplicationInfo{ID: 1, AppName: "采购端A", CompanyName: "公司A"},
		Mall:         model.MallInfo{ID: 1, ApplicationID: 1, ShopName: "商城A", CallbackLink: "", CallBackLinkValidity: 0, AppSecret: "", IsMessagePool: 0},
	}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/member-card", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 2. 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/member-card/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")

	// 3. 修改
	mUpdate := m
	mUpdate.Status = "停用"
	mUpdate.ID = 1
	jsonDataUp, _ := json.Marshal(mUpdate)
	w3 := httptest.NewRecorder()
	req3, _ := http.NewRequest("PUT", "/member-card", bytes.NewBuffer(jsonDataUp))
	req3.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w3, req3)
	assert.Equal(t, 200, w3.Code)

	// 4. 删除
	deleteBody := map[string]interface{}{"id": 1}
	jsonDel, _ := json.Marshal(deleteBody)
	w4 := httptest.NewRecorder()
	req4, _ := http.NewRequest("DELETE", "/member-card", bytes.NewBuffer(jsonDel))
	req4.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w4, req4)
	assert.Equal(t, 200, w4.Code)

	// 5. 批量生成
	w5 := httptest.NewRecorder()
	form := bytes.NewBufferString("count=2&status=正常&purchase_side=采购端A&mall=商城A")
	req5, _ := http.NewRequest("POST", "/member-card/batch-generate", form)
	req5.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	r.ServeHTTP(w5, req5)
	assert.Equal(t, 200, w5.Code)
}

// 充值余额接口测试
func TestRechargeMemberCardBalanceApi(t *testing.T) {
	r := gin.Default()
	r.POST("/member-card/recharge", RechargeMemberCardBalanceApi)
	body := struct {
		ID     uint    `json:"id"`
		Amount float64 `json:"amount"`
	}{ID: 1, Amount: 20.0}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/member-card/recharge", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}
