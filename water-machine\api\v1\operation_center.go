package v1

import (
	"water-machine/model"
	"water-machine/request"
	"water-machine/service"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
)

// 新增运营中心
func CreateOperationCenter(c *gin.Context) {
	var m model.WaterOperationCenter
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.Name == "" || m.MemberID == 0 {
		yzResponse.FailWithMessage("必填项不能为空", c)
		return
	}
	if err := service.CreateOperationCenter(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改运营中心
func UpdateOperationCenter(c *gin.Context) {
	var m model.WaterOperationCenter
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 || m.Name == "" || m.MemberID == 0 || len(m.DataPerm) == 0 {
		yzResponse.FailWithMessage("ID和必填项不能为空", c)
		return
	}
	if err := service.UpdateOperationCenter(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除运营中心
func DeleteOperationCenter(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteOperationCenter(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询运营中心列表（分页+条件）
func GetOperationCenterList(c *gin.Context) {
	var req request.OperationCenterSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	list, total, err := service.GetOperationCenterListWithPage(req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, c)
}
