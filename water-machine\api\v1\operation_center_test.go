package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupOperationCenterRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	r := gin.New()
	r.POST("/operation-center", CreateOperationCenter)
	r.PUT("/operation-center", UpdateOperationCenter)
	r.DELETE("/operation-center", DeleteOperationCenter)
	r.GET("/operation-center/list", GetOperationCenterList)
	return r
}

func TestCreateOperationCenter(t *testing.T) {
	r := setupOperationCenterRouter()

	dataPerm, _ := json.Marshal(map[string]bool{
		"machine":    true,
		"consume":    true,
		"maintainer": false,
		"repair":     true,
	})
	m := model.WaterOperationCenter{
		Name:     "运营中心A",
		MemberID: 2,
		DataPerm: dataPerm,
	}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/operation-center", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)
}

func TestGetOperationCenterList(t *testing.T) {
	r := setupOperationCenterRouter()

	// 查询列表
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("GET", "/operation-center/list", nil)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
	var respList map[string]interface{}
	_ = json.Unmarshal(w2.Body.Bytes(), &respList)
	assert.Contains(t, respList, "data")
	// 输出json内容
	println("返回内容：", w2.Body.String())
}

func TestUpdateOperationCenter(t *testing.T) {
	r := setupOperationCenterRouter()

	// 先插入一条数据

	dataPerm, _ := json.Marshal(map[string]bool{
		"machine":    true,
		"consume":    true,
		"maintainer": false,
		"repair":     true,
	})
	m := model.WaterOperationCenter{
		Name:     "运营中心A",
		MemberID: 1,
		DataPerm: dataPerm,
	}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/operation-center", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 修改
	mUpdate := m
	mUpdate.Name = "运营中心A-修改"
	mUpdate.ID = 1
	jsonDataUp, _ := json.Marshal(mUpdate)
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("PUT", "/operation-center", bytes.NewBuffer(jsonDataUp))
	req2.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
}

func TestDeleteOperationCenter(t *testing.T) {
	r := setupOperationCenterRouter()

	// 先插入一条数据

	dataPerm, _ := json.Marshal(map[string]bool{
		"machine":    true,
		"consume":    true,
		"maintainer": false,
		"repair":     true,
	})
	m := model.WaterOperationCenter{
		Name:     "运营中心A",
		MemberID: 1,
		DataPerm: dataPerm,
	}
	jsonData, _ := json.Marshal(m)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/operation-center", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w, req)
	assert.Equal(t, 200, w.Code)

	// 删除
	deleteBody := map[string]interface{}{"id": 1}
	jsonDel, _ := json.Marshal(deleteBody)
	w2 := httptest.NewRecorder()
	req2, _ := http.NewRequest("DELETE", "/operation-center", bytes.NewBuffer(jsonDel))
	req2.Header.Set("Content-Type", "application/json")
	r.ServeHTTP(w2, req2)
	assert.Equal(t, 200, w2.Code)
}
