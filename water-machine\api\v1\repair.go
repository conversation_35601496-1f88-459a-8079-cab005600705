package v1

import (
	"water-machine/model"
	"water-machine/request"
	"water-machine/service"
	yzResponse "yz-go/response"
	"yz-go/source"

	"github.com/gin-gonic/gin"
)

// 新增报修记录
func CreateRepairRecord(c *gin.Context) {
	var m model.WaterRepairRecord
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.MachineID == 0 || m.PurchaseID == 0 || m.MallID == 0 || m.RepairAddr == "" || m.RepairIssue == "" || m.MemberID == 0 {
		yzResponse.FailWithMessage("必填项不能为空", c)
		return
	}
	if err := service.CreateRepairRecord(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改报修记录
func UpdateRepairRecord(c *gin.Context) {
	var m model.WaterRepairRecord
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 || m.MachineID == 0 || m.PurchaseID == 0 || m.MallID == 0 || m.RepairAddr == "" || m.RepairIssue == "" || m.MemberID == 0 {
		yzResponse.FailWithMessage("ID和必填项不能为空", c)
		return
	}
	if err := service.UpdateRepairRecord(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除报修记录
func DeleteRepairRecord(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteRepairRecord(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询报修记录列表（分页+条件）
func GetRepairRecordList(c *gin.Context) {
	var req request.RepairRecordSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	list, total, err := service.GetRepairRecordListWithPage(req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, c)
}

// 查询报修记录接口
func GetRepairRecords(c *gin.Context) {
	var conditions map[string]interface{}
	if err := c.ShouldBindJSON(&conditions); err != nil {
		c.JSON(400, gin.H{"error": "参数错误"})
		return
	}
	db := source.DB()
	records, err := service.GetRepairRecords(db, conditions)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{"list": records})
}
