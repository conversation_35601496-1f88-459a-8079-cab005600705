package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
)

// 新增报修记录测试
func TestCreateRepairRecord(t *testing.T) {
	r := gin.Default()
	r.POST("/repair", CreateRepairRecord)
	body := model.WaterRepairRecord{
		MachineID:   1,
		PurchaseID:  1,
		MallID:      3,
		RepairAddr:  "测试地址",
		RepairIssue: "无法出水",
		MemberID:    1,
		Status:      0,
		Remark:      "测试备注",
	}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/repair", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 查询报修记录列表测试
func TestGetRepairRecordList(t *testing.T) {
	r := gin.Default()
	r.GET("/repair/list", GetRepairRecordList)
	req, _ := http.NewRequest("GET", "/repair/list", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 查询报修记录列表并输出json
func TestGetRepairRecordListWithJson(t *testing.T) {
	r := gin.Default()
	r.GET("/repair/list", GetRepairRecordList)
	req, _ := http.NewRequest("GET", "/repair/list", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
	// 输出json内容
	println("返回内容：", w.Body.String())
}

// 修改报修记录测试
func TestUpdateRepairRecord(t *testing.T) {
	r := gin.Default()
	r.PUT("/repair", UpdateRepairRecord)
	body := model.WaterRepairRecord{
		MachineID:   1,
		PurchaseID:  1,
		MallID:      3,
		RepairAddr:  "新地址",
		RepairIssue: "新问题",
		MemberID:    1,
		Status:      1,
		Remark:      "已处理",
	}
	body.ID = 1
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("PUT", "/repair", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 删除报修记录测试
func TestDeleteRepairRecord(t *testing.T) {
	r := gin.Default()
	r.DELETE("/repair", DeleteRepairRecord)
	body := struct {
		ID uint `json:"id"`
	}{ID: 1}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("DELETE", "/repair", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 查询报修记录（带Preload会员、采购端、商城信息）接口测试
func TestGetRepairRecords(t *testing.T) {
	r := gin.Default()
	r.POST("/repair/query", GetRepairRecords)
	// 构造条件参数（可为空，查全部）
	cond := map[string]interface{}{}
	jsonValue, _ := json.Marshal(cond)
	req, _ := http.NewRequest("POST", "/repair/query", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
	// 输出json内容
	println("返回内容：", w.Body.String())
}
