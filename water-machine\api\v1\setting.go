package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"water-machine/model"
	"water-machine/service"
	yzResponse "yz-go/response"
)

// GetWaterMachineSetting 获取饮水机设置
// @Summary 获取饮水机设置
// @Description 获取饮水机基础设置信息
// @Tags 饮水机设置
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Router /water-machine/setting [get]
func GetWaterMachineSetting(c *gin.Context) {
	err, setting := model.GetWaterMachineSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

// UpdateWaterMachineSetting 更新饮水机设置
// @Summary 更新饮水机设置
// @Description 更新饮水机基础设置信息
// @Tags 饮水机设置
// @Accept json
// @Produce json
// @Param setting body model.WaterMachineValue true "饮水机设置"
// @Success 200 {object} gin.H
// @Router /water-machine/setting [put]
func UpdateWaterMachineSetting(c *gin.Context) {
	var setting model.WaterMachineValue
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}

	err = service.UpdateWaterMachineSetting(setting)
	if err != nil {
		yzResponse.FailWithMessage("更新设置失败", c)
		return
	}

	yzResponse.OkWithMessage("更新设置成功", c)
}

// ToggleWaterMachine 切换饮水机开关状态
// @Summary 切换饮水机开关状态
// @Description 快速切换饮水机启用/禁用状态
// @Tags 饮水机设置
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Router /water-machine/setting/toggle [post]
func ToggleWaterMachine(c *gin.Context) {
	err, setting := model.GetWaterMachineSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}

	// 切换开关状态
	if setting.IsEnabled == 1 {
		setting.IsEnabled = 0
	} else {
		setting.IsEnabled = 1
	}

	err = service.UpdateWaterMachineSetting(setting)
	if err != nil {
		yzResponse.FailWithMessage("切换状态失败", c)
		return
	}

	status := "启用"
	if setting.IsEnabled == 0 {
		status = "禁用"
	}

	yzResponse.OkWithMessage("饮水机已"+status, c)
}
