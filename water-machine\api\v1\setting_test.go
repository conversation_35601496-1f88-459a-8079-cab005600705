package v1

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"water-machine/model"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetWaterMachineSetting(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/water-machine/setting", GetWaterMachineSetting)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/water-machine/setting", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response, "data")
	fmt.Print("333")
}

func TestUpdateWaterMachineSetting(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.PUT("/water-machine/setting", UpdateWaterMachineSetting)

	setting := model.WaterMachineValue{
		IsEnabled:  0,
		ID:         167,
		NotifyType: 1,
		TemplateID: "123456",
	}

	jsonData, _ := json.Marshal(setting)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PUT", "/water-machine/setting", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
}

func TestToggleWaterMachine(t *testing.T) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.POST("/water-machine/setting/toggle", ToggleWaterMachine)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/water-machine/setting/toggle", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
}
