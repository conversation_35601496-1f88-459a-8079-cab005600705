package v1

import (
	"water-machine/model"
	"water-machine/request"
	"water-machine/service"
	yzResponse "yz-go/response"

	"github.com/gin-gonic/gin"
)

// 新增SIM卡
func CreateSimCard(c *gin.Context) {
	var m model.WaterSimCard
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.SimNo == "" || m.Status == "" || m.Operator == "" || m.DeviceNo == "" || m.ExpireAt.IsZero() {
		yzResponse.FailWithMessage("必填项不能为空", c)
		return
	}
	if err := service.CreateSimCard(&m); err != nil {
		yzResponse.FailWithMessage("新增失败", c)
		return
	}
	yzResponse.OkWithMessage("新增成功", c)
}

// 修改SIM卡
func UpdateSimCard(c *gin.Context) {
	var m model.WaterSimCard
	if err := c.ShouldBindJSON(&m); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if m.ID == 0 || m.SimNo == "" || m.Status == "" || m.Operator == "" || m.DeviceNo == "" || m.ExpireAt.IsZero() {
		yzResponse.FailWithMessage("ID和必填项不能为空", c)
		return
	}
	if err := service.UpdateSimCard(&m); err != nil {
		yzResponse.FailWithMessage("修改失败", c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

// 删除SIM卡
func DeleteSimCard(c *gin.Context) {
	type Req struct {
		ID uint `json:"id"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.DeleteSimCard(req.ID); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// 查询SIM卡列表（分页+条件）
func GetSimCardList(c *gin.Context) {
	var req request.SimCardSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	list, total, err := service.GetSimCardListWithPage(req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{
		"list":     list,
		"total":    total,
		"page":     req.Page,
		"pageSize": req.PageSize,
	}, c)
}

// 绑定SIM卡到设备号
func BindSimCardToDeviceApi(c *gin.Context) {
	type Req struct {
		ID       uint   `json:"id"`
		DeviceNo string `json:"device_no"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil || req.ID == 0 || req.DeviceNo == "" {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	if err := service.BindSimCardToDevice(req.ID, req.DeviceNo); err != nil {
		yzResponse.FailWithMessage("绑定失败: "+err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("绑定成功", c)
}
