package v1

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"water-machine/model"

	"github.com/gin-gonic/gin"
)

// 新增SIM卡接口测试
func TestCreateSimCard(t *testing.T) {
	r := gin.Default()
	r.POST("/sim-card", CreateSimCard)
	body := model.WaterSimCard{
		SimNo:      "8986001234567890123",
		Status:     "已激活",
		Operator:   "移动",
		MonthUsage: 123.45,
		ExpireAt:   time.Now().AddDate(1, 0, 0),
		DeviceNo:   "DEV123456",
		Amount:     50.0,
	}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/sim-card", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.Serve<PERSON>TP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 查询SIM卡列表接口测试
func TestGetSimCardList(t *testing.T) {
	r := gin.Default()
	r.GET("/sim-card/list", GetSimCardList)
	req, _ := http.NewRequest("GET", "/sim-card/list", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 修改SIM卡接口测试
func TestUpdateSimCard(t *testing.T) {
	r := gin.Default()
	r.PUT("/sim-card", UpdateSimCard)
	body := model.WaterSimCard{
		SimNo:      "8986001234567890123",
		Status:     "未激活",
		Operator:   "联通",
		MonthUsage: 200.0,
		ExpireAt:   time.Now().AddDate(2, 0, 0),
		DeviceNo:   "DEV654321",
		Amount:     100.0,
	}
	body.ID = 1
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("PUT", "/sim-card", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}

// 删除SIM卡接口测试
func TestDeleteSimCard(t *testing.T) {
	r := gin.Default()
	r.DELETE("/sim-card", DeleteSimCard)
	body := struct {
		ID uint `json:"id"`
	}{ID: 1}
	jsonValue, _ := json.Marshal(body)
	req, _ := http.NewRequest("DELETE", "/sim-card", bytes.NewBuffer(jsonValue))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	if w.Code != 200 {
		t.Errorf("expected 200, got %d, body: %s", w.Code, w.Body.String())
	}
}
