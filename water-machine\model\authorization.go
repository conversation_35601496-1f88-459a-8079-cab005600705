package model

import (
	"yz-go/source"
)

// 授权状态常量
type AuthorizationStatus int

const (
	AuthorizationPending  AuthorizationStatus = iota // 等待确认
	AuthorizationApproved                            // 授权成功
	AuthorizationRejected                            // 拒绝授权
)

// 授权记录
// 发起采购端、授权采购端均为采购端ID（uint）
type WaterAuthorization struct {
	source.Model
	InitiatorID uint                `json:"initiator_id" gorm:"not null;comment:发起采购端ID"`
	TargetID    uint                `json:"target_id" gorm:"not null;comment:授权采购端ID"`
	Status      AuthorizationStatus `json:"status" gorm:"not null;default:0;comment:审核状态(0等待确认,1授权成功,2拒绝授权)"`
}
