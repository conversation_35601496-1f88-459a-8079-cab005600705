package model

import (
	"yz-go/source"
)

// 消费记录
// 设备ID、会员卡号、采购端ID、商城ID、消费金额（分）、会员卡余额、出水量
// 会员卡余额为消费后余额
// CardNo 为字符串，其他为 uint/int

type WaterConsumeRecord struct {
	source.Model
	DeviceID    uint   `json:"device_id" gorm:"not null;comment:设备ID"`
	CardNo      string `json:"card_no" gorm:"type:varchar(32);not null;comment:会员卡号"`
	PurchaseID  uint   `json:"purchase_id" gorm:"not null;comment:采购端ID"`
	MallID      uint   `json:"mall_id" gorm:"not null;comment:商城ID"`
	Amount      int    `json:"amount" gorm:"not null;comment:消费金额(分)"`
	CardBalance int    `json:"card_balance" gorm:"not null;comment:消费后会员卡余额(分)"`
	WaterVolume int    `json:"water_volume" gorm:"not null;comment:出水量(单位:毫升)"`

	// 关联结构体
	Machine  WaterMachine    `json:"machine" gorm:"foreignKey:DeviceID;references:DeviceID"`
	Purchase ApplicationInfo `json:"purchase" gorm:"foreignKey:PurchaseID;references:ID"`
	Mall     MallInfo        `json:"mall" gorm:"foreignKey:MallID;references:ID"`
}
