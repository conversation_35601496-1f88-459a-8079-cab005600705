package model

import "yz-go/source"

// WaterDevice 设备管理
// 字段：设备名称、设备类型、型号、所属厂家
type WaterDevice struct {
	source.Model
	Name           string `json:"name" gorm:"type:varchar(64);not null;comment:设备名称"`
	TypeID         uint   `json:"type_id" gorm:"not null;comment:设备类型ID"`
	DeviceModel    string `json:"device_model" gorm:"type:varchar(64);not null;comment:型号"`
	ManufacturerID uint   `json:"manufacturer_id" gorm:"not null;comment:所属厂家ID"`
}
