package model

import (
	"yz-go/source"

	"gorm.io/datatypes"
)

// WaterMachine 机器管理
// 字段：机器名称、关联设备、采购端、商城、运维人员（JSON数组）、省市区街道、详细地址、经度、纬度
type WaterMachine struct {
	source.Model
	Name        string         `json:"name" gorm:"type:varchar(64);not null;comment:机器名称"`
	DeviceID    uint           `json:"device_id" gorm:"not null;comment:关联设备ID"`
	PurchaseID  uint           `json:"purchase_id" gorm:"not null;comment:采购端ID"`
	MallID      uint           `json:"mall_id" gorm:"not null;comment:商城ID"`
	Maintainers datatypes.JSON `json:"maintainers" gorm:"type:json;comment:运维人员ID数组"`
	Province    string         `json:"province" gorm:"type:varchar(32);not null;comment:省"`
	City        string         `json:"city" gorm:"type:varchar(32);not null;comment:市"`
	District    string         `json:"district" gorm:"type:varchar(32);not null;comment:区"`
	Street      string         `json:"street" gorm:"type:varchar(64);not null;comment:街道"`
	Address     string         `json:"address" gorm:"type:varchar(128);not null;comment:详细地址"`
	Longitude   float64        `json:"longitude" gorm:"comment:经度"`
	Latitude    float64        `json:"latitude" gorm:"comment:纬度"`
}
