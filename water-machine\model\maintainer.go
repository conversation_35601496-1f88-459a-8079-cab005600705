package model

import (
	"yz-go/source"

	"gorm.io/datatypes"
)

// WaterMaintainer 运维人员管理
// 字段：姓名、绑定会员ID、绑定运营中心ID、数据权限（JSON）
type WaterMaintainer struct {
	source.Model
	Name              string         `json:"name" gorm:"type:varchar(64);not null;comment:运维人员姓名"`
	MemberID          uint           `json:"member_id" gorm:"not null;comment:绑定会员ID"`
	OperationCenterID uint           `json:"operation_center_id" gorm:"not null;comment:绑定运营中心ID"`
	DataPerm          datatypes.JSON `json:"data_perm" gorm:"type:json;comment:数据权限(JSON: machine, repair)"`

	MemberInfo          UserInfo            `json:"member_info" gorm:"foreignKey:MemberID;references:ID"`
	OperationCenterInfo OperationCenterInfo `json:"operation_center_info" gorm:"foreignKey:OperationCenterID;references:ID"`
}

// UserInfo 会员信息（只保留常用字段，避免循环依赖）
type UserInfo struct {
	ID       uint   `json:"id"`
	Mobile   string `json:"mobile"`
	Username string `json:"username"`
	NickName string `json:"nickname"`
}

func (UserInfo) TableName() string {
	return "users"
}

// OperationCenterInfo 运营中心信息（只保留常用字段，避免循环依赖）
type OperationCenterInfo struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

func (OperationCenterInfo) TableName() string {
	return "water_operation_centers"
}
