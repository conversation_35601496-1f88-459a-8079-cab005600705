package model

import (
	"time"
	"yz-go/source"
)

// WaterMemberCard 会员卡管理
// 字段：卡号、状态、采购端、商城、余额
type WaterMemberCard struct {
	source.Model
	CardNo         string `json:"card_no" gorm:"type:varchar(16);not null;unique;comment:卡号"`
	Status         string `json:"status" gorm:"type:varchar(8);not null;comment:状态(正常,停用,挂失)"`
	PurchaseSideID uint   `json:"purchase_side_id" gorm:"not null;comment:采购端ID"`
	MallID         uint   `json:"mall_id" gorm:"not null;comment:商城ID"`
	Balance        int    `json:"balance" gorm:"not null;default:10000;comment:余额(分)"`

	PurchaseSide ApplicationInfo `json:"purchase_side" gorm:"foreignKey:PurchaseSideID;references:ID"`
	Mall         MallInfo        `json:"mall" gorm:"foreignKey:MallID;references:ID"`
}

// ApplicationInfo 采购端/商城信息
// 只保留常用字段，避免循环依赖
// 采购端和商城都在 application 表

type ApplicationInfo struct {
	ID          uint   `json:"id"`
	AppName     string `json:"app_name"`
	CompanyName string `json:"company_name"`
}

func (ApplicationInfo) TableName() string {
	return "application"
}

// MallInfo 商城信息（对应 application_shops 表）
type MallInfo struct {
	ID                   uint   `json:"id"`
	ApplicationID        uint   `json:"application_id"`
	ShopName             string `json:"shop_name"`
	CallbackLink         string `json:"callback_link"`
	CallBackLinkValidity int    `json:"call_back_link_validity"`
	AppSecret            string `json:"app_secret"`
	IsMessagePool        int    `json:"is_message_pool"`
}

func (MallInfo) TableName() string {
	return "application_shops"
}

// 会员卡换绑记录
// 字段：会员卡号、原采购端ID、新采购端ID、原商城ID、新商城ID、换绑时间
type WaterMemberCardRebind struct {
	ID                uint      `json:"id" gorm:"primaryKey"`
	CardNo            string    `json:"card_no" gorm:"type:varchar(16);not null;comment:会员卡号"`
	OldPurchaseSideID uint      `json:"old_purchase_side_id" gorm:"not null;comment:原采购端ID"`
	NewPurchaseSideID uint      `json:"new_purchase_side_id" gorm:"not null;comment:新采购端ID"`
	OldMallID         uint      `json:"old_mall_id" gorm:"not null;comment:原商城ID"`
	NewMallID         uint      `json:"new_mall_id" gorm:"not null;comment:新商城ID"`
	RebindTime        time.Time `json:"rebind_time" gorm:"not null;comment:换绑时间"`

	OldPurchaseSide ApplicationInfo `json:"old_purchase_side" gorm:"foreignKey:OldPurchaseSideID;references:ID"`
	NewPurchaseSide ApplicationInfo `json:"new_purchase_side" gorm:"foreignKey:NewPurchaseSideID;references:ID"`
	OldMall         MallInfo        `json:"old_mall" gorm:"foreignKey:OldMallID;references:ID"`
	NewMall         MallInfo        `json:"new_mall" gorm:"foreignKey:NewMallID;references:ID"`
}
