package model

import (
	_ "embed"
	"encoding/json"
	"gin-vue-admin/admin/model"
	"time"
	"yz-go/component/log"
	"yz-go/source"

	"go.uber.org/zap"
)

//go:embed menu.json
var menu string

//go:embed admin_menu.json
var adminMenu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		WaterDeviceType{},
		WaterManufacturer{},
		WaterDevice{},
		WaterMachine{},
		WaterOperationCenter{},
		WaterMemberCard{},
		WaterMaintainer{},
		WaterRepairRecord{},
		WaterSimCard{},
		WaterMemberCardRebind{},
		WaterAuthorization{},
		WaterConsumeRecord{},
	)

	var authorities = model.SysAuthority{CreatedAt: &source.LocalTime{time.Now()}, UpdatedAt: &source.LocalTime{time.Now()}, AuthorityId: "115", AuthorityName: "净水机运营中心", ParentId: "0", DefaultRouter: "dashboard"}

	menus := []model.SysMenu{}
	admin_menus := []model.SysMenu{}

	menuJson := menu
	adminMenuJson := adminMenu
	err = json.Unmarshal([]byte(menuJson), &menus)
	if err != nil {
		return err
	}
	err = json.Unmarshal([]byte(adminMenuJson), &admin_menus)
	if err != nil {
		return err
	}
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)
	model.GVA_WATER_MENUS = append(model.GVA_WATER_MENUS, admin_menus...)

	err = source.DB().Where("authority_id=?", 115).FirstOrCreate(&authorities).Error
	if err != nil {
		log.Log().Error("water authorityId error", zap.Any("err", err))
		return
	}
	return
}
