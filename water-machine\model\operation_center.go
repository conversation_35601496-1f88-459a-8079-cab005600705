package model

import (
	usermodel "user/model"
	"yz-go/source"

	"gorm.io/datatypes"
)

// WaterOperationCenter 运营中心管理
// 字段：名称、关联会员、数据权限（JSON）、独立后台账号、独立后台密码
// 统计字段：机器数量、运维人员数量（不参与数据库操作）
type WaterOperationCenter struct {
	source.Model
	Name     string         `json:"name" gorm:"type:varchar(64);not null;comment:运营中心名称"`
	MemberID uint           `json:"member_id" gorm:"not null;comment:关联会员ID"`
	DataPerm datatypes.JSON `json:"data_perm" gorm:"type:json;comment:数据权限(JSON)"`
	//AdminAccount string         `json:"admin_account" gorm:"type:varchar(64);not null;comment:独立后台账号"`
	//AdminPwd     string         `json:"admin_pwd" gorm:"type:varchar(128);not null;comment:独立后台密码(加密)"`

	MachineCount    int `json:"machine_count" gorm:"-"`    // 机器数量（仅显示）
	MaintainerCount int `json:"maintainer_count" gorm:"-"` // 运维人员数量（仅显示）

	Member usermodel.User `json:"member" gorm:"foreignKey:MemberID;references:ID"` // 会员信息，Preload 查询
}
