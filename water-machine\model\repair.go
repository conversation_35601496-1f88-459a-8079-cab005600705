package model

import (
	appmodel "application/model"
	usermodel "user/model"
	"yz-go/source"
)

// WaterRepairRecord 报修记录
// 字段：机器id,采购端,商城,报修地址,报修问题,报修人（会员id）,报修状态(已处理，未处理),备注
// 机器、会员后续可做外键扩展
// Status: 0-未处理 1-已处理

type WaterRepairRecord struct {
	source.Model
	MachineID   uint   `json:"machine_id" gorm:"not null;comment:机器id"`
	PurchaseID  uint   `json:"purchase_id" gorm:"not null;comment:采购端id"`
	MallID      uint   `json:"mall_id" gorm:"not null;comment:商城id"`
	RepairAddr  string `json:"repair_addr" gorm:"type:varchar(128);not null;comment:报修地址"`
	RepairIssue string `json:"repair_issue" gorm:"type:varchar(255);not null;comment:报修问题"`
	MemberID    uint   `json:"member_id" gorm:"not null;comment:报修人(会员id)"`
	Status      int    `json:"status" gorm:"not null;default:0;comment:报修状态(0未处理,1已处理)"`
	Remark      string `json:"remark" gorm:"type:varchar(255);comment:备注"`

	// 关联结构体
	Member   usermodel.User           `json:"member" gorm:"foreignKey:MemberID;references:ID"`
	Purchase appmodel.Application     `json:"purchase" gorm:"foreignKey:PurchaseID;references:ID"`
	Mall     appmodel.ApplicationShop `json:"mall" gorm:"foreignKey:MallID;references:ID"`
}
