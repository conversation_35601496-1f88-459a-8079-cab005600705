package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

// WaterMachineSetting 饮水机设置模型
type WaterMachineSetting struct {
	model.SysSetting
	Value WaterMachineValue `json:"value"`
}

func (i WaterMachineSetting) TableName() string {
	return "sys_settings"
}

// WaterMachineValue 饮水机设置值
type WaterMachineValue struct {
	IsEnabled  int    `json:"is_enabled"`  // 是否启用饮水机模块 0:禁用 1:启用
	ID         uint   `json:"id" gorm:"-"` //
	NotifyType uint   `json:"notify_type"` // 通知类型  1 短信 2 公众号
	TemplateID string `json:"template_id"` // 模板id

}

func (value WaterMachineValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *WaterMachineValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var waterMachineSetting *WaterMachineValue

// getWaterMachineSetting 获取饮水机设置
func getWaterMachineSetting(key string) (err error, sysSetting WaterMachineSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}

// GetWaterMachineSetting 获取饮水机设置
func GetWaterMachineSetting() (err error, setting WaterMachineValue) {
	if waterMachineSetting == nil {
		var sysSetting WaterMachineSetting
		err, sysSetting = getWaterMachineSetting("water_machine_setting")
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回默认设置
			setting = WaterMachineValue{
				IsEnabled: 0,
			}
			err = nil
			return
		}
		if err != nil {
			return
		}
		waterMachineSetting = &sysSetting.Value
	}
	return err, *waterMachineSetting
}

// ResetWaterMachineSetting 重置饮水机设置缓存
func ResetWaterMachineSetting() {
	waterMachineSetting = nil
}
