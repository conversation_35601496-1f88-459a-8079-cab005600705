package model

import (
	"time"
	"yz-go/source"
)

// WaterSimCard SIM卡管理
// 字段：SIM卡号、状态、运营商、本月用量、到期时间、设备号、金额

type WaterSimCard struct {
	source.Model
	SimNo      string    `json:"sim_no" gorm:"type:varchar(32);not null;unique;comment:SIM卡号"`
	Status     string    `json:"status" gorm:"type:varchar(16);not null;default:'未激活';comment:状态(已激活,未激活)"`
	Operator   string    `json:"operator" gorm:"type:varchar(32);not null;comment:运营商"`
	MonthUsage float64   `json:"month_usage" gorm:"type:decimal(10,2);not null;default:0;comment:本月用量(MB)"`
	ExpireAt   time.Time `json:"expire_at" gorm:"not null;comment:到期时间"`
	DeviceNo   string    `json:"device_no" gorm:"type:varchar(64);not null;comment:设备号"`
	Amount     float64   `json:"amount" gorm:"type:decimal(10,2);not null;default:0;comment:金额"`
}
