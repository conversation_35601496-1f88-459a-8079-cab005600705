package request

type MemberCardSearch struct {
	CardNo         string `json:"card_no" form:"card_no"`
	Status         string `json:"status" form:"status"`
	PurchaseSideID uint   `json:"purchase_side_id" form:"purchase_side_id"`
	MallID         uint   `json:"mall_id" form:"mall_id"`
	Page           int    `json:"page" form:"page"`
	PageSize       int    `json:"pageSize" form:"pageSize"`
}

type MemberCardRebindSearch struct {
	CardNo   string `json:"card_no" form:"card_no"`
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"pageSize" form:"pageSize"`
}
