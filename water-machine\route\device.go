package route

import (
	"github.com/gin-gonic/gin"
	v1 "water-machine/api/v1"
)

// InitDeviceRouter 设备管理路由
func InitDeviceRouter(Router *gin.RouterGroup) {
	deviceRouter := Router.Group("device")
	{
		deviceRouter.POST("", v1.CreateDevice)      // 新增
		deviceRouter.PUT("", v1.UpdateDevice)       // 修改
		deviceRouter.DELETE("", v1.DeleteDevice)    // 删除
		deviceRouter.GET("list", v1.GetDeviceList)  // 查询列表
	}
} 