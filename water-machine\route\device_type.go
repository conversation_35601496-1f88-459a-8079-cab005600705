package route

import (
	"github.com/gin-gonic/gin"
	v1 "water-machine/api/v1"
)

// InitDeviceTypeRouter 设备类型管理路由
func InitDeviceTypeRouter(Router *gin.RouterGroup) {
	deviceTypeRouter := Router.Group("deviceType")
	{
		deviceTypeRouter.POST("", v1.CreateDeviceType)     // 新增
		deviceTypeRouter.PUT("", v1.UpdateDeviceType)      // 修改
		deviceTypeRouter.DELETE("", v1.DeleteDeviceType)   // 删除
		deviceTypeRouter.GET("list", v1.GetDeviceTypeList) // 查询列表
	}
}
