package route

import (
	"github.com/gin-gonic/gin"
	v1 "water-machine/api/v1"
)

// InitMachineRouter 机器管理路由
func InitMachineRouter(Router *gin.RouterGroup) {
	machineRouter := Router.Group("machine")
	{
		machineRouter.POST("", v1.CreateMachine)      // 新增
		machineRouter.PUT("", v1.UpdateMachine)       // 修改
		machineRouter.DELETE("", v1.DeleteMachine)    // 删除
		machineRouter.GET("list", v1.GetMachineList)  // 查询列表
		machineRouter.POST("import", v1.ImportMachineExcel) // Excel导入
	}
} 