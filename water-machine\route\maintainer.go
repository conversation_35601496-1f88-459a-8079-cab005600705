package route

import (
	v1 "water-machine/api/v1"

	"github.com/gin-gonic/gin"
)

// InitMaintainerRouter 运维人员管理路由
func InitMaintainerRouter(Router *gin.RouterGroup) {
	maintainerRouter := Router.Group("maintainer")
	{
		maintainerRouter.POST("", v1.CreateMaintainer)     // 新增
		maintainerRouter.PUT("", v1.UpdateMaintainer)      // 修改
		maintainerRouter.DELETE("", v1.DeleteMaintainer)   // 删除
		maintainerRouter.GET("list", v1.GetMaintainerList) // 查询列表
	}
}
