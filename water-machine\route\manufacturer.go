package route

import (
	"github.com/gin-gonic/gin"
	v1 "water-machine/api/v1"
)

// InitManufacturerRouter 厂家管理路由
func InitManufacturerRouter(Router *gin.RouterGroup) {
	manufacturerRouter := Router.Group("manufacturer")
	{
		manufacturerRouter.POST("", v1.CreateManufacturer)      // 新增
		manufacturerRouter.PUT("", v1.UpdateManufacturer)       // 修改
		manufacturerRouter.DELETE("", v1.DeleteManufacturer)    // 删除
		manufacturerRouter.GET("list", v1.GetManufacturerList)  // 查询列表
	}
} 