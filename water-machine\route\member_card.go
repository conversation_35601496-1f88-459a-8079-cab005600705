package route

import (
	v1 "water-machine/api/v1"

	"github.com/gin-gonic/gin"
)

// InitMemberCardRouter 会员卡管理路由
func InitMemberCardRouter(Router *gin.RouterGroup) {
	memberCardRouter := Router.Group("member-card")
	{
		memberCardRouter.POST("", v1.CreateMemberCard)                       // 新增
		memberCardRouter.PUT("", v1.UpdateMemberCard)                        // 修改
		memberCardRouter.DELETE("", v1.DeleteMemberCard)                     // 删除
		memberCardRouter.GET("list", v1.GetMemberCardList)                   // 查询列表
		memberCardRouter.POST("batch-generate", v1.BatchGenerateMemberCards) // 批量生成
		memberCardRouter.POST("recharge", v1.RechargeMemberCardBalanceApi)   // 充值余额
		memberCardRouter.POST("rebind", v1.RebindMemberCardApi)              // 换绑
		memberCardRouter.GET("rebind-list", v1.GetMemberCardRebindListApi)   // 换绑记录查询
	}
}
