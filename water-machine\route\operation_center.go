package route

import (
	"github.com/gin-gonic/gin"
	v1 "water-machine/api/v1"
)

// InitOperationCenterRouter 运营中心管理路由
func InitOperationCenterRouter(Router *gin.RouterGroup) {
	operationCenterRouter := Router.Group("operation-center")
	{
		operationCenterRouter.POST("", v1.CreateOperationCenter)      // 新增
		operationCenterRouter.PUT("", v1.UpdateOperationCenter)       // 修改
		operationCenterRouter.DELETE("", v1.DeleteOperationCenter)    // 删除
		operationCenterRouter.GET("list", v1.GetOperationCenterList)  // 查询列表
	}
} 