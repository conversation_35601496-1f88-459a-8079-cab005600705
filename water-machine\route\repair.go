package route

import (
	v1 "water-machine/api/v1"

	"github.com/gin-gonic/gin"
)

// InitRepairRouter 报修记录路由
func InitRepairRouter(Router *gin.RouterGroup) {
	repairRouter := Router.Group("repair")
	{
		repairRouter.POST("", v1.CreateRepairRecord)     // 新增
		repairRouter.PUT("", v1.UpdateRepairRecord)      // 修改
		repairRouter.DELETE("", v1.DeleteRepairRecord)   // 删除
		repairRouter.GET("list", v1.GetRepairRecordList) // 查询列表
	}
}
