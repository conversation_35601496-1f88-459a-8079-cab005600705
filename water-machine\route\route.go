package route

import (
	"github.com/gin-gonic/gin"
)

// InitWaterMachineRouter 初始化饮水机模块路由
// 统一注册所有饮水机相关的功能模块路由，便于维护和扩展。
func InitWaterMachineRouter(Router *gin.RouterGroup) {
	// 基础设置相关路由（如参数配置等）
	InitWaterMachineSettingRouter(Router)
	// 设备类型管理路由
	InitDeviceTypeRouter(Router)
	// 厂家管理路由
	InitManufacturerRouter(Router)
	// 设备管理路由
	InitDeviceRouter(Router)
	// 机器管理路由
	InitMachineRouter(Router)
	// 运营中心管理路由
	InitOperationCenterRouter(Router)
	// 会员卡管理路由
	InitMemberCardRouter(Router)
	// 运维人员管理路由
	InitMaintainerRouter(Router)
	// 报修记录管理路由
	InitRepairRouter(Router)
	// SIM卡管理路由
	InitSimCardRouter(Router)
	// 消费记录管理路由
	InitConsumeRecordRouter(Router)
	// 授权管理路由（交叉授权等）
	RegisterAuthorizationRoutes(Router)
	// 后续可以在这里添加其他功能模块的路由
}
