package route

import (
	"github.com/gin-gonic/gin"
	v1 "water-machine/api/v1"
)

// InitWaterMachineSettingRouter 初始化饮水机设置路由
func InitWaterMachineSettingRouter(Router *gin.RouterGroup) {
	WaterMachineSettingRouter := Router.Group("water-machine")
	{
		// 设置相关路由
		settingRouter := WaterMachineSettingRouter.Group("setting")
		{
			settingRouter.GET("", v1.GetWaterMachineSetting)           // 获取设置
			settingRouter.PUT("", v1.UpdateWaterMachineSetting)        // 更新设置
			settingRouter.POST("toggle", v1.ToggleWaterMachine)        // 切换开关状态
		}
	}
} 