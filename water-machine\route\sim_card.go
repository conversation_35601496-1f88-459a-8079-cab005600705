package route

import (
	v1 "water-machine/api/v1"

	"github.com/gin-gonic/gin"
)

// InitSimCardRouter SIM卡管理路由
func InitSimCardRouter(Router *gin.RouterGroup) {
	simCardRouter := Router.Group("sim-card")
	{
		simCardRouter.POST("", v1.CreateSimCard)                     // 新增
		simCardRouter.PUT("", v1.UpdateSimCard)                      // 修改
		simCardRouter.DELETE("", v1.DeleteSimCard)                   // 删除
		simCardRouter.GET("list", v1.GetSimCardList)                 // 查询列表
		simCardRouter.POST("bind-device", v1.BindSimCardToDeviceApi) // 绑定设备号
	}
}
