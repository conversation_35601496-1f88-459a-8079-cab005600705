package service

import (
	"water-machine/model"

	"gorm.io/gorm"
)

type AuthorizationListResult struct {
	List  []model.WaterAuthorization
	Total int64
}

// 分页查询授权记录
func GetAuthorizationList(db *gorm.DB, page, pageSize int) (AuthorizationListResult, error) {
	var res AuthorizationListResult
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	db = db.Model(&model.WaterAuthorization{})
	db.Count(&res.Total)
	err := db.Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Find(&res.List).Error
	return res, err
}

// 创建授权记录
func CreateAuthorization(db *gorm.DB, initiatorID, targetID uint) error {
	auth := model.WaterAuthorization{
		InitiatorID: initiatorID,
		TargetID:    targetID,
		Status:      model.AuthorizationPending,
	}
	return db.Create(&auth).Error
}

// 审核授权（变更状态）
func ReviewAuthorization(db *gorm.DB, id uint, status model.AuthorizationStatus) error {
	return db.Model(&model.WaterAuthorization{}).Where("id = ?", id).Update("status", status).Error
}
