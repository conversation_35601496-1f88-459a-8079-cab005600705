package service

import (
	"errors"
	"water-machine/model"

	"gorm.io/gorm"
)

// 创建消费记录，自动扣除会员卡余额
func CreateConsumeRecord(db *gorm.DB, deviceID uint, cardNo string, purchaseID, mallID uint, amount, waterVolume int) (*model.WaterConsumeRecord, error) {
	// 1. 查询会员卡
	var card model.WaterMemberCard
	err := db.Where("card_no = ?", cardNo).First(&card).Error
	if err != nil {
		return nil, errors.New("会员卡不存在")
	}
	if card.Balance < amount {
		return nil, errors.New("会员卡余额不足")
	}
	// 2. 扣除余额
	newBalance := card.Balance - amount
	err = db.Model(&model.WaterMemberCard{}).Where("card_no = ?", cardNo).Update("balance", newBalance).Error
	if err != nil {
		return nil, errors.New("扣除余额失败")
	}
	// 3. 创建消费记录
	record := model.WaterConsumeRecord{
		DeviceID:    deviceID,
		CardNo:      cardNo,
		PurchaseID:  purchaseID,
		MallID:      mallID,
		Amount:      amount,
		CardBalance: newBalance,
		WaterVolume: waterVolume,
	}
	err = db.Create(&record).Error
	if err != nil {
		return nil, errors.New("创建消费记录失败")
	}
	return &record, nil
}

// 消费记录分页+条件查询
func GetConsumeRecordList(db *gorm.DB, cond map[string]interface{}, page, pageSize int) (list []model.WaterConsumeRecord, total int64, err error) {
	db = db.Model(&model.WaterConsumeRecord{})
	if v, ok := cond["device_id"]; ok && v != nil {
		db = db.Where("device_id = ?", v)
	}
	if v, ok := cond["card_no"]; ok && v != nil && v != "" {
		db = db.Where("card_no = ?", v)
	}
	if v, ok := cond["purchase_id"]; ok && v != nil {
		db = db.Where("purchase_id = ?", v)
	}
	if v, ok := cond["mall_id"]; ok && v != nil {
		db = db.Where("mall_id = ?", v)
	}
	db.Count(&total)
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	err = db.Preload("Machine").Preload("Purchase").Preload("Mall").Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).Find(&list).Error
	return
}
