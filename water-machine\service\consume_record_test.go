package service

import (
	"testing"
	"water-machine/model"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func getTestDB() *gorm.DB {
	db, _ := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	db.AutoMigrate(&model.WaterMemberCard{}, &model.WaterConsumeRecord{})
	return db
}

func TestCreateConsumeRecord(t *testing.T) {
	db := getTestDB()
	// 先插入一张会员卡
	card := model.WaterMemberCard{CardNo: "12345678901", Balance: 1000, Status: "正常", PurchaseSideID: 1, MallID: 1}
	db.Create(&card)
	// 创建消费记录
	record, err := CreateConsumeRecord(db, 1, "12345678901", 1, 1, 200, 500)
	if err != nil {
		t.Fatalf("创建消费记录失败: %v", err)
	}
	if record.CardBalance != 800 {
		t.Errorf("扣费后余额应为800，实际为%d", record.CardBalance)
	}
}

func TestGetConsumeRecordList(t *testing.T) {
	db := getTestDB()
	// 插入测试数据
	db.Create(&model.WaterConsumeRecord{DeviceID: 1, CardNo: "12345678901", PurchaseID: 1, MallID: 1, Amount: 100, CardBalance: 900, WaterVolume: 500})
	cond := map[string]interface{}{"card_no": "12345678901"}
	list, total, err := GetConsumeRecordList(db, cond, 1, 10)
	if err != nil {
		t.Fatalf("查询消费记录失败: %v", err)
	}
	if total != 1 || len(list) != 1 {
		t.Errorf("期望1条记录，实际total=%d, len(list)=%d", total, len(list))
	}
}
