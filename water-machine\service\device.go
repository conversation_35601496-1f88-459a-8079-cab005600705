package service

import (
	"water-machine/model"
	"yz-go/source"
)

// CreateDevice 新增设备
func CreateDevice(d *model.WaterDevice) error {
	return source.DB().Create(d).Error
}

// UpdateDevice 修改设备
func UpdateDevice(d *model.WaterDevice) error {
	return source.DB().Model(&model.WaterDevice{}).Where("id = ?", d.ID).Updates(map[string]interface{}{
		"name":            d.Name,
		"type_id":         d.TypeID,
		"device_model":    d.<PERSON>,
		"manufacturer_id": d.ManufacturerID,
	}).Error
}

// DeleteDevice 删除设备
func DeleteDevice(id uint) error {
	return source.DB().Delete(&model.WaterDevice{}, id).Error
}

// GetDeviceList 查询设备列表
func GetDeviceList() (list []model.WaterDevice, err error) {
	err = source.DB().Find(&list).Error
	return
}
