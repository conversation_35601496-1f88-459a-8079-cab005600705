package service

import (
	"water-machine/model"
	"yz-go/source"
)

// CreateDeviceType 新增设备类型
func CreateDeviceType(deviceType *model.WaterDeviceType) error {
	return source.DB().Create(deviceType).Error
}

// UpdateDeviceType 修改设备类型
func UpdateDeviceType(deviceType *model.WaterDeviceType) error {
	return source.DB().Model(&model.WaterDeviceType{}).Where("id = ?", deviceType.ID).Update("name", deviceType.Name).Error
}

// DeleteDeviceType 删除设备类型
func DeleteDeviceType(id uint) error {
	return source.DB().Delete(&model.WaterDeviceType{}, id).Error
}

// GetDeviceTypeList 查询设备类型列表
func GetDeviceTypeList() (list []model.WaterDeviceType, err error) {
	err = source.DB().Find(&list).Error
	return
}
