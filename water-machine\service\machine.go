package service

import (
	"strconv"
	"water-machine/model"
	"yz-go/source"

	"github.com/xuri/excelize/v2"
)

// CreateMachine 新增机器
func CreateMachine(m *model.WaterMachine) error {
	return source.DB().Create(m).Error
}

// UpdateMachine 修改机器
func UpdateMachine(m *model.WaterMachine) error {
	return source.DB().Model(&model.WaterMachine{}).Where("id = ?", m.ID).Updates(m).Error
}

// DeleteMachine 删除机器
func DeleteMachine(id uint) error {
	return source.DB().Delete(&model.WaterMachine{}, id).Error
}

// GetMachineList 查询机器列表
func GetMachineList() (list []model.WaterMachine, err error) {
	err = source.DB().Find(&list).Error
	return
}

// ImportMachineExcel 业务逻辑：批量导入机器
func ImportMachineExcelFromFile(f *excelize.File) (int, error) {
	rows, err := f.Get<PERSON>ows("Sheet1")
	if err != nil {
		return 0, err
	}
	var importCount int
	for i, row := range rows {
		if i == 0 {
			continue // 跳过表头
		}
		if len(row) < 12 {
			continue // 字段不全
		}
		m := model.WaterMachine{
			Name:        row[0],
			DeviceID:    parseUint(row[1]),
			PurchaseID:  parseUint(row[2]),
			MallID:      parseUint(row[3]),
			Maintainers: []byte(row[4]),
			Province:    row[5],
			City:        row[6],
			District:    row[7],
			Street:      row[8],
			Address:     row[9],
			Longitude:   parseFloat(row[10]),
			Latitude:    parseFloat(row[11]),
		}
		if err := CreateMachine(&m); err == nil {
			importCount++
		}
	}
	return importCount, nil
}

func parseUint(s string) uint {
	v, _ := strconv.ParseUint(s, 10, 64)
	return uint(v)
}
func parseFloat(s string) float64 {
	v, _ := strconv.ParseFloat(s, 64)
	return v
}
