package service

import (
	"water-machine/model"
	"water-machine/request"
	"yz-go/source"
)

// CreateMaintainer 新增运维人员
func CreateMaintainer(m *model.WaterMaintainer) error {
	return source.DB().Create(m).Error
}

// UpdateMaintainer 修改运维人员
func UpdateMaintainer(m *model.WaterMaintainer) error {
	return source.DB().Model(&model.WaterMaintainer{}).Where("id = ?", m.ID).Updates(m).Error
}

// DeleteMaintainer 删除运维人员
func DeleteMaintainer(id uint) error {
	return source.DB().Delete(&model.WaterMaintainer{}, id).Error
}

// GetMaintainerList 查询运维人员列表
func GetMaintainerList() (list []model.WaterMaintainer, err error) {
	err = source.DB().Preload("MemberInfo").Preload("OperationCenterInfo").Find(&list).Error
	return
}

// 分页+条件查询
func GetMaintainerListWithPage(req request.MaintainerSearch) (list []model.WaterMaintainer, total int64, err error) {
	db := source.DB().Model(&model.WaterMaintainer{})
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.MemberID != 0 {
		db = db.Where("member_id = ?", req.MemberID)
	}
	if req.OperationCenterID != 0 {
		db = db.Where("operation_center_id = ?", req.OperationCenterID)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	page := req.Page
	pageSize := req.PageSize
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	err = db.Preload("MemberInfo").Preload("OperationCenterInfo").Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return
}
