package service

import (
	"water-machine/model"
	"yz-go/source"
)

// CreateManufacturer 新增厂家
func CreateManufacturer(m *model.WaterManufacturer) error {
	return source.DB().Create(m).Error
}

// UpdateManufacturer 修改厂家
func UpdateManufacturer(m *model.WaterManufacturer) error {
	return source.DB().Model(&model.WaterManufacturer{}).Where("id = ?", m.ID).Updates(map[string]interface{}{
		"name":  m.Name,
		"brand": m.Brand,
	}).Error
}

// DeleteManufacturer 删除厂家
func DeleteManufacturer(id uint) error {
	return source.DB().Delete(&model.WaterManufacturer{}, id).Error
}

// GetManufacturerList 查询厂家列表
func GetManufacturerList() (list []model.WaterManufacturer, err error) {
	err = source.DB().Find(&list).Error
	return
} 