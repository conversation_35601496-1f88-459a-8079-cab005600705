package service

import (
	"fmt"
	"math/rand"
	"time"
	"water-machine/model"
	"water-machine/request"
	"yz-go/source"

	"gorm.io/gorm"
)

// CreateMemberCard 新增会员卡
func CreateMemberCard(m *model.WaterMemberCard) error {
	return source.DB().Create(m).Error
}

// UpdateMemberCard 修改会员卡
func UpdateMemberCard(m *model.WaterMemberCard) error {
	return source.DB().Model(&model.WaterMemberCard{}).Where("id = ?", m.ID).Updates(m).Error
}

// DeleteMemberCard 删除会员卡
func DeleteMemberCard(id uint) error {
	return source.DB().Delete(&model.WaterMemberCard{}, id).Error
}

// GetMemberCardList 查询会员卡列表
func GetMemberCardList() (list []model.WaterMemberCard, err error) {
	err = source.DB().Find(&list).Error
	return
}

// BatchGenerateMemberCards 批量生成指定数量的唯一11位会员卡
func BatchGenerateMemberCards(count int, status string, purchaseSideID uint, mallID uint) ([]model.WaterMemberCard, error) {
	var cards []model.WaterMemberCard
	exist := make(map[string]struct{})
	rand.Seed(time.Now().UnixNano())
	for len(cards) < count {
		cardNo := random11Digit()
		if _, ok := exist[cardNo]; ok {
			continue
		}
		// 检查数据库是否已存在
		var cnt int64
		source.DB().Model(&model.WaterMemberCard{}).Where("card_no = ?", cardNo).Count(&cnt)
		if cnt > 0 {
			continue
		}
		exist[cardNo] = struct{}{}
		cards = append(cards, model.WaterMemberCard{
			CardNo:         cardNo,
			Status:         status,
			PurchaseSideID: purchaseSideID,
			MallID:         mallID,
			Balance:        100,
		})
	}
	// 批量插入
	if err := source.DB().Create(&cards).Error; err != nil {
		return nil, err
	}
	return cards, nil
}

// 会员卡充值余额
func RechargeMemberCardBalance(id uint, amount float64) error {
	if amount <= 0 {
		return fmt.Errorf("充值金额必须大于0")
	}
	return source.DB().Model(&model.WaterMemberCard{}).Where("id = ?", id).Update("balance", gorm.Expr("balance + ?", amount)).Error
}

// 分页+条件查询
func GetMemberCardListWithPage(req request.MemberCardSearch) (list []model.WaterMemberCard, total int64, err error) {
	db := source.DB().Model(&model.WaterMemberCard{})
	if req.CardNo != "" {
		db = db.Where("card_no LIKE ?", "%"+req.CardNo+"%")
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.PurchaseSideID != 0 {
		db = db.Where("purchase_side_id = ?", req.PurchaseSideID)
	}
	if req.MallID != 0 {
		db = db.Where("mall_id = ?", req.MallID)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	page := req.Page
	pageSize := req.PageSize
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	err = db.Preload("PurchaseSide").Preload("Mall").Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return
}

// 会员卡批量换绑（重新绑定采购端和商城，并记录换绑日志）
func RebindMemberCard(cardIDs []uint, newPurchaseSideID, newMallID uint) error {
	if len(cardIDs) == 0 {
		return fmt.Errorf("未指定需要换绑的会员卡")
	}

	var cards []model.WaterMemberCard
	if err := source.DB().Where("id IN ?", cardIDs).Find(&cards).Error; err != nil {
		return err
	}

	if len(cards) != len(cardIDs) {
		return fmt.Errorf("部分会员卡不存在")
	}

	var rebindRecords []model.WaterMemberCardRebind
	now := time.Now()

	for _, card := range cards {
		oldPurchaseSideID := card.PurchaseSideID
		oldMallID := card.MallID
		if oldPurchaseSideID == newPurchaseSideID && oldMallID == newMallID {
			return fmt.Errorf("会员卡[%s]新旧采购端和商城不能相同", card.CardNo)
		}
		rebindRecords = append(rebindRecords, model.WaterMemberCardRebind{
			CardNo:            card.CardNo,
			OldPurchaseSideID: oldPurchaseSideID,
			NewPurchaseSideID: newPurchaseSideID,
			OldMallID:         oldMallID,
			NewMallID:         newMallID,
			RebindTime:        now,
		})
	}

	// 批量更新会员卡
	if err := source.DB().Model(&model.WaterMemberCard{}).
		Where("id IN ?", cardIDs).
		Updates(map[string]interface{}{"purchase_side_id": newPurchaseSideID, "mall_id": newMallID}).Error; err != nil {
		return err
	}

	// 批量插入换绑日志
	if len(rebindRecords) > 0 {
		if err := source.DB().Create(&rebindRecords).Error; err != nil {
			return err
		}
	}

	return nil
}

func GetMemberCardRebindList(req request.MemberCardRebindSearch) (list []model.WaterMemberCardRebind, total int64, err error) {
	db := source.DB().Model(&model.WaterMemberCardRebind{})
	if req.CardNo != "" {
		db = db.Where("card_no = ?", req.CardNo)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	page := req.Page
	pageSize := req.PageSize
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	err = db.
		Preload("OldPurchaseSide").
		Preload("NewPurchaseSide").
		Preload("OldMall").
		Preload("NewMall").
		Order("rebind_time desc").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&list).Error
	return
}

func random11Digit() string {
	return fmt.Sprintf("%011d", rand.Int63n(1e11))
}
