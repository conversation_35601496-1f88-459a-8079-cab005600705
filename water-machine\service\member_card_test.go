package service

import (
	"testing"
	"water-machine/model"
	"water-machine/request"
)

// 新增会员卡测试
func TestCreateMemberCard(t *testing.T) {
	m := model.WaterMemberCard{
		CardNo:         "12345678901",
		Status:         "正常",
		PurchaseSideID: 1,
		MallID:         2,
		Balance:        100,
	}
	err := CreateMemberCard(&m)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("CreateMemberCard failed: %v", err)
	}
}

// 查询会员卡列表测试
func TestGetMemberCardList(t *testing.T) {
	list, err := GetMemberCardList()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetMemberCardList failed: %v", err)
	}
	_ = list // 可断言内容
}

// 修改会员卡测试
func TestUpdateMemberCard(t *testing.T) {
	m := model.WaterMemberCard{
		CardNo:         "12345678901",
		Status:         "停用",
		PurchaseSideID: 1,
		MallID:         2,
		Balance:        200,
	}
	m.ID = 1
	err := UpdateMemberCard(&m)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("UpdateMemberCard failed: %v", err)
	}
}

// 删除会员卡测试
func TestDeleteMemberCard(t *testing.T) {
	err := DeleteMemberCard(1)
	if err != nil {
		t.Errorf("DeleteMemberCard failed: %v", err)
	}
}

// 批量生成会员卡测试
func TestBatchGenerateMemberCards(t *testing.T) {
	cards, err := BatchGenerateMemberCards(2, "正常", 1, 2)
	if err != nil {
		t.Errorf("BatchGenerateMemberCards failed: %v", err)
	}
	if len(cards) != 2 {
		t.Errorf("expected 2 cards, got %d", len(cards))
	}
}

// 充值余额测试
func TestRechargeMemberCardBalance(t *testing.T) {
	id := uint(1)
	amount := 20.0
	err := RechargeMemberCardBalance(id, amount)
	if err != nil {
		t.Errorf("RechargeMemberCardBalance failed: %v", err)
	}
}

// 查询会员卡换绑记录测试
func TestGetMemberCardRebindList(t *testing.T) {
	req := request.MemberCardRebindSearch{Page: 1, PageSize: 10}
	list, total, err := GetMemberCardRebindList(req)
	if err != nil {
		t.Errorf("GetMemberCardRebindList failed: %v", err)
	}
	if total < 0 {
		t.Errorf("total should >= 0")
	}
	_ = list // 可断言内容
}

// 会员卡换绑测试
func TestRebindMemberCard(t *testing.T) {
	cardID := []uint{1, 2}
	newPurchaseSideID := uint(2)
	newMallID := uint(3)
	err := RebindMemberCard(cardID, newPurchaseSideID, newMallID)
	if err != nil {
		t.Errorf("RebindMemberCard failed: %v", err)
	}
}
