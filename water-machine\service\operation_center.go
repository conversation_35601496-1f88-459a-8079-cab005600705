package service

import (
	"water-machine/model"
	"water-machine/request"
	"yz-go/source"
)

// CreateOperationCenter 新增运营中心
func CreateOperationCenter(m *model.WaterOperationCenter) error {
	return source.DB().Create(m).Error
}

// UpdateOperationCenter 修改运营中心
func UpdateOperationCenter(m *model.WaterOperationCenter) error {
	return source.DB().Model(&model.WaterOperationCenter{}).Where("id = ?", m.ID).Updates(m).Error
}

// DeleteOperationCenter 删除运营中心
func DeleteOperationCenter(id uint) error {
	return source.DB().Delete(&model.WaterOperationCenter{}, id).Error
}

// GetOperationCenterList 查询运营中心列表
func GetOperationCenterList() (list []model.WaterOperationCenter, err error) {
	err = source.DB().Preload("Member").Find(&list).Error
	return
}

// 分页+条件查询
func GetOperationCenterListWithPage(req request.OperationCenterSearch) (list []model.WaterOperationCenter, total int64, err error) {
	db := source.DB().Model(&model.WaterOperationCenter{})
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.AdminAccount != "" {
		db = db.Where("admin_account = ?", req.AdminAccount)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	page := req.Page
	pageSize := req.PageSize
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	err = db.Preload("Member").Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	if err != nil {
		return
	}
	// 统计机器数量和运维人员数量
	for i := range list {
		// 1. 统计机器数量
		var purchaseIDs []uint
		source.DB().Table("application").Select("id").Where("member_id = ?", list[i].MemberID).Scan(&purchaseIDs)
		var machineCount int64
		if len(purchaseIDs) > 0 {
			source.DB().Table("water_machines").Where("purchase_id IN ?", purchaseIDs).Count(&machineCount)
			list[i].MachineCount = int(machineCount)
		} else {
			list[i].MachineCount = 0
		}
		// 2. 统计运维人员数量
		var maintainerCount int64
		source.DB().Table("water_maintainers").Where("operation_center_id = ?", list[i].ID).Count(&maintainerCount)
		list[i].MaintainerCount = int(maintainerCount)
	}
	return
}
