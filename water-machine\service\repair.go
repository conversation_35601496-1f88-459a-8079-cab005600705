package service

import (
	"water-machine/model"
	"water-machine/request"
	"yz-go/source"

	"gorm.io/gorm"
)

// CreateRepairRecord 新增报修记录
func CreateRepairRecord(m *model.WaterRepairRecord) error {
	return source.DB().Create(m).Error
}

// UpdateRepairRecord 修改报修记录
func UpdateRepairRecord(m *model.WaterRepairRecord) error {
	return source.DB().Model(&model.WaterRepairRecord{}).Where("id = ?", m.ID).Updates(m).Error
}

// DeleteRepairRecord 删除报修记录
func DeleteRepairRecord(id uint) error {
	return source.DB().Delete(&model.WaterRepairRecord{}, id).Error
}

// GetRepairRecordList 查询报修记录列表
func GetRepairRecordList() (list []model.WaterRepairRecord, err error) {
	err = source.DB().Find(&list).Error
	return
}

// 分页+条件查询
func GetRepairRecordListWithPage(req request.RepairRecordSearch) (list []model.WaterRepairRecord, total int64, err error) {
	db := source.DB().Model(&model.WaterRepairRecord{})
	if req.MachineID != 0 {
		db = db.Where("machine_id = ?", req.MachineID)
	}
	if req.Status != 0 {
		db = db.Where("status = ?", req.Status)
	}
	if req.MemberID != 0 {
		db = db.Where("member_id = ?", req.MemberID)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	page := req.Page
	pageSize := req.PageSize
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	err = db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return
}

// 查询报修记录时自动 Preload 会员、采购端、商城信息
func GetRepairRecords(db *gorm.DB, conditions map[string]interface{}) ([]model.WaterRepairRecord, error) {
	records := make([]model.WaterRepairRecord, 0)
	err := db.Model(&model.WaterRepairRecord{}).
		Preload("Member").
		Preload("Purchase").
		Preload("Mall").
		Where(conditions).
		Find(&records).Error
	return records, err
}
