package service

import (
	"water-machine/model"
	"yz-go/source"
)

// SaveWaterMachineSetting 保存饮水机设置
// @param: data model.WaterMachineSetting
// @return: err error
func SaveWaterMachineSetting(data model.WaterMachineSetting) (err error) {
	if data.ID != 0 {
		err = source.DB().Updates(&data).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	return err
}

// GetWaterMachineSetting 获取饮水机设置
func GetWaterMachineSetting() (err error, sysSetting model.WaterMachineSetting) {
	err = source.DB().Where("`key` = ?", "water_machine_setting").First(&sysSetting).Error
	return
}

// UpdateWaterMachineSetting 更新饮水机设置
func UpdateWaterMachineSetting(setting model.WaterMachineValue) (err error) {
	var sysSetting model.WaterMachineSetting
	sysSetting.Key = "water_machine_setting"
	sysSetting.Value = setting
	sysSetting.ID = setting.ID

	err = SaveWaterMachineSetting(sysSetting)
	if err != nil {
		return err
	}

	// 重置缓存
	model.ResetWaterMachineSetting()
	return nil
}
