package service

import (
	"fmt"
	"water-machine/model"
	"water-machine/request"
	"yz-go/source"
)

// CreateSimCard 新增SIM卡
func CreateSimCard(m *model.WaterSimCard) error {
	return source.DB().Create(m).Error
}

// UpdateSimCard 修改SIM卡
func UpdateSimCard(m *model.WaterSimCard) error {
	return source.DB().Model(&model.WaterSimCard{}).Where("id = ?", m.ID).Updates(m).Error
}

// DeleteSimCard 删除SIM卡
func DeleteSimCard(id uint) error {
	return source.DB().Delete(&model.WaterSimCard{}, id).Error
}

// GetSimCardList 查询SIM卡列表
func GetSimCardList() (list []model.WaterSimCard, err error) {
	err = source.DB().Find(&list).Error
	return
}

// 分页+条件查询
func GetSimCardListWithPage(req request.SimCardSearch) (list []model.WaterSimCard, total int64, err error) {
	db := source.DB().Model(&model.WaterSimCard{})
	if req.SimNo != "" {
		db = db.Where("sim_no LIKE ?", "%"+req.SimNo+"%")
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.Operator != "" {
		db = db.Where("operator = ?", req.Operator)
	}
	if req.DeviceNo != "" {
		db = db.Where("device_no = ?", req.DeviceNo)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	page := req.Page
	pageSize := req.PageSize
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	err = db.Offset((page - 1) * pageSize).Limit(pageSize).Find(&list).Error
	return
}

// 绑定SIM卡到设备号
func BindSimCardToDevice(simID uint, deviceNo string) error {
	if simID == 0 || deviceNo == "" {
		return fmt.Errorf("SIM卡ID和设备号不能为空")
	}
	return source.DB().Model(&model.WaterSimCard{}).Where("id = ?", simID).Update("device_no", deviceNo).Error
}
