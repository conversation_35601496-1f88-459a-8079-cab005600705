package service

import (
	"testing"
	"time"
	"water-machine/model"
)

// 新增SIM卡测试
func TestCreateSimCard(t *testing.T) {
	sim := model.WaterSimCard{
		SimNo:      "8986001234567890123",
		Status:     "正常",
		Operator:   "移动",
		MonthUsage: 123.45,
		ExpireAt:   time.Now().AddDate(1, 0, 0),
		DeviceNo:   "DEV123456",
		Amount:     50.0,
	}
	err := CreateSimCard(&sim)
	if err != nil {
		t.Errorf("CreateSimCard failed: %v", err)
	}
}

// 查询SIM卡列表测试
func TestGetSimCardList(t *testing.T) {
	_, err := GetSimCardList()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetSimCardList failed: %v", err)
	}
}

// 修改SIM卡测试
func TestUpdateSimCard(t *testing.T) {
	sim := model.WaterSimCard{
		SimNo:      "8986001234567890123",
		Status:     "停用",
		Operator:   "联通",
		MonthUsage: 200.0,
		ExpireAt:   time.Now().AddDate(2, 0, 0),
		DeviceNo:   "DEV654321",
		Amount:     100.0,
	}
	sim.ID = 1
	err := UpdateSimCard(&sim)
	if err != nil {
		t.Errorf("UpdateSimCard failed: %v", err)
	}
}

// 删除SIM卡测试
func TestDeleteSimCard(t *testing.T) {
	err := DeleteSimCard(1)
	if err != nil {
		t.Errorf("DeleteSimCard failed: %v", err)
	}
}

// 绑定SIM卡到设备号测试
func TestBindSimCardToDevice(t *testing.T) {
	simID := uint(1)
	deviceNo := "DEV99999966"
	err := BindSimCardToDevice(simID, deviceNo)
	if err != nil {
		t.Errorf("BindSimCardToDevice failed: %v", err)
	}
}
